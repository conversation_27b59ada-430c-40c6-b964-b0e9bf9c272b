﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Travis_backend.Migrations
{
    /// <inheritdoc />
    public partial class AddLiveChatV2ConfigAndUpdateWebClientSenderSchema : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "ChannelIdentityId",
                table: "SenderWebClientSenders",
                type: "nvarchar(400)",
                maxLength: 400,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ConversationId",
                table: "SenderWebClientSenders",
                type: "nvarchar(400)",
                maxLength: 400,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Metadata",
                table: "SenderWebClientSenders",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "UserProfileId",
                table: "SenderWebClientSenders",
                type: "nvarchar(400)",
                maxLength: 400,
                nullable: true);

            migrationBuilder.CreateTable(
                name: "ConfigLiveChatV2Configs",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CompanyId = table.Column<string>(type: "nvarchar(400)", maxLength: 400, nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    Settings = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ChannelIdentityId = table.Column<string>(type: "nvarchar(400)", maxLength: 400, nullable: true),
                    ChannelDisplayName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ConfigLiveChatV2Configs", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_SenderWebClientSenders_CompanyId_UserProfileId",
                table: "SenderWebClientSenders",
                columns: new[] { "CompanyId", "UserProfileId" });

            migrationBuilder.CreateIndex(
                name: "IX_SenderWebClientSenders_ConversationId_CompanyId",
                table: "SenderWebClientSenders",
                columns: new[] { "ConversationId", "CompanyId" });

            migrationBuilder.CreateIndex(
                name: "IX_ConfigLiveChatV2Configs_ChannelIdentityId",
                table: "ConfigLiveChatV2Configs",
                column: "ChannelIdentityId");

            migrationBuilder.CreateIndex(
                name: "IX_ConfigLiveChatV2Configs_ChannelIdentityId_CompanyId",
                table: "ConfigLiveChatV2Configs",
                columns: new[] { "ChannelIdentityId", "CompanyId" });

            migrationBuilder.CreateIndex(
                name: "IX_ConfigLiveChatV2Configs_CompanyId",
                table: "ConfigLiveChatV2Configs",
                column: "CompanyId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ConfigLiveChatV2Configs");

            migrationBuilder.DropIndex(
                name: "IX_SenderWebClientSenders_CompanyId_UserProfileId",
                table: "SenderWebClientSenders");

            migrationBuilder.DropIndex(
                name: "IX_SenderWebClientSenders_ConversationId_CompanyId",
                table: "SenderWebClientSenders");

            migrationBuilder.DropColumn(
                name: "ChannelIdentityId",
                table: "SenderWebClientSenders");

            migrationBuilder.DropColumn(
                name: "ConversationId",
                table: "SenderWebClientSenders");

            migrationBuilder.DropColumn(
                name: "Metadata",
                table: "SenderWebClientSenders");

            migrationBuilder.DropColumn(
                name: "UserProfileId",
                table: "SenderWebClientSenders");
        }
    }
}
