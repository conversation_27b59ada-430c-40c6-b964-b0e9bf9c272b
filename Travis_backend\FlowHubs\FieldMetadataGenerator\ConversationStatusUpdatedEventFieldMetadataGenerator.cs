﻿#nullable enable
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.Database.Services;
using Travis_backend.Extensions;
using Travis_backend.FlowHubs.Constants;
using Travis_backend.FlowHubs.Models;
using Travis_backend.FlowHubs.Services;
using Travis_backend.Utils;
using Newtonsoft.Json.Linq;

namespace Travis_backend.FlowHubs.FieldMetadataGenerator;

public sealed class ConversationStatusUpdatedEventFieldMetadataGenerator :
    FlowHubEventFieldMetadataGeneratorBase,
    IFlowHubEventFieldMetadataGenerator
{
    public string EventName => TriggerIds.ConversationStatusUpdated;

    public Dictionary<string, string> FieldPathOptionRequestPathDict { get; } = new()
    {
        { "contact.labels[*].LabelValue", "FlowHub/SelectOptions/Labels" },
        { "original_status", "FlowHub/SelectOptions/ConversationStatuses" },
        { "new_status", "FlowHub/SelectOptions/ConversationStatuses" },
        { "lists[*].id", "FlowHub/SelectOptions/ContactLists" },
        { "sleekflow_staff_id", "FlowHub/SelectOptions/Staffs" },
        { "contact.ContactOwner", "FlowHub/SelectOptions/Staffs" },
        { "contactOwner.id", "FlowHub/SelectOptions/Staffs" },
        { "contact.LastChannel", "FlowHub/SelectOptions/ChannelTypes" }
    };

    public ConversationStatusUpdatedEventFieldMetadataGenerator(
        IDbContextService dbContextService,
        IFieldMetadataVariableVisibilityService fieldMetadataVariableVisibilityService)
        : base(dbContextService, fieldMetadataVariableVisibilityService)
    {
    }

    public async Task<(HashSet<FieldMetadata> FieldMetadataSet, Type EventBodyType, string ContactObjectName)> GenerateAsync(
        string companyId,
        Dictionary<string, object?>? parameters)
    {
        var (userProfileDict, customFieldOptionsRequestPathDict) = await GenerateUserProfileDictAsync(companyId);
        var eventBody = JsonUtils.GenerateSampleObjectFromType<OnContactConversationStatusChangedEventBody>();

        eventBody.Contact = userProfileDict;
        eventBody.ConversationId = userProfileDict["conversation_id"].ToString()!;

        var eventBodyJsonString = eventBody.ToJson();
        var eventBodyJson = JObject.Parse(eventBodyJsonString);
        AddContactExpansionToEventBodyJson(eventBodyJson);

        var jsonData = JsonUtils.SimplifyJsonData(eventBodyJson.ToString());

        var fieldMetadataSet = GenerateFilteredFieldMetadata(jsonData, EventName);

        PopulateOptionRequestPath(
            fieldMetadataSet,
            customFieldOptionsRequestPathDict,
            FieldPathOptionRequestPathDict);

        return (fieldMetadataSet, typeof(OnContactConversationStatusChangedEventBody), nameof(eventBody.Contact));
    }
}