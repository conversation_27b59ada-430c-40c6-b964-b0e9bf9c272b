﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.MessageDomain.Models.Interfaces;

namespace Travis_backend.MessageDomain.Models
{
    public class WebClientSender : IMessagingChannelUser
    {
        [Key]
        public long Id { get; set; }

        // Used for live chat v2
        [MaxLength(400)]
        public string UserProfileId { get; set; } = null;

        // Used for live chat v2
        [MaxLength(400)]
        public string ConversationId { get; set; } = null;

        public string WebClientUUID { get; set; } = Guid.NewGuid().ToString();

        public string IPAddress { get; set; }

        public string Name { get; set; }

        public string locale { get; set; } = "en";

        public string SignalRConnectionId { get; set; }

        public DateTime? CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? UpdatedAt { get; set; } = DateTime.UtcNow;

        public string CompanyId { get; set; }

        public List<IPAddressInfo> IPAddressInfos { get; set; } = new List<IPAddressInfo>();

        public string Device { get; set; }

        public DeviceModel DeviceModel { get; set; }

        public string BrowserLocale { get; set; }

        public OnlineStatus OnlineStatus { get; set; } = OnlineStatus.Online;

        [NotMapped]
        public string ChannelType
        {
            get
            {
                // LiveChat V2 introduces persistent conversations and user profiles
                // When both identifiers exist, this indicates an active V2 session
                return !string.IsNullOrWhiteSpace(ChannelIdentityId) ? ChannelTypes.LiveChatV2 : ChannelTypes.LiveChat;
            }
        }

        // Used for live chat v2
        [MaxLength(400)]
        public string ChannelIdentityId { get; set; } = null;

        [NotMapped]
        public string UserIdentityId => WebClientUUID;

        // Used for live chat v2
        public Dictionary<string, object> Metadata { get; set; }
    }

    public enum DeviceModel
    {
        Desktop,
        Mobile,
        Tablet
    }

    public enum OnlineStatus
    {
        Offline,
        Online
    }

    public class IPAddressInfo : RecordTime
    {
        public long Id { get; set; }

        public long WebClientSenderId { get; set; }

        public string WebPath { get; set; }

        public string IPAddress { get; set; }

        public string IPAddressType { get; set; }

        public string Country { get; set; }

        public string Area { get; set; }

        public string Address { get; set; }

        public string OrganisationName { get; set; }

        public string BusinessName { get; set; }

        public string BusinessWebsite { get; set; }

        public string ISP { get; set; }

        public string Timezone { get; set; }

        public string region { get; set; }

        public string City { get; set; }

        public string CountryCode { get; set; }

        public string Locale { get; set; }
    }
}