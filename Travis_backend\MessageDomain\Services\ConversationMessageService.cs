﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using Force.DeepCloner;
using Hangfire;
using LinqKit;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using RestSharp;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.AutomationDomain.Models;
using Travis_backend.BroadcastDomain.Exceptions;
using Travis_backend.Cache;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.ClientCustomDomain.Services;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.CompanyDomain.ViewModels;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ContactDomain.ViewModels;
using Travis_backend.ConversationDomain.ConversationAccessControl;
using Travis_backend.ConversationDomain.ConversationInboxFilterConstants;
using Travis_backend.ConversationDomain.ConversationQueryables;
using Travis_backend.ConversationDomain.ConversationResolver;
using Travis_backend.ConversationDomain.ConversationSpecifications;
using Travis_backend.ConversationDomain.ConversationSpecifications.RbacConversationSpecifications.
    InboxViewSpecifications;
using Travis_backend.ConversationDomain.ConversationSpecifications.RbacConversationSpecifications.SpecificationBuilders;
using Travis_backend.ConversationDomain.Extensions;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.Services;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationDomain.ViewModels.Mappers;
using Travis_backend.Database;
using Travis_backend.Database.Services;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.FileDomain.Models;
using Travis_backend.FileDomain.Services;
using Travis_backend.FileDomain.ViewModels;
using Travis_backend.FlowHubs;
using Travis_backend.FlowHubs.Models;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.ChannelMessageProvider;
using Travis_backend.MessageDomain.Exceptions;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.Services;
using Travis_backend.MessageDomain.ViewModels;
using Travis_backend.OpenTelemetry.Meters;
using Travis_backend.OpenTelemetry.Meters.Constants;
using Travis_backend.PiiMaskingDomain.Services;
using Travis_backend.SignalR;
using Travis_backend.StripeIntegrationDomain.Services;
using Travis_backend.Telemetries;
using Travis_backend.Telemetries.Constants;
using Travis_backend.TenantHubDomain.Services;
using Travis_backend.TikTokAdsIntegrationDomain;
using WABA360Dialog.ApiClient.Payloads.Models.MessageObjects.InteractiveObjects;
using static Travis_backend.ContactDomain.Services.UserProfileService;
using File = System.IO.File;
using MessageStatus = Travis_backend.MessageDomain.Models.MessageStatus;

namespace Travis_backend.ConversationServices
{
    public interface IConversationMessageService
    {
        Task<IList<ConversationMessage>> SendMessage(
            Conversation conversation,
            ConversationMessage conversationMessage,
            bool triggerActivity = true,
            bool EnableChatbot = false,
            bool EnableTranslation = false);

        Task<IList<ConversationMessage>> SendFileMessageByFBURL(
            Conversation conversation,
            ConversationMessage conversationMessage,
            List<FileURLMessage> fileURLMessages,
            bool triggerActivity = true,
            bool EnableChatbot = false,
            bool EnableTranslation = false);

        Task<IList<ConversationMessage>> SendFileMessage(
            Conversation conversation,
            ConversationMessage conversationMessage,
            ConversationMessageViewModel fileMessageViewModel,
            bool triggerActivity = true,
            bool EnableChatbot = false,
            bool EnableTranslation = false);

        Task<IList<ConversationMessage>> ReceiveMessageAsync(
            Conversation conversation,
            ConversationMessage conversationMessage,
            bool triggerActivity = true);

        Task<IList<ConversationMessage>> ReceiveFileMessageByUrlAsync(
            Conversation conversation,
            ConversationMessage conversationMessage,
            List<FileURLMessage> fileUrlMessages,
            bool triggerActivity = true);

        Task<IList<ConversationMessage>> ReceiveFileMessageAsync(
            Conversation conversation,
            ConversationMessage conversationMessage,
            ConversationMessageViewModel fileMessageViewModel,
            bool triggerActivity = true);

        Task MessageCompletion(
            Conversation conversation,
            ConversationMessage conversationMessage,
            bool triggerActivity = true);

        Task<Conversation> ChangeConversationAssignedTeam(
            Conversation conversation,
            CompanyTeam companyTeam,
            bool isTriggerUpdate = true,
            string changedBy = null,
            UserProfileSource? userProfileSource = null);

        Task<Conversation> ChangeConversationAssignee(
            Conversation conversation,
            Staff assignee,
            bool isTriggerUpdate = true,
            string changedBy = null,
            UserProfileSource? userProfileSource = null);

        Task<Conversation> ChangeConversationStatus(
            string conversationId,
            string staffId,
            StatusViewModel statusViewModel,
            bool setUnread = false,
            UserProfileSource? userProfileSource = null);

        Task<List<string>> FormatParams(UserProfile userProfile, List<string> template_params);

        Task<List<string>> FormatParamsWithPaymentUrl(
            UserProfile userProfile,
            List<string> template_params,
            string paymentUrl = null);

        Task<string> SendAutomatedMessage(Conversation conversation, AutomationAction automationRule);

        Task<ConversationMessage> SendConversationNote(
            string companyId,
            string conversationId,
            string staffId,
            ConversationMessage conversationMessage,
            ConversationNoteViewModel conversationNoteViewModel);

        Task RemoveScheduledMessage(string companyId, DeleteMessagesInput input);

        Task DeleteMessage(DeleteMessageInput input);

        Task<ConversationWithCountViewModel> GetFormattedConversations(
            Staff companyUser,
            string assignedTo,
            int offset,
            int limit,
            string status,
            string channels,
            DateTime? afterUpdatedAt,
            DateTime? afterModifiedAt,
            string channelIds,
            string tags,
            long? teamId,
            bool? isTeamUnassigned,
            bool? isUnread,
            string orderBy,
            string version = "1",
            bool? isAssigned = null,
            bool? isCollaborator = null);

        Task<IQueryable<Conversation>> GetConversations(
            string companyId,
            Staff staff,
            string status = "all",
            string assignedTo = "all",
            string channels = null,
            DateTime? afterUpdatedAt = null,
            DateTime? afterModifiedAt = null,
            string channelIds = null,
            string tags = null,
            long? teamId = null,
            bool? isTeamUnassigned = null,
            bool? isUnread = null,
            string orderBy = null,
            string version = "1",
            bool? isAssigned = null,
            bool? isCollaborator = null);

        Task<IQueryable<Conversation>> DefaultGetConversations(
            string companyId,
            Staff staff,
            string status = "all",
            string assignedTo = "all",
            string channels = null,
            DateTime? afterUpdatedAt = null,
            DateTime? afterModifiedAt = null,
            string channelIds = null,
            string tags = null,
            long? teamId = null,
            bool? isTeamUnassigned = null,
            bool? isUnread = null,
            string orderBy = null,
            string version = "1",
            bool? isAssigned = null,
            bool? isCollaborator = null);

        IOrderedQueryable<ConversationMessage> GetConversationMessagesHistory(
            string companyId,
            string staffId = null,
            string status = null,
            string channels = null,
            DateTime? messageFrom = null,
            DateTime? messageTo = null,
            string channelIds = null,
            string tags = null,
            long? teamId = null,
            bool? isTeamUnassigned = null);

        IQueryable<ConversationMessage> GetConversationMessage(
            string conversationId,
            string companyId,
            long? beforeMessageId = null,
            long? afterMessageId = null,
            long? beforeTimestamp = null,
            long? afterTimestamp = null,
            string channels = null,
            string channelIds = null,
            bool? IsFromUser = null);

        Task SendReadMoreMessage(
            string companyId,
            string conversationId,
            long? whatsappSenderId,
            string messageContent,
            string contentSid = null,
            List<UploadedFile> uploadedFiles = null,
            DeliveryType deliveryType = DeliveryType.ReadMore);

        Task SendWhatsapp360DialogOptInButtonMessage(
            string companyId,
            string conversationId,
            long whatsapp360dialogConfigId);

        Task SendWhatsapp360DialogReadMoreMessage(
            string companyId,
            string conversationId,
            long whatsapp360dialogConfigId,
            long conversationMessageId);

        Task SendWhatsappCloudApiOptInButtonMessage(
            string companyId,
            string conversationId,
            long whatsappCloudApiConfigId,
            long conversationMessageId);

        Task SendWhatsappCloudApiReadMoreMessage(
            string companyId,
            string conversationId,
            long whatsappCloudApiConfigId,
            long conversationMessageId);

        Task ReadConversation(string conversationId, Staff companyUser, bool isForEveryone = false);

        Task ReadConversationPersonally(string conversationId, Staff companyUser);

        Task<ConversationStatusResponseViewModel> MarkConversationAsUnread(string conversationId, Staff companyUser);

        Task<bool> SendOptInMessageTwilio(ConversationMessage message);

        Task<ConversationWithCountViewModel> SearchConversationMessagesAsync(
            Staff companyUser,
            string assignedTo,
            string keywords,
            int offset,
            int limit,
            string status,
            string channels,
            string channelIds,
            long? teamId,
            string behaviourVersion = "1",
            bool isAllowCache = true);

        Task<List<ConversationMessageOutput>> GetConversationLastMessagesAsync(
            string sleekflowCompanyId,
            string userProfileId,
            int offSet,
            int limit,
            List<string> targetedChannels,
            string channelIdentityId = null,
            DateTimeOffset? retrievalWindowTimestamp = null,
            DateTimeOffset? agentSessionStartAtTimestamp = null);

        Task<ConversationMessage?> GetOptInReplyTargetUndeliveredMessage(
            ConversationMessage receivedMessage);

        T ExtractValueFromConversationMessageMetadata<T>(ConversationMessage conversationMessage, string key);

        Task<ConversationMessage> SendConversationIndicatorMessage(
            Conversation conversation,
            ConversationMessage conversationMessage);

        Task AddConversationMessageCache<T>(
            string conversationId,
            ConversationMessageCacheKeyPattern cacheKeyPattern,
            T data);

        Task AddConversationMessageV2Cache<T>(
            string companyId,
            ConversationMessageV2CacheKeyPattern cacheKeyPattern,
            T data);

        Task AddGetConversationMessagesCache<T>(
            string conversationId,
            GetConversationMessagesCacheKeyPattern cacheKeyPattern,
            T data);

        Task RemoveConversationMessageV2Cache(string companyId);

        Task RemoveConversationMessageCache(string conversationId);

        Task RemoveGetConversationMessagesCache(string conversationId);

        Task RemoveCache(string companyId, string conversationId);

        /// <summary>
        /// Only Uploaded Files will be included in the query.
        /// </summary>
        /// <returns>[ConversationIds]: [LatestMessage].</returns>
        Task<Dictionary<string, ConversationMessage>> BulkGetLatestConversationMessageByConversationIdAsync(
            string companyId,
            List<string> conversationIds,
            CancellationToken cancellationToken);

        /// <summary>
        /// Checks if the conversation message contains both a valid phone number with country code and an email address.
        /// </summary>
        /// <param name="conversationMessage">The conversation message to check.</param>
        /// <param name="conversation">Optional conversation context.</param>
        /// <returns>True if both phone number with country code and email address are found, false otherwise.</returns>
        bool CheckMessageContainsPhoneNumberOrEmail(ConversationMessage conversationMessage, Conversation conversation = null);
    }


    public class ConversationMessageService : IConversationMessageService
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly IMapper _mapper;
        private readonly IConfiguration _configuration;
        private readonly IConversationNoCompanyResponseViewModelMapper _conversationNoCompanyResponseViewModelViewModelMapper;
        private readonly ILogger<ConversationMessageService> _logger;
        private readonly ISignalRService _signalRService;
        private readonly IUserProfileService _userProfileService;
        private readonly IUploadService _uploadService;
        private readonly IAzureBlobStorageService _azureBlobStorageService;
        private readonly IMediaEncodingService _mediaEncodingService;
        private readonly IEmailNotificationService _emailNotificationService;
        private readonly ICompanyService _companyService;
        private readonly ICompanyUsageService _companyUsageService;
        private readonly IMediaProcessService _mediaProcessService;
        private readonly ICacheManagerService _cacheManagerService;
        private readonly ILockService _lockService;
        private readonly IConversationAssigneeService _conversationAssigneeService;
        private readonly ISleekPayService _sleekPayService;
        private readonly IConversationService _conversationService;
        private readonly IUserProfileHooks _userProfileHooks;
        private readonly IZDotComService _zDotComService;
        private readonly IConversationResolver _conversationResolver;
        private readonly IChannelMessageProvider _channelMessageProvider;
        private readonly IMessageChannelSwitcher _messageChannelSwitcher;
        private readonly IDbContextService _dbContextService;
        private readonly IFlowHubService _flowHubService;
        private readonly IPiiMaskingService _piiMaskingService;
        private readonly ITicketingHubMessageService _ticketingHubMessageService;
        private readonly IConversationMeters _conversationMeters;
        private readonly bool _isEnableTicketingLogic;
        private readonly IMeasureQueryTimeService _measureQueryTimeService;
        private readonly IAccessControlAggregationService _accessControlAggregationService;
        private readonly IApplicationInsightsTelemetryTracer _applicationInsightsTelemetryTracer;

        private readonly IConversationPermissionControlService _conversationPermissionControlService;
        private readonly IRbacService _rbacService;
        private readonly IInboxSettingManager _inboxSettingManager;
        private readonly IServiceProvider _serviceProvider;
        private readonly IMentionQueryableResolver _mentionQueryableResolver;
        private readonly IConversationNoCompanyResponseViewModelMapper _conversationNoCompanyResponseViewModelMapper;

        public ConversationMessageService(
            ApplicationDbContext appDbContext,
            IMapper mapper,
            IConfiguration configuration,
            ILogger<ConversationMessageService> logger,
            ISignalRService signalRService,
            IUserProfileService userProfileService,
            IUploadService uploadService,
            IAzureBlobStorageService azureBlobStorageService,
            IMediaEncodingService mediaEncodingService,
            IEmailNotificationService emailNotificationService,
            ICompanyService companyService,
            ICompanyUsageService companyUsageService,
            IMediaProcessService mediaProcessService,
            ICacheManagerService cacheManagerService,
            ILockService lockService,
            IConversationAssigneeService conversationAssigneeService,
            ISleekPayService sleekPayService,
            IConversationService conversationService,
            IUserProfileHooks userProfileHooks,
            IZDotComService zDotComService,
            IConversationResolver conversationResolver,
            IChannelMessageProvider channelMessageProvider,
            IMessageChannelSwitcher messageChannelSwitcher,
            IDbContextService dbContextService,
            IFlowHubService flowHubService,
            IPiiMaskingService piiMaskingService,
            ITicketingHubMessageService ticketingHubMessageService,
            IConversationMeters conversationMeters,
            IMeasureQueryTimeService measureQueryTimeService,
            IAccessControlAggregationService accessControlAggregationService,
            IConversationPermissionControlService conversationPermissionControlService,
            IApplicationInsightsTelemetryTracer applicationInsightsTelemetryTracer,
            IServiceProvider serviceProvider,
            IInboxSettingManager inboxSettingManager,
            IMentionQueryableResolver mentionQueryableResolver,
            IConversationNoCompanyResponseViewModelMapper conversationNoCompanyResponseViewModelMapper)
        {
            _appDbContext = appDbContext;
            _mapper = mapper;
            _configuration = configuration;
            _conversationNoCompanyResponseViewModelViewModelMapper = conversationNoCompanyResponseViewModelMapper;

            // _translateAPI = RestService.For<TranslateAPI>("https://api.cognitive.microsofttranslator.com/");
            _logger = logger;
            _signalRService = signalRService;
            _userProfileService = userProfileService;
            _uploadService = uploadService;
            _azureBlobStorageService = azureBlobStorageService;
            _mediaEncodingService = mediaEncodingService;
            _emailNotificationService = emailNotificationService;
            _companyService = companyService;
            _companyUsageService = companyUsageService;
            _mediaProcessService = mediaProcessService;
            _cacheManagerService = cacheManagerService;
            _lockService = lockService;
            _conversationAssigneeService = conversationAssigneeService;
            _sleekPayService = sleekPayService;
            _conversationService = conversationService;
            _userProfileHooks = userProfileHooks;
            _zDotComService = zDotComService;
            _conversationResolver = conversationResolver;
            _channelMessageProvider = channelMessageProvider;
            _messageChannelSwitcher = messageChannelSwitcher;
            _dbContextService = dbContextService;
            _flowHubService = flowHubService;
            _piiMaskingService = piiMaskingService;
            _ticketingHubMessageService = ticketingHubMessageService;
            _conversationMeters = conversationMeters;
            _isEnableTicketingLogic = _configuration.GetValue<bool>("TicketingHub:IsEnableTicketingLogic");
            _measureQueryTimeService = measureQueryTimeService;
            _accessControlAggregationService = accessControlAggregationService;
            _applicationInsightsTelemetryTracer = applicationInsightsTelemetryTracer;
            _serviceProvider = serviceProvider;
            _inboxSettingManager = inboxSettingManager;
            _rbacService = serviceProvider.GetRequiredService<IRbacService>();
            _conversationPermissionControlService = conversationPermissionControlService;
            _mentionQueryableResolver = mentionQueryableResolver;
            _conversationNoCompanyResponseViewModelMapper = conversationNoCompanyResponseViewModelMapper;
        }

        public async Task<ConversationWithCountViewModel> GetFormattedConversations(
            Staff companyUser,
            string assignedTo,
            int offset,
            int limit,
            string status,
            string channels,
            DateTime? afterUpdatedAt,
            DateTime? afterModifiedAt,
            string channelIds,
            string tags,
            long? teamId,
            bool? isTeamUnassigned,
            bool? isUnread,
            string orderBy,
            string version = "1",
            bool? isAssigned = null,
            bool? isCollaborator = null)
        {
            var isRbacEnabled = _rbacService.IsRbacEnabled();

            if (isRbacEnabled)
            {
                return await RbacGetFormattedConversations(
                    companyUser,
                    assignedTo,
                    offset,
                    limit,
                    status,
                    channels,
                    afterUpdatedAt,
                    afterModifiedAt,
                    channelIds,
                    tags,
                    teamId,
                    isTeamUnassigned,
                    isUnread,
                    orderBy,
                    version,
                    isAssigned,
                    isCollaborator);
            }

            return await DefaultGetFormattedConversations(
                companyUser,
                assignedTo,
                offset,
                limit,
                status,
                channels,
                afterUpdatedAt,
                afterModifiedAt,
                channelIds,
                tags,
                teamId,
                isTeamUnassigned,
                isUnread,
                orderBy,
                version,
                isAssigned,
                isCollaborator);
        }

        public async Task<ConversationWithCountViewModel> DefaultGetFormattedConversations(
            Staff companyUser,
            string assignedTo,
            int offset,
            int limit,
            string status,
            string channels,
            DateTime? afterUpdatedAt,
            DateTime? afterModifiedAt,
            string channelIds,
            string tags,
            long? teamId,
            bool? isTeamUnassigned,
            bool? isUnread,
            string orderBy,
            string version,
            bool? isAssigned,
            bool? isCollaborator)
        {
            try
            {
                _logger.LogInformation(
                    $"GetConversations_{companyUser.CompanyId}_{companyUser.Id}_{assignedTo}_{offset}_{limit}_{status}_{channels}_{afterUpdatedAt}_{afterModifiedAt}_{channelIds}_{tags}_{teamId}_{isTeamUnassigned}_{isUnread}_{orderBy}_{isAssigned}_{isCollaborator}");

                if (limit > 100)
                {
                    limit = 100;
                }

                var conversationMessageV2CacheKeyPattern = new ConversationMessageV2CacheKeyPattern(
                    companyUser.Id,
                    assignedTo,
                    offset,
                    limit,
                    status,
                    channels,
                    afterUpdatedAt,
                    afterModifiedAt,
                    channelIds,
                    tags,
                    teamId,
                    isTeamUnassigned,
                    isUnread,
                    orderBy,
                    version,
                    isAssigned,
                    isCollaborator);

                var data = await _cacheManagerService.GetCacheAsync(conversationMessageV2CacheKeyPattern);

                if (!string.IsNullOrEmpty(data))
                {
                    var result = JsonConvert.DeserializeObject<ConversationWithCountViewModel>(data);
                    return result;
                }

                if (companyUser != null)
                {
                    _logger.LogInformation(
                        $"{companyUser.IdentityId} GetConversations: assignTo {assignedTo}, offset: {offset}, limit: {limit}");

                    try
                    {
                        var conversationsQueryable = await GetConversations(
                            companyUser.CompanyId,
                            companyUser,
                            status,
                            assignedTo,
                            channels,
                            afterUpdatedAt,
                            afterModifiedAt,
                            channelIds,
                            tags,
                            teamId,
                            isTeamUnassigned,
                            isUnread,
                            orderBy,
                            version,
                            isAssigned,
                            isCollaborator);

                        var channelList = new List<string>();

                        if (!string.IsNullOrEmpty(channels))
                        {
                            channelList = channels
                                .Split(",")
                                .ToList();
                        }

                        var channelIdList = new List<string>();

                        if (!string.IsNullOrEmpty(channelIds))
                        {
                            channelIdList = channelIds
                                .Split(",")
                                .ToList();
                        }

                        // Add stopwatch for conversationIds query
                        var conversationIdsStopwatch = Stopwatch.StartNew();

                        var conversationIds = await conversationsQueryable
                            .Select(c => c.Id)
                            .Skip(offset)
                            .Take(limit)
                            .ToListAsync();

                        conversationIdsStopwatch.Stop();
                        _logger.LogInformation(
                            $"[PERFORMANCE] conversationIds query executed in {conversationIdsStopwatch.ElapsedMilliseconds} ms - " +
                            $"Company: {companyUser.CompanyId}, User: {companyUser.Id}, " +
                            $"Parameters: assignedTo={assignedTo}, status={status}, limit={limit}, offset={offset}, " +
                            $"ResultCount: {conversationIds.Count}");

                        var conversationsStopwatch = Stopwatch.StartNew();

                        var filteredConversations = conversationIds.Any()
                            ? _appDbContext.Conversations.Where(c => conversationIds.Contains(c.Id))
                            : _appDbContext.Conversations.Where(c => false);

                        var conversations = conversationIds.Any()
                            ? await filteredConversations
                                .Include(x => x.UserProfile)
                                .Include(x => x.facebookUser)
                                .Include(x => x.WhatsappUser)
                                .Include(x => x.Assignee.Identity)
                                .Include(x => x.EmailAddress)
                                .Include(x => x.WebClient)
                                .Include(x => x.WeChatUser)
                                .Include(x => x.LineUser)
                                .Include(x => x.SMSUser)
                                .Include(x => x.InstagramUser)
                                .Include(x => x.WhatsApp360DialogUser)
                                .Include(x => x.WhatsappCloudApiUser)
                                .Include(x => x.TelegramUser)
                                .Include(x => x.TikTokUser)
                                .Include(x => x.ViberUser)
                                .Include(x => x.AssignedTeam)
                                .Include(x => x.ConversationBookmarks)
                                .ToListAsync()
                            : new List<Conversation>();

                        conversations = ApplyOrderBy(conversations, orderBy, companyUser);

                        conversationsStopwatch.Stop();
                        _logger.LogInformation(
                            $"[PERFORMANCE] conversations query executed in {conversationsStopwatch.ElapsedMilliseconds} ms ");

                        var bookmarkedConversationIdsStopwatch = Stopwatch.StartNew();

                        // Bookmarks
                        var bookmarkedConversationIds =
                            conversationIds.Any()
                                ? (await _appDbContext.ConversationBookmarks
                                    .Where(
                                        x =>
                                            conversationIds.Contains(x.ConversationId)
                                            && x.StaffId == companyUser.Id)
                                    .Select(x => x.ConversationId)
                                    .ToListAsync())
                                .ToHashSet()
                                : new HashSet<string>();
                        conversations.ForEach(
                            x => x.IsBookmarked = bookmarkedConversationIds.Contains(x.Id));

                        bookmarkedConversationIdsStopwatch.Stop();

                        _logger.LogInformation(
                            $"[PERFORMANCE] bookmarkedConversationIds query executed in {bookmarkedConversationIdsStopwatch.ElapsedMilliseconds} ms ");

                        var conversationIdToUnreadCountStopwatch = Stopwatch.StartNew();

                        // Unread messages
                        var conversationIdToUnreadCount =
                            conversationIds.Any()
                                ? await _appDbContext.ConversationUnreadRecords
                                    .Where(
                                        x =>
                                            x.CompanyId == companyUser.CompanyId
                                            && conversationIds.Contains(x.ConversationId)
                                            && x.StaffId == companyUser.Id
                                            && x.NotificationDeliveryStatus != NotificationDeliveryStatus.Read)
                                    .GroupBy(x => x.ConversationId)
                                    .Select(
                                        g => new
                                        {
                                            ConversationId = g.Key,
                                            Count = g.Count()
                                        })
                                    .ToDictionaryAsync(x => x.ConversationId, x => x.Count)
                                : new Dictionary<string, int>();
                        conversations
                            .Where(x => x.UnreadMessageCount == 0)
                            .ForEach(
                                x => x.UnreadMessageCount = conversationIdToUnreadCount.TryGetValue(x.Id, out var value)
                                    ? value
                                    : 0);

                        conversationIdToUnreadCountStopwatch.Stop();

                        _logger.LogInformation(
                            $"[PERFORMANCE] conversationIdToUnreadCount query executed in {conversationIdToUnreadCountStopwatch.ElapsedMilliseconds} ms ");

                        var associatedTeams = await _appDbContext.CompanyStaffTeams
                            .Where(x => x.Members.Any(y => y.StaffId == companyUser.Id))
                            .ToListAsync();

                        var whatsappIds = new List<string>();
                        var twilioSenderIds = new List<string>();
                        var whatsapp360dialogDefaultChannelIds = new List<long>();
                        var whatsappCloudDefaultChannelIds = new List<string>();

                        foreach (var associatedTeam in associatedTeams)
                        {
                            if (associatedTeam.DefaultChannels?.Count > 0)
                            {
                                foreach (var defaultChannel in associatedTeam.DefaultChannels
                                             .Where(
                                                 x =>
                                                     x.channel != ChannelTypes.Whatsapp360Dialog
                                                     && x.channel != ChannelTypes.WhatsappCloudApi))
                                {
                                    channelList.Add(defaultChannel.channel);

                                    if (defaultChannel.ids != null)
                                    {
                                        foreach (var id in defaultChannel.ids)
                                        {
                                            var twilioInstance = id.Split(";", StringSplitOptions.RemoveEmptyEntries);
                                            whatsappIds.Add(twilioInstance[0]);

                                            if (twilioInstance.Count() > 1)
                                            {
                                                twilioSenderIds.Add(twilioInstance[1].Replace(": ", ":+"));
                                            }
                                        }
                                    }
                                }

                                foreach (var defaultChannel in associatedTeam.DefaultChannels
                                             .Where(x => x.channel == ChannelTypes.Whatsapp360Dialog))
                                {
                                    foreach (var channelId in defaultChannel.ids)
                                    {
                                        var validLong = long.TryParse(channelId, out var whatsapp360dialogChannelId);

                                        if (validLong)
                                        {
                                            whatsapp360dialogDefaultChannelIds.Add(whatsapp360dialogChannelId);
                                        }
                                    }
                                }

                                foreach (var defaultChannel in associatedTeam.DefaultChannels
                                             .Where(x => x.channel == ChannelTypes.WhatsappCloudApi))
                                {
                                    foreach (var channelId in defaultChannel.ids)
                                    {
                                        whatsappCloudDefaultChannelIds.Add(channelId);
                                    }
                                }
                            }
                        }

                        var conversationViewModels = await _conversationNoCompanyResponseViewModelViewModelMapper.ToViewModelsAsync(conversations);

                        var collaboratorsStopwatch = Stopwatch.StartNew();

                        var collaborators =
                            conversationIds.Any()
                                ? await _appDbContext.ConversationAdditionalAssignees
                                    .Where(
                                        y => conversationViewModels
                                            .Select(x => x.ConversationId)
                                            .Contains(y.ConversationId))
                                    .Include(y => y.Assignee.Identity)
                                    .ProjectTo<AdditionalAssigneeResponse>(_mapper.ConfigurationProvider)
                                    .ToListAsync()
                                : new List<AdditionalAssigneeResponse>();
                        conversationViewModels.ForEach(
                            x =>
                                x.AdditionalAssignees = collaborators
                                    .Where(y => y.ConversationId == x.ConversationId)
                                    .ToList());

                        collaboratorsStopwatch.Stop();

                        _logger.LogInformation(
                            $"[PERFORMANCE] collaborators query executed in {collaboratorsStopwatch.ElapsedMilliseconds} ms ");

                        var hashtagsStopwatch = Stopwatch.StartNew();

                        var hashtags =
                            conversationIds.Any()
                                ? await _appDbContext.ConversationHashtags
                                    .Where(
                                        y => conversationViewModels
                                            .Select(x => x.ConversationId)
                                            .Contains(y.ConversationId))
                                    .Include(y => y.Hashtag)
                                    .OrderByDescending(x => x.Id)
                                    .ProjectTo<ConversationHashtagResponse>(_mapper.ConfigurationProvider)
                                    .ToListAsync()
                                : new List<ConversationHashtagResponse>();
                        conversationViewModels.ForEach(
                            x =>
                                x.ConversationHashtags = hashtags
                                    .Where(y => y.ConversationId == x.ConversationId)
                                    .ToList());

                        hashtagsStopwatch.Stop();
                        _logger.LogInformation(
                            $"[PERFORMANCE] hashtags query executed in {hashtagsStopwatch.ElapsedMilliseconds} ms ");

                        var rolePermission = await _appDbContext.CompanyRolePermissions
                            .FirstOrDefaultAsync(
                                x =>
                                    x.CompanyId == companyUser.CompanyId
                                    && x.StaffUserRole == companyUser.RoleType);


                        var filterLastMessage = rolePermission != null &&
                                                rolePermission.Permission.IsShowDefaultChannelMessagesOnly &&
                                                companyUser.RoleType != StaffUserRole.Admin;

                        var isUpdatedLastMessageId = false;

                        var lastMessageStopwatch = Stopwatch.StartNew();

                        var lastMessageIds = conversationViewModels.Select(c => c.LastMessageId).ToList();

                        var lastMessages = await _appDbContext.ConversationMessages
                            .AsNoTracking()
                            .Where(x => lastMessageIds.Contains(x.Id))
                            .Include(x => x.whatsappReceiver)
                            .Include(x => x.whatsappSender)
                            .Include(x => x.Whatsapp360DialogReceiver)
                            .Include(x => x.Whatsapp360DialogSender)
                            .Include(x => x.facebookReceiver)
                            .Include(x => x.facebookSender)
                            .Include(x => x.InstagramReceiver)
                            .Include(x => x.InstagramSender)
                            .ToListAsync();

                        var conversationWithLastMessageViewModels = await _conversationService.GetConversationLastMessages(
                            conversationViewModels,
                            filterLastMessage,
                            whatsappIds,
                            whatsapp360dialogDefaultChannelIds,
                            channelIdList,
                            channelList,
                            twilioSenderIds,
                            whatsappCloudDefaultChannelIds);

                        var lastMessageByConversationId = lastMessages
                            .GroupBy(msg => msg.ConversationId) // Group messages by their ConversationId
                            .ToDictionary(
                                group => group.Key,          // The key of the dictionary is the ConversationId
                                group => group.ToList()); // The value is the list of messages in that group

                        foreach (var conversationWithLastMessageViewModel in conversationWithLastMessageViewModels)
                        {
                            ConversationMessage lastMessage = null;

                            if (conversationWithLastMessageViewModel.UnreadMessageCount > 0
                                && filterLastMessage
                                && conversationWithLastMessageViewModel.LastMessageId.HasValue
                                && (whatsappIds.Any()
                                    || whatsapp360dialogDefaultChannelIds.Any()
                                    || whatsappCloudDefaultChannelIds.Any()))
                            {
                                if (lastMessageByConversationId.TryGetValue(
                                        conversationWithLastMessageViewModel.ConversationId,
                                        out var tempLastMessages))
                                {
                                    // Key was found, assign the retrieved value
                                    conversationWithLastMessageViewModel.LastMessage =
                                        _mapper.Map<List<ConversationMessageResponseViewModel>>(tempLastMessages);
                                    lastMessage = tempLastMessages.FirstOrDefault();
                                }
                                else
                                {
                                    // Key was not found, assign a default (e.g., null or an empty list)
                                    // Choose based on the type of conversation.LastMessage and how you handle missing data
                                    conversationWithLastMessageViewModel.LastMessage = null;
                                }

                                switch (lastMessage.Channel)
                                {
                                    case ChannelTypes.WhatsappTwilio:
                                        if ((lastMessage.whatsappReceiver != null &&
                                             !whatsappIds.Contains(lastMessage.whatsappReceiver.InstanceId)) ||
                                            (lastMessage.whatsappSender != null &&
                                             !whatsappIds.Contains(lastMessage.whatsappSender.InstanceId)))
                                        {
                                            conversationWithLastMessageViewModel.UnreadMessageCount = 0;
                                        }

                                        break;
                                    case ChannelTypes.Whatsapp360Dialog:
                                        if ((lastMessage.Whatsapp360DialogReceiver != null &&
                                             lastMessage.Whatsapp360DialogReceiver.ChannelId.HasValue &&
                                             !whatsapp360dialogDefaultChannelIds.Contains(
                                                 lastMessage.Whatsapp360DialogReceiver.ChannelId.Value)) ||
                                            (lastMessage.Whatsapp360DialogSender != null &&
                                             lastMessage.Whatsapp360DialogSender.ChannelId.HasValue &&
                                             !whatsapp360dialogDefaultChannelIds.Contains(
                                                 lastMessage.Whatsapp360DialogSender.ChannelId.Value)))
                                        {
                                            conversationWithLastMessageViewModel.UnreadMessageCount = 0;
                                        }

                                        break;
                                    case ChannelTypes.WhatsappCloudApi:
                                        if (!whatsappCloudDefaultChannelIds.Contains(lastMessage.ChannelIdentityId))
                                        {
                                            conversationWithLastMessageViewModel.UnreadMessageCount = 0;
                                        }

                                        break;
                                    case ChannelTypes.Facebook:
                                        if ((lastMessage.facebookReceiver != null &&
                                             lastMessage.facebookReceiver.pageId != null &&
                                             !whatsappIds.Contains(lastMessage.facebookReceiver.pageId)) ||
                                            (lastMessage.facebookSender != null &&
                                             lastMessage.facebookSender.pageId != null &&
                                             !whatsappIds.Contains(lastMessage.facebookSender.pageId)))
                                        {
                                            conversationWithLastMessageViewModel.UnreadMessageCount = 0;
                                        }

                                        break;
                                    case ChannelTypes.Instagram:
                                        if ((lastMessage.InstagramReceiver != null &&
                                             lastMessage.InstagramReceiver.InstagramPageId != null &&
                                             !whatsappIds.Contains(lastMessage.InstagramReceiver.InstagramPageId)) ||
                                            (lastMessage.InstagramSender != null &&
                                             lastMessage.InstagramSender.InstagramPageId != null &&
                                             !whatsappIds.Contains(lastMessage.InstagramSender.InstagramPageId)))
                                        {
                                            conversationWithLastMessageViewModel.UnreadMessageCount = 0;
                                        }

                                        break;
                                }
                            }
                        }

                        lastMessageStopwatch.Stop();
                        _logger.LogInformation(
                            $"[PERFORMANCE] lastMessage query executed in {lastMessageStopwatch.ElapsedMilliseconds} ms ");

                        if (isUpdatedLastMessageId)
                        {
                            await _appDbContext.SaveChangesAsync();
                        }

                        var conversationIdsForMappedResult = conversationWithLastMessageViewModels
                            .Select(x => x.ConversationId)
                            .ToList();

                        var firstMessageIdsForMappedResultStopwatch = Stopwatch.StartNew();
                        var firstMessageIdsForMappedResult = await _appDbContext.ConversationMessages
                            .Where(y => conversationIdsForMappedResult.Contains(y.ConversationId))
                            .GroupBy(y => y.ConversationId)
                            .Select(g => new
                            {
                                ConversationId = g.Key,
                                Id = g.Min(y => y.Id) // Get the smallest Id (first message) per conversation
                            })
                            .ToDictionaryAsync(x => x.ConversationId, x => x.Id); // Convert to dictionary for performance

                        firstMessageIdsForMappedResultStopwatch.Stop();

                        _logger.LogInformation(
                            $"[PERFORMANCE] firstMessageIdsForMappedResult query executed in {firstMessageIdsForMappedResultStopwatch.ElapsedMilliseconds} ms ");

                        foreach (var item in conversationWithLastMessageViewModels)
                        {
                            if (firstMessageIdsForMappedResult.TryGetValue(item.ConversationId, out var firstMessageId))
                            {
                                item.FirstMessageId = firstMessageId;
                            }
                            else
                            {
                                // Handle the situation when no message is found for the conversation
                            }
                        }

                        var results = new ConversationWithCountViewModel
                        {
                            Data = conversationWithLastMessageViewModels,
                            Count = await conversationsQueryable.CountAsync()
                        };

                        var isEnableTicketingLogicStopwatch = Stopwatch.StartNew();

                        if (conversationIds.Any() && _isEnableTicketingLogic)
                        {
                            var userProfileIds = results.Data.Select(x => x.UserProfile.Id).ToList();
                            var ticketCount = await _ticketingHubMessageService.GetTicketingUserProfileCountAsync(
                                companyUser.CompanyId,
                                userProfileIds);

                            if (ticketCount != null)
                            {
                                results.Data = results.Data.GroupJoin(
                                ticketCount,
                                result => result.UserProfile.Id,
                                count => count.SleekflowUserProfileId,
                                (result, countGroup) => new ConversationNoCompanyResponseViewModel
                                {
                                    ConversationId = result.ConversationId,
                                    CompanyId = result.CompanyId,
                                    ConversationChannels = result.ConversationChannels,
                                    MessageGroupName = result.MessageGroupName,
                                    UserProfile = result.UserProfile,
                                    Status = result.Status,
                                    Assignee = result.Assignee,
                                    AdditionalAssignees = result.AdditionalAssignees,
                                    ConversationHashtags = result.ConversationHashtags,
                                    LastMessage = result.LastMessage,
                                    Messages = result.Messages,
                                    UpdatedTime = result.UpdatedTime,
                                    ModifiedAt = result.ModifiedAt,
                                    UnreadMessageCount = result.UnreadMessageCount,
                                    SnoozeUntil = result.SnoozeUntil,
                                    FirstMessageId = result.FirstMessageId,
                                    LastMessageId = result.LastMessageId,
                                    LastMessageChannel = result.LastMessageChannel,
                                    LastChannelIdentityId = result.LastChannelIdentityId,
                                    AssignedTeam = result.AssignedTeam,
                                    IsSandbox = result.IsSandbox,
                                    IsBookmarked = result.IsBookmarked,
                                    Metadata = result.Metadata,
                                    TicketCount = countGroup.Select(x => x.Count).DefaultIfEmpty(0).FirstOrDefault()
                                }).ToList();
                            }
                        }

                        isEnableTicketingLogicStopwatch.Stop();
                        _logger.LogInformation(
                            $"[PERFORMANCE] isEnableTicketingLogic query executed in {firstMessageIdsForMappedResultStopwatch.ElapsedMilliseconds} ms ");

                        await AddConversationMessageV2Cache(
                            companyUser.CompanyId,
                            conversationMessageV2CacheKeyPattern,
                            results);


                        return results;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[{MethodName}] inner error: {ExceptionMessage}",
                            nameof(GetFormattedConversations),
                            ex.Message);

                        return new ConversationWithCountViewModel();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] outer error: {ExceptionMessage}",
                    nameof(GetFormattedConversations),
                    ex.Message);

                return new ConversationWithCountViewModel();
            }

            return new ConversationWithCountViewModel();
        }

        public async Task SendReadMoreMessage(
            string companyId,
            string conversationId,
            long? whatsappSenderId,
            string messageContent,
            string contentSid = null,
            List<UploadedFile> uploadedFiles = null,
            DeliveryType deliveryType = DeliveryType.ReadMore)
        {
            if (uploadedFiles == null)
            {
                uploadedFiles = new List<UploadedFile>();
            }
            else
            {
                uploadedFiles.ForEach(x => x.FileId = Guid.NewGuid().ToString());
            }

            var conversation = await _appDbContext.Conversations
                .Include(x => x.WhatsappUser)
                .FirstOrDefaultAsync(
                    x =>
                        x.CompanyId == companyId
                        && x.Id == conversationId);

            var conversationMessage = new ConversationMessage
            {
                Channel = ChannelTypes.WhatsappTwilio,
                MessageType = (uploadedFiles.Count > 0) ? "file" : "text",
                MessageContent = messageContent,
                whatsappReceiver = conversation.WhatsappUser,
                whatsappReceiverId = conversation.WhatsappUserId,
                whatsappSenderId = whatsappSenderId,
                whatsappSender = await _appDbContext.SenderWhatsappSenders
                    .FirstOrDefaultAsync(x => x.Id == whatsappSenderId),
                IsSentFromSleekflow = true,
                DeliveryType = deliveryType,
                UploadedFiles = uploadedFiles
            };

            // if content api opt-in
            if (!string.IsNullOrEmpty(contentSid))
            {
                conversationMessage.ExtendedMessagePayload = new ExtendedMessagePayload()
                {
                    ExtendedMessageType = ExtendedMessageType.TwilioContentApi,
                    ExtendedMessagePayloadDetail = new ExtendedMessagePayloadDetail()
                    {
                        WhatsappTwilioContentApiObject = new WhatsappTwilioContentApiObject()
                        {
                            ContentSid = contentSid
                        }
                    }
                };
            }

            await SendMessage(conversation, conversationMessage);
        }

        public async Task<string> SendAutomatedMessage(Conversation conversation, AutomationAction automationAction)
        {
            var systemChannel = new List<string>
            {
                ChannelTypes.Note,
                "system"
            };

            var conversationMessages = await _appDbContext.ConversationMessages
                .AsNoTracking()
                .OrderByDescending(x => x.Id)
                .FirstOrDefaultAsync(x =>
                    x.ConversationId == conversation.Id
                    && !systemChannel.Contains(x.Channel));

            if (conversationMessages == null)
            {
                var firstWhatsappTwilio = await _appDbContext.ConfigWhatsAppConfigs
                    .AsNoTracking()
                    .Where(x => x.CompanyId == conversation.CompanyId)
                    .OrderBy(x => x.ConnectedDateTime)
                    .Select(
                        x => new
                        {
                            x.Id,
                            x.ConnectedDateTime
                        })
                    .FirstOrDefaultAsync();

                var firstWhatsapp360dialog = await _appDbContext.ConfigWhatsApp360DialogConfigs
                    .AsNoTracking()
                    .Where(x => x.CompanyId == conversation.CompanyId)
                    .OrderBy(x => x.CreatedAt)
                    .Select(
                        x => new
                        {
                            x.Id,
                            x.CreatedAt
                        })
                    .FirstOrDefaultAsync();

                var firstWhatsappCloudApi = await _appDbContext.ConfigWhatsappCloudApiConfigs
                    .AsNoTracking()
                    .Where(x => x.CompanyId == conversation.CompanyId)
                    .OrderBy(x => x.CreatedAt)
                    .Select(
                        x => new
                        {
                            x.Id,
                            x.CreatedAt
                        })
                    .FirstOrDefaultAsync();

                var isUseTwilio = false;
                var isUse360Dialog = false;
                var isUseCloudApi = false;

                if (firstWhatsappTwilio != null
                    && firstWhatsapp360dialog != null
                    && firstWhatsappCloudApi != null)
                {
                    isUseTwilio = firstWhatsappTwilio.ConnectedDateTime <= firstWhatsapp360dialog.CreatedAt &&
                                  firstWhatsappTwilio.ConnectedDateTime < firstWhatsappCloudApi.CreatedAt;

                    isUse360Dialog = firstWhatsappTwilio.ConnectedDateTime >= firstWhatsapp360dialog.CreatedAt &&
                                     firstWhatsapp360dialog.CreatedAt < firstWhatsappCloudApi.CreatedAt;

                    isUseCloudApi = firstWhatsappCloudApi.CreatedAt <= firstWhatsappTwilio.ConnectedDateTime &&
                                    firstWhatsappCloudApi.CreatedAt <= firstWhatsapp360dialog.CreatedAt;
                }
                else if (firstWhatsappTwilio != null)
                {
                    isUseTwilio = true;
                }
                else if (firstWhatsapp360dialog != null)
                {
                    isUse360Dialog = true;
                }
                else if (firstWhatsappCloudApi != null)
                {
                    isUseCloudApi = true;
                }

                conversationMessages = new ConversationMessage();

                if (conversation.WhatsappUser != null
                    && isUseTwilio)
                {
                    conversationMessages.Channel = ChannelTypes.WhatsappTwilio;
                }
                else if (conversation.WhatsApp360DialogUser != null
                         && conversation.WhatsApp360DialogUser.ChannelId.HasValue
                         && isUse360Dialog)
                {
                    conversationMessages.Channel = ChannelTypes.Whatsapp360Dialog;
                }
                else if (conversation.WhatsappCloudApiUser != null
                         && isUseCloudApi)
                {
                    conversationMessages.Channel = ChannelTypes.WhatsappCloudApi;
                }
                else if (conversation.SMSUser != null)
                {
                    conversationMessages.Channel = ChannelTypes.Sms;
                }
            }
            else
            {
                conversationMessages.Id = 0;
            }

            var messageContent = automationAction.MessageContent;

            var templateParams = new List<string>();

            if (automationAction.MessageParams?.Count > 0)
            {
                templateParams = await FormatParams(conversation.UserProfile, automationAction.MessageParams);
            }

            if (templateParams.Count > 0
                && !string.IsNullOrWhiteSpace(automationAction.MessageContent))
            {
                messageContent = string.Format(
                    automationAction.MessageContent,
                    templateParams
                        .Select(x => x.ToString())
                        .ToArray());
            }

            if (automationAction.TargetedChannelWithIds?.Count > 0)
            {
                foreach (var channel in automationAction.TargetedChannelWithIds)
                {
                    switch (channel.channel)
                    {
                        case ChannelTypes.WhatsappTwilio:
                        case "twilio_whatsapp":
                            if (conversation.UserProfile.WhatsAppAccount == null)
                            {
                                return null;
                            }

                            if (channel.ids?.Count > 0)
                            {
                                if (!channel.ids.Contains(
                                        ConversationHelper.GetInstanceId(conversation.UserProfile.WhatsAppAccount)))
                                {
                                    await _userProfileService.SwitchWhatsappChannel(
                                        conversation.UserProfileId,
                                        new ChangeChatAPIInstance
                                        {
                                            InstanceId = channel.ids.FirstOrDefault()
                                        });

                                    conversation = await _appDbContext.Conversations
                                        .Include(x => x.NaiveUser)
                                        .Include(x => x.facebookUser)
                                        .Include(X => X.EmailAddress)
                                        .Include(x => x.WhatsappUser)
                                        .Include(x => x.InstagramUser)
                                        .Include(x => x.WebClient)
                                        .Include(x => x.WeChatUser)
                                        .Include(x => x.LineUser)
                                        .Include(x => x.WhatsApp360DialogUser)
                                        .Include(x => x.WhatsappCloudApiUser)
                                        .Include(x => x.TelegramUser)
                                        .Include(x => x.ViberUser)
                                        .Include(x => x.SMSUser)
                                        .FirstOrDefaultAsync(x => x.Id == conversation.Id);
                                }
                            }

                            if (conversationMessages.Channel != ChannelTypes.WhatsappTwilio)
                            {
                                conversationMessages.Channel = ChannelTypes.WhatsappTwilio;
                            }

                            break;
                        case ChannelTypes.Whatsapp360Dialog:
                            if (conversation.UserProfile.WhatsApp360DialogUser == null)
                            {
                                return null;
                            }

                            if (channel.ids?.Count > 0)
                            {
                                if (conversation.UserProfile.WhatsApp360DialogUser.ChannelId == null
                                    || !channel.ids
                                            .Select(long.Parse)
                                            .Contains(conversation.UserProfile.WhatsApp360DialogUser.ChannelId.Value))
                                {
                                    await _userProfileService.SwitchWhatsapp360DialogChannel(
                                        conversation.CompanyId,
                                        conversation.UserProfileId,
                                        long.Parse(channel.ids.First()));

                                    conversation = await _appDbContext.Conversations
                                        .Include(x => x.NaiveUser)
                                        .Include(x => x.facebookUser)
                                        .Include(x => x.InstagramUser)
                                        .Include(x => x.EmailAddress)
                                        .Include(x => x.WhatsappUser)
                                        .Include(x => x.WebClient)
                                        .Include(x => x.WeChatUser)
                                        .Include(x => x.LineUser)
                                        .Include(x => x.SMSUser)
                                        .Include(x => x.ViberUser)
                                        .Include(x => x.TelegramUser)
                                        .Include(x => x.WhatsApp360DialogUser)
                                        .Include(x => x.WhatsappCloudApiUser)
                                        .FirstOrDefaultAsync(x => x.Id == conversation.Id);
                                }
                            }

                            if (conversationMessages.Channel != ChannelTypes.Whatsapp360Dialog)
                            {
                                conversationMessages.Channel = ChannelTypes.Whatsapp360Dialog;
                            }

                            break;
                        case ChannelTypes.WhatsappCloudApi:
                            if (conversation.UserProfile.WhatsappCloudApiUser == null)
                            {
                                return null;
                            }

                            if (channel.ids?.Count > 0)
                            {
                                if (conversation.UserProfile.WhatsappCloudApiUser.WhatsappChannelPhoneNumber == null
                                    || !channel.ids.Contains(
                                        await _appDbContext.ConfigWhatsappCloudApiConfigs
                                            .AsNoTracking()
                                            .Where(
                                                x =>
                                                    x.CompanyId == conversation.CompanyId
                                                    && x.WhatsappPhoneNumber == conversation.UserProfile.WhatsappCloudApiUser.WhatsappChannelPhoneNumber)
                                            .Select(x => x.WhatsappPhoneNumber)
                                            .FirstOrDefaultAsync()))
                                {
                                    await _userProfileService.SwitchWhatsappCloudApiChannel(
                                        conversation.CompanyId,
                                        conversation.UserProfileId,
                                        await _appDbContext.ConfigWhatsappCloudApiConfigs.AsNoTracking()
                                            .Where(
                                                x =>
                                                    x.CompanyId == conversation.CompanyId
                                                    && x.WhatsappPhoneNumber == channel.ids.FirstOrDefault())
                                            .Select(x => x.WhatsappPhoneNumber)
                                            .FirstOrDefaultAsync());


                                    conversation = await _appDbContext.Conversations
                                        .Include(x => x.NaiveUser)
                                        .Include(x => x.facebookUser)
                                        .Include(x => x.InstagramUser)
                                        .Include(x => x.EmailAddress)
                                        .Include(x => x.WhatsappUser)
                                        .Include(x => x.WebClient)
                                        .Include(x => x.WeChatUser)
                                        .Include(x => x.LineUser)
                                        .Include(x => x.SMSUser)
                                        .Include(x => x.ViberUser)
                                        .Include(x => x.TelegramUser)
                                        .Include(x => x.WhatsApp360DialogUser)
                                        .Include(x => x.WhatsappCloudApiUser)
                                        .FirstOrDefaultAsync(x => x.Id == conversation.Id);
                                }
                            }

                            if (conversationMessages.Channel != ChannelTypes.WhatsappCloudApi)
                            {
                                conversationMessages.Channel = ChannelTypes.WhatsappCloudApi;
                            }

                            break;
                        case ChannelTypes.Sms:
                            if (conversationMessages.Channel != ChannelTypes.Sms)
                            {
                                conversationMessages.Channel = ChannelTypes.Sms;
                            }

                            break;
                    }
                }
            }
            else
            {
                await HandlePossiblePost360DialogMigrationAsync(conversation, conversationMessages);
            }

            conversation.conversationHashtags = await _appDbContext.ConversationHashtags
                .Where(x => x.ConversationId == conversation.Id)
                .ToListAsync();

            var preparedReplyMessage = PrepareReplyMessage(conversation, conversationMessages, messageContent);

            if (automationAction.UploadedFiles.Count > 0)
            {
                preparedReplyMessage.MessageType = "file";
                preparedReplyMessage.UploadedFiles = _mapper.Map<List<UploadedFile>>(automationAction.UploadedFiles);
            }
            else if (automationAction.WhatsApp360DialogExtendedAutomationMessages is { Count: > 0 })
            {
                // 360 Dialog Template / interactive
                var whatsapp360DialogConfigId = conversation.WhatsApp360DialogUser?.ChannelId;
                whatsapp360DialogConfigId ??= conversation.LastMessageChannelId;

                if (whatsapp360DialogConfigId != null)
                {
                    var wabaAccountId = await _appDbContext.ConfigWhatsApp360DialogConfigs
                        .Where(
                            x =>
                                x.Id == whatsapp360DialogConfigId
                                && x.CompanyId == conversation.CompanyId)
                        .Select(x => x.WabaAccountId)
                        .FirstOrDefaultAsync();

                    var extendedAutomationMessage =
                        automationAction.WhatsApp360DialogExtendedAutomationMessages.FirstOrDefault(
                            x => x.WabaAccountId == wabaAccountId);

                    if (extendedAutomationMessage != null)
                    {
                        switch (extendedAutomationMessage.MessageType)
                        {
                            case "template":
                                preparedReplyMessage.Whatsapp360DialogExtendedMessagePayload =
                                    new Whatsapp360DialogExtendedMessagePayload()
                                    {
                                        Whatsapp360DialogTemplateMessage =
                                            JsonConvert.DeserializeObject<Whatsapp360DialogTemplateMessageViewModel>(
                                                JsonConvert.SerializeObject(
                                                    extendedAutomationMessage.Whatsapp360DialogTemplateMessage)),
                                    };

                                preparedReplyMessage.Whatsapp360DialogExtendedMessagePayload
                                        .Whatsapp360DialogTemplateMessage.Components =
                                    preparedReplyMessage.Whatsapp360DialogExtendedMessagePayload
                                        .Whatsapp360DialogTemplateMessage.Components.Format(templateParams);
                                preparedReplyMessage.MessageType = extendedAutomationMessage.MessageType;

                                break;
                            case "interactive":
                                preparedReplyMessage.Whatsapp360DialogExtendedMessagePayload =
                                    new Whatsapp360DialogExtendedMessagePayload()
                                    {
                                        Whatsapp360DialogInteractiveObject =
                                            JsonConvert.DeserializeObject<InteractiveObject>(
                                                JsonConvert.SerializeObject(
                                                    extendedAutomationMessage.Whatsapp360DialogInteractiveObject)),
                                    };

                                preparedReplyMessage.Whatsapp360DialogExtendedMessagePayload
                                        .Whatsapp360DialogInteractiveObject =
                                    preparedReplyMessage.Whatsapp360DialogExtendedMessagePayload
                                        .Whatsapp360DialogInteractiveObject.Format(templateParams);
                                preparedReplyMessage.MessageType = extendedAutomationMessage.MessageType;

                                break;
                        }
                    }
                }
            }
            else if (automationAction.ExtendedAutomationMessage?.Channel == ChannelTypes.WhatsappCloudApi &&
                     automationAction.ExtendedAutomationMessage?.WhatsappCloudApiByWabaExtendedAutomationMessages is
                     { Count: > 0 })
            {
                var whatsappChannelPhoneNumber = conversation.WhatsappCloudApiUser.WhatsappChannelPhoneNumber;

                var whatsappCloudApiConfig = await _appDbContext.ConfigWhatsappCloudApiConfigs
                    .AsNoTracking()
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == conversation.CompanyId
                            && x.WhatsappPhoneNumber == whatsappChannelPhoneNumber);

                whatsappCloudApiConfig ??= await _appDbContext.ConfigWhatsappCloudApiConfigs
                    .AsNoTracking()
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == conversation.CompanyId
                            && x.Id == conversation.LastMessageChannelId);

                if (whatsappCloudApiConfig != null)
                {
                    var byWabaExtendedAutomationMessage = automationAction
                        .ExtendedAutomationMessage?
                        .WhatsappCloudApiByWabaExtendedAutomationMessages
                        .FirstOrDefault(x => x.MessagingHubWabaId == whatsappCloudApiConfig.MessagingHubWabaId)
                        .DeepClone();

                    if (byWabaExtendedAutomationMessage != null)
                    {
                        switch (automationAction.ExtendedAutomationMessage.MessageType)
                        {
                            case "template":
                                preparedReplyMessage.MessageType = "template";

                                preparedReplyMessage.ExtendedMessagePayload = new ExtendedMessagePayload()
                                {
                                    Channel = ChannelTypes.WhatsappCloudApi,
                                    ExtendedMessageType = ExtendedMessageType.WhatsappCloudApiTemplateMessage,
                                    ExtendedMessagePayloadDetail = new ExtendedMessagePayloadDetail()
                                    {
                                        WhatsappCloudApiTemplateMessageObject = byWabaExtendedAutomationMessage
                                            .ExtendedMessagePayloadDetail.WhatsappCloudApiTemplateMessageObject
                                    }
                                };

                                preparedReplyMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                        .WhatsappCloudApiTemplateMessageObject.Components =
                                    preparedReplyMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                        .WhatsappCloudApiTemplateMessageObject.Components.Format(templateParams);

                                break;
                            case "interactive":
                                preparedReplyMessage.MessageType = "interactive";

                                preparedReplyMessage.ExtendedMessagePayload = new ExtendedMessagePayload()
                                {
                                    Channel = ChannelTypes.WhatsappCloudApi,
                                    ExtendedMessageType = ExtendedMessageType.WhatsappCloudApiInteractiveMessage,
                                    ExtendedMessagePayloadDetail = new ExtendedMessagePayloadDetail()
                                    {
                                        WhatsappCloudApiInteractiveObject = byWabaExtendedAutomationMessage
                                            .ExtendedMessagePayloadDetail.WhatsappCloudApiInteractiveObject
                                    }
                                };

                                preparedReplyMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                        .WhatsappCloudApiInteractiveObject =
                                    preparedReplyMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                        .WhatsappCloudApiInteractiveObject.Format(templateParams);

                                break;
                            case "contacts":
                                preparedReplyMessage.MessageType = "contacts";

                                preparedReplyMessage.ExtendedMessagePayload = new ExtendedMessagePayload()
                                {
                                    Channel = ChannelTypes.WhatsappCloudApi,
                                    ExtendedMessageType = ExtendedMessageType.WhatsappCloudApiContactsMessage,
                                    ExtendedMessagePayloadDetail = new ExtendedMessagePayloadDetail()
                                    {
                                        WhatsappCloudApiContactsObject = byWabaExtendedAutomationMessage
                                            .ExtendedMessagePayloadDetail.WhatsappCloudApiContactsObject
                                    }
                                };

                                break;
                            case "location":
                                preparedReplyMessage.MessageType = "location";

                                preparedReplyMessage.ExtendedMessagePayload = new ExtendedMessagePayload()
                                {
                                    Channel = ChannelTypes.WhatsappCloudApi,
                                    ExtendedMessageType = ExtendedMessageType.WhatsappCloudApiLocationMessage,
                                    ExtendedMessagePayloadDetail = new ExtendedMessagePayloadDetail()
                                    {
                                        WhatsappCloudApiLocationObject = byWabaExtendedAutomationMessage
                                            .ExtendedMessagePayloadDetail.WhatsappCloudApiLocationObject
                                    }
                                };

                                break;
                            case "reaction":
                                preparedReplyMessage.MessageType = "reaction";

                                preparedReplyMessage.ExtendedMessagePayload = new ExtendedMessagePayload()
                                {
                                    Channel = ChannelTypes.WhatsappCloudApi,
                                    ExtendedMessageType = ExtendedMessageType.WhatsappCloudApiReactionMessage,
                                    ExtendedMessagePayloadDetail = new ExtendedMessagePayloadDetail()
                                    {
                                        WhatsappCloudApiReactionObject = byWabaExtendedAutomationMessage
                                            .ExtendedMessagePayloadDetail.WhatsappCloudApiReactionObject
                                    }
                                };

                                preparedReplyMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                    .WhatsappCloudApiReactionObject.MessageId ??= conversationMessages.MessageUniqueID;

                                break;
                        }
                    }
                }
            }

            var sentResult = await SendMessage(
                conversation,
                preparedReplyMessage,
                triggerActivity: true,
                EnableChatbot: false,
                EnableTranslation: false);

            return messageContent;
        }

        private async Task HandlePossiblePost360DialogMigrationAsync(
            Conversation conversation,
            ConversationMessage conversationMessage)
        {
            if (string.Equals(conversationMessage.Channel, ChannelTypes.Whatsapp360Dialog, StringComparison.OrdinalIgnoreCase) &&
                conversation.WhatsApp360DialogUser.ChannelId.HasValue)
            {
                // Check if 360 dialog channel still exists
                var is360DialogChannelStillExist = await _appDbContext.ConfigWhatsApp360DialogConfigs
                    .AsNoTracking()
                    .AnyAsync(
                        config =>
                            config.CompanyId == conversation.CompanyId &&
                            config.Id == conversation.WhatsApp360DialogUser.ChannelId.Value);

                // Check if target 360 dialog channel has been migrated to Cloud API
                var cloudApiConfigMigratedFrom360Dialog = await _appDbContext.ConfigWhatsappCloudApiConfigs
                    .AsNoTracking()
                    .FirstOrDefaultAsync(
                        config =>
                            config.CompanyId == conversation.CompanyId &&
                            config.WhatsappPhoneNumber == conversation.WhatsApp360DialogUser.ChannelWhatsAppPhoneNumber);

                // No migration of target 360 dialog channel found
                if (is360DialogChannelStillExist &&
                    cloudApiConfigMigratedFrom360Dialog is null)
                {
                    return;
                }

                // Target 360 dialog channel has been migrated to Cloud API
                if (cloudApiConfigMigratedFrom360Dialog is not null)
                {
                    var userProfile = await _userProfileService.SwitchWhatsappCloudApiChannel(
                        conversation.CompanyId,
                        conversation.UserProfileId,
                        cloudApiConfigMigratedFrom360Dialog.WhatsappPhoneNumber);
                    conversation.WhatsappCloudApiUser = userProfile.WhatsappCloudApiUser;
                    conversationMessage.Channel = ChannelTypes.WhatsappCloudApi;

                    return;
                }

                // Don't have Cloud API channel migrated from 360 dialog, check if company has other 360 dialog instances
                var default360DialogChannelConfig = await _appDbContext.ConfigWhatsApp360DialogConfigs
                    .AsNoTracking()
                    .OrderBy(config => config.CreatedAt)
                    .FirstOrDefaultAsync(config => config.CompanyId == conversation.CompanyId);

                if (default360DialogChannelConfig is not null)
                {
                    var userProfile = await _userProfileService.SwitchWhatsapp360DialogChannel(
                        conversation.CompanyId,
                        conversation.UserProfileId,
                        default360DialogChannelConfig.Id);
                    conversation.WhatsApp360DialogUser = userProfile.WhatsApp360DialogUser;

                    return;
                }

                // Final resort, ideally should not reach here
                var lastChannel = await _appDbContext.UserProfileCustomFields
                    .AsNoTracking()
                    .Where(f =>
                        f.CompanyDefinedField.FieldName.ToLower() == "LastChannel" &&
                        f.UserProfileId == conversation.UserProfileId)
                    .Select(f => f.Value)
                    .FirstOrDefaultAsync();

                if (!string.IsNullOrWhiteSpace(lastChannel))
                {
                    conversationMessage.Channel = lastChannel;
                }
            }
        }

        public async Task<ConversationMessage> SendConversationNote(
            string companyId,
            string conversationId,
            string staffId,
            ConversationMessage conversationMessage,
            ConversationNoteViewModel conversationNoteViewModel)
        {
            if (string.IsNullOrEmpty(conversationMessage.MessageContent)
                && (conversationNoteViewModel?.files == null || conversationNoteViewModel.files.IsNullOrEmpty())
                && (conversationNoteViewModel?.fileUrls == null || conversationNoteViewModel.fileUrls.IsNullOrEmpty())
                && (conversationNoteViewModel?.fileNames == null || conversationNoteViewModel.fileNames.IsNullOrEmpty())
                )
            {
                return null;
            }

            var conversation = await _appDbContext.Conversations
                .FirstOrDefaultAsync(
                    x =>
                        x.CompanyId == companyId
                        && x.Id == conversationId);

            conversationMessage.Channel = ChannelTypes.Note;
            conversationMessage.ConversationId = conversationId;
            conversationMessage.CompanyId = companyId;
            conversationMessage.IsSentFromSleekflow = true;
            conversationMessage.LocalTimestamp = conversationNoteViewModel?.LocalTimestamp;

            if (conversation == null)
            {
                throw new Exception("conversation not found");
            }

            if (!string.IsNullOrEmpty(conversationNoteViewModel?.AssigneeId))
            {
                conversationMessage.MessageAssignee = await _appDbContext.UserRoleStaffs
                    .Include(x => x.Identity)
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == companyId
                            && x.IdentityId == conversationNoteViewModel.AssigneeId);
            }

            if (!string.IsNullOrEmpty(staffId))
            {
                conversationMessage.SenderId = staffId;

                conversationMessage.Sender = await _appDbContext.UserRoleStaffs
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && x.IdentityId == staffId)
                    .Include(x => x.Identity)
                    .Select(x => x.Identity)
                    .FirstOrDefaultAsync();
            }

            await _appDbContext.ConversationMessages.AddAsync(conversationMessage);
            var fileURLMessages = new List<FileURLMessage>();

            if (conversationNoteViewModel?.QuickReplyId != null)
            {
                var quickReply = await _appDbContext.CompanyQuickReplies
                    .Include(x => x.QuickReplyFile)
                    .Include(x => x.CompanyQuickReplyLinguals)
                    .FirstOrDefaultAsync(
                        x =>
                            x.Id == conversationNoteViewModel.QuickReplyId.Value
                            && x.CompanyId == companyId);

                if (quickReply.QuickReplyFile != null)
                {
                    conversationMessage.DeliveryType = DeliveryType.QuickReply;
                    conversationMessage.MessageType = "file";

                    var provider = new FileExtensionContentTypeProvider();
                    string contentType;

                    var domainName = _configuration.GetValue<string>("Values:DomainName");

                    var url =
                        $"{domainName}/company/quickReply/attachment/private/{quickReply.QuickReplyFile.QuickReplyFileId}";

                    if (!provider.TryGetContentType(url, out contentType))
                    {
                        contentType = "application/octet-stream";
                    }

                    switch (conversationMessage.Channel)
                    {
                        case ChannelTypes.Facebook:
                        case ChannelTypes.Instagram:
                        case ChannelTypes.Wechat:
                            conversationMessage.MessageContent = null;

                            break;
                    }

                    fileURLMessages.Add(
                        new FileURLMessage
                        {
                            FileName = Path.GetFileName(quickReply.QuickReplyFile.Filename),
                            FileURL = url,
                            MIMEType = contentType
                        });

                    await HandleMessageFileUpload(
                        companyId,
                        conversationId,
                        conversationMessage,
                        fileURLMessages,
                        conversation);
                }
            }
            else if (conversationNoteViewModel?.files != null)
            {
                foreach (IFormFile file in conversationNoteViewModel.files)
                {
                    var fileName = $"Conversation/{conversationId}/{DateTime.UtcNow.ToString("o")}/{file.FileName}";
                    var uploadFileResult = await _uploadService.UploadFile(companyId, fileName, file);

                    var newuUploadedFile = _mapper.Map<UploadedFile>(conversationMessage);
                    newuUploadedFile.Filename = fileName;
                    newuUploadedFile.FileSize = uploadFileResult.FileSizeInByte;
                    newuUploadedFile.BlobContainer = companyId;

                    var domainName = _configuration.GetValue<String>("Values:DomainName");
                    newuUploadedFile.Url = $"{domainName}/Message/File/Private/{newuUploadedFile.FileId}";
                    newuUploadedFile.MIMEType = file.ContentType;

                    if (newuUploadedFile.MIMEType.Contains("image"))
                    {
                        try
                        {
                            var imageSize = await ImageHelper.GetImageSizeAsync(file.OpenReadStream());

                            var dimension = new
                            {
                                width = imageSize.Width,
                                height = imageSize.Height
                            };

                            newuUploadedFile.Metadata = JsonConvert.SerializeObject(dimension);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "[{MethodName}] Company {CompanyId}, Conversation {ConversationId}, File {FileName} !!! Image File Metadata Error: {ExceptionMessage} !!!",
                                nameof(SendConversationNote),
                                companyId,
                                conversationId,
                                file.FileName,
                                ex.Message);
                        }
                    }

                    conversationMessage.UploadedFiles.Add(newuUploadedFile);

                    if ((newuUploadedFile.MIMEType.Contains("audio") && !newuUploadedFile.MIMEType.Contains("mpeg") &&
                         !newuUploadedFile.MIMEType.Contains("mp3")) ||
                        Path.GetExtension(newuUploadedFile.Filename) == ".webm" ||
                        Path.GetExtension(newuUploadedFile.Filename) == ".bin")
                    {
                        await _appDbContext.SaveChangesAsync();
                        await _mediaProcessService.ProcessMedia(conversation.Id, newuUploadedFile, "mp3");
                    }
                }
            }
            else if (conversationNoteViewModel?.fileUrls != null)
            {
                var provider = new FileExtensionContentTypeProvider();
                string contentType;

                for (var i = 0; i < conversationNoteViewModel.fileUrls.Count; i++)
                {
                    var url = conversationNoteViewModel.fileUrls[i];
                    var fileName = Path.GetFileName(url);
                    if (conversationNoteViewModel.fileNames != null)
                    {
                        fileName = conversationNoteViewModel.fileNames[i];
                    }

                    if (!provider.TryGetContentType(url, out contentType))
                    {
                        contentType = "application/octet-stream";
                    }

                    fileURLMessages.Add(
                        new FileURLMessage
                        {
                            FileName = fileName,
                            FileURL = url,
                            MIMEType = contentType
                        });
                }


                await HandleMessageFileUpload(
                    companyId,
                    conversationId,
                    conversationMessage,
                    fileURLMessages,
                    conversation);
            }

            // assign the conversation to the assignee
            // conversation.Assignee = conversationMessage.MessageAssignee;
            conversation.UpdatedTime = DateTime.UtcNow;
            conversation.ModifiedAt = DateTime.UtcNow;

            conversationMessage.Visibility = "private";
            conversationMessage.Status = MessageStatus.Read;
            await _appDbContext.SaveChangesAsync();

            // NOTE: ConversationNoteViewModel can be null
            // Certain modules (e.g., broadcast) don't use conversation notes
            // Ensure all ConversationNoteViewModel operations handle null case
            if (conversationNoteViewModel?.AssigneeId is not null)
            {
                var createdByStaffId = await _appDbContext.UserRoleStaffs
                    .Where(staff => staff.IdentityId == staffId)
                    .Select(staff => staff.Id)
                    .FirstOrDefaultAsync();

                var mentionedStaffIdentityId = conversationNoteViewModel.AssigneeId;

                var mentionedStaffId = await _appDbContext.UserRoleStaffs
                    .Where(staff => staff.IdentityId == mentionedStaffIdentityId)
                    .Select(staff => staff.Id)
                    .FirstOrDefaultAsync();

                var mention = new Mention
                {
                    Id = Guid.NewGuid().ToString(),
                    CompanyId = conversation.CompanyId,
                    ConversationId = conversation.Id,
                    MessageId = conversationMessage.Id,
                    MentionedStaffId = mentionedStaffId,
                    MentionedStaffIdentityId = mentionedStaffIdentityId,
                    CreatedByStaffId = createdByStaffId,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                };

                await _appDbContext.Mentions.AddAsync(mention);
            }

            conversation.LastMessageId = conversationMessage.Id;
            conversation.ActiveStatus = ActiveStatus.Active;

            await _appDbContext.SaveChangesAsync();

            await RemoveCache(conversation.CompanyId, conversation.Id);


            // await SignalRPublishToSubscriberConversationStatusChanged(conversation);
            await _signalRService.SignalROnConversationNoteReceived(conversation, conversationMessage);
            await _emailNotificationService.NewNoteNotification(conversation, conversationMessage);

            await _userProfileHooks.OnMessageStatusUpdatedAsync(
                        conversation.CompanyId,
                        conversation.UserProfileId,
                        conversation,
                        conversationMessage);

            return conversationMessage;
        }

        private async Task HandleMessageFileUpload(
            string companyId,
            string conversationId,
            ConversationMessage conversationMessage,
            List<FileURLMessage> fileURLMessages,
            Conversation conversation)
        {
            foreach (var file in fileURLMessages)
            {
                UploadFileResult uploadFileResult = null;

                var fileNameAndPath = ConstructFileMessageNameAndPathForUpload(
                    file,
                    conversation.Id);

                var (fileName, filePath) = fileNameAndPath;

                // Download File Url to stream
                if (!string.IsNullOrEmpty(file.FileURL))
                {
                    var client = new RestClient(file.FileURL);
                    var request = new RestRequest(Method.GET);
                    IRestResponse response = await client.ExecuteAsync(request);

                    file.MIMEType = response.ContentType;

                    var fileStream = client.DownloadData(request);
                    var fileIn = new MemoryStream(fileStream);

                    file.FileStream = fileIn;
                    file.FileStream.Position = 0;
                    file.FileURL = null;
                }

                if (file.MIMEType == "audio/ogg" || file.MIMEType == "audio/mp4" ||
                    file.FileName.Contains("audioclip"))
                {
                    var inputFile = TempFileHelper.GetTempFileFullPath("conversation");
                    var outputFile = TempFileHelper.GetTempFileFullPath("conversation");

                    try
                    {
                        await File.WriteAllBytesAsync(inputFile, ((MemoryStream) file.FileStream).ToArray());

                        try
                        {
                            var ffMpegConverter = _mediaEncodingService.GetFfMpegConverterInstance();
                            await _mediaEncodingService.ConvertMediaByGoogleTranscoder(
                                inputFile,
                                outputFile,
                                "mp3",
                                ffMpegConverter);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "[{MethodName}] !!!!! FFMPEG ERROR !!!!!! Company {CompanyId}, Conversation {ConversationId}, File name {FileName}. {ExceptionMessage}",
                                nameof(SendConversationNote),
                                companyId,
                                conversationId,
                                file.FileName,
                                ex.Message);
                        }

                        var outputStream = File.OpenRead(outputFile);
                        file.FileStream = outputStream;
                        file.MIMEType = "audio/mpeg";
                        filePath = $"{filePath}.mp3";

                        uploadFileResult = await _uploadService.UploadFileBySteam(
                            companyId,
                            filePath,
                            file.FileStream,
                            file.MIMEType);
                    }
                    finally
                    {
                        TempFileHelper.DeleteFileWithRetry(inputFile);
                        TempFileHelper.DeleteFileWithRetry(inputFile);
                    }
                }
                else
                {
                    if (string.IsNullOrEmpty(file.FileURL))
                    {
                        uploadFileResult = await _uploadService.UploadFileBySteam(
                            companyId,
                            filePath,
                            file.FileStream,
                            file.MIMEType);
                    }
                    else
                    {
                        uploadFileResult = await _uploadService.UploadFileByURL(
                            companyId,
                            filePath,
                            file.FileURL); // unreachable
                    }
                }

                if (uploadFileResult?.Url == null)
                {
                    return;
                }

                var newuUploadedFile = _mapper.Map<UploadedFile>(conversationMessage);
                newuUploadedFile.Filename = filePath;
                newuUploadedFile.FileSize = uploadFileResult.FileSizeInByte;
                newuUploadedFile.BlobContainer = companyId;

                var domainName = _configuration.GetValue<String>("Values:DomainName");
                newuUploadedFile.Url = $"{domainName}/Message/File/Private/{newuUploadedFile.FileId}";
                newuUploadedFile.MIMEType = file.MIMEType;

                conversationMessage.UploadedFiles.Add(newuUploadedFile);
            }
        }

        public async Task<ConversationMessage> SendConversationIndicatorMessage(
            Conversation conversation,
            ConversationMessage conversationMessage)
        {
            if (!conversationMessage.Metadata.ContainsKey("conversation_indicator"))
            {
                throw new Exception("Conversation indicator is missing");
            }

            await _appDbContext.ConversationMessages.AddAsync(conversationMessage);
            await _appDbContext.SaveChangesAsync();

            await RemoveCache(conversation.CompanyId, conversation.Id);

            await _signalRService.SignalROnMessageReceived(conversation, conversationMessage);

            return conversationMessage;
        }

        /*
        public async Task<IList<ConversationMessage>> SendConversationTicket(Conversation conversation, ConversationTicket conversationTicket)
        {
            _logger.LogInformation("send message");
            List<ConversationMessage> results = new List<ConversationMessage>();

            try
            {
                var conversationMessage = new ConversationMessage()
                {
                    ConversationId = conversation.Id,
                    MessageContent = $"Task: {conversationTicket.Subject}",
                    Channel = "native",
                    MessageType = "ticket",
                    Subject = conversationTicket.Subject,
                    ConversationTicket = conversationTicket
                };

                _appDbContext.ConversationMessages.Add(conversationMessage);
                conversation.UpdatedTime = DateTime.UtcNow;

                await _appDbContext.SaveChangesAsync();

                await _signalRService.SignalROnMessageReceived(conversation, conversationMessage);
                results.Add(conversationMessage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
            }

            return results;
        } */

        public async Task MessageCompletion(
            Conversation conversation,
            ConversationMessage conversationMessage,
            bool isTriggerUpdate)
        {
            try
            {
                var isCSReply = await IsCSReply(conversation, conversationMessage);

                try
                {
                    try
                    {
                        if (conversation.ActiveStatus == ActiveStatus.Inactive
                            && !(conversationMessage.Channel == ChannelTypes.LiveChat
                                 && conversationMessage.Sender == null
                                 && conversationMessage.WebClientSender == null))
                        {
                            conversation.ActiveStatus = ActiveStatus.Active;
                        }

                        if (conversation.UserProfile.ActiveStatus == ActiveStatus.Inactive
                            && conversationMessage.Channel is ChannelTypes.LiveChat or ChannelTypes.LiveChatV2
                            && conversationMessage.WebClientSender != null)
                        {
                            conversation.UserProfile.ActiveStatus = ActiveStatus.Active;
                        }

                        if (isCSReply
                            || conversationMessage.IsSentFromSleekflow)
                        {
                            conversationMessage.IsSentFromSleekflow = true;

                            if (conversationMessage.DeliveryType == DeliveryType.Normal &&
                                !string.IsNullOrEmpty(conversationMessage.SenderId))
                            {
                                // If reply the message mark is read
                                var staff = await _appDbContext.UserRoleStaffs.FirstOrDefaultAsync(
                                    x =>
                                        x.CompanyId == conversation.CompanyId
                                        && x.IdentityId == conversationMessage.SenderId);

                                await ReadConversation(conversation.Id, staff, true);
                            }
                        }
                        else if (isTriggerUpdate &&
                                 !conversationMessage.IsSentFromSleekflow &&
                                 conversationMessage.DeliveryType == DeliveryType.Normal)
                        {
                            conversation.UnreadMessageCount = conversation.UnreadMessageCount + 1;
                        }

                        await _appDbContext.SaveChangesAsync();

                        if (conversation.UpdatedTime < conversationMessage.CreatedAt ||
                            !conversation.LastMessageId.HasValue)
                        {
                            conversation.UpdatedTime = conversationMessage.CreatedAt;
                            conversation.LastMessageId = conversationMessage.Id;
                            conversation.IsUnreplied = !conversationMessage.IsSentFromSleekflow;
                        }

                        conversation.ModifiedAt = DateTime.UtcNow;
                        await _appDbContext.SaveChangesAsync();

                        conversation = await ChangeConversationStatus(
                            conversation,
                            conversationMessage,
                            isTriggerUpdate);



                        await _signalRService.SignalROnMessageReceived(conversation, conversationMessage, isCSReply);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                        "MessageCompletion Error {CompanyId} {ConversationId} {MessageId}",
                            conversation.CompanyId,
                            conversation.Id,
                            conversationMessage.Id);
                    }

                    var watch = Stopwatch.StartNew();
                    await ConversationLastActivity(conversation, conversationMessage, isTriggerUpdate);
                    watch.Stop();

                    _logger.LogInformation(
                        $"[ConversationLastActivity] Preparation executed in: {watch.ElapsedMilliseconds} ms");

                    if (conversationMessage.Channel != ChannelTypes.Note ||
                        conversation.LastMessageChannel != conversationMessage.Channel)
                    {
                        conversation.LastMessageChannel = conversationMessage.Channel;
                        conversation.LastChannelIdentityId = conversationMessage.ChannelIdentityId;
                    }

                    await _appDbContext.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "MessageCompletion ConversationLastActivity Error {CompanyId} {ConversationId} {MessageId}",
                        conversation.CompanyId,
                        conversation.Id,
                        conversationMessage.Id);
                }

                if (isCSReply)
                {
                    await _userProfileHooks.OnMessageSentAsync(
                        conversation.CompanyId,
                        conversation.UserProfileId,
                        conversation,
                        conversationMessage);
                }
                else
                {
                    await _userProfileHooks.OnMessageReceivedAsync(
                        conversation.CompanyId,
                        conversation.UserProfileId,
                        conversation,
                        conversationMessage);
                }


                if (!isCSReply && isTriggerUpdate)
                {
                    _logger.LogInformation(
                        "Trigger new message {ConversationId}, messageId: {ConversationMessageId}",
                        conversation.Id,
                        conversationMessage.Id);

                    switch (conversation.IsNewCreatedConversation)
                    {
                        case true:
                            if (await _appDbContext.CompanyAssignmentRules
                                    .AnyAsync(
                                        x =>
                                            x.CompanyId == conversation.CompanyId
                                            && x.AutomationType == AutomationType.NewContactMessage
                                            && x.Status == AutomationStatus.Live))
                            {
                                BackgroundJob.Enqueue<IAutomationService>(
                                    x => x.NewContactMessageTrigger(conversation.Id, conversationMessage.Id, true));
                            }
                            else if (await _appDbContext.CompanyAssignmentRules
                                         .AnyAsync(
                                             x =>
                                                 x.CompanyId == conversation.CompanyId
                                                 && x.AutomationType == AutomationType.MessageReceived
                                                 && x.Status == AutomationStatus.Live))
                            {
                                BackgroundJob.Enqueue<IAutomationService>(
                                    x => x.NewMessageTrigger(conversation.Id, conversationMessage.Id, true));
                            }
                            else if (await _appDbContext.CompanyAssignmentRules
                                         .AnyAsync(
                                             x =>
                                                 x.CompanyId == conversation.CompanyId
                                                 && (x.AutomationType == AutomationType.QRCodeAssigneeMapping
                                                     || x.AutomationType == AutomationType.QRCodeAssignTeamMapping)
                                                 && x.Status == AutomationStatus.Live))
                            {
                                BackgroundJob.Enqueue<IAutomationService>(
                                    x => x.NewMessageTrigger(conversation.Id, conversationMessage.Id, true));
                            }

                            break;
                        default:
                            if (await _appDbContext.CompanyAssignmentRules
                                    .AnyAsync(
                                    x =>
                                        x.CompanyId == conversation.CompanyId
                                        && x.AutomationType == AutomationType.MessageReceived
                                        && x.Status == AutomationStatus.Live))
                            {
                                BackgroundJob.Enqueue<IAutomationService>(
                                    x => x.NewMessageTrigger(conversation.Id, conversationMessage.Id, true));
                            }
                            else if (await _appDbContext.CompanyAssignmentRules
                                        .AnyAsync(
                                            x =>
                                                x.CompanyId == conversation.CompanyId
                                                && (x.AutomationType == AutomationType.QRCodeAssigneeMapping
                                                    || x.AutomationType == AutomationType.QRCodeAssignTeamMapping)
                                                && x.Status == AutomationStatus.Live))
                            {
                                BackgroundJob.Enqueue<IAutomationService>(
                                    x => x.NewMessageTrigger(conversation.Id, conversationMessage.Id, true));
                            }

                            break;
                    }
                }
                else if (!isCSReply
                         && conversationMessage.DeliveryType == DeliveryType.Normal)
                {
                    _logger.LogInformation(
                        "Not Trigger new message {ConversationId}, messageId: {ConversationMessageId}",
                        conversation.Id,
                        conversationMessage.Id);

                    if (conversation.IsNewCreatedConversation
                        && !conversation.AssigneeId.HasValue
                        && !conversation.AssignedTeamId.HasValue)
                    {
                        switch (conversation.IsNewCreatedConversation)
                        {
                            case true:
                                if (await _appDbContext.CompanyAssignmentRules
                                        .AnyAsync(
                                            x =>
                                            x.CompanyId == conversation.CompanyId
                                            && x.AutomationType == AutomationType.NewContactMessage
                                            && x.Status == AutomationStatus.Live))
                                {
                                    BackgroundJob.Enqueue<IAutomationService>(
                                        x => x.NewContactMessageTrigger(
                                            conversation.Id,
                                            conversationMessage.Id,
                                            false));
                                }
                                else if (await _appDbContext.CompanyAssignmentRules
                                            .AnyAsync(
                                                x =>
                                                    x.CompanyId == conversation.CompanyId
                                                    && x.AutomationType == AutomationType.MessageReceived
                                                    && x.Status == AutomationStatus.Live))
                                {
                                    BackgroundJob.Enqueue<IAutomationService>(
                                        x => x.NewMessageTrigger(conversation.Id, conversationMessage.Id, false));
                                }

                                break;
                            default:
                                if (await _appDbContext.CompanyAssignmentRules
                                        .AnyAsync(
                                            x =>
                                                x.CompanyId == conversation.CompanyId
                                                && x.AutomationType == AutomationType.MessageReceived
                                                && x.Status == AutomationStatus.Live))
                                {
                                    BackgroundJob.Enqueue<IAutomationService>(
                                        x => x.NewMessageTrigger(conversation.Id, conversationMessage.Id, false));
                                }

                                break;
                        }
                    }
                }

                if (isCSReply
                    && !string.IsNullOrEmpty(conversationMessage.SenderId)
                    && (conversationMessage.DeliveryType is DeliveryType.Normal or DeliveryType.QuickReply))
                {
                    BackgroundJob.Enqueue<IAutomationService>(
                        x => x.OutgoingMessageTrigger(conversation.Id, conversationMessage.Id, true));
                }

                if (!isCSReply
                    && conversationMessage.DeliveryType == DeliveryType.Normal)
                {
                    if (!conversation.AssigneeId.HasValue
                        && !conversation.AssignedTeamId.HasValue)
                    {
                        // Default assignment rule
                        if (conversation.IsSandbox)
                        {
                            BackgroundJob.Enqueue<IAutomationService>(
                                x => x.ConversationAssignmentTrigger(conversation.Id, conversationMessage.Id));
                        }
                        else if (conversation.Status.ToLower() == "open")
                        {
                            if (await _appDbContext.CompanyAssignmentRules
                                    .CountAsync(
                                        x =>
                                            x.CompanyId == conversation.CompanyId
                                            && x.Status == AutomationStatus.Live) == 1)
                            {
                                BackgroundJob.Enqueue<IAutomationService>(
                                    x => x.ConversationAssignmentTrigger(conversation.Id, conversationMessage.Id));
                            }
                            else
                            {
                                BackgroundJob.Schedule<IAutomationService>(
                                    x => x.ConversationAssignmentTrigger(conversation.Id, conversationMessage.Id),
                                    TimeSpan.FromSeconds(10));
                            }
                        }
                    }
                }

                if (conversation.IsNewCreatedConversation)
                {
                    conversation.IsNewCreatedConversation = false;
                    await _appDbContext.SaveChangesAsync();
                }

                await RemoveCache(conversation.CompanyId, conversation.Id);

                // await ConversationAssignment(conversation, conversationMessage);
            }
            catch (DbUpdateException e)
            {
                _logger.LogError(
                    e,
                    "Complete Message Error: {CompanyId} {ConversationId} {MessageId}",
                    conversation.CompanyId,
                    conversation.Id,
                    conversationMessage.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Complete Message Error: {CompanyId} {ConversationId} {MessageId}",
                    conversation.CompanyId,
                    conversation.Id,
                    conversationMessage.Id);
            }
        }

        public async Task<IList<ConversationMessage>> SendMessage(
            Conversation conversation,
            ConversationMessage conversationMessage,
            bool triggerActivity = true,
            bool EnableChatbot = false,
            bool EnableTranslation = false)
        {
            var watch = Stopwatch.StartNew();

            _logger.LogInformation(
                "[Send Message] [Conversation:{ConversationId}] [{Conversation}] [{ConversationMessage}] [{ConversationMessageChannel}] started",
                conversation.Id,
                JsonConvert.SerializeObject(
                    conversation,
                    new JsonSerializerSettings
                    {
                        ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                        NullValueHandling = NullValueHandling.Ignore,
                    }),
                JsonConvert.SerializeObject(
                    conversationMessage,
                    new JsonSerializerSettings
                    {
                        ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                        NullValueHandling = NullValueHandling.Ignore,
                    }),
                conversationMessage.Channel);

            List<ConversationMessage> results = new List<ConversationMessage>();

            try
            {
                if (!string.IsNullOrEmpty(conversationMessage.MessageUniqueID))
                {
                    if (await _appDbContext.ConversationMessages
                            .AnyAsync(x => x.MessageUniqueID == conversationMessage.MessageUniqueID))
                    {
                        return results;
                    }
                }

                conversation = await _conversationResolver.GetConversationAsync(conversation, conversationMessage);
                conversationMessage = await GetConversationMessage(conversation, conversationMessage);

                if (conversationMessage.DeliveryType == DeliveryType.Broadcast)
                {
                    var response = await _companyUsageService.GetCompanyUsage(conversation.CompanyId);

                    if (response.billingPeriodUsages.FirstOrDefault()?.TotalMessagesSentFromSleekflow >
                        response.MaximumAutomatedMessages)
                    {
                        throw new BroadcastMessageExceedingLimitException(
                            conversation.CompanyId,
                            conversation.UserProfileId);
                    }
                }

                _appDbContext.ConversationMessages.Add(conversationMessage);
                await _appDbContext.SaveChangesAsync();

                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.MessageStored,
                    new Dictionary<string, string>
                    {
                        {
                            "channel_type", conversationMessage.Channel
                        },
                        {
                            "direction", conversationMessage.IsSentFromSleekflow ? "sent" : "received"
                        },
                        {
                            "message_type", conversationMessage.MessageType
                        },
                    });

                watch = Stopwatch.StartNew();

                conversationMessage = await SendOrScheduleMessage(conversation, conversationMessage);

                watch.Stop();

                _logger.LogInformation(
                    "[Send Message] [Conversation:{ConversationId}] [{Conversation}] [{ConversationMessage}] [{ConversationMessageChannel}] Channel sent executed in: {ElapsedMilliseconds} ms",
                    conversation.Id,
                    JsonConvert.SerializeObject(
                        conversation,
                        new JsonSerializerSettings
                        {
                            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                            NullValueHandling = NullValueHandling.Ignore,
                        }),
                    JsonConvert.SerializeObject(
                        conversationMessage,
                        new JsonSerializerSettings
                        {
                            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                            NullValueHandling = NullValueHandling.Ignore,
                        }),
                    conversationMessage.Channel,
                    watch.ElapsedMilliseconds);

                watch = Stopwatch.StartNew();
                results.Add(conversationMessage);

                await MessageCompletion(conversation, conversationMessage, triggerActivity);

                watch.Stop();

                _logger.LogInformation(
                    "[Send Message] [Conversation:{ConversationId}] [{Conversation}] [{ConversationMessage}] [{ConversationMessageChannel}] Completion executed in: {ElapsedMilliseconds} ms",
                    conversation.Id,
                    JsonConvert.SerializeObject(
                        conversation,
                        new JsonSerializerSettings
                        {
                            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                            NullValueHandling = NullValueHandling.Ignore,
                        }),
                    JsonConvert.SerializeObject(
                        conversationMessage,
                        new JsonSerializerSettings
                        {
                            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                            NullValueHandling = NullValueHandling.Ignore,
                        }),
                    conversationMessage.Channel,
                    watch.ElapsedMilliseconds);
            }
            catch (DbUpdateException e)
            {
                _logger.LogError(
                    e,
                    "[DbUpdateException] [Send Message Error] {CompanyId} {ConversationId} {MessageId} [{Conversation}] [{ConversationMessage}]",
                    conversation.CompanyId,
                    conversation.Id,
                    conversationMessage.Id,
                    JsonConvert.SerializeObject(
                        conversation,
                        new JsonSerializerSettings
                        {
                            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                            NullValueHandling = NullValueHandling.Ignore,
                        }),
                    JsonConvert.SerializeObject(
                        conversationMessage,
                        new JsonSerializerSettings
                        {
                            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                            NullValueHandling = NullValueHandling.Ignore,
                        }));

                _conversationMeters.IncrementCounter(conversationMessage.Channel, ConversationMeterOptions.ProcessMessageFailed);

                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[Exception] [Send Message Error] {CompanyId} {ConversationId} {MessageId} [{Conversation}] [{ConversationMessage}]",
                    conversation.CompanyId,
                    conversation.Id,
                    conversationMessage.Id,
                    JsonConvert.SerializeObject(
                        conversation,
                        new JsonSerializerSettings
                        {
                            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                            NullValueHandling = NullValueHandling.Ignore,
                        }),
                    JsonConvert.SerializeObject(
                        conversationMessage,
                        new JsonSerializerSettings
                        {
                            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                            NullValueHandling = NullValueHandling.Ignore,
                        }));

                _conversationMeters.IncrementCounter(conversationMessage.Channel, ConversationMeterOptions.ProcessMessageFailed);

                throw;
            }

            _conversationMeters.IncrementCounter(conversationMessage.Channel, ConversationMeterOptions.ProcessMessageSuccess);

            return results;
        }

        public async Task<IList<ConversationMessage>> SendFileMessage(
            Conversation conversation,
            ConversationMessage conversationMessage,
            ConversationMessageViewModel fileMessageViewModel,
            bool triggerActivity = true,
            bool EnableChatbot = false,
            bool EnableTranslation = false)
        {
            var watch = Stopwatch.StartNew();

            _logger.LogInformation(
                "[Send File Message] [Conversation:{ConversationId}] [{ConversationMessageChannel}] started",
                conversation.Id,
                conversationMessage.Channel);

            List<ConversationMessage> results = new List<ConversationMessage>();

            try
            {
                conversation = await _conversationResolver.GetConversationAsync(conversation, conversationMessage);
                conversationMessage = await GetConversationMessage(conversation, conversationMessage);

                if (conversationMessage.DeliveryType == DeliveryType.Broadcast)
                {
                    var response = await _companyUsageService.GetCompanyUsage(conversation.CompanyId);

                    if (response.billingPeriodUsages.FirstOrDefault()?.TotalMessagesSentFromSleekflow >
                        response.MaximumAutomatedMessages)
                    {
                        throw new BroadcastMessageExceedingLimitException(
                            conversation.CompanyId,
                            conversation.UserProfileId);
                    }
                }

                List<UploadedFile> uploadedFiles = new List<UploadedFile>();

                if (fileMessageViewModel.files != null)
                {
                    foreach (IFormFile file in fileMessageViewModel.files)
                    {
                        var newuUploadedFile = _mapper.Map<UploadedFile>(conversationMessage);

                        var fileName =
                            $"Conversation/{conversation.Id}/{DateTime.UtcNow.ToString("o")}/{newuUploadedFile.FileId}/{file.FileName}";

                        var uploadFileResult = await _uploadService.UploadFile(conversation.CompanyId, fileName, file);

                        if (uploadFileResult?.Url == null)
                        {
                            return null;
                        }

                        newuUploadedFile.Filename = fileName;
                        newuUploadedFile.FileSize = uploadFileResult.FileSizeInByte;
                        newuUploadedFile.BlobContainer = conversation.CompanyId;

                        var domainName = _configuration.GetValue<String>("Values:DomainName");
                        newuUploadedFile.Url = $"{domainName}/Message/File/Private/{newuUploadedFile.FileId}/{file.FileName}";
                        newuUploadedFile.MIMEType = file.ContentType;

                        if (newuUploadedFile.MIMEType.Contains("image"))
                        {
                            try
                            {
                                var imageSize = await ImageHelper.GetImageSizeAsync(file.OpenReadStream());

                                var dimension = new
                                {
                                    width = imageSize.Width,
                                    height = imageSize.Height
                                };

                                newuUploadedFile.Metadata = JsonConvert.SerializeObject(dimension);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(
                                    ex,
                                    "[{MethodName}] Company {CompanyId}, Conversation {ConversationId}, File {FileName} !!! Image File Metadata Error: {ExceptionMessage} !!!",
                                    nameof(SendConversationNote),
                                    conversation.CompanyId,
                                    conversation.Id,
                                    file.FileName,
                                    ex.Message);
                            }
                        }

                        conversationMessage.UploadedFiles.Add(newuUploadedFile);
                    }
                }

                // conversation = ChangeConversationStatus(conversation, conversationMessage);
                _appDbContext.ConversationMessages.Add(conversationMessage);

                await _appDbContext.SaveChangesAsync();

                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.MessageStored,
                    new Dictionary<string, string>
                    {
                        {
                            "channel_type", conversationMessage.Channel
                        },
                        {
                            "direction", conversationMessage.IsSentFromSleekflow ? "sent" : "received"
                        },
                        {
                            "message_type", conversationMessage.MessageType
                        },
                    });

                watch.Stop();

                _logger.LogInformation(
                    "[Send File Message] [Conversation:{ConversationId}] [{ConversationMessageChannel}] Preparation executed in: {ElapsedMilliseconds} ms",
                    conversation.Id,
                    conversationMessage.Channel,
                    watch.ElapsedMilliseconds);

                watch = Stopwatch.StartNew();

                conversationMessage = await SendOrScheduleMessage(conversation, conversationMessage);

                await _appDbContext.SaveChangesAsync();

                watch.Stop();

                _logger.LogInformation(
                    "[Send File Message] [Conversation:{ConversationId}] [{ConversationMessageChannel}] Channel sent executed in: {ElapsedMilliseconds} ms",
                    conversation.Id,
                    conversationMessage.Channel,
                    watch.ElapsedMilliseconds);

                watch = Stopwatch.StartNew();

                results.Add(conversationMessage);

                await MessageCompletion(conversation, conversationMessage, triggerActivity);

                watch.Stop();

                _logger.LogInformation(
                    "[Send File Message] [Conversation:{ConversationId}] [{ConversationMessageChannel}] Completion executed in: {ElapsedMilliseconds} ms",
                    conversation.Id,
                    conversationMessage.Channel,
                    watch.ElapsedMilliseconds);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[DbUpdateException] [Send File Message Error] {CompanyId} {ConversationId} {MessageId}",
                    conversation.CompanyId,
                    conversation.Id,
                    conversationMessage.Id);

                _conversationMeters.IncrementCounter(conversationMessage.Channel, ConversationMeterOptions.ProcessMessageFailed);

                throw;
            }

            _conversationMeters.IncrementCounter(conversationMessage.Channel, ConversationMeterOptions.ProcessMessageSuccess);

            return results;
        }

        public async Task<IList<ConversationMessage>> SendFileMessageByFBURL(
            Conversation conversation,
            ConversationMessage conversationMessage,
            List<FileURLMessage> fileURLMessages,
            bool triggerActivity = true,
            bool EnableChatbot = false,
            bool EnableTranslation = false)
        {
            var watch = Stopwatch.StartNew();

            _logger.LogInformation(
                "[Send File Message URL] [Conversation:{ConversationId}] [{Conversation}] [{ConversationMessage}] [{ConversationMessageChannel}] started ms",
                conversation.Id,
                JsonConvert.SerializeObject(
                    conversation,
                    new JsonSerializerSettings
                    {
                        ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                        NullValueHandling = NullValueHandling.Ignore,
                    }),
                JsonConvert.SerializeObject(
                    conversationMessage,
                    new JsonSerializerSettings
                    {
                        ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                        NullValueHandling = NullValueHandling.Ignore,
                    }),
                conversationMessage.Channel);

            List<ConversationMessage> results = new List<ConversationMessage>();

            try
            {
                conversation = await _conversationResolver.GetConversationAsync(conversation, conversationMessage);
                conversationMessage = await GetConversationMessage(conversation, conversationMessage);

                if (conversationMessage.DeliveryType == DeliveryType.Broadcast &&
                    !string.IsNullOrEmpty(conversationMessage.SenderId))
                {
                    var response = await _companyUsageService.GetCompanyUsage(conversation.CompanyId);

                    if (response.billingPeriodUsages.FirstOrDefault()?.TotalMessagesSentFromSleekflow >
                        response.MaximumAutomatedMessages)
                    {
                        throw new BroadcastMessageExceedingLimitException(
                            conversation.CompanyId,
                            conversation.UserProfileId);
                    }
                }

                foreach (FileURLMessage file in fileURLMessages)
                {
                    UploadFileResult uploadFileResult = null;
                    string fileMetadata = null;
                    var fileNameAndPath = ConstructFileMessageNameAndPathForUpload(
                        file,
                        conversation.Id);

                    var (fileName, filePath) = fileNameAndPath;

                    // Download File Url to stream
                    if (!string.IsNullOrEmpty(file.FileURL))
                    {
                        var client = new RestClient(file.FileURL);
                        var request = new RestRequest(Method.GET);
                        IRestResponse response = await client.ExecuteAsync(request);

                        if (!(conversationMessage.Channel == ChannelTypes.Instagram
                              && response.ContentType != file.MIMEType
                              && file.MIMEType == "audio/ogg"))
                        {
                            file.MIMEType = response.ContentType;
                        }

                        var fileStream = client.DownloadData(request);
                        var fileIn = new MemoryStream(fileStream);

                        file.FileStream = fileIn;
                        file.FileStream.Position = 0;
                        file.FileURL = null;
                    }

                    if (file.MIMEType.Contains("image"))
                    {
                        try
                        {
                            var imageSize = await ImageHelper.GetImageSizeAsync(file.FileStream, file.MIMEType);

                            var dimension = new
                            {
                                width = imageSize.Width,
                                height = imageSize.Height
                            };

                            fileMetadata = JsonConvert.SerializeObject(dimension);

                            file.FileStream.Position = 0;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "[{MethodName}]Conversation Id: {ConversationId}, Company Id: {ConversationCompanyId}, file: {FileName}|{FileMimeType} !!! Image File Metadata Error: !!!\\n{ExMessage}",
                                nameof(SendFileMessageByFBURL),
                                conversation?.Id,
                                conversation?.CompanyId,
                                file?.FileName,
                                file?.MIMEType,
                                ex.Message);
                        }
                    }

                    if (file.MIMEType == "audio/ogg"
                        || file.MIMEType == "audio/mp4"
                        || file.MIMEType == "audio/x-m4a"
                        || (file.FileName != null && file.FileName.Contains("audioclip")))
                    {
                        var inputFile = TempFileHelper.GetTempFileFullPath("conversation");
                        var outputFile = TempFileHelper.GetTempFileFullPath("conversation");

                        try
                        {
                            await File.WriteAllBytesAsync(inputFile, ((MemoryStream) file.FileStream).ToArray());

                            try
                            {
                                var ffMpegConverter = _mediaEncodingService.GetFfMpegConverterInstance();
                                await _mediaEncodingService.ConvertMediaByGoogleTranscoder(inputFile, outputFile, "mp3", ffMpegConverter);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(
                                    ex,
                                    "[{MethodName}] !!!!! FFMPEG ERROR !!!!!! Company {CompanyId}, Conversation {ConversationId}, File name {FileName}. {ExceptionMessage}",
                                    nameof(SendConversationNote),
                                    conversation?.CompanyId,
                                    conversation?.Id,
                                    file.FileName,
                                    ex.Message);
                            }

                            var outputStream = File.OpenRead(outputFile);
                            file.FileStream = outputStream;
                            file.MIMEType = "audio/mpeg";

                            file.FileName = Path.ChangeExtension(file.FileName, ".mp3");
                            fileNameAndPath = ConstructFileMessageNameAndPathForUpload(
                                file,
                                conversation.Id);
                            (fileName, filePath) = fileNameAndPath;

                            uploadFileResult = await _uploadService.UploadFileBySteam(
                                conversation.CompanyId,
                                filePath,
                                file.FileStream,
                                file.MIMEType);
                        }
                        finally
                        {
                            TempFileHelper.DeleteFileWithRetry(inputFile);
                            TempFileHelper.DeleteFileWithRetry(outputFile);
                        }
                    }
                    else
                    {
                        fileNameAndPath = ConstructFileMessageNameAndPathForUpload(
                            file,
                            conversation.Id);
                        (fileName, filePath) = fileNameAndPath;

                        if (string.IsNullOrEmpty(file.FileURL))
                        {
                            uploadFileResult = await _uploadService.UploadFileBySteam(
                                conversation.CompanyId,
                                filePath,
                                file.FileStream,
                                file.MIMEType);
                        }
                        else
                        {
                            uploadFileResult = await _uploadService.UploadFileByURL(
                                conversation.CompanyId,
                                filePath,
                                file.FileURL); // unreachable
                        }
                    }

                    if (uploadFileResult?.Url == null)
                    {
                        return null;
                    }

                    var newuUploadedFile = _mapper.Map<UploadedFile>(conversationMessage);
                    newuUploadedFile.Filename = filePath; // actually db is storing file path, rather than just file name
                    newuUploadedFile.FileSize = uploadFileResult.FileSizeInByte;
                    newuUploadedFile.BlobContainer = conversation.CompanyId;

                    var domainName = _configuration.GetValue<String>("Values:DomainName");
                    newuUploadedFile.Url = $"{domainName}/Message/File/Private/{newuUploadedFile.FileId}/{fileName}";
                    newuUploadedFile.MIMEType = file.MIMEType;
                    newuUploadedFile.Metadata = fileMetadata;

                    conversationMessage.UploadedFiles.Add(newuUploadedFile);
                }

                watch.Stop();

                _logger.LogInformation(
                    "[Send File Message URL] [Conversation:{ConversationId}] [{ConversationMessageChannel}] Preparation executed in: {ElapsedMilliseconds} ms",
                    conversation.Id,
                    conversationMessage.Channel,
                    watch.ElapsedMilliseconds);

                watch = Stopwatch.StartNew();

                // conversation = ChangeConversationStatus(conversation, conversationMessage);
                _appDbContext.ConversationMessages.Add(conversationMessage);
                await _appDbContext.SaveChangesAsync();

                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.MessageStored,
                    new Dictionary<string, string>
                    {
                        {
                            "channel_type", conversationMessage.Channel
                        },
                        {
                            "direction", conversationMessage.IsSentFromSleekflow ? "sent" : "received"
                        },
                        {
                            "message_type", conversationMessage.MessageType
                        },
                    });

                conversationMessage = await SendMessageToChannels(conversation, conversationMessage);

                var isMessageStatusChanged = false;
                if (conversationMessage.Status < MessageStatus.Sent
                    && !string.IsNullOrEmpty(conversationMessage.MessageUniqueID))
                {
                    conversationMessage.Status = MessageStatus.Sent;
                    isMessageStatusChanged = true;
                }

                await _appDbContext.SaveChangesAsync();

                if (isMessageStatusChanged)
                {
                    await _userProfileHooks.OnMessageStatusUpdatedAsync(
                        conversation.CompanyId,
                        conversation.UserProfileId,
                        conversation,
                        conversationMessage);
                }

                results.Add(conversationMessage);

                await MessageCompletion(conversation, conversationMessage, triggerActivity);

                watch.Stop();

                _logger.LogInformation(
                    "[Send File Message URL] [Conversation:{ConversationId}] [{ConversationMessageChannel}] Completion executed in: {ElapsedMilliseconds} ms",
                    conversation.Id,
                    conversationMessage.Channel,
                    watch.ElapsedMilliseconds);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[DbUpdateException] [Send File URL Message Error] {CompanyId} {ConversationId} {MessageId}",
                    conversation.CompanyId,
                    conversation.Id,
                    conversationMessage.Id);

                _conversationMeters.IncrementCounter(conversationMessage.Channel, ConversationMeterOptions.ProcessMessageFailed);

                throw;
            }

            _conversationMeters.IncrementCounter(conversationMessage.Channel, ConversationMeterOptions.ProcessMessageSuccess);

            return results;
        }

        public async Task<IList<ConversationMessage>> ReceiveMessageAsync(
            Conversation conversation,
            ConversationMessage conversationMessage,
            bool triggerActivity = true)
        {
            _logger.LogInformation(
                "[Receive Message] [Conversation:{ConversationId}] [{Conversation}] [{ConversationMessage}] [{ConversationMessageChannel}] started",
                conversation.Id,
                JsonConvert.SerializeObject(
                    conversation,
                    new JsonSerializerSettings
                    {
                        ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                        NullValueHandling = NullValueHandling.Ignore,
                    }),
                JsonConvert.SerializeObject(
                    conversationMessage,
                    new JsonSerializerSettings
                    {
                        ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                        NullValueHandling = NullValueHandling.Ignore,
                    }),
                conversationMessage.Channel);

            var results = new List<ConversationMessage>();

            try
            {
                if (!string.IsNullOrEmpty(conversationMessage.MessageUniqueID))
                {
                    if (await _appDbContext.ConversationMessages
                            .AnyAsync(x => x.MessageUniqueID == conversationMessage.MessageUniqueID))
                    {
                        return results;
                    }
                }

                conversation = await _conversationResolver.GetConversationAsync(conversation, conversationMessage);
                conversationMessage = await GetConversationMessage(conversation, conversationMessage);

                _appDbContext.ConversationMessages.Add(conversationMessage);
                await _appDbContext.SaveChangesAsync();

                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.MessageStored,
                    new Dictionary<string, string>
                    {
                        {
                            "channel_type", conversationMessage.Channel
                        },
                        {
                            "direction", conversationMessage.IsSentFromSleekflow ? "sent" : "received"
                        },
                        {
                            "message_type", conversationMessage.MessageType
                        },
                    });

                var watch = Stopwatch.StartNew();

                conversationMessage = await ReceiveMessageFromChannels(conversation, conversationMessage);

                watch.Stop();

                _logger.LogInformation(
                    "[Receive Message] [Conversation:{ConversationId}] [{Conversation}] [{ConversationMessage}] [{ConversationMessageChannel}] Channel sent executed in: {ElapsedMilliseconds} ms",
                    conversation.Id,
                    JsonConvert.SerializeObject(
                        conversation,
                        new JsonSerializerSettings
                        {
                            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                            NullValueHandling = NullValueHandling.Ignore,
                        }),
                    JsonConvert.SerializeObject(
                        conversationMessage,
                        new JsonSerializerSettings
                        {
                            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                            NullValueHandling = NullValueHandling.Ignore,
                        }),
                    conversationMessage.Channel,
                    watch.ElapsedMilliseconds);

                watch = Stopwatch.StartNew();
                results.Add(conversationMessage);

                HandleLowerFunnelEvent(conversation, conversationMessage);

                await MessageCompletion(conversation, conversationMessage, triggerActivity);

                watch.Stop();

                _logger.LogInformation(
                    "[Receive Message] [Conversation:{ConversationId}] [{Conversation}] [{ConversationMessage}] [{ConversationMessageChannel}] Completion executed in: {ElapsedMilliseconds} ms",
                    conversation.Id,
                    JsonConvert.SerializeObject(
                        conversation,
                        new JsonSerializerSettings
                        {
                            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                            NullValueHandling = NullValueHandling.Ignore,
                        }),
                    JsonConvert.SerializeObject(
                        conversationMessage,
                        new JsonSerializerSettings
                        {
                            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                            NullValueHandling = NullValueHandling.Ignore,
                        }),
                    conversationMessage.Channel,
                    watch.ElapsedMilliseconds);
            }
            catch (DbUpdateException e)
            {
                _logger.LogError(
                    e,
                    "[DbUpdateException] [Receive Message Error] {CompanyId} {ConversationId} {MessageId} [{Conversation}] [{ConversationMessage}]",
                    conversation.CompanyId,
                    conversation.Id,
                    conversationMessage.Id,
                    JsonConvert.SerializeObject(
                        conversation,
                        new JsonSerializerSettings
                        {
                            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                            NullValueHandling = NullValueHandling.Ignore,
                        }),
                    JsonConvert.SerializeObject(
                        conversationMessage,
                        new JsonSerializerSettings
                        {
                            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                            NullValueHandling = NullValueHandling.Ignore,
                        }));

                _conversationMeters.IncrementCounter(conversationMessage.Channel, ConversationMeterOptions.ProcessMessageFailed);

                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[Exception] [Receive Message Error] {CompanyId} {ConversationId} {MessageId} [{Conversation}] [{ConversationMessage}]",
                    conversation.CompanyId,
                    conversation.Id,
                    conversationMessage.Id,
                    JsonConvert.SerializeObject(
                        conversation,
                        new JsonSerializerSettings
                        {
                            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                            NullValueHandling = NullValueHandling.Ignore,
                        }),
                    JsonConvert.SerializeObject(
                        conversationMessage,
                        new JsonSerializerSettings
                        {
                            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                            NullValueHandling = NullValueHandling.Ignore,
                        }));

                _conversationMeters.IncrementCounter(conversationMessage.Channel, ConversationMeterOptions.ProcessMessageFailed);

                throw;
            }

            _conversationMeters.IncrementCounter(conversationMessage.Channel, ConversationMeterOptions.ProcessMessageSuccess);

            return results;
        }

        public async Task<IList<ConversationMessage>> ReceiveFileMessageAsync(
            Conversation conversation,
            ConversationMessage conversationMessage,
            ConversationMessageViewModel fileMessageViewModel,
            bool triggerActivity = true)
        {
            var watch = Stopwatch.StartNew();

            _logger.LogInformation(
                "[Receive File Message] [Conversation:{ConversationId}] [{ConversationMessageChannel}] started",
                conversation.Id,
                conversationMessage.Channel);

            var results = new List<ConversationMessage>();

            try
            {
                conversation = await _conversationResolver.GetConversationAsync(conversation, conversationMessage);
                conversationMessage = await GetConversationMessage(conversation, conversationMessage);

                if (fileMessageViewModel.files != null)
                {
                    foreach (IFormFile file in fileMessageViewModel.files)
                    {
                        var newuUploadedFile = _mapper.Map<UploadedFile>(conversationMessage);

                        var fileName =
                            $"Conversation/{conversation.Id}/{DateTime.UtcNow.ToString("o")}/{newuUploadedFile.FileId}/{file.FileName}";

                        var uploadFileResult = await _uploadService.UploadFile(conversation.CompanyId, fileName, file);

                        if (uploadFileResult?.Url == null)
                        {
                            return null;
                        }

                        newuUploadedFile.Filename = fileName;
                        newuUploadedFile.FileSize = uploadFileResult.FileSizeInByte;
                        newuUploadedFile.BlobContainer = conversation.CompanyId;

                        var domainName = _configuration.GetValue<String>("Values:DomainName");
                        newuUploadedFile.Url = $"{domainName}/Message/File/Private/{newuUploadedFile.FileId}/{file.FileName}";
                        newuUploadedFile.MIMEType = file.ContentType;

                        if (newuUploadedFile.MIMEType.Contains("image"))
                        {
                            try
                            {
                                var imageSize = await ImageHelper.GetImageSizeAsync(file.OpenReadStream());

                                var dimension = new
                                {
                                    width = imageSize.Width,
                                    height = imageSize.Height
                                };

                                newuUploadedFile.Metadata = JsonConvert.SerializeObject(dimension);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(
                                    ex,
                                    "[{MethodName}] Company {CompanyId}, Conversation {ConversationId}, File {FileName} !!! Image File Metadata Error: {ExceptionMessage} !!!",
                                    nameof(SendConversationNote),
                                    conversation.CompanyId,
                                    conversation.Id,
                                    file.FileName,
                                    ex.Message);
                            }
                        }

                        conversationMessage.UploadedFiles.Add(newuUploadedFile);
                    }
                }

                _appDbContext.ConversationMessages.Add(conversationMessage);

                await _appDbContext.SaveChangesAsync();

                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.MessageStored,
                    new Dictionary<string, string>
                    {
                        {
                            "channel_type", conversationMessage.Channel
                        },
                        {
                            "direction", conversationMessage.IsSentFromSleekflow ? "sent" : "received"
                        },
                        {
                            "message_type", conversationMessage.MessageType
                        },
                    });

                watch.Stop();

                _logger.LogInformation(
                    "[Receive File Message] [Conversation:{ConversationId}] [{ConversationMessageChannel}] Preparation executed in: {ElapsedMilliseconds} ms",
                    conversation.Id,
                    conversationMessage.Channel,
                    watch.ElapsedMilliseconds);

                watch = Stopwatch.StartNew();

                conversationMessage = await ReceiveMessageFromChannels(conversation, conversationMessage);

                await _appDbContext.SaveChangesAsync();

                watch.Stop();

                _logger.LogInformation(
                    "[Receive File Message] [Conversation:{ConversationId}] [{ConversationMessageChannel}] Channel sent executed in: {ElapsedMilliseconds} ms",
                    conversation.Id,
                    conversationMessage.Channel,
                    watch.ElapsedMilliseconds);

                watch = Stopwatch.StartNew();

                results.Add(conversationMessage);

                HandleLowerFunnelEvent(conversation, conversationMessage);

                await MessageCompletion(conversation, conversationMessage, triggerActivity);

                watch.Stop();

                _logger.LogInformation(
                    "[Receive File Message] [Conversation:{ConversationId}] [{ConversationMessageChannel}] Completion executed in: {ElapsedMilliseconds} ms",
                    conversation.Id,
                    conversationMessage.Channel,
                    watch.ElapsedMilliseconds);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[Exception] [Receive File Message Error] {CompanyId} {ConversationId} {MessageId}",
                    conversation.CompanyId,
                    conversation.Id,
                    conversationMessage.Id);

                _conversationMeters.IncrementCounter(conversationMessage.Channel, ConversationMeterOptions.ProcessMessageFailed);

                throw;
            }

            _conversationMeters.IncrementCounter(conversationMessage.Channel, ConversationMeterOptions.ProcessMessageSuccess);

            return results;
        }

        public async Task<IList<ConversationMessage>> ReceiveFileMessageByUrlAsync(
            Conversation conversation,
            ConversationMessage conversationMessage,
            List<FileURLMessage> fileUrlMessages,
            bool triggerActivity = true)
        {
            var watch = Stopwatch.StartNew();

            _logger.LogInformation(
                "[Receive File Message URL] [Conversation:{ConversationId}] [{Conversation}] [{ConversationMessage}] [{ConversationMessageChannel}] started ms",
                conversation.Id,
                JsonConvert.SerializeObject(
                    conversation,
                    new JsonSerializerSettings
                    {
                        ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                        NullValueHandling = NullValueHandling.Ignore,
                    }),
                JsonConvert.SerializeObject(
                    conversationMessage,
                    new JsonSerializerSettings
                    {
                        ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                        NullValueHandling = NullValueHandling.Ignore,
                    }),
                conversationMessage.Channel);

            var results = new List<ConversationMessage>();

            try
            {
                conversation = await _conversationResolver.GetConversationAsync(conversation, conversationMessage);
                conversationMessage = await GetConversationMessage(conversation, conversationMessage);

                foreach (var file in fileUrlMessages)
                {
                    // var fileName = $"Conversation/{conversation.Id}/{DateTime.UtcNow:o}";
                    UploadFileResult uploadFileResult = null;
                    string fileMetadata = null;
                    var fileNameAndPath = ConstructFileMessageNameAndPathForUpload(
                        file,
                        conversation.Id);

                    var (fileName, filePath) = fileNameAndPath;

                    // Download File Url to stream
                    if (!string.IsNullOrEmpty(file.FileURL))
                    {
                        var client = new RestClient(file.FileURL);
                        var request = new RestRequest(Method.GET);
                        IRestResponse response = await client.ExecuteAsync(request);

                        if (!(conversationMessage.Channel == ChannelTypes.Instagram
                              && response.ContentType != file.MIMEType
                              && file.MIMEType == "audio/ogg"))
                        {
                            file.MIMEType = response.ContentType;
                        }

                        var fileStream = client.DownloadData(request);
                        var fileIn = new MemoryStream(fileStream);

                        file.FileStream = fileIn;
                        file.FileStream.Position = 0;
                        file.FileURL = null;
                    }

                    if (file.MIMEType.Contains("image"))
                    {
                        try
                        {
                            var imageSize = await ImageHelper.GetImageSizeAsync(file.FileStream, file.MIMEType);

                            var dimension = new
                            {
                                width = imageSize.Width,
                                height = imageSize.Height
                            };

                            fileMetadata = JsonConvert.SerializeObject(dimension);

                            file.FileStream.Position = 0;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "[{MethodName}]Conversation Id: {ConversationId}, Company Id: {ConversationCompanyId}, file: {FileName}|{FileMimeType} !!! Image File Metadata Error: !!!\\n{ExMessage}",
                                nameof(SendFileMessageByFBURL),
                                conversation?.Id,
                                conversation?.CompanyId,
                                file?.FileName,
                                file?.MIMEType,
                                ex.Message);
                        }
                    }

                    if (file.MIMEType == "audio/ogg"
                        || file.MIMEType == "audio/mp4"
                        || file.MIMEType == "audio/x-m4a"
                        || (file.FileName != null && file.FileName.Contains("audioclip")))
                    {
                        var inputFile = TempFileHelper.GetTempFileFullPath("conversation");
                        var outputFile = TempFileHelper.GetTempFileFullPath("conversation");

                        try
                        {
                            await File.WriteAllBytesAsync(inputFile, ((MemoryStream) file.FileStream).ToArray());

                            try
                            {
                                var ffMpegConverter = _mediaEncodingService.GetFfMpegConverterInstance();
                                await _mediaEncodingService.ConvertMediaByGoogleTranscoder(inputFile, outputFile, "mp3", ffMpegConverter);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(
                                    ex,
                                    "[{MethodName}] !!!!! FFMPEG ERROR !!!!!! Company {CompanyId}, Conversation {ConversationId}, File name {FileName}. {ExceptionMessage}",
                                    nameof(SendConversationNote),
                                    conversation?.CompanyId,
                                    conversation?.Id,
                                    file.FileName,
                                    ex.Message);
                            }

                            var outputStream = File.OpenRead(outputFile);
                            file.FileStream = outputStream;
                            file.MIMEType = "audio/mpeg";

                            file.FileName = Path.ChangeExtension(file.FileName, ".mp3");
                            fileNameAndPath = ConstructFileMessageNameAndPathForUpload(
                                file,
                                conversation.Id);
                            (fileName, filePath) = fileNameAndPath;

                            uploadFileResult = await _uploadService.UploadFileBySteam(
                                conversation.CompanyId,
                                filePath,
                                file.FileStream,
                                file.MIMEType);
                        }
                        finally
                        {
                            TempFileHelper.DeleteFileWithRetry(inputFile);
                            TempFileHelper.DeleteFileWithRetry(outputFile);
                        }
                    }
                    else
                    {
                        fileNameAndPath = ConstructFileMessageNameAndPathForUpload(
                            file,
                            conversation.Id);
                        (fileName, filePath) = fileNameAndPath;

                        if (string.IsNullOrEmpty(file.FileURL))
                        {
                            uploadFileResult = await _uploadService.UploadFileBySteam(
                                conversation.CompanyId,
                                filePath,
                                file.FileStream,
                                file.MIMEType);
                        }
                        else
                        {
                            uploadFileResult = await _uploadService.UploadFileByURL(
                                conversation.CompanyId,
                                filePath,
                                file.FileURL); // unreachable
                        }
                    }

                    if (uploadFileResult?.Url == null)
                    {
                        return null;
                    }

                    var newUploadedFile = _mapper.Map<UploadedFile>(conversationMessage);
                    newUploadedFile.Filename = filePath; // actually db is storing file path, rather than just file name
                    newUploadedFile.FileSize = uploadFileResult.FileSizeInByte;
                    newUploadedFile.BlobContainer = conversation.CompanyId;

                    var domainName = _configuration.GetValue<String>("Values:DomainName");
                    newUploadedFile.Url = $"{domainName}/Message/File/Private/{newUploadedFile.FileId}/{fileName}";
                    newUploadedFile.MIMEType = file.MIMEType;
                    newUploadedFile.Metadata = fileMetadata;

                    conversationMessage.UploadedFiles.Add(newUploadedFile);
                }

                watch.Stop();

                _logger.LogInformation(
                    "[Receive File Message URL] [Conversation:{ConversationId}] [{ConversationMessageChannel}] Preparation executed in: {ElapsedMilliseconds} ms",
                    conversation.Id,
                    conversationMessage.Channel,
                    watch.ElapsedMilliseconds);

                watch = Stopwatch.StartNew();

                _appDbContext.ConversationMessages.Add(conversationMessage);
                await _appDbContext.SaveChangesAsync();

                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.MessageStored,
                    new Dictionary<string, string>
                    {
                        {
                            "channel_type", conversationMessage.Channel
                        },
                        {
                            "direction", conversationMessage.IsSentFromSleekflow ? "sent" : "received"
                        },
                        {
                            "message_type", conversationMessage.MessageType
                        },
                    });

                conversationMessage = await ReceiveMessageFromChannels(conversation, conversationMessage);

                var isMessageStatusChanged = false;
                if (conversationMessage.Status < MessageStatus.Sent
                    && !string.IsNullOrEmpty(conversationMessage.MessageUniqueID))
                {
                    conversationMessage.Status = MessageStatus.Sent;
                    isMessageStatusChanged = true;
                }

                await _appDbContext.SaveChangesAsync();

                if (isMessageStatusChanged)
                {
                    await _userProfileHooks.OnMessageStatusUpdatedAsync(
                        conversation.CompanyId,
                        conversation.UserProfileId,
                        conversation,
                        conversationMessage);
                }

                results.Add(conversationMessage);

                HandleLowerFunnelEvent(conversation, conversationMessage);

                await MessageCompletion(conversation, conversationMessage, triggerActivity);

                watch.Stop();

                _logger.LogInformation(
                    "[Receive File Message URL] [Conversation:{ConversationId}] [{ConversationMessageChannel}] Completion executed in: {ElapsedMilliseconds} ms",
                    conversation.Id,
                    conversationMessage.Channel,
                    watch.ElapsedMilliseconds);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[Exception] [Receive File URL Message] {CompanyId} {ConversationId} {MessageId}",
                    conversation.CompanyId,
                    conversation.Id,
                    conversationMessage.Id);

                _conversationMeters.IncrementCounter(conversationMessage.Channel, ConversationMeterOptions.ProcessMessageFailed);

                throw;
            }

            _conversationMeters.IncrementCounter(conversationMessage.Channel, ConversationMeterOptions.ProcessMessageSuccess);

            return results;
        }

        private FileMessageNameAndPath ConstructFileMessageNameAndPathForUpload(
            FileURLMessage fileUrlMessage,
            string conversationId)
        {
            // Create the base path using conversationId and current UTC timestamp
            var basePath = $"Conversation/{conversationId}/{DateTime.UtcNow:o}";

            // Determine the file name,
            // 1. fileName not empty with MimeType
            // 2. fileName not empty without MimeType (likely coming from FB IG), and then will use url to get MimeType after this function
            // 3. fileName empty with MimeType (from FB IG as well)
            string fileName;
            if (!string.IsNullOrEmpty(fileUrlMessage.FileName))
            {
                fileName = FilenameHelper.FormatFileName(fileUrlMessage.FileName, fileUrlMessage.MIMEType);
            }
            else
            {
                var extension = MimeTypeMap.GetExtension(fileUrlMessage.MIMEType, false);
                fileName = $"{Guid.NewGuid()}{extension}";
            }

            // Combine the base path and file name
            var fullPath = $"{basePath}/{fileName}";

            return new FileMessageNameAndPath(fileName, fullPath);
        }

        private async Task<ConversationMessage> SendOrScheduleMessage(
            Conversation conversation,
            ConversationMessage conversationMessage)
        {
            if (conversationMessage.ScheduleSentAt.HasValue)
            {
                // Schedule Message
                var ts = conversationMessage.ScheduleSentAt.Value - DateTime.UtcNow;

                if (ts.TotalSeconds > 0)
                {
                    var jobId = BackgroundJob.Schedule(
                        () => SendMessageToChannels(conversation.Id, conversationMessage.Id),
                        ts);
                    conversationMessage.JobId = jobId;
                    conversationMessage.Status = MessageStatus.Scheduled;
                    await _appDbContext.SaveChangesAsync();
                }
                else
                {
                    conversationMessage = await SendMessageToChannels(conversation, conversationMessage);
                }
            }
            else
            {
                // Send Message
                conversationMessage = await SendMessageToChannels(conversation, conversationMessage);
            }

            return conversationMessage;
        }

        public async Task<ConversationMessage> SendMessageToChannels(string conversationId, long conversationMessageId)
        {
            var conversation = await _appDbContext.Conversations
                .Include(x => x.UserProfile)
                .Include(x => x.ViberUser)
                .Include(x => x.TelegramUser)
                .Include(x => x.InstagramUser)
                .Include(x => x.WhatsappUser)
                .Include(x => x.facebookUser)
                .Include(x => x.WhatsappUser)
                .Include(x => x.NaiveUser.Identity)
                .Include(x => x.Assignee.Identity)
                .Include(x => x.EmailAddress)
                .Include(x => x.WebClient)
                .Include(x => x.WeChatUser)
                .Include(x => x.LineUser)
                .Include(x => x.SMSUser)
                .Include(x => x.WhatsApp360DialogUser)
                .Include(x => x.WhatsappCloudApiUser)
                .Include(x => x.TikTokUser)
                .FirstOrDefaultAsync(x => x.Id == conversationId);

            // Split one-to-many query
            conversation.conversationHashtags = await _appDbContext.ConversationHashtags
                .Include(x => x.Hashtag)
                .Where(x => x.ConversationId == conversationId)
                .ToListAsync();

            conversation.AdditionalAssignees = await _appDbContext.ConversationAdditionalAssignees
                .Include(x => x.Assignee.Identity)
                .Where(x => x.ConversationId == conversationId)
                .ToListAsync();

            conversation.UserProfile.CustomFields = await _appDbContext.UserProfileCustomFields
                .Where(x => x.UserProfileId == conversation.UserProfileId)
                .ToListAsync();

            var conversationMessage = await _appDbContext.ConversationMessages
                .Include(x => x.UploadedFiles)
                .Include(x => x.EmailFrom)
                .Include(x => x.Sender)
                .Include(x => x.Receiver)
                .Include(x => x.SenderDevice)
                .Include(x => x.ReceiverDevice)
                .Include(x => x.facebookSender)
                .Include(x => x.facebookReceiver)
                .Include(x => x.whatsappSender)
                .Include(x => x.whatsappReceiver)
                .Include(x => x.Whatsapp360DialogSender)
                .Include(x => x.Whatsapp360DialogReceiver)
                .Include(x => x.WebClientSender)
                .Include(x => x.WebClientReceiver)
                .Include(x => x.MessageAssignee.Identity)
                .Include(x => x.WeChatSender)
                .Include(x => x.WeChatReceiver)
                .Include(x => x.LineSender)
                .Include(x => x.LineReceiver)
                .Include(x => x.SMSSender)
                .Include(x => x.SMSReceiver)
                .Include(x => x.InstagramSender)
                .Include(x => x.InstagramReceiver)
                .Include(x => x.Whatsapp360DialogExtendedMessagePayload)
                .Include(x => x.ExtendedMessagePayload)
                .FirstOrDefaultAsync(x => x.Id == conversationMessageId);

            if (conversationMessage.Status == MessageStatus.Scheduled)
            {
                conversationMessage = await SendMessageToChannels(conversation, conversationMessage);

                // Update Sent Time to Schedule time
                if (conversationMessage.ScheduleSentAt != null)
                {
                    conversationMessage.CreatedAt = conversationMessage.ScheduleSentAt.Value;
                    conversationMessage.UpdatedAt = conversationMessage.ScheduleSentAt.Value;
                    conversationMessage.Timestamp =
                        new DateTimeOffset(conversationMessage.ScheduleSentAt.Value).ToUnixTimeSeconds();
                    conversationMessage.FrondendTimestamp =
                        new DateTimeOffset(conversationMessage.ScheduleSentAt.Value).ToUnixTimeMilliseconds();
                }

                await _appDbContext.SaveChangesAsync();

                await _signalRService.SignalROnMessageStatusChanged(conversationMessage);

                await MessageCompletion(conversation, conversationMessage, false);
            }

            return conversationMessage;
        }

        public async Task<ConversationMessage> SendMessageToChannels(
            Conversation conversation,
            ConversationMessage conversationMessage)
        {
            var (conv, message) =
                await _messageChannelSwitcher.SwitchDefaultChannelIfRequiredAsync(
                    conversation,
                    conversationMessage);

            var channelMessageHandler = _channelMessageProvider.GetChannelMessageHandler(message.Channel);

            return await channelMessageHandler.SendChannelMessageAsync(conv, message);
        }

        public async Task<ConversationMessage> ReceiveMessageFromChannels(
            Conversation conversation,
            ConversationMessage conversationMessage)
        {
            var (conv, message) =
                await _messageChannelSwitcher.SwitchDefaultChannelIfRequiredAsync(
                    conversation,
                    conversationMessage);

            var channelMessageHandler = _channelMessageProvider.GetChannelMessageHandler(message.Channel);

            return await channelMessageHandler.ReceiveChannelMessageAsync(conv, message);
        }

        public async Task<Conversation> ChangeConversationAssignedTeam(
            Conversation conversation,
            CompanyTeam companyTeam,
            bool isTriggerUpdate = true,
            string changedBy = null,
            UserProfileSource? userProfileSource = null)
        {
            var isChangeAssignee =
                conversation.AssignedTeamId != companyTeam?.Id
                || !conversation.AssigneeId.HasValue;

            if (conversation.AssignedTeamId.HasValue
                && conversation.AssignedTeam == null)
            {
                conversation.AssignedTeam = await _appDbContext.CompanyStaffTeams
                    .FirstOrDefaultAsync(x => x.Id == conversation.AssignedTeamId);
            }

            if (!isChangeAssignee)
            {
                return conversation;
            }

            TeamData newTeam = null;

            if (companyTeam is not null)
            {
                newTeam = new TeamData(
                    companyTeam.Id,
                    companyTeam.TeamName);
            }

            await _userProfileService.SetFieldValueByFieldNameSafe(
                conversation.UserProfileId,
                "AssignedTeam",
                newTeam?.TeamId.ToString(),
                isTriggerUpdate);

            conversation.AssignedTeamId = newTeam?.TeamId;
            conversation.ModifiedAt = DateTime.UtcNow;
            await _appDbContext.SaveChangesAsync();

            if (conversation.ActiveStatus == ActiveStatus.Inactive)
            {
                return conversation;
            }

            await _cacheManagerService.DeleteCacheWithConstantKeyAsync($":1:conversation:{conversation.Id}");
            await _signalRService.SignalROnConversationAssignTeamChanged(conversation, userProfileSource);

            return conversation;
        }

        public async Task<Conversation> ChangeConversationAssignee(
            Conversation conversation,
            Staff assignee,
            bool isTriggerUpdate = true,
            string changedBy = null,
            UserProfileSource? userProfileSource = null)
        {
            var isRbacEnabled = await _rbacService.IsRbacEnabled(conversation.CompanyId);
            if (isRbacEnabled)
            {
                var stopwatch = Stopwatch.StartNew();
                var result = await RbacChangeConversationAssignee(conversation, assignee, isTriggerUpdate, changedBy, userProfileSource);
                stopwatch.Stop();
                _logger.LogInformation(
                    "[ChangeConversationAssignee - RbacChangeConversationAssignee] Execution time: {ElapsedMilliseconds} ms",
                    stopwatch);
                return result;
            }

            return await DefaultChangeConversationAssignee(
                conversation,
                assignee,
                isTriggerUpdate,
                changedBy,
                userProfileSource);
        }

        public async Task<Conversation> DefaultChangeConversationAssignee(
            Conversation conversation,
            Staff assignee,
            bool isTriggerUpdate,
            string changedBy,
            UserProfileSource? userProfileSource = null)
        {
            _logger.LogInformation(
                "Executing {MethodName} [ConversationId: {ConversationId}] [ConversationAssigneeId: {ConversationAssigneeId}] [ConversationAssignedTeamId: {ConversationAssignedTeamId}] [AssigneeId: {AssigneeId}]",
                nameof(DefaultChangeConversationAssignee),
                conversation?.Id,
                conversation?.AssigneeId,
                conversation?.AssignedTeamId,
                assignee?.Id);

            var oldConversationListener = await _signalRService.GetSignalRTargetUserIds(
                conversation.CompanyId,
                conversation.Id,
                conversation.AssignedTeamId,
                conversation.AssigneeId,
                !conversation.AssigneeId.HasValue,
                isGetActiveUserOnly: false);

            var isChangeAssignee =
                conversation.AssigneeId != assignee?.Id
                                  || (conversation.AssigneeId.HasValue && assignee == null);

            _logger.LogInformation(
                "[{MethodName}] ConversationId: {ConversationId}, isChangeAssignee: {IsChangeAssignee}",
                nameof(DefaultChangeConversationAssignee),
                conversation.Id,
                isChangeAssignee);
            if (!isChangeAssignee)
            {
                return conversation;
            }

            var originalAssignee = conversation.AssigneeId;

            if (conversation.AssigneeId != assignee?.Id)
            {
                // log assignee changed
                await _userProfileHooks.OnConversationAssigneeChangedAsync(
                    conversation.CompanyId,
                    conversation.UserProfileId,
                    changedBy,
                    async () =>
                    {
                        var originalAssigneeData = conversation.AssigneeId.HasValue ?
                            await _appDbContext.UserRoleStaffs
                        .Where(x => x.Id == conversation.AssigneeId)
                        .Select(x => new StaffData(x.Id.ToString(), x.IdentityId, x.Identity.DisplayName))
                        .FirstOrDefaultAsync() :
                            null;

                        var newAssigneeData = assignee != null
                            ? await _appDbContext.UserRoleStaffs.Where(x => x.Id == assignee.Id)
                                .Select(x => new StaffData(x.Id.ToString(), x.IdentityId, x.Identity.DisplayName))
                                .FirstOrDefaultAsync()
                            : null;

                        _logger.LogInformation(
                            "[{MethodName}] ConversationId: {ConversationId}, Original Assignee: {OriginalAssignee}, New Assignee: {NewAssignee}",
                            nameof(DefaultChangeConversationAssignee),
                            conversation?.Id,
                            JsonConvert.SerializeObject(
                                originalAssigneeData,
                                new JsonSerializerSettings
                                {
                                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                                    NullValueHandling = NullValueHandling.Ignore,
                                }),
                            JsonConvert.SerializeObject(
                                newAssigneeData,
                                new JsonSerializerSettings
                                {
                                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                                    NullValueHandling = NullValueHandling.Ignore,
                                }));

                        return new OnConversationAssigneeChangedData(
                            originalAssigneeData,
                            newAssigneeData,
                            conversation.Id);
                    });
            }

            conversation.Assignee = assignee;
            conversation.ModifiedAt = DateTime.UtcNow;
            await _appDbContext.SaveChangesAsync();

            _logger.LogInformation(
                "[{MethodName}] ConversationId: {ConversationId} assignee changed to {AssigneeId}, Conversation: {Conversation}",
                nameof(DefaultChangeConversationAssignee),
                conversation.Id,
                assignee?.Id,
                JsonConvert.SerializeObject(
                    conversation,
                    new JsonSerializerSettings
                    {
                        ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                        NullValueHandling = NullValueHandling.Ignore,
                    }));

            if (assignee != null)
            {
                var rolePermission = await _appDbContext.CompanyRolePermissions
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == conversation.CompanyId
                            && x.StaffUserRole == assignee.RoleType);

                if (rolePermission != null
                    && rolePermission.Permission.AddAsCollaboratorWhenAssignedToOthers)
                {
                    try
                    {
                        // Add orignal assignee to additioanl assignee
                        if (originalAssignee.HasValue)
                        {
                            if (!await _appDbContext.ConversationAdditionalAssignees.AnyAsync(
                                    x => x.ConversationId == conversation.Id &&
                                         x.AssigneeId == originalAssignee))
                            {
                                conversation = await _conversationAssigneeService.AddAdditionalAssignees(
                                    conversation,
                                    new List<long>
                                    {
                                        originalAssignee.Value
                                    },
                                    userProfileSource: userProfileSource);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[{MethodName}] Add to additional assignee error for conversation {ConversationId}: {ExceptionString}",
                            nameof(ChangeConversationAssignee),
                            conversation?.Id,
                            ex.ToString());
                    }
                }
            }


            try
            {
                // Add new assignee to additional assignee
                if (conversation.AssigneeId.HasValue
                    && conversation.AdditionalAssignees
                        .Any(x => x.AssigneeId == conversation.AssigneeId))
                {
                    conversation = await _conversationAssigneeService.RemoveAdditionalAssignees(
                        conversation,
                        new List<long>
                        {
                            conversation.AssigneeId.Value
                        });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Remove additional assignee error for conversation {ConversationId}: {ExceptionString}",
                    nameof(ChangeConversationAssignee),
                    conversation?.Id,
                    ex.ToString());
            }

            var preUpdatedUserProfile = await _flowHubService.GetUserProfileDictAsync(
                conversation.CompanyId,
                conversation.UserProfileId);

            if (conversation.Assignee != null)
            {
                await _userProfileService.SetFieldValueByFieldNameSafe(
                    conversation.UserProfileId,
                    "ContactOwner",
                    assignee.IdentityId,
                    true);
                await _signalRService.SignalROnConversationAssigneeDeleted(conversation, oldConversationListener, userProfileSource);
                await _signalRService.SignalROnConversationAssigneeChanged(conversation, isTriggerUpdate, userProfileSource);
            }
            else
            {
                BackgroundJob.Enqueue<IUserProfileService>(
                    x => x.SetFieldValueByFieldNameSafe(
                    conversation.UserProfileId,
                    "ContactOwner",
                    string.Empty,
                    true));

                await _signalRService.SignalROnConversationAssigneeDeleted(conversation, userProfileSource);
            }

            if (!conversation.AssignedTeamId.HasValue)
            {
                // BackgroundJob.Enqueue<IUserProfileService>(x =>
                //     x.SetFieldValueByFieldNameSafe(conversation.UserProfileId, "AssignedTeam", "", true));
                await _userProfileService.SetFieldValueByFieldNameSafe(
                    conversation.UserProfileId,
                    "AssignedTeam",
                    string.Empty,
                    true);
            }

            await _userProfileHooks.OnUserProfileFieldChangedAsync(
                conversation.CompanyId,
                conversation.UserProfileId,
                changedBy,
                async () =>
                {
                    var postUpdatedUserProfile = await _flowHubService.GetUserProfileDictAsync(
                        conversation.CompanyId,
                        conversation.UserProfileId);

                    return new OnUserProfileFieldChangedData(
                        postUpdatedUserProfile,
                        preUpdatedUserProfile,
                        "Updated");
                });

            if (conversation.ActiveStatus == ActiveStatus.Inactive)
            {
                return conversation;
            }

            try
            {
                if (conversation.ActiveStatus == ActiveStatus.Active
                    && isChangeAssignee
                    && conversation.Status == "open"
                    && DateTime.UtcNow.AddHours(-1) < conversation.UpdatedTime)
                {
                    var conversationMessage = await _appDbContext.ConversationMessages
                        .OrderByDescending(x => x.CreatedAt)
                        .FirstOrDefaultAsync(
                            message =>
                                message.ConversationId == conversation.Id
                                && message.Channel != ChannelTypes.Note);

                    if (!conversationMessage.IsSentFromSleekflow)
                    {
                        await _signalRService.SendNewMessagePushNotification(conversation, conversationMessage);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Send new message push push notification error for conversation {ConversationId}: {ExceptionMessage}",
                    nameof(ChangeConversationAssignee),
                    conversation?.Id,
                    ex.Message);
            }

            await _cacheManagerService.DeleteCacheWithConstantKeyAsync($":1:conversation:{conversation.Id}");

            return conversation;
        }

        public async Task<Conversation> ChangeConversationStatus(
            string conversationId,
            string staffId,
            StatusViewModel statusViewModel,
            bool setUnread = false,
            UserProfileSource? userProfileSource = null)
        {
            var conversation = await _appDbContext.Conversations
                .Include(x => x.UserProfile)
                .Include(x => x.Assignee.Identity)
                .FirstOrDefaultAsync(x => x.Id == conversationId);

            if (conversation == null)
            {
                throw new EntryPointNotFoundException("conversation not found");
            }

            // setUnread flag use for snooze function.
            if (conversation.Status == "closed" && statusViewModel.Status == "open" && setUnread)
            {
                // not allow to update the status to open from snooze if conversation status is closed
                return conversation;
            }

            if (conversation != null)
            {
                switch (statusViewModel.Status.ToLower())
                {
                    case "open":
                    case "pending":
                    case "closed":
                        if (conversation.Status != statusViewModel.Status)
                        {
                            // _appDbContext.ConversationActivityLogs.Add(new ConversationActivityLog
                            // {
                            //    ConversationId = conversation.Id,
                            //    LogMessage = $"Status: {conversation.Status} to {statusViewModel.Status}",
                            //    Status = "done",
                            //    Type = "StatusChanged",
                            //    ModifiedBy = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == staffId).FirstOrDefaultAsync()
                            // });
                            var oldStatus = GetStatusName(conversation.Status);
                            var newStatus = GetStatusName(statusViewModel.Status);

                            conversation.ModifiedAt = DateTime.UtcNow;
                            var conversationStatusModifiedAt = conversation.ModifiedAt;

                            conversation.Status = statusViewModel.Status;
                            conversation.SnoozeUntil = null;

                            await _appDbContext.SaveChangesAsync();

                            if (setUnread)
                            {
                                conversation.UnreadMessageCount = 1;

                                // conversation.UpdatedTime = DateTime.UtcNow;
                                await _appDbContext.SaveChangesAsync();

                                await _signalRService.SendSnoozeCompletedReminder(conversation);

                                try
                                {
                                    await SendConversationNote(
                                        conversation.CompanyId,
                                        conversation.Id,
                                        null,
                                        new ConversationMessage
                                        {
                                            Channel = ChannelTypes.Note,
                                            MessageType = "text",
                                            MessageContent = "Reminder"
                                        },
                                        null);
                                }
                                catch (Exception ex)
                                {
                                    _logger.LogError(
                                        ex,
                                        "Note reminder error {sleekflow_company_id} {sleekflow_conversation_id}",
                                        conversation.CompanyId,
                                        conversationId);
                                }
                            }

                            if (statusViewModel.Status.ToLower() == "pending")
                            {
                                var timeSpan = new TimeSpan();

                                if (statusViewModel.SnoozeUntil.HasValue)
                                {
                                    conversation.SnoozeUntil = statusViewModel.SnoozeUntil.Value;
                                    timeSpan = conversation.SnoozeUntil.Value - DateTime.UtcNow;
                                }
                                else
                                {
                                    switch (statusViewModel.SnoozeOptions)
                                    {
                                        case SnoozeOptions.OneHour:
                                            timeSpan = TimeSpan.FromHours(1);

                                            break;
                                        case SnoozeOptions.ThreeHour:
                                            timeSpan = TimeSpan.FromHours(3);

                                            break;
                                        case SnoozeOptions.OneDay:
                                            timeSpan = TimeSpan.FromDays(1);

                                            break;
                                        case SnoozeOptions.OneWeek:
                                            timeSpan = TimeSpan.FromDays(7);

                                            break;
                                        case SnoozeOptions.OneMonth:
                                            timeSpan = TimeSpan.FromDays(30);

                                            break;
                                    }

                                    conversation.SnoozeUntil = DateTime.UtcNow + timeSpan;
                                }

                                await _appDbContext.SaveChangesAsync();


                                var jobId = BackgroundJob.Schedule<IConversationMessageService>(
                                    x => x.ChangeConversationStatus(
                                        conversationId,
                                        staffId,
                                        new StatusViewModel
                                        {
                                            Status = "open"
                                        },
                                        true,
                                        userProfileSource),
                                    timeSpan);

                                await _conversationService.UpdateConversationMetadataAsync(
                                    conversation.CompanyId,
                                    conversation.Id,
                                    "snooze_job_id",
                                    jobId);

                                await _userProfileHooks.OnConversationStatusChangedAsync(
                                    conversation.CompanyId,
                                    conversation.UserProfileId,
                                    staffId,
                                    () => Task.FromResult(
                                        new OnConversationStatusChangedData(
                                            oldStatus,
                                            newStatus,
                                            conversation.SnoozeUntil?.ToUniversalTime(),
                                            conversationId)));
                            }
                            else
                            {
                                if (conversation.Metadata != null)
                                {
                                    if (conversation.Metadata.TryGetValue("snooze_job_id", out var value))
                                    {
                                        var jobId = (string) value ?? string.Empty;
                                        if (!jobId.IsNullOrEmpty())
                                        {
                                            BackgroundJob.Delete(jobId);
                                            await _conversationService.UpdateConversationMetadataAsync(
                                                conversation.CompanyId,
                                                conversation.Id,
                                                "snooze_job_id",
                                                string.Empty);
                                        }
                                    }
                                }

                                await _userProfileHooks.OnConversationStatusChangedAsync(
                                    conversation.CompanyId,
                                    conversation.UserProfileId,
                                    staffId,
                                    () => Task.FromResult(
                                        new OnConversationStatusChangedData(
                                            oldStatus,
                                            newStatus,
                                            null,
                                            conversationId)));

                                // Custom Module for Z.Com Backup Chat History
                                var shouldBackupChatHistory = await _zDotComService.ShouldBackupChatHistoryAsync(
                                    conversation,
                                    oldStatus,
                                    newStatus,
                                    conversationStatusModifiedAt);

                                if (shouldBackupChatHistory)
                                {
                                    BackgroundJob.Enqueue<IZDotComService>(x => x.CreateChatHistoryBackupAndSendEmailAsync(conversationId, staffId));
                                }
                            }

                            var headers = new List<ImportHeader>
                            {
                                new ImportHeader
                                {
                                    HeaderName = "ConversationStatus"
                                }
                            };

                            var fields = new List<string>
                            {
                                statusViewModel.Status
                            };
                            var importUserProfile = new ImportUserProfileObject(headers, fields);

                            var header = new ImportUserProfileObject(headers, fields);

                            BackgroundJob.Enqueue<IAutomationService>(
                                x => x.CustomFieldsBulkChangedTrigger(
                                    conversation.UserProfileId,
                                    importUserProfile,
                                    importUserProfile._fields));

                            await _signalRService.SignalROnConversationStatusChanged(conversation, userProfileSource);

                            await _cacheManagerService.DeleteCacheWithConstantKeyAsync($":1:conversation:{conversationId}");
                        }

                        return conversation;
                }
            }

            return conversation;
        }

        private string GetStatusName(string status)
        {
            switch (status.ToLower())
            {
                case "open":
                    return "Open";
                case "pending":
                    return "Snooze";
                case "closed":
                    return "Closed";
            }

            return status;
        }

        public bool IsConversationStatusChangedFromClosedToOpen(string oldStatus, string newStatus)
        {
            return oldStatus.ToLower() == "closed" && newStatus.ToLower() == "open";
        }

        public bool IsConversationStatusChangedToClosed(string newStatus)
        {
            return newStatus.ToLower() == "closed";
        }

        public async Task<MemoryStream> DownloadUploadedFile(UploadedFile file)
        {
            var fileMemoryStream = await _azureBlobStorageService.DownloadFromAzureBlob(
                file.Filename,
                file.BlobContainer);
            return fileMemoryStream;
        }

        public async ValueTask<Conversation> ChangeConversationStatus(
            Conversation conversation,
            ConversationMessage conversationMessage,
            bool isTriggerUpdate,
            UserProfileSource? userProfileSource = null)
        {
            try
            {
                if (!isTriggerUpdate
                    && conversationMessage.Channel == ChannelTypes.LiveChat)
                {
                    return conversation;
                }

                if (conversationMessage.DeliveryType == DeliveryType.Broadcast
                    || conversationMessage.DeliveryType == DeliveryType.AutomatedMessage
                    || conversationMessage.DeliveryType == DeliveryType.FlowHubAction
                    || conversationMessage.DeliveryType == DeliveryType.AiAgentAction
                    || conversationMessage.DeliveryType == DeliveryType.ReadMore
                    || conversationMessage.MessageType == MessageTypes.System
                    || conversationMessage.IsFromImport)
                {
                    return conversation;
                }

                if ((conversation.UpdatedTime > DateTime.UtcNow.AddDays(-7) && conversation.Status != "open")
                    || conversation.Status == "pending")
                {
                    await _userProfileHooks.OnConversationStatusChangedAsync(
                        conversation.CompanyId,
                        conversation.UserProfileId,
                        null,
                        () => Task.FromResult(
                            new OnConversationStatusChangedData(
                                conversation.Status,
                                GetStatusName("open"),
                                null,
                                conversation.Id)));

                    if (IsConversationStatusChangedFromClosedToOpen(conversation.Status, "open"))
                    {
                        // conversationMessage will not be null
                        // prevent incoming message that reopens the conversation missing from chat history backup
                        conversation.StatusChangedFromClosedToOpenAt =
                            conversationMessage.CreatedAt.AddMilliseconds(-200);
                    }

                    conversation.Status = "open";
                    conversation.SnoozeUntil = null;

                    await _appDbContext.SaveChangesAsync();



                    if (conversation.Metadata != null)
                    {
                        if (conversation.Metadata.TryGetValue("snooze_job_id", out var value))
                        {
                            var jobId = (string) value ?? string.Empty;
                            if (!jobId.IsNullOrEmpty())
                            {
                                BackgroundJob.Delete(jobId);
                                await _conversationService.UpdateConversationMetadataAsync(
                                    conversation.CompanyId,
                                    conversation.Id,
                                    "snooze_job_id",
                                    string.Empty);
                            }
                        }
                    }

                    await _signalRService.SignalROnConversationStatusChanged(conversation, userProfileSource);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Conversation {ConversationId} error: {ExceptionString}",
                    nameof(ChangeConversationStatus),
                    conversation?.Id,
                    ex.ToString());
            }

            return conversation;
        }

        private async ValueTask<bool> IsCSReply(Conversation conversation, ConversationMessage conversationMessage)
        {
            if (conversationMessage.Sender != null
                || conversationMessage.DeliveryType == DeliveryType.Broadcast
                || conversationMessage.DeliveryType == DeliveryType.AutomatedMessage
                || conversationMessage.DeliveryType == DeliveryType.FlowHubAction
                || conversationMessage.DeliveryType == DeliveryType.AiAgentAction
                || conversationMessage.MessageType == MessageTypes.System
                || conversationMessage.IsSentFromSleekflow)
            {
                return true;
            }

            switch (conversationMessage.Channel)
            {
                case "naive":
                    return !string.IsNullOrEmpty(conversationMessage.ReceiverId)
                           && !string.IsNullOrEmpty(conversationMessage.ReceiverDeviceUUID);
                case ChannelTypes.Email:
                    var companyEmailConfigDomain = await _appDbContext.CompanyCompanies
                        .Where(x => x.Id == conversation.CompanyId)
                        .Select(x => x.EmailConfig.Domain)
                        .AsNoTracking()
                        .FirstOrDefaultAsync();

                    if (companyEmailConfigDomain == null)
                    {
                        throw new Exception($"No email config found");
                    }

                    return !conversationMessage.EmailTo.Contains(companyEmailConfigDomain)
                           && conversationMessage.EmailCC != null
                           && !conversationMessage.EmailCC.Contains(companyEmailConfigDomain);
                case ChannelTypes.Facebook:
                    return conversationMessage.facebookSender?.pageId ==
                           conversationMessage.facebookSender?.FacebookId;
                case ChannelTypes.Instagram:
                    return conversationMessage.InstagramSender?.InstagramPageId ==
                           conversationMessage.InstagramSender?.InstagramId;
                case ChannelTypes.WhatsappTwilio:
                    // Sandbox must be false
                    if (conversationMessage.IsSandbox)
                    {
                        return false;
                    }

                    var whatsAppConfigSender = await _appDbContext.ConfigWhatsAppConfigs
                        .Where(x => x.CompanyId == conversation.CompanyId)
                        .Select(x => x.WhatsAppSender)
                        .AsNoTracking()
                        .ToListAsync();

                    if (!whatsAppConfigSender.Any())
                    {
                        throw new Exception($"No WhatsApp Sender found");
                    }

                    return whatsAppConfigSender.Contains(conversationMessage.whatsappSender?.whatsAppId);
                case ChannelTypes.Whatsapp360Dialog:
                    return conversationMessage.Whatsapp360DialogSender == null;
                case ChannelTypes.WhatsappCloudApi:
                    return conversationMessage.IsSentFromSleekflow;
                case ChannelTypes.LiveChat:
                    return conversationMessage.Sender == null
                           && conversationMessage.WebClientSender == null;
                case ChannelTypes.Wechat:
                    return conversationMessage.WeChatSender == null;
                case ChannelTypes.Line:
                    return conversationMessage.LineSender == null;
                case ChannelTypes.Viber:
                    return conversationMessage.ViberSender == null;
                case ChannelTypes.Telegram:
                    return conversationMessage.TelegramSender == null;
                case ChannelTypes.Sms:
                    {
                        var smsConfigSender = await _appDbContext.ConfigSMSConfigs
                            .Where(x => x.CompanyId == conversation.CompanyId)
                            .Select(x => x.SMSSender)
                            .AsNoTracking()
                            .ToListAsync();

                        if (!smsConfigSender.Any())
                        {
                            throw new Exception($"No WhatsApp Sender found");
                        }

                        return smsConfigSender.Contains(conversationMessage.SMSSender?.SMSId);
                    }
            }

            return false;
        }

        public async Task SendWhatsapp360DialogOptInButtonMessage(
            string companyId,
            string conversationId,
            long whatsapp360dialogConfigId)
        {
            var conversation = await _appDbContext.Conversations
                .Include(x => x.WhatsApp360DialogUser)
                .FirstOrDefaultAsync(
                    x =>
                        x.Id == conversationId &&
                        x.CompanyId == companyId);

            var optInConfig = await _appDbContext.ConfigWhatsApp360DialogConfigs
                .Where(x => x.Id == whatsapp360dialogConfigId && x.IsOptInEnable)
                .Select(x => x.OptInConfig)
                .FirstOrDefaultAsync();

            if (conversation == null
                || optInConfig == null)
            {
                return;
            }

            var conversationMessage = new ConversationMessage
            {
                Channel = ChannelTypes.Whatsapp360Dialog,
                MessageType = "template",
                MessageContent = optInConfig.TemplateMessageContent,
                Whatsapp360DialogReceiver = conversation.WhatsApp360DialogUser,
                Whatsapp360DialogReceiverId = conversation.WhatsApp360DialogUserId,
                IsSentFromSleekflow = true,
                Whatsapp360DialogExtendedMessagePayload = new Whatsapp360DialogExtendedMessagePayload()
                {
                    Whatsapp360DialogTemplateMessage = new Whatsapp360DialogTemplateMessageViewModel()
                    {
                        Language = optInConfig.Language,
                        TemplateName = optInConfig.TemplateName,
                        TemplateNamespace = optInConfig.TemplateNamespace
                    }
                },
                DeliveryType = DeliveryType.ReadMore,
            };

            await SendMessage(conversation, conversationMessage);
        }

        public async Task SendWhatsapp360DialogReadMoreMessage(
            string companyId,
            string conversationId,
            long whatsapp360dialogConfigId,
            long conversationMessageId)
        {
            var conversation = await _appDbContext.Conversations
                .Include(x => x.WhatsApp360DialogUser)
                .FirstOrDefaultAsync(
                    x =>
                        x.Id == conversationId
                        && x.CompanyId == companyId);

            var undeliveredConversationMessage = await _appDbContext.ConversationMessages
                .AsNoTracking()
                .Include(x => x.UploadedFiles)
                .Include(x => x.Whatsapp360DialogExtendedMessagePayload)
                .FirstOrDefaultAsync(
                    x =>
                        x.Id == conversationMessageId
                        && x.Status == MessageStatus.Undelivered);

            var optInConfig = await _appDbContext.ConfigWhatsApp360DialogConfigs
                .Where(
                    x =>
                        x.Id == whatsapp360dialogConfigId
                        && x.IsOptInEnable)
                .Select(x => x.OptInConfig)
                .FirstOrDefaultAsync();

            if (conversation == null
                || optInConfig == null
                || undeliveredConversationMessage == null)
            {
                return;
            }

            if (undeliveredConversationMessage.Whatsapp360DialogExtendedMessagePayload != null)
            {
                undeliveredConversationMessage.Whatsapp360DialogExtendedMessagePayload.Id = 0;
                undeliveredConversationMessage.Whatsapp360DialogExtendedMessagePayload.ConversationMessageId = 0;
            }

            var conversationMessage = new ConversationMessage
            {
                Channel = ChannelTypes.Whatsapp360Dialog,
                MessageType = undeliveredConversationMessage.MessageType,
                MessageContent = undeliveredConversationMessage.MessageContent,
                Whatsapp360DialogReceiver = undeliveredConversationMessage.Whatsapp360DialogReceiver,
                Whatsapp360DialogReceiverId = undeliveredConversationMessage.Whatsapp360DialogReceiverId,
                UploadedFiles = undeliveredConversationMessage.UploadedFiles,
                Whatsapp360DialogExtendedMessagePayload =
                    undeliveredConversationMessage.Whatsapp360DialogExtendedMessagePayload,
                IsSentFromSleekflow = true,
                DeliveryType = DeliveryType.AutomatedMessage,
            };

            if (conversationMessage.UploadedFiles is { Count: > 0 })
            {
                foreach (var conversationMessageUploadedFile in conversationMessage.UploadedFiles)
                {
                    conversationMessageUploadedFile.Id = 0;
                    conversationMessageUploadedFile.ConversationMessageId = conversationMessage.Id;
                    conversationMessageUploadedFile.FileId = Guid.NewGuid().ToString();
                }
            }

            await SendMessage(conversation, conversationMessage);
        }

        public async Task SendWhatsappCloudApiOptInButtonMessage(
            string companyId,
            string conversationId,
            long whatsappCloudApiConfigId,
            long conversationMessageId)
        {
            var conversation = await _appDbContext.Conversations
                .Include(x => x.WhatsappCloudApiUser)
                .FirstOrDefaultAsync(
                    x =>
                        x.Id == conversationId
                        && x.CompanyId == companyId);

            var optInConfig = await _appDbContext.ConfigWhatsappCloudApiConfigs
                .Where(
                    x =>
                        x.Id == whatsappCloudApiConfigId
                        && x.IsOptInEnable)
                .Select(x => x.OptInConfig)
                .FirstOrDefaultAsync();

            if (conversation == null
                || optInConfig == null)
            {
                return;
            }

            var extendedMessagePayload = new ExtendedMessagePayload()
            {
                Channel = ChannelTypes.WhatsappCloudApi
            };

            extendedMessagePayload.SetExtendedMessagePayloadDetailWithType(
                new ExtendedMessagePayloadDetail
                {
                    WhatsappCloudApiTemplateMessageObject = new WhatsappCloudApiTemplateMessageViewModel
                    {
                        TemplateName = optInConfig.TemplateName,
                        Language = optInConfig.Language,
                    },
                });

            var conversationMessage = new ConversationMessage
            {
                Channel = ChannelTypes.WhatsappCloudApi,
                MessageType = "template",
                MessageContent = optInConfig.TemplateMessageContent,
                WhatsappCloudApiReceiver = conversation.WhatsappCloudApiUser,
                IsSentFromSleekflow = true,
                ExtendedMessagePayload = extendedMessagePayload,
                DeliveryType = DeliveryType.ReadMore,
                Metadata = new Dictionary<string, object>()
                {
                    {
                        "targetedConversationMessageId", conversationMessageId.ToString()
                    },
                }
            };

            await SendMessage(conversation, conversationMessage);
        }

        public async Task SendWhatsappCloudApiReadMoreMessage(
            string companyId,
            string conversationId,
            long whatsappCloudApiConfigId,
            long conversationMessageId)
        {
            var conversation = await _appDbContext.Conversations
                .Include(x => x.WhatsappCloudApiUser)
                .FirstOrDefaultAsync(
                    x =>
                        x.Id == conversationId
                        && x.CompanyId == companyId);

            var undeliveredConversationMessage = await _appDbContext.ConversationMessages
                .AsNoTracking()
                .Include(x => x.UploadedFiles)
                .Include(x => x.ExtendedMessagePayload)
                .FirstOrDefaultAsync(
                    x =>
                        x.Id == conversationMessageId
                        && x.Status == MessageStatus.Undelivered);

            var optInConfig = await _appDbContext.ConfigWhatsappCloudApiConfigs
                .Where(
                    x =>
                        x.Id == whatsappCloudApiConfigId
                        && x.IsOptInEnable)
                .Select(x => x.OptInConfig)
                .FirstOrDefaultAsync();

            if (conversation == null
                || optInConfig == null
                || undeliveredConversationMessage == null)
            {
                return;
            }

            if (undeliveredConversationMessage.ExtendedMessagePayload != null)
            {
                undeliveredConversationMessage.ExtendedMessagePayload.Id = Guid.NewGuid().ToString();
                undeliveredConversationMessage.ExtendedMessagePayload.ConversationMessageId = 0;
            }

            var conversationMessage = new ConversationMessage
            {
                Channel = ChannelTypes.WhatsappCloudApi,
                MessageType = undeliveredConversationMessage.MessageType,
                MessageContent = undeliveredConversationMessage.MessageContent,
                WhatsappCloudApiReceiver = undeliveredConversationMessage.WhatsappCloudApiReceiver,
                UploadedFiles = undeliveredConversationMessage.UploadedFiles,
                ExtendedMessagePayload = undeliveredConversationMessage.ExtendedMessagePayload,
                IsSentFromSleekflow = true,
                DeliveryType = DeliveryType.AutomatedMessage,
            };

            if (conversationMessage.UploadedFiles is { Count: > 0 })
            {
                foreach (var conversationMessageUploadedFile in conversationMessage.UploadedFiles)
                {
                    conversationMessageUploadedFile.Id = 0;
                    conversationMessageUploadedFile.ConversationMessageId = conversationMessage.Id;
                    conversationMessageUploadedFile.FileId = Guid.NewGuid().ToString();
                }
            }

            await SendMessage(conversation, conversationMessage);
        }

        private async Task<ConversationMessage> GetConversationMessage(
            Conversation conversation,
            ConversationMessage conversationMessage)
        {
            conversationMessage.ConversationId = conversation.Id;
            conversationMessage.CompanyId = conversation.CompanyId;

            // Bug Fix: DEVS-12501: Fixed a NullReferenceException caused by a Conversation with a null UserProfile.
            conversation.UserProfile ??= await HandleMissingUserProfile(conversation);

            // Add Quote message to QuotedMsgBody
            try
            {
                if (!string.IsNullOrEmpty(conversationMessage.QuotedMsgId)
                    && string.IsNullOrEmpty(conversationMessage.QuotedMsgBody))
                {
                    conversationMessage.QuotedMsgBody = await _appDbContext.ConversationMessages
                        .Where(x => x.MessageUniqueID == conversationMessage.QuotedMsgId)
                        .Select(x => x.MessageContent)
                        .FirstOrDefaultAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex, "Add quote message body error for conversation {ConversationId}",
                    conversation?.Id);
            }

            switch (conversationMessage.Channel)
            {
                case "naive":
                    var senderDevice = await _appDbContext.UserUserDevices
                        .Where(x => x.DeviceUUID == conversationMessage.SenderDeviceUUID)
                        .ToListAsync();
                    var receiverDevice = await _appDbContext.UserUserDevices
                        .Where(x => x.DeviceUUID == conversationMessage.ReceiverDeviceUUID)
                        .ToListAsync();

                    var sender = senderDevice.FirstOrDefault();
                    var receiver = receiverDevice.FirstOrDefault();

                    sender.CompanyId = conversation.CompanyId;
                    sender.CompanyId = conversation.CompanyId;

                    conversationMessage.SenderDevice = senderDevice.FirstOrDefault();
                    conversationMessage.ReceiverDevice = receiverDevice.FirstOrDefault();

                    break;
                case ChannelTypes.Facebook:
                    if (conversationMessage.facebookSender == null)
                    {
                        var facebookConfig = await _appDbContext.ConfigFacebookConfigs
                            .FirstOrDefaultAsync(x => x.PageId == conversationMessage.facebookReceiver.pageId);
                        conversationMessage.facebookSender = await _appDbContext.SenderFacebookSenders
                            .FirstOrDefaultAsync(x => x.FacebookId == conversationMessage.facebookReceiver.pageId);

                        if (conversationMessage.facebookSender == null)
                        {
                            conversationMessage.facebookSender = new FacebookSender
                            {
                                FacebookId = conversationMessage.facebookReceiver.pageId,
                                pageId = conversationMessage.facebookReceiver.pageId,
                                name = facebookConfig.PageName
                            };
                        }
                    }

                    if (conversation.UserProfile.FirstName == "Anonymous"
                        || string.IsNullOrEmpty(conversation.UserProfile.FirstName))
                    {
                        if (!string.IsNullOrEmpty(conversation.facebookUser?.name))
                        {
                            conversation.UserProfile.FirstName = conversation.facebookUser.name;
                        }

                        if (!string.IsNullOrEmpty(conversation.facebookUser?.first_name))
                        {
                            conversation.UserProfile.FirstName = conversation.facebookUser?.first_name;
                            conversation.UserProfile.LastName = conversation.facebookUser?.last_name;
                        }
                    }

                    conversation.facebookUser.CompanyId = conversation.CompanyId;


                    // conversationMessage.facebookSender.pageId = conversation.Company.FacebookConfigs.PageId;
                    // conversationMessage.facebookReceiver.pageId = conversation.Company.FacebookConfigs.PageId;
                    break;
                case ChannelTypes.Instagram:
                    if (conversationMessage.InstagramSender == null)
                    {
                        var instagramConfig = await _appDbContext.ConfigInstagramConfigs
                            .FirstOrDefaultAsync(
                                x => x.InstagramPageId == conversationMessage.InstagramReceiver.InstagramPageId);

                        conversationMessage.InstagramSender = await _appDbContext.SenderInstagramSenders
                            .FirstOrDefaultAsync(
                                x => x.InstagramId == conversationMessage.InstagramReceiver.InstagramPageId);

                        if (conversationMessage.InstagramSender == null)
                        {
                            conversationMessage.InstagramSender = new InstagramSender
                            {
                                InstagramId = conversationMessage.InstagramReceiver.InstagramId,
                                InstagramPageId = conversationMessage.InstagramReceiver.InstagramPageId,
                                PageId = conversationMessage.InstagramReceiver.PageId,
                                Name = instagramConfig.PageName
                            };
                        }
                    }

                    if (conversation.UserProfile.FirstName == "Anonymous"
                        || string.IsNullOrEmpty(conversation.UserProfile.FirstName))
                    {
                        if (!string.IsNullOrEmpty(conversation.InstagramUser?.Username))
                        {
                            conversation.UserProfile.FirstName = conversation.InstagramUser?.Username;
                        }

                        if (!string.IsNullOrEmpty(conversation.InstagramUser?.Name))
                        {
                            conversation.UserProfile.FirstName = conversation.InstagramUser?.Name;
                        }
                    }

                    conversation.InstagramUser.CompanyId = conversation.CompanyId;

                    break;
                case ChannelTypes.WhatsappTwilio:
                    if (!string.IsNullOrEmpty(conversation.WhatsappUser.name)
                        && (string.IsNullOrEmpty(conversation.UserProfile.FirstName)
                            || conversation.UserProfile.FirstName == "Anonymous"
                            || conversation.UserProfile.FirstName == conversation.WhatsappUser?.phone_number))
                    {
                        conversation.UserProfile.FirstName = conversation.WhatsappUser.name;
                    }

                    var phoneNumberUtil = PhoneNumbers.PhoneNumberUtil.GetInstance();

                    try
                    {
                        var phoneNumber = phoneNumberUtil.Parse(
                            conversation.UserProfile.FirstName
                                .Replace(" ", string.Empty)
                                .ToLower(),
                            null);

                        if (phoneNumber != null
                            && phoneNumber.HasCountryCode)
                        {
                            conversation.UserProfile.FirstName = conversation.WhatsappUser.name;
                        }
                    }
                    catch (Exception)
                    {
                        try
                        {
                            var sanitizedFirstName = conversation.UserProfile.FirstName
                                .Replace("@c.us", string.Empty)
                                .Replace("@g.us", string.Empty)
                                .TrimStart()
                                .TrimEnd();

                            var hasNonNumericCharacters = Regex.Matches(sanitizedFirstName, "[^\\d\\+\\-\\s]+").Any();

                            var phoneNumber = phoneNumberUtil.Parse($"+{sanitizedFirstName}", null);

                            if (!hasNonNumericCharacters &&
                                phoneNumber != null &&
                                phoneNumber.HasCountryCode)
                            {
                                conversation.UserProfile.FirstName = conversation.WhatsappUser.name;
                            }
                        }
                        catch (Exception ex)
                        {
                            // noop, user profile firstname should remain as it is
                            _logger.LogInformation(
                                ex,
                                "User profile first name {FirstName} is not replaced with whatsapp name {WhatsappName}",
                                conversation.UserProfile.FirstName,
                                conversation.WhatsappUser.name);
                        }
                    }

                    if (string.IsNullOrEmpty(conversation.UserProfile.FirstName))
                    {
                        conversation.UserProfile.FirstName = conversation.WhatsappUser.phone_number;
                    }

                    conversation.WhatsappUser.CompanyId = conversation.CompanyId;

                    break;
                case "email":
                    if (conversation.UserProfile.FirstName == "Anonymous")
                    {
                        if (conversation.EmailAddress != null)
                        {
                            conversation.UserProfile.FirstName = conversation.EmailAddress.Name;
                        }
                    }

                    conversation.EmailAddress.CompanyId = conversation.CompanyId;

                    break;
                case ChannelTypes.Wechat:
                    if (conversation.UserProfile.FirstName == "Anonymous")
                    {
                        if (conversation.WeChatUser != null)
                        {
                            conversation.UserProfile.FirstName = conversation.WeChatUser.nickname;
                        }
                    }

                    conversation.WeChatUser.CompanyId = conversation.CompanyId;

                    break;
                case ChannelTypes.LiveChat:
                    if (conversation.UserProfile.FirstName == "Anonymous")
                    {
                        if (conversation.WebClient != null)
                        {
                            conversation.UserProfile.FirstName = conversation.WebClient.Name;
                        }
                    }

                    conversation.WebClient.CompanyId = conversation.CompanyId;

                    break;
                case ChannelTypes.LiveChatV2:
                    // [LiveChatV2] TODO: Add the naming handling
                    if (conversation.UserProfile.FirstName == "Anonymous")
                    {
                        if (conversationMessage.WebClientSender != null)
                        {
                            conversation.UserProfile.FirstName = conversationMessage.WebClientSender.Name;
                        }
                        else if (conversationMessage.WebClientReceiver != null)
                        {
                            conversation.UserProfile.FirstName = conversationMessage.WebClientReceiver.Name;
                        }
                    }

                    break;
                case ChannelTypes.Line:
                    if (conversation.UserProfile.FirstName == "Anonymous")
                    {
                        if (conversation.LineUser != null)
                        {
                            conversation.UserProfile.FirstName = conversation.LineUser.displayName;
                        }
                    }

                    conversation.LineUser.CompanyId = conversation.CompanyId;

                    break;
                case ChannelTypes.Viber:
                    if (conversation.UserProfile.FirstName == "Anonymous")
                    {
                        if (conversation.ViberUser != null)
                        {
                            conversation.UserProfile.FirstName = conversation.ViberUser.DisplayName;
                        }
                    }

                    conversation.ViberUser.CompanyId = conversation.CompanyId;

                    break;
                case ChannelTypes.Whatsapp360Dialog:
                    if (string.IsNullOrEmpty(conversation.UserProfile.FirstName)
                        || conversation.UserProfile.FirstName == "Anonymous"
                        || conversation.UserProfile.FirstName == conversation.WhatsApp360DialogUser.WhatsAppId)
                    {
                        if (conversation.WhatsApp360DialogUser != null)
                        {
                            if (!string.IsNullOrEmpty(conversation.WhatsApp360DialogUser.Name))
                            {
                                conversation.UserProfile.FirstName = conversation.WhatsApp360DialogUser.Name;
                            }
                            else if (!string.IsNullOrEmpty(conversationMessage.MessageUniqueID) &&
                                     !string.IsNullOrEmpty(conversation.WhatsApp360DialogUser.PhoneNumber))
                            {
                                conversation.UserProfile.FirstName = conversation.WhatsApp360DialogUser.WhatsAppId;
                            }
                        }
                    }

                    conversation.WhatsApp360DialogUser.CompanyId = conversation.CompanyId;

                    break;
                case ChannelTypes.WhatsappCloudApi:
                    if (conversation.UserProfile.FirstName == "Anonymous"
                        || string.IsNullOrEmpty(conversation.UserProfile.FirstName)
                        || conversation.UserProfile.FirstName == conversation.WhatsappCloudApiUser.WhatsappId)
                    {
                        if (conversation.WhatsappCloudApiUser != null)
                        {
                            if (!string.IsNullOrEmpty(conversation.WhatsappCloudApiUser.WhatsappUserDisplayName))
                            {
                                conversation.UserProfile.FirstName =
                                    conversation.WhatsappCloudApiUser.WhatsappUserDisplayName;
                            }
                            else if (!string.IsNullOrEmpty(conversationMessage.MessageUniqueID) &&
                                     !string.IsNullOrEmpty(conversation.WhatsappCloudApiUser.WhatsappId))
                            {
                                conversation.UserProfile.FirstName = conversation.WhatsappCloudApiUser.WhatsappId;
                            }
                        }
                    }

                    conversation.WhatsappCloudApiUser.CompanyId = conversation.CompanyId;

                    break;
                case ChannelTypes.Telegram:
                    if (conversation.UserProfile.FirstName == "Anonymous")
                    {
                        if (conversation.TelegramUser != null)
                        {
                            conversation.UserProfile.FirstName = conversation.TelegramUser.FirstName;
                            conversation.UserProfile.LastName = conversation.TelegramUser.LastName;
                        }
                    }

                    conversation.TelegramUser.CompanyId = conversation.CompanyId;

                    break;
                case ChannelTypes.Sms:
                    if (conversation.UserProfile.FirstName == "Anonymous")
                    {
                        if (conversation.SMSUser != null)
                        {
                            conversation.UserProfile.FirstName = conversation.SMSUser.phone_number;
                        }
                    }

                    conversation.SMSUser.CompanyId = conversation.CompanyId;

                    break;
            }

            // Append ChannelIdentityId if not existed
            if (string.IsNullOrEmpty(conversationMessage.ChannelIdentityId))
            {
                switch (conversationMessage.Channel)
                {
                    case ChannelTypes.WhatsappTwilio:
                    case ChannelTypes.Whatsapp360Dialog:
                    case ChannelTypes.WhatsappCloudApi:
                    case ChannelTypes.Facebook:
                    case ChannelTypes.Instagram:
                    case ChannelTypes.Sms:
                    case ChannelTypes.Wechat:
                    case ChannelTypes.Line:
                    case ChannelTypes.Viber:
                    case ChannelTypes.Telegram:
                    case ChannelTypes.LiveChatV2:
                        // [LiveChatV2]
                        conversationMessage.ChannelIdentityId = conversationMessage.GetIMessagingChannelUser()?.ChannelIdentityId;
                        break;
                    default:
                        break;
                }
            }

            DateTimeOffset dateTimeOffset = conversationMessage.CreatedAt;
            conversationMessage.Timestamp = dateTimeOffset.ToUnixTimeSeconds();
            conversationMessage.FrondendTimestamp = dateTimeOffset.ToUnixTimeMilliseconds();

            return conversationMessage;
        }

        private async Task ConversationLastActivity(
            Conversation conversation,
            ConversationMessage conversationMessage,
            bool trigerAssignment = true)
        {
            var isRbacEnabled = await _rbacService.IsRbacEnabled(conversation.CompanyId);

            if (isRbacEnabled)
            {
                await RbacConversationLastActivity(conversation, conversationMessage, trigerAssignment);
            }

            await DefaultConversationLastActivity(conversation, conversationMessage, trigerAssignment);
        }

        public async Task DefaultConversationLastActivity(
            Conversation conversation,
            ConversationMessage conversationMessage,
            bool trigerAssignment)
        {
            try
            {
                if (!trigerAssignment
                    && conversationMessage.Channel == ChannelTypes.LiveChat)
                {
                    return;
                }

                if (conversation.LastMessageChannel != conversationMessage.Channel)
                {
                    conversation.LastMessageChannel = conversationMessage.Channel;

                    BackgroundJob.Enqueue<IUserProfileService>(
                        x => x.SetFieldValueByFieldNameSafe(
                            conversation.UserProfile.Id,
                            "LastChannel",
                            conversationMessage.Channel,
                            true));
                }

                conversation.LastChannelIdentityId = conversationMessage.ChannelIdentityId;

                // BackgroundJob.Enqueue<IUserProfileService>(x => x.SetFieldValueByFieldNameSafe(conversation.UserProfile.Id, "LastChannel", conversationMessage.Channel, true));
                var isCSReply = await IsCSReply(conversation, conversationMessage);

                if (isCSReply
                    || conversationMessage.IsSentFromSleekflow)
                {
                    if (!conversation.UserProfile.LastContact.HasValue
                        || conversation.UserProfile.LastContact < conversationMessage.CreatedAt)
                    {
                        conversation.UserProfile.LastContact = conversationMessage.CreatedAt;
                        await _userProfileService.SetFieldValueByFieldNameSafe(
                            conversation.UserProfile,
                            "LastContact",
                            conversationMessage.CreatedAt.ToString("o"),
                            trigerAssignment);
                    }
                }

                // BackgroundJob.Enqueue<IUserProfileService>(x => x.SetFieldValueByFieldNameSafe(conversation.UserProfile.Id, "LastContact", conversation.UpdatedTime.ToString("o"), true));

                // if system message for web, don't log the last contact
                if (conversationMessage.Channel == ChannelTypes.LiveChat
                    && conversationMessage.Sender == null
                    && conversationMessage.WebClientSender == null)
                {
                    return;
                }

                if (isCSReply
                    && conversationMessage.DeliveryType == DeliveryType.Normal)
                {
                    // Add collaborator when reply
                    if (!string.IsNullOrEmpty(conversationMessage.SenderId)
                        && conversation.Assignee != null
                        && conversation.Assignee.IdentityId != conversationMessage.SenderId)
                    {
                        var sender = await _appDbContext.UserRoleStaffs
                            .FirstOrDefaultAsync(
                                x =>
                                    x.CompanyId == conversation.CompanyId
                                    && x.IdentityId == conversationMessage.SenderId);

                        var rolePermission = await _appDbContext.CompanyRolePermissions
                            .FirstOrDefaultAsync(
                                x =>
                                    x.CompanyId == conversation.CompanyId
                                    && x.StaffUserRole == sender.RoleType);

                        if (rolePermission != null
                            && rolePermission.Permission.AddAsCollaboratorWhenReply)
                        {
                            conversation.AdditionalAssignees = await _appDbContext.ConversationAdditionalAssignees
                                .Where(x => x.ConversationId == conversation.Id)
                                .ToListAsync();

                            if (conversation.AdditionalAssignees.All(x => x.AssigneeId != sender.Id))
                            {
                                conversation = await _conversationAssigneeService.AddAdditionalAssignees(
                                    conversation,
                                    new List<long>
                                    {
                                        sender.Id
                                    });
                            }
                        }
                    }

                    if (!conversation.AssigneeId.HasValue && trigerAssignment)
                    {
                        _logger.LogInformation(
                            "ConversationId: {ConversationId} didn't have assignee",
                            conversation?.Id);
                        var userProfile = await _appDbContext.UserProfiles
                            .FirstOrDefaultAsync(x => x.Id == conversation.UserProfileId);

                        var contactOwner = await _userProfileService.GetCustomFieldByFieldName(
                            userProfile,
                            "ContactOwner");

                        if (contactOwner != null
                            && !string.IsNullOrEmpty(contactOwner.Value))
                        {
                            conversation.Assignee = await _appDbContext.UserRoleStaffs
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.CompanyId == userProfile.CompanyId
                                        && x.IdentityId == contactOwner.Value);

                            await ChangeConversationAssignee(conversation, conversation.Assignee, trigerAssignment);
                        }
                        else if (conversationMessage.Sender != null)
                        {
                            var staffRepliedLock = await _lockService.AcquireLockAsync(
                                $"staff-replied-as-owner:{conversation.Id}",
                                TimeSpan.FromSeconds(10));

                            if (staffRepliedLock != null)
                            {
                                var assignee = await _appDbContext.UserRoleStaffs
                                    .FirstOrDefaultAsync(x => x.IdentityId == conversationMessage.SenderId);

                                if (assignee.Id != 1)
                                {
                                    conversation.Assignee = assignee;

                                    if (conversation.Assignee != null)
                                    {
                                        await ChangeConversationAssignee(
                                            conversation,
                                            conversation.Assignee,
                                            trigerAssignment);
                                    }

                                    if (!conversation.AssignedTeamId.HasValue)
                                    {
                                        var assignedTeam = await _appDbContext.CompanyStaffTeams
                                            .FirstOrDefaultAsync(
                                                x => x.Members.Any(
                                                    y => y.Staff.IdentityId == conversationMessage.SenderId));

                                        if (assignedTeam != null)
                                        {
                                            await ChangeConversationAssignedTeam(
                                                conversation,
                                                assignedTeam,
                                                trigerAssignment);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                if (!isCSReply)
                {
                    try
                    {
                        if (!conversationMessage.IsSentFromSleekflow)
                        {
                            if (!conversation.UserProfile.LastContactFromCustomers.HasValue
                                || conversation.UserProfile.LastContactFromCustomers < conversationMessage.CreatedAt)
                            {
                                conversation.UserProfile.LastContactFromCustomers = conversationMessage.CreatedAt;

                                await _userProfileService.SetFieldValueByFieldNameSafe(
                                    conversation.UserProfile,
                                    "LastContactFromCustomers",
                                    conversationMessage.CreatedAt.ToString("o"),
                                    true);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[{MethodName}] Update LastContactFromCustomers error for user profile {UserProfileId}: {ExceptionMessage}",
                            nameof(ConversationLastActivity),
                            conversation.UserProfileId,
                            ex.Message);


                        if (!conversation.UserProfile.LastContactFromCustomers.HasValue
                            || conversation.UserProfile.LastContactFromCustomers < conversationMessage.CreatedAt)
                        {
                            conversation.UserProfile.LastContactFromCustomers = conversationMessage.CreatedAt;

                            await _userProfileService.SetFieldValueByFieldNameSafe(
                                conversation.UserProfile,
                                "LastContactFromCustomers",
                                conversationMessage.CreatedAt.ToString("o"),
                                true);
                        }
                    }
                }

                if (conversation.ActiveStatus == ActiveStatus.Inactive)
                {
                    conversation.ActiveStatus = ActiveStatus.Active;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] error for conversation {ConversationId}: {ExceptionString}",
                    nameof(ConversationLastActivity),
                    conversation?.Id,
                    ex.ToString());
            }
        }

        private ConversationMessage PrepareReplyMessage(
            Conversation conversation,
            ConversationMessage conversationMessage,
            string replyMessage)
        {
            var webcomeMessage = new ConversationMessage
            {
                ConversationId = conversation.Id,
                Channel = conversationMessage.Channel,
                MessageType = "text",
                MessageContent = replyMessage,
                facebookReceiver = conversation.facebookUser,
                whatsappReceiver = conversation.WhatsappUser,
                WebClientReceiver = conversation.WebClient,
                WeChatReceiver = conversation.WeChatUser,
                LineReceiver = conversation.LineUser,
                SMSReceiver = conversation.SMSUser,
                ViberReceiver = conversation.ViberUser,
                TelegramReceiver = conversation.TelegramUser,
                InstagramReceiver = conversation.InstagramUser,
                Whatsapp360DialogReceiver = conversation.WhatsApp360DialogUser,
                WhatsappCloudApiReceiver = conversation.WhatsappCloudApiUser,
                DeliveryType = DeliveryType.AutomatedMessage,
                IsSentFromSleekflow = true
            };

            return webcomeMessage;
        }

        public class Params
        {
            public string Name { get; set; }
        }

        public async Task<List<string>> FormatParamsWithPaymentUrl(
            UserProfile userProfile,
            List<string> template_params,
            string paymentUrl = null)
        {
            List<Params> formatedParams = new List<Params>();

            foreach (var myparams in template_params)
            {
                formatedParams.Add(
                    new Params
                    {
                        Name = myparams
                    });
            }

            // Broadcast payment link
            if (formatedParams
                    .Any(x => x.Name == "payment_url")
                && paymentUrl != null)
            {
                formatedParams
                    .Where(x => x.Name == "payment_url")
                    .ToList()
                    .ForEach(x => x.Name = paymentUrl);

                template_params = formatedParams
                    .Select(x => x.Name)
                    .ToList();
            }

            return await FormatParams(userProfile, template_params);
        }

        public async Task<List<string>> FormatParams(UserProfile userProfile, List<string> templateParams)
        {
            List<Params> formatedParams = new List<Params>();

            foreach (var myparams in templateParams)
            {
                formatedParams.Add(
                    new Params
                    {
                        Name = myparams
                    });
            }

            var dbContext = _dbContextService.GetDbContext();

            formatedParams
                .Where(x => x.Name == "@firstname")
                .ToList()
                .ForEach(x => x.Name = userProfile.FirstName);

            formatedParams
                .Where(x => x.Name == "@lastname")
                .ToList()
                .ForEach(x => x.Name = userProfile.LastName);

            formatedParams
                .Where(x => string.IsNullOrEmpty(x.Name))
                .ToList()
                .ForEach(x => x.Name = string.Empty);

            if (formatedParams.Any(x => x.Name == "@contactownerphonenumber"))
            {
                var staff = await dbContext.Conversations
                    .Where(x => x.UserProfileId == userProfile.Id)
                    .Select(x => x.Assignee)
                    .FirstOrDefaultAsync();

                var contactOwnerPhoneNumber = await _companyService.GetContactOwnerPhoneNumber(staff);


                formatedParams
                    .Where(x => x.Name == "@contactownerphonenumber")
                    .ToList()
                    .ForEach(x => x.Name = contactOwnerPhoneNumber);
            }

            var fields = formatedParams
                .Where(
                    x =>
                    x.Name != null
                    && x.Name.Contains("@"))
                .ToList();

            foreach (var fieldName in fields)
            {
                try
                {
                    var targetField = await (
                            from _field in dbContext.CompanyCustomUserProfileFields
                            where _field.FieldName.ToLower() == fieldName.Name.Replace("@", string.Empty).ToLower() &&
                                  _field.CompanyId == userProfile.CompanyId
                            select _field)
                        .FirstOrDefaultAsync();

                    var targetFieldValue = await (from _userProfile in dbContext.UserProfiles
                                                  join userProfileCustomField in dbContext.UserProfileCustomFields on _userProfile.Id equals
                                                      userProfileCustomField.UserProfileId
                                                  where userProfileCustomField.CompanyDefinedFieldId == targetField.Id &&
                                                        userProfileCustomField.UserProfileId == userProfile.Id &&
                                                        _userProfile.CompanyId == userProfile.CompanyId
                                                  select new
                                                  {
                                                      _userProfile,
                                                      userProfileCustomField
                                                  })
                        .FirstOrDefaultAsync();

                    if (targetFieldValue != null)
                    {
                        try
                        {
                            switch (targetField.Type)
                            {
                                case FieldDataType.TravisUser:
                                    var contactOwner = await dbContext.UserRoleStaffs
                                        .Include(x => x.Identity)
                                        .FirstOrDefaultAsync(
                                            x =>
                                                x.CompanyId == userProfile.CompanyId
                                                && x.IdentityId == targetFieldValue.userProfileCustomField.Value);

                                    var contactOwnerName = (!string.IsNullOrEmpty(contactOwner.Identity.FirstName))
                                        ? contactOwner.Identity.FirstName
                                        : contactOwner.Identity.DisplayName;

                                    formatedParams
                                        .Where(x => x.Name == fieldName.Name)
                                        .ToList()
                                        .ForEach(x => x.Name = contactOwnerName);

                                    break;
                                case FieldDataType.DateTime:
                                case FieldDataType.Date:
                                    var timeZoneId = await dbContext.CompanyCompanies
                                        .Where(x => x.Id == userProfile.CompanyId)
                                        .Select(x => x.TimeZoneInfoId)
                                        .FirstOrDefaultAsync();

                                    var timezoneInfo = TimeZoneHelper.GetTimeZoneById(timeZoneId);

                                    var datetime = Convert.ToDateTime(targetFieldValue.userProfileCustomField.Value)
                                        .ToUniversalTime();

                                    if (timezoneInfo != null)
                                    {
                                        datetime = datetime.AddHours(timezoneInfo.BaseUtcOffsetInHour);
                                    }

                                    if (targetField.Type == FieldDataType.DateTime)
                                    {
                                        formatedParams
                                            .Where(x => x.Name == fieldName.Name)
                                            .ToList()
                                            .ForEach(
                                            x => x.Name = datetime.ToString("dd MMM yyyy HH:mm"));
                                    }
                                    else
                                    {
                                        formatedParams
                                            .Where(x => x.Name == fieldName.Name)
                                            .ToList()
                                            .ForEach(x => x.Name = datetime.ToString("dd MMM yyyy"));
                                    }

                                    break;
                                case FieldDataType.ContactOwnerField:
                                    var converstion = await dbContext.Conversations
                                        .FirstOrDefaultAsync(x => x.UserProfileId == userProfile.Id);


                                    if (converstion == null
                                        || !converstion.AssigneeId.HasValue)
                                    {
                                        break;
                                    }

                                    var _contactOwner = await dbContext.UserRoleStaffs
                                        .FirstOrDefaultAsync(
                                            x =>
                                                x.CompanyId == userProfile.CompanyId &&
                                                x.Id == converstion.AssigneeId);

                                    switch (targetField.FieldName)
                                    {
                                        case "PositionOfContactOwner":
                                            formatedParams
                                                .Where(x => x.Name == fieldName.Name)
                                                .ToList()
                                                .ForEach(x => x.Name = _contactOwner.Position);

                                            break;
                                        case "MessageOfContactOwner":
                                            formatedParams
                                                .Where(x => x.Name == fieldName.Name)
                                                .ToList()
                                                .ForEach(x => x.Name = _contactOwner.Message);

                                            break;
                                    }

                                    break;

                                case FieldDataType.MultiLineText:
                                    var targetMultiLineTextValue =
                                        TrimTextValue(targetFieldValue.userProfileCustomField.Value);

                                    formatedParams
                                        .Where(x => x.Name == fieldName.Name)
                                        .ToList()
                                        .ForEach(x => x.Name = targetMultiLineTextValue);
                                    break;

                                case FieldDataType.SingleLineText:
                                    var targetSingleLineTextValue =
                                        TrimTextValue(targetFieldValue.userProfileCustomField.Value);

                                    formatedParams
                                        .Where(x => x.Name == fieldName.Name)
                                        .ToList()
                                        .ForEach(x => x.Name = targetSingleLineTextValue);
                                    break;
                                default:
                                    if (fieldName.Name.ToLower() == "@assignedteam")
                                    {
                                        var teamId = Convert.ToInt64(targetFieldValue.userProfileCustomField.Value);

                                        var teams = await dbContext.CompanyStaffTeams
                                            .FirstOrDefaultAsync(
                                                x =>
                                                    x.CompanyId == userProfile.CompanyId
                                                    && x.Id == teamId);


                                        formatedParams
                                            .Where(x => x.Name == fieldName.Name)
                                            .ToList()
                                            .ForEach(x => x.Name = teams.TeamName);
                                    }
                                    else
                                    {
                                        formatedParams
                                            .Where(x => x.Name == fieldName.Name)
                                            .ToList()
                                            .ForEach(x => x.Name = targetFieldValue.userProfileCustomField.Value?
                                                .Replace(Environment.NewLine, " ")
                                                .Replace("\n", " "));
                                    }

                                    break;
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "[{MethodName}] {TargetFieldName} Convert Error: {ExceptionString}",
                                nameof(FormatParams),
                                targetField.FieldName,
                                ex.ToString());


                            formatedParams
                                .Where(x => x.Name == fieldName.Name)
                                .ToList()
                                .ForEach(x => x.Name = targetFieldValue.userProfileCustomField.Value?
                                    .Replace(Environment.NewLine, " ")
                                    .Replace("\n", " "));
                        }
                    }
                    else
                    {
                        switch (targetField.Type)
                        {
                            case FieldDataType.TravisUser:
                                var _converstion = await dbContext.Conversations
                                    .Include(x => x.Assignee.Identity)
                                    .FirstOrDefaultAsync(x => x.UserProfileId == userProfile.Id);


                                if (_converstion == null ||
                                    !_converstion.AssigneeId.HasValue)
                                {
                                    formatedParams
                                        .Where(x => x.Name == fieldName.Name)
                                        .ToList()
                                        .ForEach(x => x.Name = string.Empty);

                                    break;
                                }

                                var contactOwnerName = (!string.IsNullOrEmpty(_converstion.Assignee.Identity.FirstName))
                                    ? _converstion.Assignee.Identity.FirstName
                                    : _converstion.Assignee.Identity.DisplayName;

                                formatedParams
                                    .Where(x => x.Name == fieldName.Name)
                                    .ToList()
                                    .ForEach(x => x.Name = contactOwnerName);

                                break;
                            case FieldDataType.ContactOwnerField:
                                var converstion = await dbContext.Conversations
                                    .FirstOrDefaultAsync(x => x.UserProfileId == userProfile.Id);


                                if (converstion == null ||
                                    !converstion.AssigneeId.HasValue)
                                {
                                    break;
                                }

                                var _contactOwner = await dbContext.UserRoleStaffs
                                    .FirstOrDefaultAsync(
                                        x =>
                                            x.CompanyId == userProfile.CompanyId &&
                                            x.Id == converstion.AssigneeId);

                                switch (targetField.FieldName)
                                {
                                    case "PositionOfContactOwner":
                                        formatedParams
                                            .Where(x => x.Name == fieldName.Name)
                                            .ToList()
                                            .ForEach(
                                                x =>
                                                    x.Name = string.IsNullOrEmpty(_contactOwner.Position) ?
                                                        string.Empty :
                                                        _contactOwner.Position);
                                        break;
                                    case "MessageOfContactOwner":
                                    case "UniqueLink":
                                        formatedParams
                                            .Where(x => x.Name == fieldName.Name)
                                            .ToList()
                                            .ForEach(
                                                x =>
                                                    x.Name = string.IsNullOrEmpty(_contactOwner.Message) ?
                                                        string.Empty :
                                                        _contactOwner.Message);
                                        break;
                                }

                                break;
                            default:
                                formatedParams
                                    .Where(x => x.Name == fieldName.Name)
                                    .ToList()
                                    .ForEach(x => x.Name = string.Empty);

                                break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "Company {CompanyId} could not format params for user profile {UserProfileId}. Params: {@Params}",
                        userProfile.CompanyId,
                        userProfile.Id,
                        templateParams);
                }
            }

            foreach (var name in formatedParams)
            {
                if (name.Name == null)
                {
                    name.Name = string.Empty;
                }
            }

            return formatedParams.Select(x => x.Name).ToList();
        }

        /// <summary>
        /// Replace new lines, tabs and spaces to at most 3 consecutive spaces
        ///
        /// Resource: https://developers.facebook.com/docs/whatsapp/message-templates/creation/
        /// Whatsapp template param cannot contain newline or tab characters, and cannot have more than 4 consecutive spaces
        /// </summary>
        /// <param name="value">Value.</param>
        /// <returns>Trimmed value.</returns>
        private static string TrimTextValue(string value)
        {
            return value == null
                ? null
                : Regex.Replace(
                    Regex.Replace(
                        value,
                        @"\r|\n|\t",
                        " "),
                    @"\ {4,}",
                    "   ");
        }

        public async Task RemoveScheduledMessage(string companyId, DeleteMessagesInput input)
        {
            foreach (var messageId in input.MessageIds)
            {
                var scheduledMessage = await _appDbContext.ConversationMessages
                    .Include(x => x.Conversation)
                    .Include(x => x.UploadedFiles)
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == companyId
                            && x.Id == messageId
                            && x.Status == MessageStatus.Scheduled);

                if (scheduledMessage == null)
                {
                    continue;
                }

                scheduledMessage.Status = MessageStatus.Deleted;
                await _appDbContext.SaveChangesAsync();

                await _signalRService.SignalROnMessageStatusChanged(scheduledMessage);

                BackgroundJob.Delete(scheduledMessage.JobId);
            }
        }

        public async Task DeleteMessage(DeleteMessageInput input)
        {
            var message = await _appDbContext.ConversationMessages.Where(
                cm => cm.Id == input.MessageId && cm.CompanyId == input.CompanyId)
                .Include(cm => cm.ExtendedMessagePayload)
                .Include(cm => cm.Whatsapp360DialogExtendedMessagePayload)
                .Include(cm => cm.UploadedFiles)
                .FirstOrDefaultAsync();

            if (message is null)
            {
                throw new MessageNotFoundException(input.CompanyId, input.MessageId);
            }

            if (message.MessageType == "file")
            {
                try
                {
                    if (message.UploadedFiles != null && message.UploadedFiles.Any())
                    {
                        foreach (var file in message.UploadedFiles)
                        {
                            await _azureBlobStorageService.DeleteFromAzureBlob(file.Filename, file.BlobContainer);
                        }
                    }
                }
                catch (Exception e)
                {
                    _logger.LogError(e, "Error deleting file from Azure Blob Storage for message {MessageId}", input.MessageId);
                }
            }

            _appDbContext.Remove(message);

            await _appDbContext.SaveChangesAsync();

            // Update the last message id of the conversation after we deleted the message
            var conversation = await _appDbContext.Conversations.FirstOrDefaultAsync(x => x.Id == message.ConversationId);

            // Check if the message being deleted is the most recent one
            if (conversation.LastMessageId == input.MessageId)
            {
                var recentMessageId = await _appDbContext.ConversationMessages
                    .Where(x => x.CompanyId == input.CompanyId && x.ConversationId == conversation.Id)
                    .OrderByDescending(x => x.Timestamp)
                    .Take(1)
                    .Select(cm => (long?) cm.Id)
                    .FirstOrDefaultAsync();
                if (recentMessageId != null)
                {
                    conversation.LastMessageId = recentMessageId.Value;
                    await _appDbContext.SaveChangesAsync();
                }
            }
        }

        public async Task<IQueryable<Conversation>> GetConversations(
            string companyId,
            Staff staff,
            string status = "all",
            string assignedTo = "all",
            string channels = null,
            DateTime? afterUpdatedAt = null,
            DateTime? afterModifiedAt = null,
            string channelIds = null,
            string tags = null,
            long? teamId = null,
            bool? isTeamUnassigned = null,
            bool? isUnread = null,
            string orderBy = null,
            string version = "1",
            bool? isAssigned = null,
            bool? isCollaborator = null)
        {
            var isRbacEnabled = await _rbacService.IsRbacEnabled(companyId);

            if (isRbacEnabled)
            {
                return await RbacGetConversations(
                   companyId,
                   staff,
                   status,
                   assignedTo,
                   channels,
                   afterUpdatedAt,
                   afterModifiedAt,
                   channelIds,
                   tags,
                   teamId,
                   isTeamUnassigned,
                   isUnread,
                   orderBy,
                   version,
                   isAssigned,
                   isCollaborator);
            }

            return await DefaultGetConversations(
                companyId,
                staff,
                status,
                assignedTo,
                channels,
                afterUpdatedAt,
                afterModifiedAt,
                channelIds,
                tags,
                teamId,
                isTeamUnassigned,
                isUnread,
                orderBy,
                version,
                isAssigned,
                isCollaborator);
        }

        public async Task<IQueryable<Conversation>> DefaultGetConversations(
            string companyId,
            Staff staff,
            string status = "all",
            string assignedTo = "all",
            string channels = null,
            DateTime? afterUpdatedAt = null,
            DateTime? afterModifiedAt = null,
            string channelIds = null,
            string tags = null,
            long? teamId = null,
            bool? isTeamUnassigned = null,
            bool? isUnread = null,
            string orderBy = null,
            string version = "1",
            bool? isAssigned = null,
            bool? isCollaborator = null)
        {
            var channelList = new List<string>();

            if (!string.IsNullOrEmpty(channels))
            {
                channelList = channels.Split(",").ToList();
            }

            var channelIdList = new List<string>();

            if (!string.IsNullOrEmpty(channelIds))
            {
                channelIdList = channelIds.Split(",").ToList();
            }

            var tagsList = new List<string>();

            if (!string.IsNullOrEmpty(tags))
            {
                tagsList = tags.Split(",").ToList();
            }

            var to = assignedTo;

            var dbContext = _dbContextService.GetDbContext();

            var conversations = dbContext.Conversations
                .Where(
                    x =>
                        x.CompanyId == companyId
                        && x.ActiveStatus == ActiveStatus.Active)
                .WhereIf(
                    status != "all" && status != "scheduled",
                    conversation => conversation.Status == status.ToLower())
                .WhereIf(
                    status == "scheduled",
                    conversation => conversation.ChatHistory.Any(x => x.Status == MessageStatus.Scheduled))
                .WhereIf(afterUpdatedAt.HasValue, conversation => conversation.UpdatedTime > afterUpdatedAt)
                .WhereIf(afterModifiedAt.HasValue, conversation => conversation.ModifiedAt > afterModifiedAt)
                .WhereIf(isAssigned.HasValue && isAssigned.Value, conversation => conversation.AssigneeId.HasValue)
                .WhereIf(isAssigned.HasValue && !isAssigned.Value, conversation => !conversation.AssigneeId.HasValue)
                .WhereIf(
                    isCollaborator.HasValue && isCollaborator.Value,
                    conversation =>
                        conversation.AdditionalAssignees.Any(
                            x => x.CompanyId == companyId && x.Assignee.IdentityId == to));

            var mentionQueryable = _mentionQueryableResolver.GetMentionQueryable(
                dbContext,
                staff.Id,
                staff.CompanyId);

            if (tagsList.Count > 0)
            {
                var hashtagsList = await dbContext.CompanyDefinedHashtags
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && (tagsList.Contains(x.Hashtag) || tagsList.Contains(x.Id)))
                    .Select(x => x.Id)
                    .ToListAsync();

                conversations = conversations.Where(
                    conversation => conversation.conversationHashtags
                        .Any(
                            x => x.CompanyId == companyId
                                 && hashtagsList.Contains(x.HashtagId)));
            }

            if (isUnread.HasValue)
            {
                var personalUnreadConversationIds = dbContext.ConversationUnreadRecords
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && x.StaffId == staff.Id
                            && x.NotificationDeliveryStatus != NotificationDeliveryStatus.Read)
                    .Select(x => x.ConversationId);

                if (isUnread.Value)
                {
                    conversations = from conversation in conversations
                                    where conversation.UnreadMessageCount > 0
                                          || personalUnreadConversationIds.Contains(conversation.Id)
                                    select conversation;
                }
                else
                {
                    conversations = from conversation in conversations
                                    where conversation.UnreadMessageCount <= 0
                                          || personalUnreadConversationIds.Contains(conversation.Id)
                                    select conversation;
                }
            }

            switch (assignedTo)
            {
                case "all":
                    switch (staff.RoleType)
                    {
                        case StaffUserRole.Admin:
                        case StaffUserRole.DemoAdmin:
                            break;
                        case StaffUserRole.TeamAdmin:
                        case StaffUserRole.Staff:
                            var managedTeam = await dbContext.CompanyStaffTeams
                                .Where(x => x.Members.Any(m => m.StaffId == staff.Id))
                                .Select(x => x.Id)
                                .ToListAsync();

                            if (staff.RoleType == StaffUserRole.TeamAdmin)
                            {
                                var staffAccessControlAggregate =
                                    await _accessControlAggregationService.GetStaffAccessControlAggregateAsync(staff);
                                var teamAdminSpec = new TeamAdminConversationSpecification(staffAccessControlAggregate)
                                    .ToExpression();
                                var predicate = PredicateBuilder.New<Conversation>();
                                predicate = predicate.Or(teamAdminSpec);
                                predicate = predicate.Or(
                                    c => mentionQueryable.Select(m => m.ConversationId).Contains(c.Id));
                                conversations = conversations.Where(predicate);
                            }
                            else
                            {
                                var isShowDefaultChannelMessagesOnly = false;

                                var staffPermission = await _appDbContext.CompanyRolePermissions
                                    .FirstOrDefaultAsync(
                                        x =>
                                            x.CompanyId == companyId
                                            && x.StaffUserRole == staff.RoleType);
                                if (staffPermission != null)
                                {
                                    isShowDefaultChannelMessagesOnly =
                                        staffPermission.Permission.IsShowDefaultChannelMessagesOnly;
                                }

                                if (isShowDefaultChannelMessagesOnly)
                                {
                                    conversations = conversations.Where(
                                        conversation =>
                                            conversation.AssigneeId == staff.Id //Assigned to the staff
                                            || (conversation.AssignedTeamId.HasValue
                                                && ((managedTeam.Contains(conversation.AssignedTeamId.Value)
                                                     && !conversation.AssigneeId.HasValue)
                                                    || conversation.AdditionalAssignees
                                                        .Any(
                                                            x => x.CompanyId == companyId
                                                                 && x.AssigneeId == staff.Id)))
                                    );
                                }
                                else
                                {
                                    conversations = conversations.Where(
                                        conversation =>
                                            conversation.AssigneeId == staff.Id //Assigned to the staff
                                            || (!conversation.AssignedTeamId.HasValue && !conversation.AssigneeId.HasValue) //Assigned to neither team nor staff
                                            || (conversation.AssignedTeamId.HasValue
                                                && managedTeam.Contains(conversation.AssignedTeamId.Value)
                                                && !conversation.AssigneeId.HasValue) //Assigned to the team, not to others
                                            || conversation.AdditionalAssignees
                                                .Any(
                                                    x => x.CompanyId == companyId
                                                         && x.AssigneeId == staff.Id) //Assigned to collaborator
                                    );
                                }
                            }

                            break;
                    }

                    break;
                case "unassigned":
                    conversations = from conversation in conversations
                                    where conversation.AssigneeId == null
                                    select conversation;

                    switch (staff.RoleType)
                    {
                        case StaffUserRole.TeamAdmin:
                        case StaffUserRole.Staff:
                            var managedTeam = await dbContext.CompanyStaffTeams
                                .Where(x => x.Members.Any(m => m.StaffId == staff.Id))
                                .Select(x => x.Id)
                                .ToListAsync();

                            if (managedTeam.Count == 0)
                            {
                                break;
                            }

                            var isShowDefaultChannelMessagesOnly = false;

                            var staffPermission = await dbContext.CompanyRolePermissions
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.CompanyId == companyId
                                        && x.StaffUserRole == staff.RoleType);
                            if (staffPermission != null)
                            {
                                isShowDefaultChannelMessagesOnly =
                                    staffPermission.Permission.IsShowDefaultChannelMessagesOnly;
                            }

                            if (isShowDefaultChannelMessagesOnly)
                            {
                                conversations = conversations.Where(
                                    conversation =>
                                        conversation.AssignedTeamId.HasValue
                                        && (managedTeam.Contains(conversation.AssignedTeamId.Value)
                                            || conversation.AdditionalAssignees
                                                .Any(
                                                    x => x.CompanyId == companyId
                                                         && x.AssigneeId == staff.Id))
                                );
                            }
                            else
                            {
                                conversations = conversations.Where(
                                    conversation =>
                                        (!conversation.AssignedTeamId.HasValue && !conversation.AssigneeId.HasValue)
                                        || ((conversation.AssignedTeamId.HasValue
                                             && managedTeam.Contains(conversation.AssignedTeamId.Value))
                                            || conversation.AdditionalAssignees
                                                .Any(
                                                    x => x.CompanyId == companyId
                                                         && x.AssigneeId == staff.Id)));
                            }

                            break;
                    }

                    break;
                case "mentioned":
                    conversations =
                        conversations.Where(c => mentionQueryable.Select(m => m.ConversationId).Contains(c.Id));

                    break;
                case "team":
                    switch (staff.RoleType)
                    {
                        case StaffUserRole.Admin:
                        case StaffUserRole.DemoAdmin:
                        case StaffUserRole.TeamAdmin:
                            conversations = conversations.Where(conversation => conversation.AssignedTeamId == teamId);

                            break;
                        case StaffUserRole.Staff:
                            conversations = conversations.Where(
                                conversation => conversation.AssignedTeamId == teamId &&
                                                (!conversation.AssigneeId.HasValue ||
                                                 conversation.AssigneeId == staff.Id));

                            break;
                    }

                    if (isTeamUnassigned.HasValue)
                    {
                        conversations = conversations.Where(conversation => !conversation.AssigneeId.HasValue);
                    }

                    break;
                case "collaborator":
                    conversations = conversations
                        .Where(
                            conversation =>
                                conversation.AdditionalAssignees.Any(
                                    x => x.CompanyId == companyId && x.AssigneeId == staff.Id));
                    break;
                case "bookmarked":
                    conversations = conversations
                        .Where(conversation => conversation.ConversationBookmarks.Any(x => x.StaffId == staff.Id));
                    break;
                default:
                    if (isCollaborator.HasValue && isCollaborator.Value)
                    {
                        break;
                    }

                    if (assignedTo == "you")
                    {
                        assignedTo = staff.IdentityId;
                    }

                    switch (staff.RoleType)
                    {
                        case StaffUserRole.Admin:
                        case StaffUserRole.DemoAdmin:
                            break;
                        case StaffUserRole.TeamAdmin:
                            var isManagedTeam = await dbContext.CompanyStaffTeams
                                .AnyAsync(
                                    x =>
                                        x.Members.Any(m => m.StaffId == staff.Id)
                                        && x.Members.Any(m => m.Staff.IdentityId == assignedTo));
                            if (!isManagedTeam)
                            {
                                if (assignedTo != staff.IdentityId)
                                {
                                    throw new Exception($"{assignedTo} is not your teammates");
                                }
                            }

                            break;
                        case StaffUserRole.Staff:
                            if (assignedTo != staff.IdentityId)
                            {
                                throw new Exception($"You cannot access conversations assigned to {assignedTo}");
                            }

                            var managedTeam = await dbContext.CompanyStaffTeams
                                .Where(x => x.Members.Any(m => m.StaffId == staff.Id))
                                .Select(x => x.Id)
                                .ToListAsync();

                            if (managedTeam.Count == 0)
                            {
                                break;
                            }

                            var isShowDefaultChannelMessagesOnly = false;

                            var staffPermission = await dbContext.CompanyRolePermissions
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.CompanyId == companyId
                                        && x.StaffUserRole == staff.RoleType);

                            if (staffPermission != null)
                            {
                                isShowDefaultChannelMessagesOnly =
                                    staffPermission.Permission.IsShowDefaultChannelMessagesOnly;
                            }

                            if (isShowDefaultChannelMessagesOnly)
                            {
                                conversations = conversations.Where(
                                    conversation =>
                                        conversation.AssigneeId == staff.Id //Assigned to the staff
                                        || (conversation.AssignedTeamId.HasValue
                                        && ((managedTeam.Contains(conversation.AssignedTeamId.Value)
                                             && !conversation.AssigneeId.HasValue)
                                            || conversation.AdditionalAssignees
                                                .Any(
                                                    x => x.CompanyId == companyId
                                                         && x.AssigneeId == staff.Id))));
                            }
                            else
                            {
                                conversations = conversations.Where(
                                    conversation =>
                                        conversation.AssigneeId == staff.Id //Assigned to the staff
                                        || (!conversation.AssignedTeamId.HasValue &&
                                            !conversation.AssigneeId.HasValue) //Assigned to neither team nor staff
                                        || (conversation.AssignedTeamId.HasValue
                                            && managedTeam.Contains(conversation.AssignedTeamId.Value)
                                            && !conversation.AssigneeId.HasValue) //Assigned to the team, not to others
                                        || conversation.AdditionalAssignees
                                            .Any(
                                                x => x.CompanyId == companyId
                                                     && x.AssigneeId == staff.Id)); //Assigned to collaborator
                            }

                            break;
                    }

                    var assignedToId = await dbContext.UserRoleStaffs
                        .Where(
                            x =>
                                x.CompanyId == companyId
                                && x.IdentityId == assignedTo)
                        .Select(x => x.Id)
                        .FirstOrDefaultAsync();

                    // If only assigned to me then no need to check teammates as contact owner or collaborator
                    conversations = conversations
                        .WhereIf(
                            version == "1",
                            conversation =>
                                conversation.AssigneeId == assignedToId
                                || conversation.AdditionalAssignees.Any(
                                    x => x.CompanyId == companyId && x.AssigneeId == assignedToId))
                        .WhereIf(
                            version == "2",
                            conversation => conversation.AssigneeId == assignedToId);

                    break;
            }

            // Filter channel at last
            if (!string.IsNullOrEmpty(channels))
            {
                conversations = FilterByChannel(
                    companyId,
                    channels,
                    channelIdList,
                    dbContext,
                    conversations);
            }

            return ApplyOrderBy(conversations, orderBy, staff);
        }

        private IQueryable<Conversation> FilterByChannel(
            string companyId,
            string channels,
            List<string> channelIdList,
            BaseDbContext dbContext,
            IQueryable<Conversation> conversations)
        {
            try
            {
                if (channels.Contains(ChannelTypes.WhatsappTwilio)
                    && !channels.Contains(ChannelTypes.Whatsapp360Dialog)
                    && !channels.Contains(ChannelTypes.WhatsappCloudApi))
                {
                    var newChannelIds = new List<string>();
                    var newInstanceSender = new List<string>();

                    foreach (var myChannelId in channelIdList)
                    {
                        var twilioInstance = myChannelId.Split(";", StringSplitOptions.RemoveEmptyEntries);
                        newChannelIds.Add(twilioInstance[0]);

                        if (twilioInstance.Count() > 1)
                        {
                            newInstanceSender.Add(twilioInstance[1].Replace(": ", ":+"));
                        }
                    }

                    if (newInstanceSender.Count > 0)
                    {
                        var twilioConversationQueryable = dbContext.ConversationWhatsappHistories.Where(
                            ws =>
                                newInstanceSender.Contains(ws.InstanceSender) &&
                                newChannelIds.Contains(ws.InstanceId));

                        conversations = conversations.Where(
                            c =>
                                twilioConversationQueryable.Select(wsh => wsh.ConversationId).Contains(c.Id));
                    }
                    else
                    {
                        var twilioConversationQueryable = dbContext.ConversationWhatsappHistories.Where(
                            ws =>
                                newChannelIds.Contains(ws.InstanceId));

                        conversations = conversations.Where(
                            c => twilioConversationQueryable.Select(wsh => wsh.ConversationId)
                                .Contains(c.Id));
                    }
                }

                if (channels.Contains(ChannelTypes.Facebook))
                {
                    conversations = conversations.Where(
                        c => c.facebookUser.CompanyId == companyId && channelIdList.Contains(c.facebookUser.pageId));
                }

                if (channels.Contains(ChannelTypes.Instagram))
                {
                    conversations = conversations.Where(
                        c => c.InstagramUser.CompanyId == companyId && channelIdList.Contains(c.InstagramUser.InstagramPageId));
                }

                if (channels.Contains(ChannelTypes.Whatsapp360Dialog))
                {
                    var whatsapp360DialogIds = new List<long?>();

                    foreach (var channelId in channelIdList)
                    {
                        var good = long.TryParse(channelId, out var id);

                        if (good)
                        {
                            whatsapp360DialogIds.Add(id);
                        }
                    }

                    conversations = conversations.Where(
                        c => c.WhatsApp360DialogUser.CompanyId == companyId &&
                             whatsapp360DialogIds.Contains(c.WhatsApp360DialogUser.ChannelId));
                }

                if (channels.Contains(ChannelTypes.WhatsappCloudApi))
                {
                    var whatsappCloudApiConversationQueryable = dbContext.WhatsappCloudApiSenders.Where(
                        ws =>
                            ws.CompanyId == companyId && channelIdList.Contains(ws.WhatsappChannelPhoneNumber));

                    conversations = conversations.Where(
                        c =>
                            whatsappCloudApiConversationQueryable.Select(ws => ws.ConversationId).Contains(c.Id));
                }

                if (channels.Contains(ChannelTypes.Line))
                {
                    conversations = conversations.Where(c => c.LineUserId != null);
                }

                if (channels.Contains(ChannelTypes.Viber))
                {
                    conversations = conversations.Where(c => c.ViberUserId != null);
                }

                if (channels.Contains(ChannelTypes.Wechat))
                {
                    conversations = conversations.Where(c => c.WeChatUserId != null);
                }

                if (channels.Contains(ChannelTypes.Telegram))
                {
                    conversations = conversations.Where(c => c.TelegramUserId != null);
                }

                if (channels.Contains(ChannelTypes.Sms))
                {
                    conversations = conversations.Where(c => c.SMSUserId != null);
                }

                if (channels.Contains(ChannelTypes.LiveChat))
                {
                    conversations = conversations.Where(c => c.WebClientId != null);
                }

                if (channels.Contains(ChannelTypes.Email))
                {
                    conversations = conversations.Where(c => c.EmailAddressId != null);
                }

                if (channels.Contains(ChannelTypes.LiveChatV2))
                {
                    var liveChatV2ConversationQueryable = dbContext.SenderWebClientSenders.Where(
                        sender =>
                            sender.CompanyId == companyId && channelIdList.Contains(sender.ChannelIdentityId));

                    conversations = conversations.Where(
                        c =>
                           liveChatV2ConversationQueryable.Select(sender => sender.ConversationId).Contains(c.Id));
                }

                if (channels.Contains(ChannelTypes.Tiktok))
                {
                    var tikTokConversationQueryable = dbContext.SenderTikTokSenders.Where(
                        ts =>
                            ts.CompanyId == companyId &&
                            (channelIdList.Count == 0 || channelIdList.Contains(ts.ChannelIdentityId)));

                    conversations = conversations.Where(
                        c =>
                            tikTokConversationQueryable.Select(ts => ts.ConversationId).Contains(c.Id));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Company {CompanyId} unable to parse TargetedChannelModel {ExceptionMessage}",
                    nameof(GetConversations),
                    companyId,
                    ex.Message);
            }

            return conversations;
        }

        public IQueryable<ConversationMessage> GetConversationMessage(
            string conversationId,
            string companyId,
            long? beforeMessageId = null,
            long? afterMessageId = null,
            long? beforeTimestamp = null,
            long? afterTimestamp = null,
            string channel = null,
            string channelIds = null,
            bool? IsFromUser = null)
        {
            var result = _appDbContext.ConversationMessages
                .Where(conversationMessages => conversationMessages.CompanyId == companyId);

            result = result.WhereIf(
                !string.IsNullOrEmpty(conversationId) && conversationId != "all",
                conversationMessages => conversationMessages.ConversationId == conversationId);

            // Deprecated message id fetch
            if (!beforeMessageId.HasValue && !afterMessageId.HasValue && afterTimestamp == null && beforeTimestamp == null)
            {
                beforeMessageId = result
                    .OrderByDescending(x => x.Id)
                    .Take(1)
                    .Select(x => x.Id)
                    .FirstOrDefault();
                if (beforeMessageId.HasValue)
                {
                    beforeMessageId += 1;
                }
            }

            if (beforeMessageId.HasValue
                && afterMessageId.HasValue)
            {
                result = result.Where(m => m.Id < beforeMessageId && m.Id > afterMessageId);
            }
            else if (beforeMessageId.HasValue)
            {
                result = result.Where(m => m.Id < beforeMessageId);
            }
            else if (afterMessageId.HasValue)
            {
                result = result.Where(m => m.Id > afterMessageId);
            }

            result = result.WhereIf(afterTimestamp.HasValue, m => afterTimestamp <= m.Timestamp);
            result = result.WhereIf(beforeTimestamp.HasValue, m => m.Timestamp <= beforeTimestamp);

            var channelIdList = new List<string>();

            if (!string.IsNullOrEmpty(channelIds))
            {
                channelIdList = channelIds
                    .Split(",")
                    .Select(x => x.Trim()).ToList();
            }

            if (!string.IsNullOrEmpty(channel))
            {
                try
                {
                    result = result
                        .Where(x => x.Channel == channel || x.Channel == "note");
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Company {CompanyId} error filtering query by channel. {ExceptionMessage}",
                        nameof(GetConversationMessage),
                        companyId,
                        ex.Message);
                }
            }

            if (channelIdList.Count > 0)
            {
                try
                {
                    if (channel == ChannelTypes.WhatsappTwilio)
                    {
                        var newChannelIds = new List<string>();
                        var newInstanceSender = new List<string>();

                        foreach (var myChannelId in channelIdList)
                        {
                            var twilioInstance = myChannelId.Split(";", StringSplitOptions.RemoveEmptyEntries);
                            newChannelIds.Add(twilioInstance[0]);

                            if (twilioInstance.Length > 1)
                            {
                                newInstanceSender.Add(twilioInstance[1].Replace(": ", ":+"));
                            }
                        }

                        result = result.Where(
                            conversationMessage =>
                                newChannelIds.Contains(conversationMessage.whatsappReceiver.InstanceId)
                                || newChannelIds.Contains(conversationMessage.whatsappSender.InstanceId));

                        if (newInstanceSender.Count > 0)
                        {
                            result = result.Where(
                                conversationMessage =>
                                    newInstanceSender.Contains(conversationMessage.whatsappReceiver.InstaneSender)
                                    || newInstanceSender.Contains(conversationMessage.whatsappSender.InstaneSender));
                        }
                    }
                    else if (channel == ChannelTypes.Whatsapp360Dialog)
                    {
                        result = result.Where(
                            x => channelIdList.Contains(x.Whatsapp360DialogSender.ChannelWhatsAppPhoneNumber)
                                 || channelIdList.Contains(x.Whatsapp360DialogReceiver.ChannelWhatsAppPhoneNumber));
                    }
                    else if (channel == ChannelTypes.WhatsappCloudApi)
                    {
                        result = result.Where(x => channelIdList.Contains(x.ChannelIdentityId));
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Company {CompanyId} error filtering query by channelIdList: {@ChannelIdList}. {ExceptionMessage}",
                        nameof(GetConversationMessage),
                        companyId,
                        channelIdList,
                        ex.Message);
                }
            }

            if (IsFromUser.HasValue)
            {
                result = result.Where(
                    conversationMessage => conversationMessage.IsSentFromSleekflow == !IsFromUser.Value);
            }

            return result;
        }

        public IOrderedQueryable<ConversationMessage> GetConversationMessagesHistory(
            string companyId,
            string staffId = null,
            string status = null,
            string channels = null,
            DateTime? messageFrom = null,
            DateTime? messageTo = null,
            string channelIds = null,
            string tags = null,
            long? teamId = null,
            bool? isTeamUnassigned = null)
        {
            var channelList = new List<string>();

            if (!string.IsNullOrEmpty(channels))
            {
                channelList = channels.Split(",").ToList();
            }

            var channelIdList = new List<string>();

            if (!string.IsNullOrEmpty(channelIds))
            {
                channelIdList = channelIds.Split(",").ToList();
            }

            var tagsList = new List<string>();

            if (!string.IsNullOrEmpty(tags))
            {
                tagsList = tags.Split(",").ToList();
            }

            var conversationMessages =
                from conversationMessage in _appDbContext.ConversationMessages
                where conversationMessage.CompanyId == companyId
                      && _appDbContext.Conversations
                          .Where(
                              x =>
                                  x.CompanyId == companyId
                                  && x.UserProfile.ActiveStatus == ActiveStatus.Active)
                          .Select(x => x.Id)
                          .Contains(conversationMessage.ConversationId)
                orderby conversationMessage.Timestamp
                select conversationMessage;

            if (!string.IsNullOrEmpty(status))
            {
                conversationMessages =
                    from conversationMessage in _appDbContext.ConversationMessages
                    where _appDbContext.Conversations
                        .Where(
                            x =>
                                x.CompanyId == companyId
                                && x.Status == status)
                        .Select(x => x.Id)
                        .Contains(conversationMessage.ConversationId)
                    orderby conversationMessage.Timestamp
                    select conversationMessage;
            }

            if (!string.IsNullOrEmpty(staffId))
            {
                if (staffId == "unassigned")
                {
                    conversationMessages =
                        from conversationMessage in conversationMessages
                        where _appDbContext.Conversations
                            .Where(
                                x =>
                                    x.CompanyId == companyId
                                    && !x.AssigneeId.HasValue)
                            .Select(x => x.Id)
                            .Contains(conversationMessage.ConversationId)
                        orderby conversationMessage.Timestamp
                        select conversationMessage;
                }
                else
                {
                    conversationMessages =
                        from conversationMessage in conversationMessages
                        where _appDbContext.Conversations
                                  .Where(
                                      x =>
                                          x.CompanyId == companyId
                                          && x.Assignee.IdentityId == staffId)
                                  .Select(x => x.Id)
                                  .Contains(conversationMessage.ConversationId)
                              || _appDbContext.Conversations
                                  .Where(
                                      x =>
                                          x.CompanyId == companyId
                                          && x.AdditionalAssignees.Any(x => x.Assignee.IdentityId == staffId && x.CompanyId == companyId))
                                  .Select(x => x.Id)
                                  .Contains(conversationMessage.ConversationId)
                        orderby conversationMessage.Timestamp
                        select conversationMessage;
                }
            }
            else if (teamId.HasValue)
            {
                var results = _appDbContext.CompanyTeamMembers
                    .Where(y => y.CompanyTeamId == teamId.Value)
                    .Select(y => y.StaffId);

                conversationMessages =
                    from conversationMessage in conversationMessages
                    where _appDbContext.Conversations
                              .Where(
                                  x =>
                                      x.CompanyId == companyId
                                      && x.AssignedTeam.Id == teamId.Value)
                              .Select(x => x.Id)
                              .Contains(conversationMessage.ConversationId)
                          || _appDbContext.Conversations
                              .Where(
                                  x =>
                                      x.CompanyId == companyId
                                      && results.Contains(x.AssigneeId.Value))
                              .Select(x => x.Id)
                              .Contains(conversationMessage.ConversationId)
                    orderby conversationMessage.Timestamp
                    select conversationMessage;

                if (isTeamUnassigned.HasValue
                    && isTeamUnassigned.Value)
                {
                    conversationMessages =
                        from conversationMessage in conversationMessages
                        where _appDbContext.Conversations
                            .Where(
                                x =>
                                    x.CompanyId == companyId
                                    && !x.AssigneeId.HasValue)
                            .Select(x => x.Id)
                            .Contains(conversationMessage.ConversationId)
                        orderby conversationMessage.Timestamp
                        select conversationMessage;
                }
            }

            if (!string.IsNullOrEmpty(channels))
            {
                conversationMessages =
                    from conversationMessage in conversationMessages
                    where channelList.Contains(conversationMessage.Channel)
                    orderby conversationMessage.Timestamp
                    select conversationMessage;
            }

            if (!string.IsNullOrEmpty(channelIds))
            {
                try
                {
                    if (channels.Contains(ChannelTypes.WhatsappTwilio)
                        && !channels.Contains(ChannelTypes.Whatsapp360Dialog)
                        && !channels.Contains(ChannelTypes.WhatsappCloudApi))
                    {
                        var newChannelIds = new List<string>();
                        var newInstanceSender = new List<string>();

                        foreach (var myChannelId in channelIdList)
                        {
                            var twilioInstance = myChannelId.Split(";", StringSplitOptions.RemoveEmptyEntries);
                            newChannelIds.Add(twilioInstance[0]);

                            if (twilioInstance.Count() > 1)
                            {
                                newInstanceSender.Add(twilioInstance[1].Replace(": ", ":+"));
                            }
                        }

                        conversationMessages =
                            from conversationMessage in conversationMessages
                            where newChannelIds.Contains(conversationMessage.whatsappReceiver.InstanceId)
                                  || newChannelIds.Contains(conversationMessage.whatsappSender.InstanceId)
                            orderby conversationMessage.Timestamp
                            select conversationMessage;

                        if (newInstanceSender.Count > 0)
                        {
                            conversationMessages =
                                from conversationMessage in conversationMessages
                                where newChannelIds.Contains(conversationMessage.whatsappReceiver.InstanceId)
                                      || newChannelIds.Contains(conversationMessage.whatsappSender.InstanceId)
                                orderby conversationMessage.Timestamp
                                select conversationMessage;
                        }
                    }

                    if (channels.Contains(ChannelTypes.Facebook))
                    {
                        conversationMessages =
                            from conversationMessage in conversationMessages
                            where channelIdList.Contains(conversationMessage.facebookReceiver.pageId)
                                  || channelIdList.Contains(conversationMessage.facebookSender.pageId)
                            orderby conversationMessage.Timestamp
                            select conversationMessage;
                    }

                    if (channels.Contains(ChannelTypes.Whatsapp360Dialog))
                    {
                        var whatsapp360DialogIds = new List<long?>();

                        foreach (var channelId in channelIdList)
                        {
                            var good = long.TryParse(channelId, out var id);

                            if (good)
                            {
                                whatsapp360DialogIds.Add(id);
                            }
                        }

                        conversationMessages =
                            from conversationMessage in conversationMessages
                            where whatsapp360DialogIds.Contains(conversationMessage.Whatsapp360DialogReceiver.ChannelId)
                                  || whatsapp360DialogIds.Contains(conversationMessage.Whatsapp360DialogSender.ChannelId)
                            orderby conversationMessage.Timestamp
                            select conversationMessage;
                    }

                    if (channels.Contains(ChannelTypes.WhatsappCloudApi))
                    {
                        conversationMessages =
                            from conversationMessage in conversationMessages
                            where channelIds.Contains(conversationMessage.ChannelIdentityId)
                            orderby conversationMessage.Timestamp
                            select conversationMessage;
                    }

                    if (channels.Contains(ChannelTypes.Tiktok))
                    {
                        conversationMessages =
                            from conversationMessage in conversationMessages
                            where channelIds.Contains(conversationMessage.ChannelIdentityId)
                            orderby conversationMessage.Timestamp
                            select conversationMessage;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Company {CompanyId} error filtering query by channelIds: {ChannelIds}. {ExceptionMessage}",
                        nameof(GetConversationMessagesHistory),
                        companyId,
                        channelIds,
                        ex.Message);
                }
            }

            if (tagsList.Count > 0)
            {
                var hashTagsResultQ =
                    from conversation in _appDbContext.Conversations
                    where conversation.CompanyId == companyId
                    select conversation;

                foreach (var tag in tagsList)
                {
                    hashTagsResultQ =
                        from _hashTagsResult in hashTagsResultQ
                        where _hashTagsResult.conversationHashtags
                            .Select(x => x.Hashtag.Hashtag.ToLower())
                            .Contains(tag.ToLower())
                        select _hashTagsResult;
                }

                var hashTagsResult = hashTagsResultQ.Select(x => x.Id);

                conversationMessages =
                    from conversationMessage in conversationMessages
                    where hashTagsResult.Contains(conversationMessage.ConversationId)
                    orderby conversationMessage.Timestamp
                    select conversationMessage;
            }

            if (messageFrom.HasValue)
            {
                conversationMessages =
                    from conversationMessage in conversationMessages
                    where messageFrom.Value <= conversationMessage.CreatedAt
                    orderby conversationMessage.Timestamp
                    select conversationMessage;
            }

            if (messageTo.HasValue)
            {
                conversationMessages =
                    from conversationMessage in conversationMessages
                    where messageTo.Value > conversationMessage.CreatedAt
                    orderby conversationMessage.Timestamp
                    select conversationMessage;
            }

            return conversationMessages;
        }

        public async Task AckConversationMessageReceived(string companyId, long staffId, List<long> messageIds)
        {
            await _appDbContext.ConversationUnreadRecords.Where(
                    x => x.CompanyId == companyId
                         && x.StaffId == staffId
                         && messageIds.Contains(x.MessageId)
                         && x.NotificationDeliveryStatus == NotificationDeliveryStatus.Sent)
                .ExecuteUpdateAsync(
                    unreadRecord =>
                        unreadRecord
                            .SetProperty(r => r.DeliveredAt, DateTime.Now)
                            .SetProperty(r => r.NotificationDeliveryStatus, NotificationDeliveryStatus.Delivered));
        }

        public async Task ReadConversationPersonally(string conversationId, Staff companyUser)
        {
            var myLock = await _lockService.AcquireLockAsync(
                $"ReadConversationPersonally:{conversationId}:{companyUser.Id}",
                TimeSpan.FromSeconds(2));

            if (myLock == null)
            {
                return;
            }

            var userProfileId = await _appDbContext.Conversations
                .Where(x => x.Id == conversationId)
                .Select(x => x.UserProfileId)
                .FirstOrDefaultAsync();

            if (!await _appDbContext.Conversations
                    .AnyAsync(
                        x =>
                            x.Id == conversationId
                            && x.UnreadMessageCount > 0)
                && !await _appDbContext.ConversationUnreadRecords
                        .AnyAsync(
                            x =>
                                x.CompanyId == companyUser.CompanyId
                                && x.StaffId == companyUser.Id
                                && x.ConversationId == conversationId
                                && x.NotificationDeliveryStatus != NotificationDeliveryStatus.Read))
            {
                // BackgroundJob.Enqueue<IDbAuditLogService>(x => x.AddActivityLogs(companyUser.CompanyId, userProfileId, companyUser.IdentityId, new List<RemarkViewModel> {new RemarkViewModel {Remarks = $"{companyUser.Identity.DisplayName} viewed this conversation"}}));
                return;
            }

            // No need to reset the unread count of conversation anymore
            await _appDbContext.Conversations
                .Where(x => x.Id == conversationId)
                .ExecuteUpdateAsync(
                    conversation =>
                        conversation.SetProperty(c => c.UnreadMessageCount, 0));


            await _appDbContext.ConversationUnreadRecords
                .Where(
                    x =>
                        x.CompanyId == companyUser.CompanyId
                        && x.StaffId == companyUser.Id
                        && x.ConversationId == conversationId
                        && x.NotificationDeliveryStatus != NotificationDeliveryStatus.Read)
                .ExecuteUpdateAsync(
                    unreadRecord =>
                        unreadRecord
                            .SetProperty(r => r.ReadAt, DateTime.Now)
                            .SetProperty(r => r.NotificationDeliveryStatus, NotificationDeliveryStatus.Read));

            if (companyUser.Id != 1)
            {
                await _userProfileHooks.OnConversationReadAsync(
                    companyUser.CompanyId,
                    userProfileId,
                    null,
                    () => Task.FromResult(
                        new OnConversationReadData(new StaffData(companyUser.Id.ToString(), companyUser.IdentityId,
                            companyUser.Identity.DisplayName))));
            }
        }

        public async Task ReadConversation(string conversationId, Staff companyUser, bool isForEveryone = false)
        {
            ILockService.Lock myLock = null;

            var dbContext = _dbContextService.GetDbContext();

            if (!isForEveryone)
            {
                myLock = await _lockService.AcquireLockAsync(
                    $"ReadConversation:{conversationId}:{companyUser.Id}",
                    TimeSpan.FromSeconds(2));

                if (myLock == null)
                {
                    return;
                }
            }

            var userProfileId = await dbContext.Conversations
                .Where(x => x.Id == conversationId)
                .Select(x => x.UserProfileId)
                .FirstOrDefaultAsync();

            if (!await dbContext.Conversations
                    .AnyAsync(
                        x =>
                            x.Id == conversationId &&
                            x.UnreadMessageCount > 0)
                && !await dbContext.ConversationUnreadRecords
                    .Where(
                        x =>
                            x.CompanyId == companyUser.CompanyId
                            && x.ConversationId == conversationId
                            && x.NotificationDeliveryStatus != NotificationDeliveryStatus.Read)
                    .WhereIf(
                        !isForEveryone,
                        x => x.StaffId == companyUser.Id)
                    .AnyAsync())
            {
                return;
            }

            // No need to reset the unread count of conversation anymore
            await dbContext.Conversations
                .Where(x => x.Id == conversationId)
                .ExecuteUpdateAsync(
                    conversation => conversation.SetProperty(c => c.UnreadMessageCount, 0));

            var unreadRecordCount = await dbContext.ConversationUnreadRecords
                .Where(
                    x =>
                        x.CompanyId == companyUser.CompanyId
                        && x.ConversationId == conversationId
                        && x.NotificationDeliveryStatus != NotificationDeliveryStatus.Read)
                .WhereIf(
                    !isForEveryone,
                    x => x.StaffId == companyUser.Id).CountAsync();

            var affectedRows = await dbContext.ConversationUnreadRecords
                .Where(x =>
                    x.CompanyId == companyUser.CompanyId
                    && x.ConversationId == conversationId
                    && x.NotificationDeliveryStatus != NotificationDeliveryStatus.Read)
                .WhereIf(
                    !isForEveryone,
                    x => x.StaffId == companyUser.Id)
                .ExecuteDeleteAsync();

            if (companyUser.Id != 1)
            {
                await _userProfileHooks.OnConversationReadAsync(
                    companyUser.CompanyId,
                    userProfileId,
                    null,
                    () => Task.FromResult(
                        new OnConversationReadData(new StaffData(companyUser.Id.ToString(), companyUser.IdentityId,
                                companyUser.Identity.DisplayName))));
            }

            if (myLock != null)
            {
                await _lockService.ReleaseLockAsync(myLock);
            }
        }

        public async Task<ConversationStatusResponseViewModel> MarkConversationAsUnread(
            string conversationId,
            Staff companyUser)
        {
            var myLock = await _lockService.AcquireLockAsync(
                $"MarkConversationAsUnread:{conversationId}:{companyUser.Id}",
                TimeSpan.FromSeconds(2));

            if (myLock == null)
            {
                throw new Exception("locked");
            }

            var conversation = await _appDbContext.Conversations
                .Include(x => x.UserProfile)
                .Include(x => x.Assignee.Identity)
                .Include(x => x.AdditionalAssignees)
                .FirstOrDefaultAsync(
                    x =>
                        x.Id == conversationId
                        && x.CompanyId == companyUser.CompanyId);

            conversation.UnreadMessageCount = 1;

            // Current assignment type between the Conversation and User
            NotificationDeliveryType notificationDeliveryType;
            if (conversation.AssigneeId == companyUser.Id)
            {
                notificationDeliveryType = NotificationDeliveryType.ContactOwner;
            }
            else if (conversation.AdditionalAssignees.Any(x => x.AssigneeId == companyUser.Id && x.CompanyId == companyUser.CompanyId))
            {
                notificationDeliveryType = NotificationDeliveryType.Collaborator;
            }
            else
            {
                notificationDeliveryType = NotificationDeliveryType.Mentioned;
            }

            var updatedUnreadRecordCount = await _appDbContext.ConversationUnreadRecords
                .Where(
                    x =>
                        x.CompanyId == companyUser.CompanyId
                        && x.StaffId == companyUser.Id
                        && x.ConversationId == conversationId
                        && x.NotificationDeliveryType == notificationDeliveryType)
                .OrderByDescending(x => x.Id)
                .Take(1)
                .ExecuteUpdateAsync(
                    unreadRecord =>
                        unreadRecord.SetProperty(r => r.NotificationDeliveryStatus, NotificationDeliveryStatus.Delivered));

            // If no unread records between the conversation and this User
            if (updatedUnreadRecordCount == 0)
            {
                var conversationMessageId = await _appDbContext.ConversationMessages
                    .Where(x => x.CompanyId == companyUser.CompanyId && x.ConversationId == conversationId)
                    .OrderByDescending(x => x.CreatedAt)
                    .Select(x => x.Id)
                    .FirstOrDefaultAsync();

                if (conversationMessageId != 0)
                {
                    await _appDbContext.ConversationUnreadRecords.AddAsync(
                        new ConversationUnreadRecord()
                        {
                            CompanyId = companyUser.CompanyId,
                            ConversationId = conversationId,
                            MessageId = conversationMessageId,
                            StaffId = companyUser.Id,
                            StaffIdentityId = companyUser.IdentityId,
                            NotificationDeliveryType = notificationDeliveryType,
                            NotificationDeliveryStatus = NotificationDeliveryStatus.Delivered,
                            SentAt = DateTime.UtcNow,
                            DeliveredAt = DateTime.UtcNow,
                            ReadAt = DateTime.UtcNow
                        });
                }
                else
                {
                    throw new Exception("Mark as unread failed because no valid message found!");
                }
            }

            await _appDbContext.SaveChangesAsync();

            var responseVm = _mapper.Map<ConversationStatusResponseViewModel>(conversation);
            await _signalRService.SignalROnConversationUnreadStatusChanged(conversation, companyUser.IdentityId);

            return responseVm;
        }

        public async Task<List<ConversationMessageResponseViewModel>> GetUndeliveredMessages(Staff companyUser)
        {
            var getUndeliveredMessageCacheKeyPattern = new GetUndeliveredMessageCacheKeyPattern(companyUser.Id);
            var data = await _cacheManagerService.GetCacheAsync(getUndeliveredMessageCacheKeyPattern);

            if (!string.IsNullOrEmpty(data))
            {
                return JsonConvert.DeserializeObject<List<ConversationMessageResponseViewModel>>(data);
            }

            var myLock = await _lockService.AcquireLockAsync(
                $"GetUndeliveredMessages{companyUser.Id}",
                TimeSpan.FromSeconds(10));

            if (myLock == null)
            {
                throw new Exception("locked");
            }

            var undeliveredMessageIds = await _appDbContext.ConversationUnreadRecords
                .Where(
                    x =>
                        x.CompanyId == companyUser.CompanyId
                        && x.StaffId == companyUser.Id
                        && x.NotificationDeliveryStatus == NotificationDeliveryStatus.Sent)
                .Select(x => x.MessageId)
                .ToListAsync();

            var response = await _appDbContext.ConversationMessages
                .Where(
                    x =>
                        x.CompanyId == companyUser.CompanyId
                        && undeliveredMessageIds.Contains(x.Id))
                .Include(x => x.UploadedFiles)
                .Include(x => x.EmailFrom)
                .Include(x => x.Sender)
                .Include(x => x.facebookSender)
                .Include(x => x.facebookReceiver)
                .Include(x => x.whatsappSender)
                .Include(x => x.whatsappReceiver)
                .Include(x => x.WebClientSender)
                .Include(x => x.WebClientReceiver)
                .Include(x => x.MessageAssignee.Identity)
                .Include(x => x.WeChatSender)
                .Include(x => x.WeChatReceiver)
                .Include(x => x.LineSender)
                .Include(x => x.LineReceiver)
                .Include(x => x.SMSSender)
                .Include(x => x.SMSReceiver)
                .Include(x => x.InstagramSender)
                .Include(x => x.InstagramReceiver)
                .Include(x => x.ViberSender)
                .Include(x => x.ViberReceiver)
                .Include(x => x.TelegramSender)
                .Include(x => x.TelegramReceiver)
                .Include(x => x.Whatsapp360DialogSender)
                .Include(x => x.Whatsapp360DialogReceiver)
                .Include(x => x.Whatsapp360DialogExtendedMessagePayload)
                .Include(x => x.ExtendedMessagePayload)
                .OrderByDescending(x => x.Timestamp)
                .ProjectTo<ConversationMessageResponseViewModel>(_mapper.ConfigurationProvider)
                .ToListAsync();

            await _sleekPayService.AddSleekPayRecord(response);

            foreach (var message in response)
            {
                await _signalRService.SignalRResendOnMessageReceived(companyUser.IdentityId, message);
            }

            await _cacheManagerService.SaveCacheAsync(
                getUndeliveredMessageCacheKeyPattern,
                response);

            return response;
        }

        public async Task<bool> SendOptInMessageTwilio(ConversationMessage message)
        {
            var hasSentOptInToday = await _appDbContext.ConversationMessages
                .AnyAsync(
                    x => x.ConversationId == message.ConversationId
                         && x.Channel.ToLower() == message.Channel.ToLower()
                         && x.whatsappSenderId == message.whatsappSenderId
                         && x.UpdatedAt.Date == DateTime.UtcNow.Date
                         && x.DeliveryType == DeliveryType.ReadMore);

            if (hasSentOptInToday)
            {
                return false;
            }

            var whatsappReceiver = await _appDbContext.SenderWhatsappSenders
                .FirstOrDefaultAsync(x => x.Id == message.whatsappReceiverId);

            // Send read more status
            var configQ = _appDbContext.ConfigWhatsAppConfigs
                .Where(x => x.TwilioAccountId == whatsappReceiver.InstanceId);

            if (!string.IsNullOrEmpty(whatsappReceiver.InstaneSender))
            {
                configQ = configQ
                    .Where(x => x.WhatsAppSender == whatsappReceiver.InstaneSender);
            }

            var config = await configQ.FirstOrDefaultAsync();

            var companySetting = await _appDbContext.CompanyCompanies
                .Where(x => x.Id == message.CompanyId)
                .Select(x => x.CompanySetting)
                .FirstOrDefaultAsync();

            if (companySetting == null)
            {
                return false;
            }

            if (!string.IsNullOrEmpty(config?.ReadMoreTemplateMessage)
                && companySetting.IsOptInOn)
            {
                await SendReadMoreMessage(
                    message.CompanyId,
                    message.ConversationId,
                    message.whatsappSenderId,
                    config.ReadMoreTemplateMessage,
                    config.ReadMoreMessageContentSid);

                return true;
            }

            return false;
        }

        public async Task<ConversationWithCountViewModel> SearchConversationMessagesAsync(
            Staff companyUser,
            string assignedTo,
            string keywords,
            int offset,
            int limit,
            string status,
            string channels,
            string channelIds,
            long? teamId,
            string behaviourVersion = "1",
            bool isAllowCache = true)
        {
            var isRbacEnabled = _rbacService.IsRbacEnabled();

            if (isRbacEnabled)
            {
                return await RbacSearchConversationMessagesAsync(
                    companyUser,
                    assignedTo,
                    keywords,
                    offset,
                    limit,
                    status,
                    channels,
                    channelIds,
                    teamId,
                    behaviourVersion,
                    isAllowCache);
            }

            return await DefaultSearchConversationMessagesAsync(
                companyUser,
                assignedTo,
                keywords,
                offset,
                limit,
                status,
                channels,
                channelIds,
                teamId,
                behaviourVersion,
                isAllowCache);
        }

        public async Task<ConversationWithCountViewModel> DefaultSearchConversationMessagesAsync(
            Staff companyUser,
            string assignedTo,
            string keywords,
            int offset,
            int limit,
            string status,
            string channels,
            string channelIds,
            long? teamId,
            string behaviourVersion,
            bool isAllowCache)
        {
            var searchMessageCacheKeyPattern = new SearchMessageCacheKeyPattern(
                companyUser.Id,
                assignedTo,
                offset,
                limit,
                keywords,
                status,
                channels,
                channelIds,
                teamId,
                behaviourVersion);

            if (isAllowCache)
            {
                var data = await _cacheManagerService.GetCacheAsync(searchMessageCacheKeyPattern);

                if (data != null)
                {
                    return JsonConvert.DeserializeObject<ConversationWithCountViewModel>(data);
                }
            }

            while (true)
            {
                var myLock = await _lockService.AcquireLockAsync(
                    $"lock:v2_search_message{searchMessageCacheKeyPattern.GenerateKeyPattern()}",
                    TimeSpan.FromSeconds(15));

                if (myLock == null)
                {
                    if (isAllowCache)
                    {
                        var cacheData = await _cacheManagerService.GetCacheAsync(searchMessageCacheKeyPattern);

                        if (cacheData != null)
                        {
                            return JsonConvert.DeserializeObject<ConversationWithCountViewModel>(
                                cacheData);
                        }
                    }

                    await Task.Delay(5000);
                }
                else
                {
                    break;
                }
            }

            var channelList = new List<string>();

            if (!string.IsNullOrEmpty(channels))
            {
                channelList = channels.Split(",").ToList();
            }

            var dateThreshold = DateTime.UtcNow.AddDays(-365).ToString("yyyy-MM-dd");
            var keywordsParameter = new SqlParameter(
                "@keywords",
                string.IsNullOrEmpty(keywords) ? string.Empty : keywords);

            var filteredConversationMessageQueryable = _appDbContext.ConversationMessages.FromSqlRaw(
                    $"""
                     SELECT [c].*
                     FROM [ConversationMessages] AS [c]
                     LEFT JOIN [Conversations] AS [c0] ON [c].[ConversationId] = [c0].[Id]
                     WHERE [c].[CompanyId] = '{companyUser.CompanyId}'
                       AND [c].[CreatedAt] > '{dateThreshold}'
                       AND [c0].[CompanyId] = '{companyUser.CompanyId}'
                       AND ((@keywords = N'') OR CHARINDEX(@keywords, [c].[MessageContent]) > 0)
                       {status.ToLower() switch
                    {
                        "open" => "AND [c0].[Status] = 'open'",
                        "closed" => "AND [c0].[Status] = 'closed'",
                        "pending" => "AND [c0].[Status] = 'pending'",
                        _ => string.Empty
                    }}
                       AND [c0].[ActiveStatus] = 1
                     """,
                    keywordsParameter)
                .WhereIf(
                    channelList.Count > 0,
                    cm => channelList.Contains(cm.Channel));


            var mentionedConversationMessageQueryable =
                _mentionQueryableResolver.GetConversationMessageQueryable(
                    _appDbContext,
                    companyUser.Id,
                    companyUser.CompanyId);

            switch (assignedTo)
            {
                case "all":
                    switch (companyUser.RoleType)
                    {
                        case StaffUserRole.TeamAdmin:
                            var managedTeam = await _appDbContext.CompanyStaffTeams
                                .Where(x => x.Members.Any(member => member.StaffId == companyUser.Id))
                                .Select(x => x.Id)
                                .ToListAsync();

                            if (managedTeam == null)
                            {
                                throw new ArgumentNullException(nameof(managedTeam.ToString));
                            }

                            var teamAdminTeamIds = await _appDbContext.CompanyStaffTeams
                                .Where(
                                    x => x.Members.Any(member => member.StaffId == companyUser.Id) &&
                                         x.CompanyId == companyUser.CompanyId).Select(x => x.Id)
                                .ToListAsync();

                            var teamMemberIds = await _appDbContext.CompanyTeamMembers
                                .Where(x => teamAdminTeamIds.Contains(x.CompanyTeamId) && x.StaffId != companyUser.Id)
                                .Select(x => x.StaffId)
                                .Distinct()
                                .ToListAsync();

                            filteredConversationMessageQueryable = filteredConversationMessageQueryable.Where(
                                cm =>
                                    (cm.Conversation.AssigneeId == null &&
                                     cm.Conversation.AssignedTeamId == null) // Unassigned conversation
                                    || cm.Conversation.AssigneeId == companyUser.Id // Team Admin is the contact owner
                                    || cm.Conversation.AdditionalAssignees.Any(
                                        x => x.AssigneeId == companyUser.Id
                                             && x.CompanyId == companyUser.CompanyId) // Team Admin is the collaborator
                                    || (cm.Conversation.AssignedTeamId.HasValue
                                        && teamAdminTeamIds.Contains(
                                            cm.Conversation.AssignedTeamId
                                                .Value)) // The conversations managed under the team admin's team.//
                                    || cm.Conversation.AdditionalAssignees
                                        .Any( // The conversation has team member as collaborator
                                            x => teamMemberIds.Contains((long) x.AssigneeId))
                                    || mentionedConversationMessageQueryable.Select(mcm => mcm.Id).Contains(cm.Id));
                            break;
                        case StaffUserRole.Staff:
                            var associatedTeam = await _appDbContext.CompanyStaffTeams
                                .Where(x => x.Members.Any(member => member.StaffId == companyUser.Id))
                                .Select(x => x.Id)
                                .ToListAsync();

                            if (associatedTeam == null)
                            {
                                throw new ArgumentNullException(nameof(associatedTeam.ToString));
                            }

                            var staffAssociatedTeamIds = await _appDbContext.CompanyStaffTeams
                                .Where(
                                    x => x.Members.Any(member => member.StaffId == companyUser.Id) &&
                                         x.CompanyId == companyUser.CompanyId).Select(x => x.Id)
                                .ToListAsync();

                            var staffAssociatedTeamMemberIds = await _appDbContext.CompanyTeamMembers
                                .Where(x => staffAssociatedTeamIds.Contains(x.CompanyTeamId) && x.StaffId != companyUser.Id)
                                .Select(x => x.StaffId)
                                .Distinct()
                                .ToListAsync();

                            filteredConversationMessageQueryable = filteredConversationMessageQueryable.Where(
                                cm =>
                                    cm.Conversation.AssigneeId == companyUser.Id // Staff is the contact owner
                                    || cm.Conversation.AdditionalAssignees.Any( // Staff is the collaborator
                                        x => x.AssigneeId == companyUser.Id
                                             && x.CompanyId == companyUser.CompanyId)
                                    || (cm.Conversation.AssignedTeamId.HasValue && !cm.Conversation.AssigneeId.HasValue // The conversation is assigned to team inbox
                                        && staffAssociatedTeamMemberIds.Contains(cm.Conversation.AssignedTeamId.Value)) // The conversation is under the staff associated teams
                                    || mentionedConversationMessageQueryable.Select(mcm => mcm.Id).Contains(cm.Id));

                            if (companyUser.RoleType == StaffUserRole.Staff)
                            {
                                var isShowDefaultChannelMessagesOnly = false;

                                var staffPermission = await _appDbContext.CompanyRolePermissions
                                    .FirstOrDefaultAsync(
                                        x =>
                                            x.CompanyId == companyUser.CompanyId
                                            && x.StaffUserRole == companyUser.RoleType);
                                if (staffPermission != null)
                                {
                                    isShowDefaultChannelMessagesOnly =
                                        staffPermission.Permission.IsShowDefaultChannelMessagesOnly;
                                }

                                if (isShowDefaultChannelMessagesOnly)
                                {
                                    filteredConversationMessageQueryable = filteredConversationMessageQueryable
                                        .Where(
                                            cm => cm.Conversation.AssignedTeamId.HasValue
                                                  && (associatedTeam.Contains(cm.Conversation.AssignedTeamId.Value)
                                                      || cm.Conversation.AdditionalAssignees
                                                          .Any(
                                                              x => x.CompanyId == companyUser.CompanyId
                                                                   && x.AssigneeId == companyUser.Id)
                                                  ));
                                }
                                else
                                {
                                    filteredConversationMessageQueryable = filteredConversationMessageQueryable
                                        .Where(
                                            cm => cm.Conversation.AssigneeId == companyUser.Id
                                                  || (!cm.Conversation.AssignedTeamId.HasValue
                                                      && !cm.Conversation.AssigneeId.HasValue)
                                                  || (cm.Conversation.AssignedTeamId.HasValue
                                                      && associatedTeam.Contains(cm.Conversation.AssignedTeamId.Value)
                                                      && !cm.Conversation.AssigneeId.HasValue)
                                                  || cm.Conversation.AdditionalAssignees
                                                      .Any(
                                                          x => x.CompanyId == companyUser.CompanyId
                                                               && x.AssigneeId == companyUser.Id));
                                }
                            }

                            break;
                    }

                    break;

                case "mentioned":
                    var unixTime = DateTime.UtcNow.AddDays(-2).ToUnixTime();

                    var mentionQueryable = _mentionQueryableResolver
                        .GetMentionQueryable(_appDbContext, companyUser.Id, companyUser.CompanyId);

                    filteredConversationMessageQueryable = filteredConversationMessageQueryable
                        .Where(
                            cm =>
                                mentionQueryable.Select(m => m.ConversationId).Contains(cm.ConversationId)
                                && cm.Timestamp > unixTime);

                    break;

                case "unassigned":
                    filteredConversationMessageQueryable = filteredConversationMessageQueryable.Where(
                        cm =>
                            !cm.Conversation.AssigneeId.HasValue);

                    break;

                case "collaborator":
                    filteredConversationMessageQueryable = filteredConversationMessageQueryable.Where(
                        cm =>
                            cm.Conversation.AdditionalAssignees.Any(
                                x =>
                                    x.AssigneeId == companyUser.Id && x.CompanyId == companyUser.CompanyId));

                    break;

                case "team":
                    switch (companyUser.RoleType)
                    {
                        case StaffUserRole.Admin:
                        case StaffUserRole.DemoAdmin:
                        case StaffUserRole.TeamAdmin:
                            filteredConversationMessageQueryable = filteredConversationMessageQueryable
                                .Where(
                                    conversationMessage => conversationMessage.Conversation.AssignedTeamId == teamId);

                            break;
                        case StaffUserRole.Staff:
                            filteredConversationMessageQueryable = filteredConversationMessageQueryable
                                .Where(
                                    conversationMessage => conversationMessage.Conversation.AssignedTeamId == teamId &&
                                                           (!conversationMessage.Conversation.AssigneeId.HasValue ||
                                                            conversationMessage.Conversation.AssigneeId ==
                                                            companyUser.Id));

                            break;
                    }

                    break;

                default:
                    if (companyUser.RoleType != StaffUserRole.Admin &&
                        companyUser.RoleType != StaffUserRole.DemoAdmin &&
                        assignedTo != companyUser.IdentityId)
                    {
                        throw new Exception($"You cannot access conversations assigned to {assignedTo}");
                    }

                    var assignedToId = await _appDbContext.UserRoleStaffs
                        .Where(x => x.CompanyId == companyUser.CompanyId && x.IdentityId == assignedTo)
                        .Select(x => x.Id)
                        .FirstOrDefaultAsync();

                    filteredConversationMessageQueryable = filteredConversationMessageQueryable
                        .WhereIf(
                            behaviourVersion == "1",
                            cm =>
                                cm.Conversation.AssigneeId == assignedToId
                                || cm.Conversation.AdditionalAssignees.Any(
                                    x => x.CompanyId == companyUser.CompanyId && x.AssigneeId == assignedToId))
                        .WhereIf(
                            behaviourVersion == "2",
                            cm => cm.Conversation.AssigneeId == assignedToId);

                    break;
            }

            filteredConversationMessageQueryable = filteredConversationMessageQueryable.Take(10000);

            var chanelIds = new List<string>();

            if (!string.IsNullOrEmpty(channelIds))
            {
                chanelIds = channelIds.Split(",").ToList();
            }

            if (chanelIds.Count > 0)
            {
                try
                {
                    if (chanelIds.Contains(ChannelTypes.WhatsappTwilio))
                    {
                        var newChannelIds = new List<string>();
                        var newInstanceSender = new List<string>();

                        foreach (var myChannelId in chanelIds)
                        {
                            var twilioInstance = myChannelId.Split(";", StringSplitOptions.RemoveEmptyEntries);
                            newChannelIds.Add(twilioInstance[0]);

                            if (twilioInstance.Count() > 1)
                            {
                                newInstanceSender.Add(twilioInstance[1].Replace(": ", ":+"));
                            }
                        }

                        filteredConversationMessageQueryable = filteredConversationMessageQueryable
                            .Where(
                                cm =>
                                    newChannelIds.Contains(cm.whatsappReceiver.InstanceId) ||
                                    newChannelIds.Contains(cm.whatsappSender.InstanceId));

                        if (newInstanceSender.Count > 0)
                        {
                            filteredConversationMessageQueryable = filteredConversationMessageQueryable
                                .Where(
                                    cm =>
                                        newInstanceSender.Contains(cm.whatsappReceiver.InstaneSender) ||
                                        newInstanceSender.Contains(cm.whatsappSender.InstaneSender));
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Company {CompanyId} error filtering query by channelIds: {ChannelIds}. {ExceptionMessage}",
                        nameof(SearchConversationMessagesAsync),
                        companyUser?.CompanyId,
                        channelIds,
                        ex.Message);
                }
            }

            var conversationMessageIds = await _measureQueryTimeService.MeasureQueryTimeFromFuncIQueryable(
                () => filteredConversationMessageQueryable
                    .OrderByDescending(x => x.CreatedAt)
                    .Select(cm => cm.Id)
                    .Skip(offset)
                    .Take(limit),
                nameof(SearchConversationMessagesAsync));

            var conversationMessages = conversationMessageIds.Any()
                ? await _appDbContext.ConversationMessages
                    .Where(cm => conversationMessageIds.Contains(cm.Id))
                    .Include(x => x.UploadedFiles)
                    .Include(x => x.EmailFrom)
                    .Include(x => x.Sender)
                    .Include(x => x.facebookSender)
                    .Include(x => x.facebookReceiver)
                    .Include(x => x.whatsappSender)
                    .Include(x => x.whatsappReceiver)
                    .Include(x => x.WebClientSender)
                    .Include(x => x.WebClientReceiver)
                    .Include(x => x.MessageAssignee.Identity)
                    .Include(x => x.WeChatSender)
                    .Include(x => x.WeChatReceiver)
                    .Include(x => x.LineSender)
                    .Include(x => x.LineReceiver)
                    .Include(x => x.SMSSender)
                    .Include(x => x.SMSReceiver)
                    .Include(x => x.ViberSender)
                    .Include(x => x.ViberReceiver)
                    .Include(x => x.TelegramSender)
                    .Include(x => x.TelegramReceiver)
                    .Include(x => x.Whatsapp360DialogSender)
                    .Include(x => x.Whatsapp360DialogReceiver)
                    .Include(x => x.Whatsapp360DialogExtendedMessagePayload)
                    .Include(x => x.ExtendedMessagePayload)
                    .AsSplitQuery()
                    .AsNoTracking()
                    .ToListAsync()
                : new List<ConversationMessage>();

            // PII masking operations.
            if (await _piiMaskingService.IsConfiguredAsync(companyUser.CompanyId))
            {
                // Remove messages that match company PII masking pattern.
                var toBeRemovedMessageIds = new List<long>();
                foreach (var message in conversationMessages)
                {
                    // Only match messages from end users.
                    if (!message.IsSentFromSleekflow
                        && message.MessageContent is not null
                        && await _piiMaskingService.IsMatchAsync(companyUser.CompanyId, message.MessageContent))
                    {
                        toBeRemovedMessageIds.Add(message.Id);
                    }
                }

                conversationMessages.RemoveAll(x => toBeRemovedMessageIds.Contains(x.Id));
                filteredConversationMessageQueryable = filteredConversationMessageQueryable
                    .Where(cm => !toBeRemovedMessageIds.Contains(cm.Id));
            }

            var conversationIdToMessagesDict = conversationMessages
                .GroupBy(x => x.ConversationId)
                .Select(g => g.ToList())
                .ToDictionary(list => list[0].ConversationId, list => list);

            // conversation id list
            var conversationIds = conversationIdToMessagesDict.Keys.ToList();

            var conversations = await _appDbContext.Conversations
                .Where(x => conversationIds.Contains(x.Id))
                .Include(x => x.UserProfile)
                .Include(x => x.WhatsappUser)
                .Include(x => x.facebookUser)
                .Include(x => x.Assignee.Identity)
                .Include(x => x.EmailAddress)
                .Include(x => x.WebClient)
                .Include(x => x.WeChatUser)
                .Include(x => x.LineUser)
                .Include(x => x.SMSUser)
                .Include(x => x.WhatsApp360DialogUser)
                .Include(x => x.WhatsappCloudApiUser)
                .Include(x => x.TelegramUser)
                .Include(x => x.TikTokUser)
                .Include(x => x.ViberUser)
                .AsSplitQuery()
                .ToListAsync();

            // all fetched conversations
            var conversationViewModel = (await _conversationNoCompanyResponseViewModelMapper
                .ToViewModelsAsync(conversations))
                .ToDictionary(x => x.ConversationId, x => x);

            // related AdditionalAssignees group by conversationId
            var additionalAssigneeResponses = await _appDbContext.ConversationAdditionalAssignees
                .Include(x => x.Assignee.Identity)
                .Where(x => conversationIds.Contains(x.ConversationId))
                .ProjectTo<AdditionalAssigneeResponse>(_mapper.ConfigurationProvider)
                .GroupBy(x => x.ConversationId)
                .Select(g => g.ToList())
                .ToDictionaryAsync(list => list[0].ConversationId, list => list);

            // related ConversationHashtags group by conversationId
            var conversationHashtagResponses = await _appDbContext.ConversationHashtags
                .Include(x => x.Hashtag)
                .Where(x => conversationIds.Contains(x.ConversationId))
                .ProjectTo<ConversationHashtagResponse>(_mapper.ConfigurationProvider)
                .GroupBy(x => x.ConversationId)
                .Select(g => g.ToList())
                .ToDictionaryAsync(list => list[0].ConversationId, list => list);

            // get channel for each conversation
            var conversationsChannelList = await _appDbContext.ConversationMessages
                .Where(cm => conversationIds.Contains(cm.ConversationId))
                .Select(
                    cm => new
                    {
                        cm.ConversationId,
                        cm.UpdatedAt,
                        cm.Channel
                    })
                .GroupBy(cm => cm.ConversationId)
                .Select(
                    grp =>
                        grp.OrderByDescending(g => g.UpdatedAt).FirstOrDefault())
                .ToDictionaryAsync(
                    x => x.ConversationId,
                    x => new List<string>
                    {
                        x.Channel
                    });

            var results = new List<ConversationNoCompanyResponseViewModel>();

            // assemble messages to related conversation response view model
            foreach (var conversationId in conversationIds)
            {
                conversationViewModel[conversationId].AdditionalAssignees =
                    additionalAssigneeResponses.TryGetValue(conversationId, out var additionalAssignees)
                        ? additionalAssignees
                        : new List<AdditionalAssigneeResponse>();

                conversationViewModel[conversationId].ConversationHashtags =
                    conversationHashtagResponses.TryGetValue(conversationId, out var conversationHashtags)
                        ? conversationHashtags
                        : new List<ConversationHashtagResponse>();

                conversationViewModel[conversationId].ConversationChannels =
                    conversationsChannelList[conversationId];

                conversationViewModel[conversationId].Messages =
                    _mapper.Map<List<ConversationMessageResponseViewModel>>(
                        conversationIdToMessagesDict[conversationId]
                            .OrderByDescending(x => x.CreatedAt)
                            .ToList());
                await _sleekPayService.AddSleekPayRecord(conversationViewModel[conversationId].Messages);

                results.Add(conversationViewModel[conversationId]);
            }

            var response = new ConversationWithCountViewModel
            {
                Data = results.OrderByDescending(x => x.UpdatedTime).ToList(),
                Count = await filteredConversationMessageQueryable.CountAsync()
            };

            if (conversationIds.Any() && _isEnableTicketingLogic)
            {
                var userProfileIds = results.Select(x => x.UserProfile.Id).ToList();

                var ticketCount = await _ticketingHubMessageService.GetTicketingUserProfileCountAsync(
                    companyUser.CompanyId,
                    userProfileIds);

                if (ticketCount != null)
                {
                    response.Data = response.Data.GroupJoin(
                        ticketCount,
                        result => result.UserProfile.Id,
                        count => count.SleekflowUserProfileId,
                        (result, countGroup) => new ConversationNoCompanyResponseViewModel
                        {
                            ConversationId = result.ConversationId,
                            CompanyId = result.CompanyId,
                            ConversationChannels = result.ConversationChannels,
                            MessageGroupName = result.MessageGroupName,
                            UserProfile = result.UserProfile,
                            Status = result.Status,
                            Assignee = result.Assignee,
                            AdditionalAssignees = result.AdditionalAssignees,
                            ConversationHashtags = result.ConversationHashtags,
                            LastMessage = result.LastMessage,
                            Messages = result.Messages,
                            UpdatedTime = result.UpdatedTime,
                            ModifiedAt = result.ModifiedAt,
                            UnreadMessageCount = result.UnreadMessageCount,
                            SnoozeUntil = result.SnoozeUntil,
                            FirstMessageId = result.FirstMessageId,
                            LastMessageId = result.LastMessageId,
                            LastMessageChannel = result.LastMessageChannel,
                            LastChannelIdentityId = result.LastChannelIdentityId,
                            AssignedTeam = result.AssignedTeam,
                            IsSandbox = result.IsSandbox,
                            IsBookmarked = result.IsBookmarked,
                            Metadata = result.Metadata,
                            TicketCount = countGroup.Select(x => x.Count).DefaultIfEmpty(0).FirstOrDefault()
                        }).ToList();
                }
            }

            await _cacheManagerService.SaveCacheAsync(searchMessageCacheKeyPattern, response);

            return response;
        }

        public async Task<List<ConversationMessageOutput>> GetConversationLastMessagesAsync(
            string sleekflowCompanyId,
            string userProfileId,
            int offSet,
            int limit,
            List<string> targetedChannels,
            string channelIdentityId = null,
            DateTimeOffset? retrievalWindowTimestamp = null,
            DateTimeOffset? agentSessionStartAtTimestamp = null)
        {
            var validMessageStatus = new List<MessageStatus>
            {
                MessageStatus.Sent,
                MessageStatus.Received,
                MessageStatus.Read
            };

            var conversationMessages = await _appDbContext.ConversationMessages
                .AsNoTracking()
                .Where(c => c.CompanyId == sleekflowCompanyId
                            && c.Conversation.UserProfileId == userProfileId
                            && validMessageStatus.Contains(c.Status)
                            && targetedChannels.Contains(c.Channel))
                .WhereIf(!string.IsNullOrWhiteSpace(channelIdentityId), c => c.ChannelIdentityId == channelIdentityId)
                .WhereIf(agentSessionStartAtTimestamp.HasValue, c => c.Timestamp >= agentSessionStartAtTimestamp.Value.ToUnixTimeSeconds())
                .WhereIf(retrievalWindowTimestamp.HasValue, c => c.Timestamp <= retrievalWindowTimestamp.Value.ToUnixTimeSeconds())
                .Include(c => c.UploadedFiles)
                .OrderByDescending(c => c.Timestamp)
                .Skip(offSet)
                .Take(limit)
                .Select(c => new ConversationMessageOutput(
                    c.Id.ToString(),
                    c.MessageUniqueID,
                    c.MessageContent,
                    c.IsSentFromSleekflow,
                    DateTime.SpecifyKind(c.CreatedAt, DateTimeKind.Utc),
                    c.UploadedFiles
                        .Select(f => new ConversationMessageOutputFileDto(f.Url, f.MIMEType, f.FileSize ?? -1L))
                        .ToList()))
                .ToListAsync();

            conversationMessages.Reverse();

            return conversationMessages;
        }

        // get the related message id from the quoted message's Metadata
        public async Task<ConversationMessage?> GetOptInReplyTargetUndeliveredMessage(ConversationMessage receivedMessage)
        {
            if (receivedMessage.QuotedMsgId is null)
            {
                return null;
            }

            var conversationMessageQueryable = _appDbContext
                .ConversationMessages
                .AsNoTracking()
                .Where(x => x.CompanyId == receivedMessage.CompanyId)
                .Where(x => x.ConversationId == receivedMessage.ConversationId);

            var quotedMessage = await conversationMessageQueryable
                    .Where(x => x.MessageUniqueID == receivedMessage.QuotedMsgId)
                    .FirstOrDefaultAsync();

            if (quotedMessage is null)
            {
                return null;
            }

            var conversationMessageId = ExtractValueFromConversationMessageMetadata<long>(quotedMessage, "targetedConversationMessageId");

            if (conversationMessageId == 0)
            {
                return null;
            }

            var targetedUndeliveredMessage = await conversationMessageQueryable
                .Where(x => x.Id == conversationMessageId)
                .Where(x => x.Status == MessageStatus.Undelivered)
                .FirstOrDefaultAsync();

            return targetedUndeliveredMessage;
        }

        public T ExtractValueFromConversationMessageMetadata<T>(ConversationMessage conversationMessage, string key)
        {
            if (!conversationMessage.Metadata.TryGetValue(
                    key,
                    out var objectItem))
            {
                return default;
            }

            if (objectItem is T value)
            {
                return value;
            }

            try
            {
                return (T) Convert.ChangeType(objectItem, typeof(T));
            }
            catch (InvalidCastException)
            {
                return default;
            }
        }

        public async Task AddConversationMessageCache<T>(
            string conversationId,
            ConversationMessageCacheKeyPattern cacheKeyPattern,
            T data)
        {
            var masterCacheKeyPattern = new ConversationMessageMasterCacheKeyPattern(conversationId);

            var masterCacheKey = await _cacheManagerService.GetCacheAsync(masterCacheKeyPattern);
            var cacheKeyPatterns = new List<ConversationMessageCacheKeyPattern>();
            if (masterCacheKey != null)
            {
                cacheKeyPatterns =
                    JsonConvert.DeserializeObject<List<ConversationMessageCacheKeyPattern>>(masterCacheKey);
            }

            cacheKeyPatterns.Add(cacheKeyPattern);
            await _cacheManagerService.SaveCacheAsync(
                masterCacheKeyPattern,
                cacheKeyPatterns);

            await _cacheManagerService.SaveCacheAsync(
                cacheKeyPattern,
                data);
        }

        public async Task AddConversationMessageV2Cache<T>(
            string companyId,
            ConversationMessageV2CacheKeyPattern cacheKeyPattern,
            T data)
        {
            var masterCacheKeyPattern = new ConversationMessageV2MasterCacheKeyPattern(companyId);

            var masterCacheKey = await _cacheManagerService.GetCacheAsync(masterCacheKeyPattern);
            var cacheKeyPatterns = new List<ConversationMessageV2CacheKeyPattern>();
            if (masterCacheKey != null)
            {
                cacheKeyPatterns =
                    JsonConvert.DeserializeObject<List<ConversationMessageV2CacheKeyPattern>>(masterCacheKey);
            }

            cacheKeyPatterns.Add(cacheKeyPattern);
            await _cacheManagerService.SaveCacheAsync(
                masterCacheKeyPattern,
                cacheKeyPatterns);

            await _cacheManagerService.SaveCacheAsync(
                cacheKeyPattern,
                data);
        }

        public async Task AddGetConversationMessagesCache<T>(
            string conversationId,
            GetConversationMessagesCacheKeyPattern cacheKeyPattern,
            T data)
        {
            var masterCacheKeyPattern = new GetConversationMessagesMasterCacheKeyPattern(conversationId);

            var masterCacheKey = await _cacheManagerService.GetCacheAsync(masterCacheKeyPattern);
            var cacheKeyPatterns = new List<GetConversationMessagesCacheKeyPattern>();
            if (masterCacheKey != null)
            {
                cacheKeyPatterns =
                    JsonConvert.DeserializeObject<List<GetConversationMessagesCacheKeyPattern>>(masterCacheKey);
            }

            cacheKeyPatterns.Add(cacheKeyPattern);
            await _cacheManagerService.SaveCacheAsync(
                masterCacheKeyPattern,
                cacheKeyPatterns);

            await _cacheManagerService.SaveCacheAsync(
                cacheKeyPattern,
                data);
        }

        public async Task RemoveConversationMessageV2Cache(string companyId)
        {
            var v2MasterCacheKeyPattern = new ConversationMessageV2MasterCacheKeyPattern(companyId);

            var v2MasterCacheKey = await _cacheManagerService.GetCacheAsync(v2MasterCacheKeyPattern);
            if (v2MasterCacheKey != null)
            {
                await _cacheManagerService.DeleteCacheAsync(v2MasterCacheKeyPattern);
                var cacheKeyPatterns =
                    JsonConvert.DeserializeObject<List<ConversationMessageV2CacheKeyPattern>>(v2MasterCacheKey);
                foreach (var cacheKeyPattern in cacheKeyPatterns)
                {
                    await _cacheManagerService.DeleteCacheAsync(cacheKeyPattern);
                }
            }
        }

        public async Task RemoveConversationMessageCache(string conversationId)
        {
            var masterCacheKeyPattern = new ConversationMessageMasterCacheKeyPattern(conversationId);

            var masterCacheKey = await _cacheManagerService.GetCacheAsync(masterCacheKeyPattern);
            if (masterCacheKey != null)
            {
                await _cacheManagerService.DeleteCacheAsync(masterCacheKeyPattern);
                var cacheKeyPatterns =
                    JsonConvert.DeserializeObject<List<ConversationMessageCacheKeyPattern>>(masterCacheKey);
                foreach (var cacheKeyPattern in cacheKeyPatterns)
                {
                    await _cacheManagerService.DeleteCacheAsync(cacheKeyPattern);
                }
            }
        }

        public async Task RemoveCache(string companyId, string conversationId)
        {
            await RemoveConversationMessageCache(conversationId);
            await RemoveConversationMessageV2Cache(companyId);
            await RemoveGetConversationMessagesCache(conversationId);
        }

        public async Task RemoveGetConversationMessagesCache(string conversationId)
        {
            var masterCacheKeyPattern = new GetConversationMessagesMasterCacheKeyPattern(conversationId);

            var masterCacheKey = await _cacheManagerService.GetCacheAsync(masterCacheKeyPattern);
            if (masterCacheKey != null)
            {
                await _cacheManagerService.DeleteCacheAsync(masterCacheKeyPattern);
                var cacheKeyPatterns =
                    JsonConvert.DeserializeObject<List<GetConversationMessagesCacheKeyPattern>>(masterCacheKey);
                foreach (var cacheKeyPattern in cacheKeyPatterns)
                {
                    await _cacheManagerService.DeleteCacheAsync(cacheKeyPattern);
                }
            }
        }

        public async Task<Dictionary<string, ConversationMessage>> BulkGetLatestConversationMessageByConversationIdAsync(
            string companyId,
            List<string> conversationIds,
            CancellationToken cancellationToken)
        {
            return await _appDbContext.ConversationMessages
                .AsNoTracking()
                .Include(cm => cm.UploadedFiles)
                .Where(cm =>
                    cm.CompanyId == companyId &&
                    conversationIds.Contains(cm.ConversationId))
                .GroupBy(cm => cm.ConversationId)
                .Select(
                    g => g.OrderByDescending(cm => cm.Timestamp).FirstOrDefault())
                .ToDictionaryAsync(
                    cm => cm.ConversationId,
                    cm => cm,
                    cancellationToken: cancellationToken);
        }

        #region Rbac methods

        public async Task<Conversation> RbacChangeConversationAssignee(
            Conversation conversation,
            Staff assignee,
            bool isTriggerUpdate,
            string changedBy,
            UserProfileSource? userProfileSource = null)
        {
            var stopwatch = Stopwatch.StartNew();

            var originalContactOwner = await _accessControlAggregationService.GetStaffAccessControlAggregateAsync(
                conversation.AssigneeId,
                conversation.CompanyId);
            stopwatch.Stop();
            _logger.LogInformation(
                "[RbacChangeConversationAssignee] - Time taken to get original contact owner: {ElapsedMilliseconds} ms",
                stopwatch.ElapsedMilliseconds);

            stopwatch.Restart();
            var newContactOwner = await _accessControlAggregationService.GetStaffAccessControlAggregateAsync(assignee);
            stopwatch.Stop();
            _logger.LogInformation(
                "[RbacChangeConversationAssignee] - Time taken to get new contact owner: {ElapsedMilliseconds} ms",
                stopwatch.ElapsedMilliseconds);

            if (conversation.IsContactOwnerUnchanged(newContactOwner))
            {
                return conversation;
            }

            stopwatch.Restart();
            var aggregatedConversation =
                await _accessControlAggregationService.GetAggregatedConversationAsync(conversation);
            stopwatch.Stop();
            _logger.LogInformation(
                "[RbacChangeConversationAssignee] - Time taken to get aggregated conversation: {ElapsedMilliseconds} ms",
                stopwatch.ElapsedMilliseconds);

            stopwatch.Restart();
            var oldConversationListener = await _signalRService.GetSignalRTargetUserIds(
                conversation.CompanyId,
                conversation.Id,
                conversation.AssignedTeamId,
                conversation.AssigneeId,
                !conversation.AssigneeId.HasValue,
                isGetActiveUserOnly: false);
            stopwatch.Stop();
            _logger.LogInformation(
                "[RbacChangeConversationAssignee] - Time taken to get old conversation listener: {ElapsedMilliseconds} ms",
                stopwatch.ElapsedMilliseconds);

            stopwatch.Restart();
            // log assignee changed
            await _userProfileHooks.OnConversationAssigneeChangedAsync(
                conversation.CompanyId,
                conversation.UserProfileId,
                changedBy,
                async () =>
                {
                    var originalAssigneeData = conversation.AssigneeId.HasValue
                        ? await _appDbContext.UserRoleStaffs
                            .Where(x => x.Id == conversation.AssigneeId)
                            .Select(x => new StaffData(x.Id.ToString(), x.IdentityId, x.Identity.DisplayName))
                            .FirstOrDefaultAsync()
                        : null;

                    var newAssigneeData = assignee != null
                        ? await _appDbContext.UserRoleStaffs.Where(x => x.Id == assignee.Id)
                            .Select(x => new StaffData(x.Id.ToString(), x.IdentityId, x.Identity.DisplayName))
                            .FirstOrDefaultAsync()
                        : null;
                    return new OnConversationAssigneeChangedData(
                        originalAssigneeData,
                        newAssigneeData,
                        conversation.Id);
                });
            stopwatch.Stop();
            _logger.LogInformation(
                "[RbacChangeConversationAssignee] - Time taken to log assignee changed: {ElapsedMilliseconds} ms",
                stopwatch.ElapsedMilliseconds);

            stopwatch.Restart();
            conversation.Assignee = assignee;
            conversation.ModifiedAt = DateTime.UtcNow;
            await _appDbContext.SaveChangesAsync();
            stopwatch.Stop();
            _logger.LogInformation(
                "[RbacChangeConversationAssignee] - Time taken to save changes: {ElapsedMilliseconds} ms",
                stopwatch.ElapsedMilliseconds);

            stopwatch.Restart();
            var shouldRemainAsCollaborator =
                _inboxSettingManager.ShouldRemainAsCollaboratorWhenReassigned(
                    originalContactOwner,
                    newContactOwner,
                    aggregatedConversation);
            stopwatch.Stop();
            _logger.LogInformation(
                "[RbacChangeConversationAssignee] - Time taken to check collaborator status: {ElapsedMilliseconds} ms",
                stopwatch.ElapsedMilliseconds);

            if (shouldRemainAsCollaborator)
            {
                stopwatch.Restart();
                await AddCollaborator(conversation, originalContactOwner);
                stopwatch.Stop();
                _logger.LogInformation(
                    "[RbacChangeConversationAssignee] - Time taken to add collaborator: {ElapsedMilliseconds} ms",
                    stopwatch.ElapsedMilliseconds);
            }

            // Remove new contact owner from collaborators
            if (aggregatedConversation.HasCollaborator(newContactOwner))
            {
                stopwatch.Restart();
                await RemoveCollaborator(conversation, newContactOwner);
                stopwatch.Stop();
                _logger.LogInformation(
                    "[RbacChangeConversationAssignee] - Time taken to remove collaborator: {ElapsedMilliseconds} ms",
                    stopwatch.ElapsedMilliseconds);
            }

            stopwatch.Restart();
            var preUpdatedUserProfile = await _flowHubService.GetUserProfileDictAsync(
                conversation.CompanyId,
                conversation.UserProfileId);
            stopwatch.Stop();
            _logger.LogInformation(
                "[RbacChangeConversationAssignee] - Time taken to get pre-updated user profile: {ElapsedMilliseconds} ms",
                stopwatch.ElapsedMilliseconds);

            if (conversation.Assignee != null)
            {
                stopwatch.Restart();
                await _userProfileService.SetFieldValueByFieldNameSafe(
                    conversation.UserProfileId,
                    "ContactOwner",
                    assignee.IdentityId,
                    true);
                await _signalRService.SignalROnConversationAssigneeDeleted(conversation, oldConversationListener);
                await _signalRService.SignalROnConversationAssigneeChanged(conversation, isTriggerUpdate);
                stopwatch.Stop();
                _logger.LogInformation(
                    "[RbacChangeConversationAssignee] - Time taken to update user profile and signalR: {ElapsedMilliseconds} ms",
                    stopwatch.ElapsedMilliseconds);
            }
            else
            {
                stopwatch.Restart();
                BackgroundJob.Enqueue<IUserProfileService>(
                    x => x.SetFieldValueByFieldNameSafe(
                        conversation.UserProfileId,
                        "ContactOwner",
                        string.Empty,
                        true));
                await _signalRService.SignalROnConversationAssigneeDeleted(conversation);
                stopwatch.Stop();
                _logger.LogInformation(
                    "[RbacChangeConversationAssignee] - Time taken to enqueue background job and signalR: {ElapsedMilliseconds} ms",
                    stopwatch.ElapsedMilliseconds);
            }

            if (!conversation.AssignedTeamId.HasValue)
            {
                stopwatch.Restart();
                await _userProfileService.SetFieldValueByFieldNameSafe(
                    conversation.UserProfileId,
                    "AssignedTeam",
                    string.Empty,
                    true);
                stopwatch.Stop();
                _logger.LogInformation(
                    "[RbacChangeConversationAssignee] - Time taken to update assigned team: {ElapsedMilliseconds} ms",
                    stopwatch.ElapsedMilliseconds);
            }

            stopwatch.Restart();
            await _userProfileHooks.OnUserProfileFieldChangedAsync(
                conversation.CompanyId,
                conversation.UserProfileId,
                changedBy,
                async () =>
                {
                    var postUpdatedUserProfile = await _flowHubService.GetUserProfileDictAsync(
                        conversation.CompanyId,
                        conversation.UserProfileId);

                    return new OnUserProfileFieldChangedData(
                        postUpdatedUserProfile,
                        preUpdatedUserProfile,
                        "Updated");
                });
            stopwatch.Stop();
            _logger.LogInformation(
                "[RbacChangeConversationAssignee] - Time taken to handle user profile field change: {ElapsedMilliseconds} ms",
                stopwatch.ElapsedMilliseconds);

            if (conversation.ActiveStatus == ActiveStatus.Inactive)
            {
                return conversation;
            }

            try
            {
                if (conversation.ActiveStatus == ActiveStatus.Active
                    && conversation.Status == "open"
                    && DateTime.UtcNow.AddHours(-1) < conversation.UpdatedTime)
                {
                    stopwatch.Restart();
                    var conversationMessage = await _appDbContext.ConversationMessages
                        .OrderByDescending(x => x.CreatedAt)
                        .FirstOrDefaultAsync(
                            message =>
                                message.ConversationId == conversation.Id
                                && message.Channel != ChannelTypes.Note);
                    stopwatch.Stop();
                    _logger.LogInformation(
                        "[RbacChangeConversationAssignee] - Time taken to get conversation message: {ElapsedMilliseconds} ms",
                        stopwatch.ElapsedMilliseconds);

                    if (!conversationMessage.IsSentFromSleekflow)
                    {
                        stopwatch.Restart();
                        await _signalRService.SendNewMessagePushNotification(conversation, conversationMessage);
                        stopwatch.Stop();
                        _logger.LogInformation(
                            "[RbacChangeConversationAssignee] - Time taken to send push notification: {ElapsedMilliseconds} ms",
                            stopwatch.ElapsedMilliseconds);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Send new message push push notification error for conversation {ConversationId}: {ExceptionMessage}",
                    nameof(ChangeConversationAssignee),
                    conversation?.Id,
                    ex.Message);
            }

            stopwatch.Restart();
            await _cacheManagerService.DeleteCacheWithConstantKeyAsync($":1:conversation:{conversation.Id}");
            stopwatch.Stop();
            _logger.LogInformation(
                "[RbacChangeConversationAssignee] - Time taken to delete cache: {ElapsedMilliseconds} ms",
                stopwatch.ElapsedMilliseconds);

            return conversation;
        }

        private async Task RemoveCollaborator(Conversation conversation, StaffAccessControlAggregate newContactOwner)
        {
            try
            {
                conversation = await _conversationAssigneeService.RemoveAdditionalAssignees(
                    conversation,
                    [newContactOwner.StaffId]);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Remove additional assignee error for conversation {ConversationId}: {ExceptionString}",
                    nameof(ChangeConversationAssignee),
                    conversation?.Id,
                    ex.ToString());
            }
        }

        private async Task AddCollaborator(Conversation conversation, StaffAccessControlAggregate originalContactOwner)
        {
            try
            {
                conversation = await _conversationAssigneeService.AddAdditionalAssignees(
                    conversation,
                    [originalContactOwner.StaffId]);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Add to additional assignee error for conversation {ConversationId}: {ExceptionString}",
                    nameof(ChangeConversationAssignee),
                    conversation?.Id,
                    ex.ToString());
            }
        }

        public async Task<IQueryable<Conversation>> RbacGetConversations(
            string companyId,
            Staff companyUser,
            string status,
            string assignedTo,
            string channels,
            DateTime? afterUpdatedAt,
            DateTime? afterModifiedAt,
            string channelIds,
            string tags,
            long? teamId,
            bool? isTeamUnassigned,
            bool? isUnread,
            string orderBy,
            string version,
            bool? isAssigned,
            bool? isCollaborator)
        {
            var channelList = new List<string>();

            if (!string.IsNullOrEmpty(channels))
            {
                channelList = channels.Split(",").ToList();
            }

            var channelIdList = new List<string>();

            if (!string.IsNullOrEmpty(channelIds))
            {
                channelIdList = channelIds.Split(",").ToList();
            }

            var tagsList = new List<string>();

            if (!string.IsNullOrEmpty(tags))
            {
                tagsList = tags.Split(",").ToList();
            }

            var to = assignedTo;

            var staff = await _accessControlAggregationService.GetStaffAccessControlAggregateAsync(companyUser);

            var dbContext = _dbContextService.GetDbContext();

            var conversations = dbContext.Conversations.AsNoTracking()
                .Where(
                    x =>
                        x.CompanyId == companyId
                        && x.ActiveStatus == ActiveStatus.Active)
                .WhereIf(
                    status != "all" && status != "scheduled",
                    conversation => conversation.Status == status.ToLower())
                .WhereIf(
                    status == "scheduled",
                    conversation => conversation.ChatHistory.Any(x => x.Status == MessageStatus.Scheduled))
                .WhereIf(afterUpdatedAt.HasValue, conversation => conversation.UpdatedTime > afterUpdatedAt)
                .WhereIf(afterModifiedAt.HasValue, conversation => conversation.ModifiedAt > afterModifiedAt)
                .WhereIf(isAssigned.HasValue && isAssigned.Value, conversation => conversation.AssigneeId.HasValue)
                .WhereIf(isAssigned.HasValue && !isAssigned.Value, conversation => !conversation.AssigneeId.HasValue)
                .WhereIf(
                    isCollaborator.HasValue && isCollaborator.Value,
                    conversation =>
                        conversation.AdditionalAssignees.Any(
                            x => x.CompanyId == companyId && x.Assignee.IdentityId == to));

            if (tagsList.Count > 0)
            {
                var hashtagsList = await dbContext.CompanyDefinedHashtags
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && (tagsList.Contains(x.Hashtag) || tagsList.Contains(x.Id)))
                    .Select(x => x.Id)
                    .ToListAsync();

                conversations = conversations.Where(
                    conversation => conversation.conversationHashtags
                        .Any(
                            x => x.CompanyId == companyId
                                 && hashtagsList.Contains(x.HashtagId)));
            }

            if (isUnread.HasValue)
            {
                var personalUnreadConversationIds = dbContext.ConversationUnreadRecords
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && x.StaffId == staff.StaffId
                            && x.NotificationDeliveryStatus != NotificationDeliveryStatus.Read)
                    .Select(x => x.ConversationId);

                if (isUnread.Value)
                {
                    conversations = from conversation in conversations
                                    where conversation.UnreadMessageCount > 0
                                          || personalUnreadConversationIds.Contains(conversation.Id)
                                    select conversation;
                }
                else
                {
                    conversations = from conversation in conversations
                                    where conversation.UnreadMessageCount <= 0
                                          || personalUnreadConversationIds.Contains(conversation.Id)
                                    select conversation;
                }
            }

            var mentionQueryable =
                _mentionQueryableResolver.GetMentionQueryable(
                    dbContext,
                    staffId: staff.StaffId,
                    companyId: companyId);

            Expression<Func<Conversation, bool>> mentionExpression = c =>
                mentionQueryable.Select(m => m.ConversationId).Contains(c.Id);

            var viewConversationsSpec = new RbacViewConversationsSpecificationBuilder(staff).Build();
            var predicate = PredicateBuilder.New<Conversation>();
            predicate = predicate.Or(viewConversationsSpec.ToExpression());
            predicate = predicate.Or(c => mentionQueryable.Select(m => m.ConversationId).Contains(c.Id));
            conversations = conversations.Where(predicate);


            switch (assignedTo)
            {
                case "all":
                    break;
                case "unassigned":
                    conversations = conversations.Where(c => c.AssigneeId == null);

                    break;

                case "mentioned":
                    conversations = conversations.Where(mentionExpression);

                    break;
                case "team":
                    var teamInboxSpec = new TeamInboxSpecification(staff, teamId.Value);
                    conversations = conversations.Where(teamInboxSpec.ToExpression());

                    if (isTeamUnassigned.HasValue)
                    {
                        conversations = conversations.Where(c => c.AssigneeId == null);
                    }

                    break;
                case "collaborator":
                    var collaboratorSpec = new CollaborationsSpecification(staff);
                    conversations = conversations.Where(collaboratorSpec.ToExpression());

                    break;
                case "bookmarked":
                    conversations = conversations
                        .Where(conversation => conversation.ConversationBookmarks.Any(x => x.StaffId == staff.StaffId));
                    break;
                default:
                    // User IdentityId
                    if (isCollaborator.HasValue && isCollaborator.Value)
                    {
                        break;
                    }

                    if (assignedTo == "you")
                    {
                        assignedTo = staff.StaffIdentityId;
                    }

                    var tryingToAccessStaff =
                        await _accessControlAggregationService.GetStaffAccessControlAggregateAsync(
                            assignedTo,
                            staff.CompanyId);

                    var hasAccess = staff.HasAccessToViewOtherStaffOrSelfConversations(tryingToAccessStaff);
                    if (!hasAccess)
                    {
                        throw new Exception($"You cannot access conversations assigned to {assignedTo}");
                    }

                    // If only assigned to me then no need to check teammates as contact owner or collaborator
                    conversations = conversations
                        .WhereIf(
                            version == "1",
                            conversation =>
                                conversation.AssigneeId == tryingToAccessStaff.StaffId
                                || conversation.AdditionalAssignees.Any(
                                    x => x.CompanyId == companyId && x.AssigneeId == tryingToAccessStaff.StaffId))
                        .WhereIf(
                            version == "2",
                            conversation => conversation.AssigneeId == tryingToAccessStaff.StaffId)
                        .WhereIf(
                            tryingToAccessStaff.StaffId != staff.StaffId && // Assign to me allow both contact owner and collaborator for self to view
                            staff.HasAccessToViewOtherStaffAsContactOwnerConversationsOnly(),
                            c => c.AssigneeId == tryingToAccessStaff.StaffId);

                    break;
            }

            // Filter channel at last
            if (!string.IsNullOrEmpty(channels))
            {
                conversations = FilterByChannel(
                    companyId,
                    channels,
                    channelIdList,
                    dbContext,
                    conversations);
            }

            return ApplyOrderBy(conversations, orderBy, companyUser);
        }

        public async Task<ConversationWithCountViewModel> RbacGetFormattedConversations(
            Staff companyUser,
            string assignedTo,
            int offset,
            int limit,
            string status,
            string channels,
            DateTime? afterUpdatedAt,
            DateTime? afterModifiedAt,
            string channelIds,
            string tags,
            long? teamId,
            bool? isTeamUnassigned,
            bool? isUnread,
            string orderBy,
            string version,
            bool? isAssigned,
            bool? isCollaborator)
        {
            try
            {
                _logger.LogInformation(
                    $"GetConversations_{companyUser.CompanyId}_{companyUser.Id}_{assignedTo}_{offset}_{limit}_{status}_{channels}_{afterUpdatedAt}_{afterModifiedAt}_{channelIds}_{tags}_{teamId}_{isTeamUnassigned}_{isUnread}_{orderBy}_{isAssigned}_{isCollaborator}");

                if (limit > 100)
                {
                    limit = 100;
                }

                var conversationMessageV2CacheKeyPattern = new ConversationMessageV2CacheKeyPattern(
                    companyUser.Id,
                    assignedTo,
                    offset,
                    limit,
                    status,
                    channels,
                    afterUpdatedAt,
                    afterModifiedAt,
                    channelIds,
                    tags,
                    teamId,
                    isTeamUnassigned,
                    isUnread,
                    orderBy,
                    version,
                    isAssigned,
                    isCollaborator);

                var data = await _cacheManagerService.GetCacheAsync(conversationMessageV2CacheKeyPattern);

                if (!string.IsNullOrEmpty(data))
                {
                    return JsonConvert.DeserializeObject<ConversationWithCountViewModel>(data);
                }

                if (companyUser != null)
                {
                    _logger.LogInformation(
                        $"{companyUser.IdentityId} GetConversations: assignTo {assignedTo}, offset: {offset}, limit: {limit}");

                    try
                    {
                        var conversationsQueryable = await GetConversations(
                            companyUser.CompanyId,
                            companyUser,
                            status,
                            assignedTo,
                            channels,
                            afterUpdatedAt,
                            afterModifiedAt,
                            channelIds,
                            tags,
                            teamId,
                            isTeamUnassigned,
                            isUnread,
                            orderBy,
                            version,
                            isAssigned,
                            isCollaborator);

                        var channelList = new List<string>();

                        if (!string.IsNullOrEmpty(channels))
                        {
                            channelList = channels
                                .Split(",")
                                .ToList();
                        }

                        var channelIdList = new List<string>();

                        if (!string.IsNullOrEmpty(channelIds))
                        {
                            channelIdList = channelIds
                                .Split(",")
                                .ToList();
                        }

                        var conversationIds = await conversationsQueryable
                            .Select(c => c.Id)
                            .Skip(offset)
                            .Take(limit)
                            .ToListAsync();

                        var filteredConversations = conversationIds.Count != 0
                            ? _appDbContext.Conversations.Where(c => conversationIds.Contains(c.Id))
                            : _appDbContext.Conversations.Where(c => false);

                        var conversations = conversationIds.Any()
                            ? await filteredConversations
                                .Where(c => conversationIds.Contains(c.Id))
                                .Include(x => x.UserProfile)
                                .Include(x => x.facebookUser)
                                .Include(x => x.WhatsappUser)
                                .Include(x => x.Assignee.Identity)
                                .Include(x => x.EmailAddress)
                                .Include(x => x.WebClient)
                                .Include(x => x.WeChatUser)
                                .Include(x => x.LineUser)
                                .Include(x => x.SMSUser)
                                .Include(x => x.InstagramUser)
                                .Include(x => x.WhatsApp360DialogUser)
                                .Include(x => x.WhatsappCloudApiUser)
                                .Include(x => x.TelegramUser)
                                .Include(x => x.TikTokUser)
                                .Include(x => x.ViberUser)
                                .Include(x => x.AssignedTeam)
                                .Include(x => x.ConversationBookmarks)
                                .ToListAsync()
                            : new List<Conversation>();

                        conversations = ApplyOrderBy(conversations, orderBy, companyUser);

                        // Bookmarks
                        var bookmarkedConversationIds =
                            conversationIds.Any()
                                ? (await _appDbContext.ConversationBookmarks
                                    .Where(
                                        x =>
                                            conversationIds.Contains(x.ConversationId)
                                            && x.StaffId == companyUser.Id)
                                    .Select(x => x.ConversationId)
                                    .ToListAsync())
                                .ToHashSet()
                                : new HashSet<string>();
                        conversations.ForEach(
                            x => x.IsBookmarked = bookmarkedConversationIds.Contains(x.Id));

                        // Unread messages
                        var conversationIdToUnreadCount =
                            conversationIds.Any()
                                ? await _appDbContext.ConversationUnreadRecords
                                    .Where(
                                        x =>
                                            x.CompanyId == companyUser.CompanyId
                                            && conversationIds.Contains(x.ConversationId)
                                            && x.StaffId == companyUser.Id
                                            && x.NotificationDeliveryStatus != NotificationDeliveryStatus.Read)
                                    .GroupBy(x => x.ConversationId)
                                    .Select(
                                        g => new
                                        {
                                            ConversationId = g.Key,
                                            Count = g.Count()
                                        })
                                    .ToDictionaryAsync(x => x.ConversationId, x => x.Count)
                                : new Dictionary<string, int>();
                        conversations
                            .Where(x => x.UnreadMessageCount == 0)
                            .ForEach(
                                x => x.UnreadMessageCount = conversationIdToUnreadCount.TryGetValue(x.Id, out var value)
                                    ? value
                                    : 0);

                        var associatedTeams = await _appDbContext.CompanyStaffTeams
                            .Where(x => x.Members.Any(y => y.StaffId == companyUser.Id))
                            .ToListAsync();

                        var whatsappIds = new List<string>();
                        var twilioSenderIds = new List<string>();
                        var whatsapp360dialogDefaultChannelIds = new List<long>();
                        var whatsappCloudDefaultChannelIds = new List<string>();

                        foreach (var associatedTeam in associatedTeams)
                        {
                            if (associatedTeam.DefaultChannels?.Count > 0)
                            {
                                foreach (var defaultChannel in associatedTeam.DefaultChannels
                                             .Where(
                                                 x =>
                                                     x.channel != ChannelTypes.Whatsapp360Dialog
                                                     && x.channel != ChannelTypes.WhatsappCloudApi))
                                {
                                    channelList.Add(defaultChannel.channel);

                                    if (defaultChannel.ids != null)
                                    {
                                        foreach (var id in defaultChannel.ids)
                                        {
                                            var twilioInstance = id.Split(";", StringSplitOptions.RemoveEmptyEntries);
                                            whatsappIds.Add(twilioInstance[0]);

                                            if (twilioInstance.Count() > 1)
                                            {
                                                twilioSenderIds.Add(twilioInstance[1].Replace(": ", ":+"));
                                            }
                                        }
                                    }
                                }

                                foreach (var defaultChannel in associatedTeam.DefaultChannels
                                             .Where(x => x.channel == ChannelTypes.Whatsapp360Dialog))
                                {
                                    foreach (var channelId in defaultChannel.ids)
                                    {
                                        var validLong = long.TryParse(channelId, out var whatsapp360dialogChannelId);

                                        if (validLong)
                                        {
                                            whatsapp360dialogDefaultChannelIds.Add(whatsapp360dialogChannelId);
                                        }
                                    }
                                }

                                foreach (var defaultChannel in associatedTeam.DefaultChannels
                                             .Where(x => x.channel == ChannelTypes.WhatsappCloudApi))
                                {
                                    foreach (var channelId in defaultChannel.ids)
                                    {
                                        whatsappCloudDefaultChannelIds.Add(channelId);
                                    }
                                }
                            }
                        }

                        var conversationViewModels =
                            await _conversationNoCompanyResponseViewModelViewModelMapper.ToViewModelsAsync(conversations);

                        var conversationDict =
                            (await _accessControlAggregationService.GetAggregatedConversationsAsync(conversations))
                            .ToDictionary(conversation => conversation.Id, conversation => conversation);

                        var collaborators =
                            conversationIds.Any()
                                ? await _appDbContext.ConversationAdditionalAssignees
                                    .Where(
                                        y => conversationViewModels
                                            .Select(x => x.ConversationId)
                                            .Contains(y.ConversationId))
                                    .Include(y => y.Assignee.Identity)
                                    .ProjectTo<AdditionalAssigneeResponse>(_mapper.ConfigurationProvider)
                                    .ToListAsync()
                                : new List<AdditionalAssigneeResponse>();
                        conversationViewModels.ForEach(
                            x =>
                                x.AdditionalAssignees = collaborators
                                    .Where(y => y.ConversationId == x.ConversationId)
                                    .ToList());

                        var hashtags =
                            conversationIds.Any()
                                ? await _appDbContext.ConversationHashtags
                                    .Where(
                                        y => conversationViewModels
                                            .Select(x => x.ConversationId)
                                            .Contains(y.ConversationId))
                                    .Include(y => y.Hashtag)
                                    .OrderByDescending(x => x.Id)
                                    .ProjectTo<ConversationHashtagResponse>(_mapper.ConfigurationProvider)
                                    .ToListAsync()
                                : new List<ConversationHashtagResponse>();

                        conversationViewModels.ForEach(
                            x =>
                                x.ConversationHashtags = hashtags
                                    .Where(y => y.ConversationId == x.ConversationId)
                                    .ToList());

                        var staff = await _accessControlAggregationService.GetStaffAccessControlAggregateAsync(
                            companyUser);

                        // Rbac changes
                        var filterLastMessage = staff.HasDefaultChannelSettingEnabled();

                        var isUpdatedLastMessageId = false;

                        var lastMessageIds = conversationViewModels.Select(c => c.LastMessageId).ToList();

                        var lastMessages = await _appDbContext.ConversationMessages
                            .AsNoTracking()
                            .Where(x => lastMessageIds.Contains(x.Id))
                            .Include(x => x.whatsappReceiver)
                            .Include(x => x.whatsappSender)
                            .Include(x => x.Whatsapp360DialogReceiver)
                            .Include(x => x.Whatsapp360DialogSender)
                            .Include(x => x.facebookReceiver)
                            .Include(x => x.facebookSender)
                            .Include(x => x.InstagramReceiver)
                            .Include(x => x.InstagramSender)
                            .ToListAsync();

                        var conversationWithLastMessageViewModels = await _conversationService.GetConversationLastMessages(
                            conversationViewModels,
                            filterLastMessage,
                            whatsappIds,
                            whatsapp360dialogDefaultChannelIds,
                            channelIdList,
                            channelList,
                            twilioSenderIds,
                            whatsappCloudDefaultChannelIds);

                        var lastMessageByConversationId = lastMessages
                            .GroupBy(msg => msg.ConversationId) // Group messages by their ConversationId
                            .ToDictionary(
                                group => group.Key,          // The key of the dictionary is the ConversationId
                                group => group.ToList()); // The value is the list of messages in that group

                        foreach (var conversationWithLastMessageViewModel in conversationWithLastMessageViewModels)
                        {
                            conversationWithLastMessageViewModel.StaffConversationPermission = await _conversationPermissionControlService.GetStaffConversationPermissionAsync(
                                staff,
                                conversationDict[conversationWithLastMessageViewModel.ConversationId]);

                            ConversationMessage lastMessage = null;

                            if (conversationWithLastMessageViewModel.UnreadMessageCount > 0
                                && filterLastMessage
                                && conversationWithLastMessageViewModel.LastMessageId.HasValue
                                && (whatsappIds.Any()
                                    || whatsapp360dialogDefaultChannelIds.Any()
                                    || whatsappCloudDefaultChannelIds.Any()))
                            {
                                if (lastMessageByConversationId.TryGetValue(
                                        conversationWithLastMessageViewModel.ConversationId,
                                        out var tempLastMessages))
                                {
                                    // Key was found, assign the retrieved value
                                    conversationWithLastMessageViewModel.LastMessage =
                                        _mapper.Map<List<ConversationMessageResponseViewModel>>(tempLastMessages);
                                    lastMessage = tempLastMessages.FirstOrDefault();
                                }
                                else
                                {
                                    // Key was not found, assign a default (e.g., null or an empty list)
                                    // Choose based on the type of conversation.LastMessage and how you handle missing data
                                    conversationWithLastMessageViewModel.LastMessage = null;
                                }

                                switch (lastMessage.Channel)
                                {
                                    case ChannelTypes.WhatsappTwilio:
                                        if ((lastMessage.whatsappReceiver != null &&
                                             !whatsappIds.Contains(lastMessage.whatsappReceiver.InstanceId)) ||
                                            (lastMessage.whatsappSender != null &&
                                             !whatsappIds.Contains(lastMessage.whatsappSender.InstanceId)))
                                        {
                                            conversationWithLastMessageViewModel.UnreadMessageCount = 0;
                                        }

                                        break;
                                    case ChannelTypes.Whatsapp360Dialog:
                                        if ((lastMessage.Whatsapp360DialogReceiver != null &&
                                             lastMessage.Whatsapp360DialogReceiver.ChannelId.HasValue &&
                                             !whatsapp360dialogDefaultChannelIds.Contains(
                                                 lastMessage.Whatsapp360DialogReceiver.ChannelId.Value)) ||
                                            (lastMessage.Whatsapp360DialogSender != null &&
                                             lastMessage.Whatsapp360DialogSender.ChannelId.HasValue &&
                                             !whatsapp360dialogDefaultChannelIds.Contains(
                                                 lastMessage.Whatsapp360DialogSender.ChannelId.Value)))
                                        {
                                            conversationWithLastMessageViewModel.UnreadMessageCount = 0;
                                        }

                                        break;
                                    case ChannelTypes.WhatsappCloudApi:
                                        if (!whatsappCloudDefaultChannelIds.Contains(lastMessage.ChannelIdentityId))
                                        {
                                            conversationWithLastMessageViewModel.UnreadMessageCount = 0;
                                        }

                                        break;
                                    case ChannelTypes.Facebook:
                                        if ((lastMessage.facebookReceiver != null &&
                                             lastMessage.facebookReceiver.pageId != null &&
                                             !whatsappIds.Contains(lastMessage.facebookReceiver.pageId)) ||
                                            (lastMessage.facebookSender != null &&
                                             lastMessage.facebookSender.pageId != null &&
                                             !whatsappIds.Contains(lastMessage.facebookSender.pageId)))
                                        {
                                            conversationWithLastMessageViewModel.UnreadMessageCount = 0;
                                        }

                                        break;
                                    case ChannelTypes.Instagram:
                                        if ((lastMessage.InstagramReceiver != null &&
                                             lastMessage.InstagramReceiver.InstagramPageId != null &&
                                             !whatsappIds.Contains(lastMessage.InstagramReceiver.InstagramPageId)) ||
                                            (lastMessage.InstagramSender != null &&
                                             lastMessage.InstagramSender.InstagramPageId != null &&
                                             !whatsappIds.Contains(lastMessage.InstagramSender.InstagramPageId)))
                                        {
                                            conversationWithLastMessageViewModel.UnreadMessageCount = 0;
                                        }

                                        break;
                                }
                            }
                        }

                        if (isUpdatedLastMessageId)
                        {
                            await _appDbContext.SaveChangesAsync();
                        }

                        var conversationIdsForMappedResult = conversationWithLastMessageViewModels
                            .Select(x => x.ConversationId)
                            .ToList();

                        var firstMessageIdsForMappedResult = await _appDbContext.ConversationMessages
                            .Where(y => conversationIdsForMappedResult.Contains(y.ConversationId))
                            .GroupBy(y => y.ConversationId)
                            .Select(
                                g => new
                                {
                                    ConversationId = g.Key,
                                    Id = g.Min(y => y.Id) // Get the smallest Id (first message) per conversation
                                })
                            .ToDictionaryAsync(
                                x => x.ConversationId,
                                x => x.Id); // Convert to dictionary for performance

                        foreach (var item in conversationWithLastMessageViewModels)
                        {
                            if (firstMessageIdsForMappedResult.TryGetValue(item.ConversationId, out var firstMessageId))
                            {
                                item.FirstMessageId = firstMessageId;
                            }
                            else
                            {
                                // Handle the situation when no message is found for the conversation
                            }
                        }

                        var results = new ConversationWithCountViewModel
                        {
                            Data = conversationWithLastMessageViewModels,
                            Count = await conversationsQueryable.CountAsync()
                        };

                        if (conversationIds.Any() && _isEnableTicketingLogic)
                        {
                            var userProfileIds = results.Data.Select(x => x.UserProfile.Id).ToList();
                            var ticketCount = await _ticketingHubMessageService.GetTicketingUserProfileCountAsync(
                                companyUser.CompanyId,
                                userProfileIds);

                            if (ticketCount != null)
                            {
                                results.Data = results.Data.GroupJoin(
                                    ticketCount,
                                    result => result.UserProfile.Id,
                                    count => count.SleekflowUserProfileId,
                                    (result, countGroup) => new ConversationNoCompanyResponseViewModel
                                    {
                                        ConversationId = result.ConversationId,
                                        CompanyId = result.CompanyId,
                                        ConversationChannels = result.ConversationChannels,
                                        MessageGroupName = result.MessageGroupName,
                                        UserProfile = result.UserProfile,
                                        Status = result.Status,
                                        Assignee = result.Assignee,
                                        AdditionalAssignees = result.AdditionalAssignees,
                                        ConversationHashtags = result.ConversationHashtags,
                                        LastMessage = result.LastMessage,
                                        Messages = result.Messages,
                                        UpdatedTime = result.UpdatedTime,
                                        ModifiedAt = result.ModifiedAt,
                                        UnreadMessageCount = result.UnreadMessageCount,
                                        SnoozeUntil = result.SnoozeUntil,
                                        FirstMessageId = result.FirstMessageId,
                                        LastMessageId = result.LastMessageId,
                                        LastMessageChannel = result.LastMessageChannel,
                                        LastChannelIdentityId = result.LastChannelIdentityId,
                                        AssignedTeam = result.AssignedTeam,
                                        IsSandbox = result.IsSandbox,
                                        IsBookmarked = result.IsBookmarked,
                                        Metadata = result.Metadata,
                                        TicketCount = countGroup.Select(x => x.Count).DefaultIfEmpty(0).FirstOrDefault()
                                    }).ToList();
                            }
                        }

                        await _cacheManagerService.SaveCacheAsync(
                            conversationMessageV2CacheKeyPattern,
                            results);

                        return results;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[{MethodName}] inner error: {ExceptionMessage}",
                            nameof(GetFormattedConversations),
                            ex.Message);

                        return new ConversationWithCountViewModel();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] outer error: {ExceptionMessage}",
                    nameof(GetFormattedConversations),
                    ex.Message);

                return new ConversationWithCountViewModel();
            }

            return new ConversationWithCountViewModel();
        }

        public async Task<ConversationWithCountViewModel> RbacSearchConversationMessagesAsync(
            Staff companyUser,
            string assignedTo,
            string keywords,
            int offset,
            int limit,
            string status,
            string channels,
            string channelIds,
            long? teamId,
            string behaviourVersion,
            bool isAllowCache)
        {
            var dbContext = _dbContextService.GetDbContext();

            var searchMessageCacheKeyPattern = new SearchMessageCacheKeyPattern(
                companyUser.Id,
                assignedTo,
                offset,
                limit,
                keywords,
                status,
                channels,
                channelIds,
                teamId,
                behaviourVersion);

            if (isAllowCache)
            {
                var data = await _cacheManagerService.GetCacheAsync(searchMessageCacheKeyPattern);

                if (data != null)
                {
                    return JsonConvert.DeserializeObject<ConversationWithCountViewModel>(data);
                }
            }

            while (true)
            {
                var myLock = await _lockService.AcquireLockAsync(
                    $"lock:v2_search_message{searchMessageCacheKeyPattern.GenerateKeyPattern()}",
                    TimeSpan.FromSeconds(15));

                if (myLock == null)
                {
                    if (isAllowCache)
                    {
                        var cacheData = await _cacheManagerService.GetCacheAsync(searchMessageCacheKeyPattern);

                        if (cacheData != null)
                        {
                            return JsonConvert.DeserializeObject<ConversationWithCountViewModel>(
                                cacheData);
                        }
                    }

                    await Task.Delay(5000);
                }
                else
                {
                    break;
                }
            }

            var channelList = new List<string>();

            if (!string.IsNullOrEmpty(channels))
            {
                channelList = channels.Split(",").ToList();
            }

            var staff = await _accessControlAggregationService.GetStaffAccessControlAggregateAsync(companyUser);
            var tryingToAccessStaff = InboxAssignedTos.IsUserIdentityIdFilter(assignedTo)
                ? await _accessControlAggregationService.GetStaffAccessControlAggregateAsync(assignedTo, staff.CompanyId)
                : null;

            var filteredConversationMessageQueryable = new SearchConversationMessageQueryableBuilder(dbContext, _mentionQueryableResolver)
                .Build(
                    companyId: companyUser.CompanyId,
                    assignedTo: assignedTo,
                    keywords: keywords,
                    status: status,
                    teamId: teamId,
                    behaviourVersion: behaviourVersion,
                    channelList: channelList,
                    staff,
                    tryingToAccessStaff);

            filteredConversationMessageQueryable = filteredConversationMessageQueryable.Take(10000);

            var chanelIds = new List<string>();

            if (!string.IsNullOrEmpty(channelIds))
            {
                chanelIds = channelIds.Split(",").ToList();
            }

            if (chanelIds.Count > 0)
            {
                try
                {
                    if (chanelIds.Contains(ChannelTypes.WhatsappTwilio))
                    {
                        var newChannelIds = new List<string>();
                        var newInstanceSender = new List<string>();

                        foreach (var myChannelId in chanelIds)
                        {
                            var twilioInstance = myChannelId.Split(";", StringSplitOptions.RemoveEmptyEntries);
                            newChannelIds.Add(twilioInstance[0]);

                            if (twilioInstance.Count() > 1)
                            {
                                newInstanceSender.Add(twilioInstance[1].Replace(": ", ":+"));
                            }
                        }

                        filteredConversationMessageQueryable = filteredConversationMessageQueryable
                            .Where(
                                cm =>
                                    newChannelIds.Contains(cm.whatsappReceiver.InstanceId) ||
                                    newChannelIds.Contains(cm.whatsappSender.InstanceId));

                        if (newInstanceSender.Count > 0)
                        {
                            filteredConversationMessageQueryable = filteredConversationMessageQueryable
                                .Where(
                                    cm =>
                                        newInstanceSender.Contains(cm.whatsappReceiver.InstaneSender) ||
                                        newInstanceSender.Contains(cm.whatsappSender.InstaneSender));
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Company {CompanyId} error filtering query by channelIds: {ChannelIds}. {ExceptionMessage}",
                        nameof(SearchConversationMessagesAsync),
                        companyUser?.CompanyId,
                        channelIds,
                        ex.Message);
                }
            }

            var conversationMessageIds = await _measureQueryTimeService.MeasureQueryTimeFromFuncIQueryable(
                () => filteredConversationMessageQueryable
                    .OrderByDescending(x => x.CreatedAt)
                    .Select(cm => cm.Id)
                    .Skip(offset)
                    .Take(limit),
                nameof(SearchConversationMessagesAsync));

            var conversationMessages = conversationMessageIds.Any()
                ? await _appDbContext.ConversationMessages
                    .Where(cm => conversationMessageIds.Contains(cm.Id))
                    .Include(x => x.UploadedFiles)
                    .Include(x => x.EmailFrom)
                    .Include(x => x.Sender)
                    .Include(x => x.facebookSender)
                    .Include(x => x.facebookReceiver)
                    .Include(x => x.whatsappSender)
                    .Include(x => x.whatsappReceiver)
                    .Include(x => x.WebClientSender)
                    .Include(x => x.WebClientReceiver)
                    .Include(x => x.MessageAssignee.Identity)
                    .Include(x => x.WeChatSender)
                    .Include(x => x.WeChatReceiver)
                    .Include(x => x.LineSender)
                    .Include(x => x.LineReceiver)
                    .Include(x => x.SMSSender)
                    .Include(x => x.SMSReceiver)
                    .Include(x => x.ViberSender)
                    .Include(x => x.ViberReceiver)
                    .Include(x => x.TelegramSender)
                    .Include(x => x.TelegramReceiver)
                    .Include(x => x.Whatsapp360DialogSender)
                    .Include(x => x.Whatsapp360DialogReceiver)
                    .Include(x => x.Whatsapp360DialogExtendedMessagePayload)
                    .Include(x => x.ExtendedMessagePayload)
                    .AsSplitQuery()
                    .AsNoTracking()
                    .ToListAsync()
                : new List<ConversationMessage>();

            // PII masking operations.
            if (await _piiMaskingService.IsConfiguredAsync(companyUser.CompanyId))
            {
                // Remove messages that match company PII masking pattern.
                var toBeRemovedMessageIds = new List<long>();
                foreach (var message in conversationMessages)
                {
                    // Only match messages from end users.
                    if (!message.IsSentFromSleekflow
                        && message.MessageContent is not null
                        && await _piiMaskingService.IsMatchAsync(companyUser.CompanyId, message.MessageContent))
                    {
                        toBeRemovedMessageIds.Add(message.Id);
                    }
                }

                conversationMessages.RemoveAll(x => toBeRemovedMessageIds.Contains(x.Id));
                filteredConversationMessageQueryable = filteredConversationMessageQueryable
                    .Where(cm => !toBeRemovedMessageIds.Contains(cm.Id));
            }

            var conversationIdToMessagesDict = conversationMessages
                .GroupBy(x => x.ConversationId)
                .Select(g => g.ToList())
                .ToDictionary(list => list[0].ConversationId, list => list);

            // conversation id list
            var conversationIds = conversationIdToMessagesDict.Keys.ToList();

            // Fetch conversations with necessary includes
            var conversations = await _appDbContext.Conversations
                .Where(x => conversationIds.Contains(x.Id))
                .Include(x => x.UserProfile)
                .Include(x => x.WhatsappUser)
                .Include(x => x.facebookUser)
                .Include(x => x.Assignee.Identity)
                .Include(x => x.EmailAddress)
                .Include(x => x.WebClient)
                .Include(x => x.WeChatUser)
                .Include(x => x.LineUser)
                .Include(x => x.SMSUser)
                .Include(x => x.WhatsApp360DialogUser)
                .Include(x => x.WhatsappCloudApiUser)
                .Include(x => x.TelegramUser)
                .Include(x => x.TikTokUser)
                .Include(x => x.ViberUser)
                .AsSplitQuery()
                .ToListAsync();

            // Map the fetched conversations to ConversationNoCompanyResponseViewModel objects
            var conversationViewModelList = await _conversationNoCompanyResponseViewModelViewModelMapper.ToViewModelsAsync(conversations);

            var conversationViewModel = conversationViewModelList
                .ToDictionary(x => x.ConversationId, x => x);

            // Fetch ConversationAdditionalAssignees with necessary includes
            var conversationAdditionalAssignees = await _appDbContext.ConversationAdditionalAssignees
                .Include(x => x.Assignee.Identity)
                .Where(x => conversationIds.Contains(x.ConversationId))
                .ToListAsync();

            // Map the fetched ConversationAdditionalAssignees to AdditionalAssigneeResponse objects
            var additionalAssigneeResponses = _mapper.Map<List<AdditionalAssigneeResponse>>(conversationAdditionalAssignees)
                .GroupBy(x => x.ConversationId)
                .Select(g => g.ToList())
                .ToDictionary(list => list[0].ConversationId, list => list);

            // related ConversationHashtags group by conversationId
            var conversationHashtagResponses = await _appDbContext.ConversationHashtags
                .Include(x => x.Hashtag)
                .Where(x => conversationIds.Contains(x.ConversationId))
                .ProjectTo<ConversationHashtagResponse>(_mapper.ConfigurationProvider)
                .GroupBy(x => x.ConversationId)
                .Select(g => g.ToList())
                .ToDictionaryAsync(list => list[0].ConversationId, list => list);

            // get channel for each conversation
            var conversationsChannelList = await _appDbContext.ConversationMessages
                .Where(cm => conversationIds.Contains(cm.ConversationId))
                .Select(
                    cm => new
                    {
                        cm.ConversationId,
                        cm.UpdatedAt,
                        cm.Channel
                    })
                .GroupBy(cm => cm.ConversationId)
                .Select(
                    grp =>
                        grp.OrderByDescending(g => g.UpdatedAt).FirstOrDefault())
                .ToDictionaryAsync(
                    x => x.ConversationId,
                    x => new List<string>
                    {
                        x.Channel
                    });

            var mentionsDictionary = await _mentionQueryableResolver.GetMentionQueryable(
                    _appDbContext,
                    conversationIds,
                    staff.CompanyId)
                .GroupBy(x => x.ConversationId)
                .Select(g => g.ToList())
                .ToDictionaryAsync(list => list[0].ConversationId, list => list);

            foreach (var conversation in conversations)
            {
                conversation.AdditionalAssignees =
                    conversationAdditionalAssignees.Where(x => x.ConversationId == conversation.Id).ToList();

                conversation.Mentions = mentionsDictionary.TryGetValue(conversation.Id, out var mentions)
                    ? mentions
                    : new List<Mention>();
            }

            var conversationDict = conversations.ToDictionary(conversation => conversation.Id, conversation => conversation);

            var results = new List<ConversationNoCompanyResponseViewModel>();

            // assemble messages to related conversation response view model
            foreach (var conversationId in conversationIds)
            {
                conversationViewModel[conversationId].AdditionalAssignees =
                    additionalAssigneeResponses.TryGetValue(conversationId, out var additionalAssignees)
                        ? additionalAssignees
                        : new List<AdditionalAssigneeResponse>();

                conversationViewModel[conversationId].ConversationHashtags =
                    conversationHashtagResponses.TryGetValue(conversationId, out var conversationHashtags)
                        ? conversationHashtags
                        : new List<ConversationHashtagResponse>();

                conversationViewModel[conversationId].ConversationChannels =
                    conversationsChannelList[conversationId];

                conversationViewModel[conversationId].Messages =
                    _mapper.Map<List<ConversationMessageResponseViewModel>>(
                        conversationIdToMessagesDict[conversationId]
                            .OrderByDescending(x => x.CreatedAt)
                            .ToList());
                await _sleekPayService.AddSleekPayRecord(conversationViewModel[conversationId].Messages);

                conversationViewModel[conversationId].StaffConversationPermission =
                    conversationDict.TryGetValue(conversationId, out var conversation)
                        ? await _conversationPermissionControlService.GetStaffConversationPermissionAsync(
                            staff,
                            conversation)
                        : null;

                results.Add(conversationViewModel[conversationId]);
            }

            var response = new ConversationWithCountViewModel
            {
                Data = results.OrderByDescending(x => x.UpdatedTime).ToList(),
                Count = await filteredConversationMessageQueryable.CountAsync()
            };

            if (conversationIds.Any() && _isEnableTicketingLogic)
            {
                var userProfileIds = results.Select(x => x.UserProfile.Id).ToList();

                var ticketCount = await _ticketingHubMessageService.GetTicketingUserProfileCountAsync(
                    companyUser.CompanyId,
                    userProfileIds);

                if (ticketCount != null)
                {
                    response.Data = response.Data.GroupJoin(
                        ticketCount,
                        result => result.UserProfile.Id,
                        count => count.SleekflowUserProfileId,
                        (result, countGroup) => new ConversationNoCompanyResponseViewModel
                        {
                            ConversationId = result.ConversationId,
                            CompanyId = result.CompanyId,
                            ConversationChannels = result.ConversationChannels,
                            MessageGroupName = result.MessageGroupName,
                            UserProfile = result.UserProfile,
                            Status = result.Status,
                            Assignee = result.Assignee,
                            AdditionalAssignees = result.AdditionalAssignees,
                            ConversationHashtags = result.ConversationHashtags,
                            LastMessage = result.LastMessage,
                            Messages = result.Messages,
                            UpdatedTime = result.UpdatedTime,
                            ModifiedAt = result.ModifiedAt,
                            UnreadMessageCount = result.UnreadMessageCount,
                            SnoozeUntil = result.SnoozeUntil,
                            FirstMessageId = result.FirstMessageId,
                            LastMessageId = result.LastMessageId,
                            LastMessageChannel = result.LastMessageChannel,
                            LastChannelIdentityId = result.LastChannelIdentityId,
                            AssignedTeam = result.AssignedTeam,
                            IsSandbox = result.IsSandbox,
                            IsBookmarked = result.IsBookmarked,
                            Metadata = result.Metadata,
                            TicketCount = countGroup.Select(x => x.Count).DefaultIfEmpty(0).FirstOrDefault(),
                            StaffConversationPermission = result.StaffConversationPermission
                        }).ToList();
                }
            }

            await _cacheManagerService.SaveCacheAsync(searchMessageCacheKeyPattern, response);

            return response;
        }

        public async Task RbacConversationLastActivity(
            Conversation conversation,
            ConversationMessage conversationMessage,
            bool triggerAssignment = true)
        {
            try
            {
                if (!triggerAssignment
                    && conversationMessage.Channel == ChannelTypes.LiveChat)
                {
                    return;
                }

                if (conversation.LastMessageChannel != conversationMessage.Channel)
                {
                    conversation.LastMessageChannel = conversationMessage.Channel;

                    BackgroundJob.Enqueue<IUserProfileService>(
                        x => x.SetFieldValueByFieldNameSafe(
                            conversation.UserProfile.Id,
                            "LastChannel",
                            conversationMessage.Channel,
                            true));
                }

                conversation.LastChannelIdentityId = conversationMessage.ChannelIdentityId;

                // BackgroundJob.Enqueue<IUserProfileService>(x => x.SetFieldValueByFieldNameSafe(conversation.UserProfile.Id, "LastChannel", conversationMessage.Channel, true));
                var isCSReply = await IsCSReply(conversation, conversationMessage);

                if (isCSReply
                    || conversationMessage.IsSentFromSleekflow)
                {
                    if (!conversation.UserProfile.LastContact.HasValue
                        || conversation.UserProfile.LastContact < conversationMessage.CreatedAt)
                    {
                        conversation.UserProfile.LastContact = conversationMessage.CreatedAt;
                        await _userProfileService.SetFieldValueByFieldNameSafe(
                            conversation.UserProfile,
                            "LastContact",
                            conversationMessage.CreatedAt.ToString("o"),
                            triggerAssignment);
                    }
                }

                // BackgroundJob.Enqueue<IUserProfileService>(x => x.SetFieldValueByFieldNameSafe(conversation.UserProfile.Id, "LastContact", conversation.UpdatedTime.ToString("o"), true));

                // if system message for web, don't log the last contact
                if (conversationMessage.Channel == ChannelTypes.LiveChat
                    && conversationMessage.Sender == null
                    && conversationMessage.WebClientSender == null)
                {
                    return;
                }

                if (isCSReply
                    && conversationMessage.DeliveryType == DeliveryType.Normal)
                {
                    // Add collaborator when reply
                    if (!string.IsNullOrEmpty(conversationMessage.SenderId))
                    {
                        var sender = await _appDbContext.UserRoleStaffs
                            .FirstOrDefaultAsync(
                                x =>
                                    x.CompanyId == conversation.CompanyId
                                    && x.IdentityId == conversationMessage.SenderId);

                        var staff = await _accessControlAggregationService.GetStaffAccessControlAggregateAsync(sender);
                        conversation.AdditionalAssignees = await _appDbContext.ConversationAdditionalAssignees
                            .Where(x => x.ConversationId == conversation.Id)
                            .ToListAsync();

                        var shouldBecomeCollaboratorOnReply =
                            _inboxSettingManager.ShouldBecomeCollaboratorOnReply(staff, conversation);

                        if (shouldBecomeCollaboratorOnReply)
                        {
                            conversation = await _conversationAssigneeService.AddAdditionalAssignees(
                                conversation,
                                new List<long>
                                {
                                    sender.Id
                                });
                        }
                    }

                    if (!conversation.AssigneeId.HasValue && triggerAssignment)
                    {
                        _logger.LogInformation(
                            "ConversationId: {ConversationId} didn't have assignee",
                            conversation?.Id);
                        var userProfile = await _appDbContext.UserProfiles
                            .FirstOrDefaultAsync(x => x.Id == conversation.UserProfileId);

                        var contactOwner = await _userProfileService.GetCustomFieldByFieldName(
                            userProfile,
                            "ContactOwner");

                        if (contactOwner != null
                            && !string.IsNullOrEmpty(contactOwner.Value))
                        {
                            conversation.Assignee = await _appDbContext.UserRoleStaffs
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.CompanyId == userProfile.CompanyId
                                        && x.IdentityId == contactOwner.Value);

                            await ChangeConversationAssignee(conversation, conversation.Assignee, triggerAssignment);
                        }
                        else if (conversationMessage.Sender != null)
                        {
                            var staffRepliedLock = await _lockService.AcquireLockAsync(
                                $"staff-replied-as-owner:{conversation.Id}",
                                TimeSpan.FromSeconds(10));

                            if (staffRepliedLock != null)
                            {
                                var assignee = await _appDbContext.UserRoleStaffs
                                    .FirstOrDefaultAsync(x => x.IdentityId == conversationMessage.SenderId);

                                if (assignee.Id != 1)
                                {
                                    conversation.Assignee = assignee;

                                    if (conversation.Assignee != null)
                                    {
                                        await ChangeConversationAssignee(
                                            conversation,
                                            conversation.Assignee,
                                            triggerAssignment);
                                    }

                                    if (!conversation.AssignedTeamId.HasValue)
                                    {
                                        var assignedTeam = await _appDbContext.CompanyStaffTeams
                                            .FirstOrDefaultAsync(
                                                x => x.Members.Any(
                                                    y => y.Staff.IdentityId == conversationMessage.SenderId));

                                        if (assignedTeam != null)
                                        {
                                            await ChangeConversationAssignedTeam(
                                                conversation,
                                                assignedTeam,
                                                triggerAssignment);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                if (!isCSReply)
                {
                    try
                    {
                        if (!conversationMessage.IsSentFromSleekflow)
                        {
                            if (!conversation.UserProfile.LastContactFromCustomers.HasValue
                                || conversation.UserProfile.LastContactFromCustomers < conversationMessage.CreatedAt)
                            {
                                conversation.UserProfile.LastContactFromCustomers = conversationMessage.CreatedAt;

                                await _userProfileService.SetFieldValueByFieldNameSafe(
                                    conversation.UserProfile,
                                    "LastContactFromCustomers",
                                    conversationMessage.CreatedAt.ToString("o"),
                                    true);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[{MethodName}] Update LastContactFromCustomers error for user profile {UserProfileId}: {ExceptionMessage}",
                            nameof(ConversationLastActivity),
                            conversation.UserProfileId,
                            ex.Message);


                        if (!conversation.UserProfile.LastContactFromCustomers.HasValue
                            || conversation.UserProfile.LastContactFromCustomers < conversationMessage.CreatedAt)
                        {
                            conversation.UserProfile.LastContactFromCustomers = conversationMessage.CreatedAt;

                            await _userProfileService.SetFieldValueByFieldNameSafe(
                                conversation.UserProfile,
                                "LastContactFromCustomers",
                                conversationMessage.CreatedAt.ToString("o"),
                                true);
                        }
                    }
                }

                if (conversation.ActiveStatus == ActiveStatus.Inactive)
                {
                    conversation.ActiveStatus = ActiveStatus.Active;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] error for conversation {ConversationId}: {ExceptionString}",
                    nameof(ConversationLastActivity),
                    conversation?.Id,
                    ex.ToString());
            }
        }

        #endregion

        private async Task<UserProfile> HandleMissingUserProfile(Conversation conversation)
        {
            if (conversation == null)
            {
                _logger.LogError(
                    "[{MethodName}] Conversation is null.",
                    nameof(HandleMissingUserProfile));

                return null;
            }

            if (conversation.UserProfileId is null)
            {
                _logger.LogError(
                    "[{MethodName}] Missing both user profile and UserProfileId for conversation: {ConversationId}. Reattachment is not possible.",
                    nameof(HandleMissingUserProfile),
                    conversation.Id);

                return null; // Stop further execution because we cannot attempt reattachment.
            }

            _logger.LogWarning(
                "[{MethodName}] Missing user profile for conversation: {ConversationId}. Attempting reattachment using UserProfileId: {UserProfileId}.",
                nameof(HandleMissingUserProfile),
                conversation.Id,
                conversation.UserProfileId);

            var userProfile = await _appDbContext.UserProfiles
                .Where(u => u.Id == conversation.UserProfileId)
                .FirstOrDefaultAsync();

            if (conversation.UserProfile is null)
            {
                _logger.LogError(
                    "[{MethodName}] Reattachment failed. User profile not found for UserProfileId: {UserProfileId}, ConversationId: {ConversationId}.",
                    nameof(HandleMissingUserProfile),
                    conversation.UserProfileId,
                    conversation.Id);
            }

            return userProfile;
        }

        /// <summary>
        /// Checks if the conversation message contains both a valid phone number with country code and an email address.
        /// </summary>
        /// <param name="conversationMessage">The conversation message to check.</param>
        /// <param name="conversation">Optional conversation context.</param>
        /// <returns>True if both phone number with country code and email address are found, false otherwise.</returns>
        public bool CheckMessageContainsPhoneNumberOrEmail(ConversationMessage conversationMessage, Conversation conversation = null)
        {
            if (conversationMessage?.MessageContent == null)
            {
                return false;
            }

            var messageContent = conversationMessage.MessageContent;

            // Regex pattern for international phone numbers with country codes
            // Matches formats like: +1234567890, ****** 567 8900, +44 20 1234 5678, +86-138-0013-8000, etc.
            var phonePattern = @"\+\d{1,4}[\s\-\.]?(?:\(\d{1,4}\)[\s\-\.]?)?\d{1,4}[\s\-\.]?\d{1,4}[\s\-\.]?\d{1,9}";

            // Regex pattern for email addresses
            // Matches standard email formats like: <EMAIL>, <EMAIL>, etc.
            var emailPattern = @"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}\b";

            try
            {
                // Check if either 1 of the pattern found in the message content
                var hasPhoneNumber = Regex.IsMatch(messageContent, phonePattern);
                var hasEmailAddress = Regex.IsMatch(messageContent, emailPattern);

                _logger.LogInformation(
                    "[{MethodName}] Message contains phone number and email. ConversationId: {ConversationId}, MessageId: {MessageId}",
                    nameof(CheckMessageContainsPhoneNumberOrEmail),
                    conversationMessage.ConversationId,
                    conversationMessage.Id);

                return hasPhoneNumber || hasEmailAddress;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Error checking message content for phone and email. ConversationId: {ConversationId}, MessageId: {MessageId}",
                    nameof(CheckMessageContainsPhoneNumberOrEmail),
                    conversationMessage.ConversationId,
                    conversationMessage.Id);

                return false;
            }
        }

        /// <summary>
        /// Handles phone number and email detection for specific channels and triggers TikTok ads integration if detected.
        /// </summary>
        /// <param name="conversation">The conversation context.</param>
        /// <param name="conversationMessage">The conversation message to check.</param>
        private void HandleLowerFunnelEvent(Conversation conversation, ConversationMessage conversationMessage)
        {
            if (conversationMessage.Channel != ChannelTypes.WhatsappCloudApi &&
                conversationMessage.Channel != ChannelTypes.Facebook &&
                conversationMessage.Channel != ChannelTypes.Tiktok)
            {
                return;
            }

            try
            {
                var containsPhoneAndEmail = CheckMessageContainsPhoneNumberOrEmail(conversationMessage, conversation);
                if (!containsPhoneAndEmail)
                {
                    return;
                }

                _logger.LogInformation(
                    "[Phone & Email Detected] Channel: {Channel}, ConversationId: {ConversationId}, MessageId: {MessageId}, Content: {MessageContent}",
                    conversationMessage.Channel,
                    conversationMessage.ConversationId,
                    conversationMessage.Id,
                    conversationMessage.MessageContent);

                BackgroundJob.Enqueue<ITikTokAdsIntegrationService>(
                    tikTokAdsIntegrationService => tikTokAdsIntegrationService.SendLowerFunnelEventAsync(
                        conversation.CompanyId,
                        conversation.UserProfileId,
                        conversationMessage.Channel,
                        conversationMessage.ChannelIdentityId,
                        "SubmitForm",
                        CancellationToken.None));
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[Phone & Email Check Error] Channel: {Channel}, ConversationId: {ConversationId}, MessageId: {MessageId}",
                    conversationMessage.Channel,
                    conversationMessage.ConversationId,
                    conversationMessage.Id);
            }
        }

        private static IQueryable<Conversation> ApplyOrderBy(
            IQueryable<Conversation> conversations,
            string orderBy,
            Staff staff)
        {
            return orderBy switch
            {
                "asc" => conversations
                    .OrderByDescending(
                        conversation => conversation.ConversationBookmarks.Any(x => x.StaffId == staff.Id))
                    .ThenBy(conversation => conversation.UpdatedTime),
                "desc" => conversations
                    .OrderByDescending(
                        conversation => conversation.ConversationBookmarks.Any(x => x.StaffId == staff.Id))
                    .ThenByDescending(conversation => conversation.UpdatedTime),
                "ascUnreplied" => conversations.OrderByDescending(x => x.IsUnreplied).ThenBy(x => x.UpdatedTime),
                "descUnreplied" => conversations.OrderByDescending(x => x.IsUnreplied)
                    .ThenByDescending(x => x.UpdatedTime),
                _ => conversations.OrderByDescending(conversation => conversation.UpdatedTime)
            };
        }

        private static List<Conversation> ApplyOrderBy(
            List<Conversation> conversations,
            string orderBy,
            Staff staff)
        {
            return orderBy switch
            {
                "asc" => conversations
                    .OrderByDescending(
                        conversation => conversation.ConversationBookmarks.Exists(x => x.StaffId == staff.Id))
                    .ThenBy(conversation => conversation.UpdatedTime).ToList(),
                "desc" => conversations
                    .OrderByDescending(
                        conversation => conversation.ConversationBookmarks.Exists(x => x.StaffId == staff.Id))
                    .ThenByDescending(conversation => conversation.UpdatedTime).ToList(),
                "ascUnreplied" => conversations.OrderByDescending(x => x.IsUnreplied).ThenBy(x => x.UpdatedTime).ToList(),
                "descUnreplied" => conversations.OrderByDescending(x => x.IsUnreplied)
                    .ThenByDescending(x => x.UpdatedTime).ToList(),
                _ => conversations.OrderByDescending(conversation => conversation.UpdatedTime).ToList()
            };
        }
    }
}
