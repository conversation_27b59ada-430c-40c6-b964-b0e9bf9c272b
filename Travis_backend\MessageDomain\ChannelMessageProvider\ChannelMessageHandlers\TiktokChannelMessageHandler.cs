using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Sleekflow.Apis.MessagingHub.Api;
using Sleekflow.Apis.MessagingHub.Model;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.FileDomain.Models;
using Travis_backend.FileDomain.Services;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.ViewModels;
using Travis_backend.OpenTelemetry.Meters;
using Travis_backend.OpenTelemetry.Meters.Constants;
using AutoMapper;

namespace Travis_backend.MessageDomain.ChannelMessageProvider.ChannelMessageHandlers;

public class TiktokChannelMessageHandler : IChannelMessageHandler
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IChannelsApi _channelsApi;
    private readonly ILogger<TiktokChannelMessageHandler> _logger;
    private readonly IConversationMeters _conversationMeters;
    private readonly IAzureBlobStorageService _azureBlobStorageService;
    private readonly IUploadService _uploadService;
    private readonly IMapper _mapper;
    private readonly IConfiguration _configuration;

    public string ChannelType => ChannelTypes.Tiktok;

    public TiktokChannelMessageHandler(
        ApplicationDbContext appDbContext,
        IChannelsApi channelsApi,
        ILogger<TiktokChannelMessageHandler> logger,
        IConversationMeters conversationMeters,
        IAzureBlobStorageService azureBlobStorageService,
        IUploadService uploadService,
        IMapper mapper,
        IConfiguration configuration)
    {
        _appDbContext = appDbContext;
        _channelsApi = channelsApi;
        _logger = logger;
        _conversationMeters = conversationMeters;
        _azureBlobStorageService = azureBlobStorageService;
        _uploadService = uploadService;
        _mapper = mapper;
        _configuration = configuration;
    }

    public async ValueTask<ConversationMessage> SendChannelMessageAsync(
        Conversation conversation,
        ConversationMessage conversationMessage)
    {
        // Skip if message is already sent or not from Sleekflow
        if (!string.IsNullOrEmpty(conversationMessage.MessageUniqueID))
        {
            return conversationMessage;
        }

        // Check if we have TikTok user information
        if (conversation.TikTokUser == null)
        {
            _logger.LogError("TikTok user information not found for conversation {ConversationId}", conversation.Id);
            conversationMessage.Status = MessageStatus.Failed;
            conversationMessage.ChannelStatusMessage = "TikTok user information not found.";

            await _appDbContext.SaveChangesAsync();
            _conversationMeters.IncrementCounter(ChannelTypes.Tiktok, ConversationMeterOptions.SendFailed);
            return conversationMessage;
        }

        // Get TikTok configuration
        var tikTokConfig = await _appDbContext.ConfigTikTokConfigs
            .FirstOrDefaultAsync(x => x.CompanyId == conversation.CompanyId &&
                                      x.ChannelIdentityId == conversation.TikTokUser.ChannelIdentityId);

        if (tikTokConfig == null)
        {
            _logger.LogError("TikTok configuration not found for company {CompanyId}", conversation.CompanyId);
            conversationMessage.Status = MessageStatus.Failed;
            conversationMessage.ChannelStatusMessage = "TikTok Channel not found.";

            await _appDbContext.SaveChangesAsync();
            _conversationMeters.IncrementCounter(ChannelTypes.Tiktok, ConversationMeterOptions.SendFailed);
            return conversationMessage;
        }

        // Get message content (with translation if available)
        string messageContent = string.Empty;
        if (conversationMessage.TranslationResults != null && conversationMessage.TranslationResults.Any())
        {
            messageContent = conversationMessage.TranslationResults.FirstOrDefault()?.translations?.FirstOrDefault()
                ?.text;
        }

        if (string.IsNullOrEmpty(messageContent))
        {
            messageContent = conversationMessage.MessageContent ?? string.Empty;
        }

        try
        {
            // Set channel identity
            conversationMessage.ChannelIdentityId = conversation.TikTokUser.ChannelIdentityId;

            // Call TikTok API based on message type
            switch (conversationMessage.MessageType.ToLower())
            {
                case "text":
                    // Validate message content
                    if (string.IsNullOrWhiteSpace(messageContent))
                    {
                        _logger.LogWarning(
                            "Empty text message content for TikTok message in conversation {ConversationId}",
                            conversation.Id);
                        conversationMessage.Status = MessageStatus.Failed;
                        conversationMessage.ChannelStatusMessage = "Message content cannot be empty";

                        _conversationMeters.IncrementCounter(ChannelTypes.Tiktok, ConversationMeterOptions.SendFailed);
                        break;
                    }

                    _logger.LogInformation(
                        "Sending TikTok text message to user {UserId} for company {CompanyId}",
                        conversation.TikTokUser.UserIdentityId,
                        conversationMessage.CompanyId);

                    try
                    {
                        // API call to send TikTok message
                        var textMessageResponse = await _channelsApi.ChannelsSendTikTokMessagePostAsync(
                            sendTikTokMessageInput: new SendTikTokMessageInput(
                                conversationMessage.CompanyId,
                                tikTokConfig.TikTokConfigId,
                                conversation.TikTokUser.TiktokConversationId,
                                "TEXT",
                                messageContent,
                                sleekflowStaffId: conversationMessage.SenderId
                            ));

                        if (textMessageResponse?.Success == true && textMessageResponse.Data?.MessageId != null)
                        {
                            conversationMessage.MessageUniqueID = textMessageResponse.Data.MessageId;
                            conversationMessage.Status = MessageStatus.Sending;
                            conversation.TikTokUser.RemainMessageCount -= 1;

                            _logger.LogInformation(
                                "Successfully sent TikTok text message. MessageId: {MessageUniqueID}",
                                conversationMessage.MessageUniqueID);

                            _conversationMeters.IncrementCounter(
                                ChannelTypes.Tiktok,
                                ConversationMeterOptions.SendSuccess);
                        }
                        else
                        {
                            // Extract error message from response payload
                            var errorMessage = "Unknown error from TikTok API";

                            if (!string.IsNullOrEmpty(textMessageResponse?.Data?.ErrorMessage))
                            {
                                errorMessage = textMessageResponse.Data.ErrorMessage;
                            }
                            else if (!string.IsNullOrEmpty(textMessageResponse?.Message))
                            {
                                errorMessage = textMessageResponse.Message;
                            }
                            else if (textMessageResponse?.ErrorCode.HasValue == true)
                            {
                                errorMessage = $"TikTok API error (Code: {textMessageResponse.ErrorCode})";
                            }

                            _logger.LogError(
                                "TikTok API returned error response for conversation {ConversationId}: {ErrorMessage}",
                                conversation.Id,
                                errorMessage);
                            conversationMessage.Status = MessageStatus.Failed;
                            conversationMessage.ChannelStatusMessage = errorMessage;
                            _conversationMeters.IncrementCounter(
                                ChannelTypes.Tiktok,
                                ConversationMeterOptions.SendFailed);
                        }
                    }
                    catch (HttpRequestException httpEx)
                    {
                        _logger.LogError(
                            httpEx,
                            "HTTP error sending TikTok text message for conversation {ConversationId}: {Error}",
                            conversation.Id,
                            httpEx.Message);
                        conversationMessage.Status = MessageStatus.Failed;
                        conversationMessage.ChannelStatusMessage = $"Network error: {httpEx.Message}";
                        _conversationMeters.IncrementCounter(ChannelTypes.Tiktok, ConversationMeterOptions.SendFailed);
                    }
                    catch (TaskCanceledException timeoutEx)
                    {
                        _logger.LogError(
                            timeoutEx,
                            "Timeout sending TikTok text message for conversation {ConversationId}",
                            conversation.Id);
                        conversationMessage.Status = MessageStatus.Failed;
                        conversationMessage.ChannelStatusMessage =
                            "Request timeout - TikTok API did not respond in time";
                        _conversationMeters.IncrementCounter(ChannelTypes.Tiktok, ConversationMeterOptions.SendFailed);
                    }
                    catch (Exception apiEx)
                    {
                        _logger.LogError(
                            apiEx,
                            "API error sending TikTok text message for conversation {ConversationId}: {Error}",
                            conversation.Id,
                            apiEx.Message);
                        conversationMessage.Status = MessageStatus.Failed;
                        conversationMessage.ChannelStatusMessage = $"TikTok API error: {apiEx.Message}";
                        _conversationMeters.IncrementCounter(ChannelTypes.Tiktok, ConversationMeterOptions.SendFailed);
                    }

                    break;

                case "file":
                    // Validate file attachments
                    if (conversationMessage.UploadedFiles == null || conversationMessage.UploadedFiles.Count == 0)
                    {
                        _logger.LogWarning(
                            "No file attachments found for TikTok file message in conversation {ConversationId}",
                            conversation.Id);
                        conversationMessage.Status = MessageStatus.Failed;
                        conversationMessage.ChannelStatusMessage = "No file attachments found";

                        _conversationMeters.IncrementCounter(ChannelTypes.Tiktok, ConversationMeterOptions.SendFailed);
                        break;
                    }

                    _logger.LogInformation(
                        "Processing TikTok file message for conversation {ConversationId}, company {CompanyId}",
                        conversation.Id,
                        conversationMessage.CompanyId);

                    try
                    {
                        var uploadedFile = conversationMessage.UploadedFiles.First();

                        _logger.LogInformation(
                            "Processing TikTok file upload: FileName={FileName}, Container={Container}, MimeType={MimeType}",
                            uploadedFile.Filename,
                            uploadedFile.BlobContainer,
                            uploadedFile.MIMEType);

                        // Step 1: Get blob URI from Travis backend blob storage
                        var rawImageUri = _azureBlobStorageService.GetAzureBlobSasUri(
                            uploadedFile.Filename,
                            uploadedFile.BlobContainer);

                        if (string.IsNullOrEmpty(rawImageUri))
                        {
                            _logger.LogError(
                                "Failed to get blob URI from storage for conversation {ConversationId}",
                                conversation.Id);
                            conversationMessage.Status = MessageStatus.Failed;
                            conversationMessage.ChannelStatusMessage = "Failed to get file URI from storage";
                            _conversationMeters.IncrementCounter(
                                ChannelTypes.Tiktok,
                                ConversationMeterOptions.SendFailed);
                            break;
                        }

                        // URL encode only the sig parameter to ensure it works correctly with TikTok API
                        // var imageUri = EncodeSignatureParameter(rawImageUri);
                        var imageUri = rawImageUri;

                        var fileName = Path.GetFileName(uploadedFile.Filename) ?? "image";

                        _logger.LogInformation(
                            "Uploading TikTok image from URI: {ImageUri} for conversation {ConversationId}",
                            imageUri,
                            conversation.Id);

                        // Step 2: Upload to TikTok using UploadTikTokImageFromUrl
                        var uploadMediaResponse = await _channelsApi.ChannelsUploadTikTokImageFromUrlPostAsync(
                            uploadTikTokImageFromUrlInput: new UploadTikTokImageFromUrlInput(
                                conversationMessage.CompanyId,
                                tikTokConfig.TikTokConfigId,
                                imageUri,
                                fileName,
                                "IMAGE",
                                sleekflowStaffId: conversationMessage.SenderId
                            ));

                        string mediaId = null;
                        if (uploadMediaResponse.Success == true && uploadMediaResponse.Data.MediaId != null)
                        {
                            mediaId = uploadMediaResponse.Data.MediaId;

                            _logger.LogInformation(
                                "Successfully uploaded media to TikTok. MediaId: {MediaId}",
                                mediaId);
                        }
                        else
                        {
                            // Extract error message from response payload
                            var errorMessage = "Unknown error uploading media to TikTok";

                            if (!string.IsNullOrEmpty(uploadMediaResponse?.Data?.ErrorMessage))
                            {
                                errorMessage = uploadMediaResponse.Data.ErrorMessage;
                            }
                            else if (!string.IsNullOrEmpty(uploadMediaResponse?.Message))
                            {
                                errorMessage = uploadMediaResponse.Message;
                            }
                            else if (uploadMediaResponse?.ErrorCode.HasValue == true)
                            {
                                errorMessage =
                                    $"TikTok API error uploading media (Code: {uploadMediaResponse.ErrorCode})";
                            }

                            _logger.LogError(
                                "Failed to upload media to TikTok for conversation {ConversationId}: {ErrorMessage}",
                                conversation.Id,
                                errorMessage);
                            conversationMessage.Status = MessageStatus.Failed;
                            conversationMessage.ChannelStatusMessage = errorMessage;
                            _conversationMeters.IncrementCounter(
                                ChannelTypes.Tiktok,
                                ConversationMeterOptions.SendFailed);
                            break;
                        }

                        // Step 3: Send message with media ID
                        var fileMessageResponse = await _channelsApi.ChannelsSendTikTokMessagePostAsync(
                            sendTikTokMessageInput: new SendTikTokMessageInput(
                                conversationMessage.CompanyId,
                                tikTokConfig.TikTokConfigId,
                                conversation.TikTokUser.TiktokConversationId,
                                "IMAGE",
                                null,
                                mediaId,
                                sleekflowStaffId: conversationMessage.SenderId
                            ));

                        if (fileMessageResponse.Data.MessageId != null)
                        {
                            conversationMessage.MessageUniqueID = fileMessageResponse.Data.MessageId;
                            conversationMessage.Status = MessageStatus.Sending;
                            conversation.TikTokUser.RemainMessageCount -= 1;

                            _logger.LogInformation(
                                "Successfully sent TikTok file message. MessageId: {MessageUniqueID}, MediaId: {MediaId}",
                                conversationMessage.MessageUniqueID,
                                mediaId);

                            _conversationMeters.IncrementCounter(
                                ChannelTypes.Tiktok,
                                ConversationMeterOptions.SendSuccess);
                        }
                        else
                        {
                            _logger.LogError(
                                "TikTok API returned invalid response for file message in conversation {ConversationId}",
                                conversation.Id);

                            var errorMessage = "Unknown error uploading media to TikTok";

                            if (!string.IsNullOrEmpty(fileMessageResponse?.Data?.ErrorMessage))
                            {
                                errorMessage = fileMessageResponse.Data.ErrorMessage;
                            }
                            else if (!string.IsNullOrEmpty(fileMessageResponse?.Message))
                            {
                                errorMessage = fileMessageResponse.Message;
                            }
                            else if (fileMessageResponse?.ErrorCode.HasValue == true)
                            {
                                errorMessage =
                                    $"TikTok API error on sending file message (Code: {fileMessageResponse.ErrorCode})";
                            }

                            conversationMessage.Status = MessageStatus.Failed;
                            conversationMessage.ChannelStatusMessage = errorMessage;
                            _conversationMeters.IncrementCounter(
                                ChannelTypes.Tiktok,
                                ConversationMeterOptions.SendFailed);
                        }
                    }
                    catch (HttpRequestException httpEx)
                    {
                        _logger.LogError(
                            httpEx,
                            "HTTP error processing TikTok file message for conversation {ConversationId}: {Error}",
                            conversation.Id,
                            httpEx.Message);
                        conversationMessage.Status = MessageStatus.Failed;
                        conversationMessage.ChannelStatusMessage = $"Network error: {httpEx.Message}";
                        _conversationMeters.IncrementCounter(ChannelTypes.Tiktok, ConversationMeterOptions.SendFailed);
                    }
                    catch (TaskCanceledException timeoutEx)
                    {
                        _logger.LogError(
                            timeoutEx,
                            "Timeout processing TikTok file message for conversation {ConversationId}",
                            conversation.Id);
                        conversationMessage.Status = MessageStatus.Failed;
                        conversationMessage.ChannelStatusMessage =
                            "Request timeout - TikTok API did not respond in time";
                        _conversationMeters.IncrementCounter(ChannelTypes.Tiktok, ConversationMeterOptions.SendFailed);
                    }
                    catch (Exception apiEx)
                    {
                        _logger.LogError(
                            apiEx,
                            "Error processing TikTok file message for conversation {ConversationId}: {Error}",
                            conversation.Id,
                            apiEx.Message);
                        conversationMessage.Status = MessageStatus.Failed;
                        conversationMessage.ChannelStatusMessage = $"TikTok file processing error: {apiEx.Message}";
                        _conversationMeters.IncrementCounter(ChannelTypes.Tiktok, ConversationMeterOptions.SendFailed);
                    }

                    break;

                default:
                    _logger.LogWarning(
                        "Unsupported TikTok message type: {MessageType} for conversation {ConversationId}",
                        conversationMessage.MessageType,
                        conversation.Id);
                    conversationMessage.Status = MessageStatus.Failed;
                    conversationMessage.ChannelStatusMessage =
                        $"Unsupported message type: {conversationMessage.MessageType}";

                    _conversationMeters.IncrementCounter(ChannelTypes.Tiktok, ConversationMeterOptions.SendFailed);
                    break;
            }

            // Mark as sent from Sleekflow
            conversationMessage.IsSentFromSleekflow = true;

            // Update conversation last message info
            conversation.LastChannelIdentityId = conversationMessage.ChannelIdentityId;
            conversation.LastMessageChannel = ChannelTypes.Tiktok;
            conversation.LastMessageId = conversationMessage.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error sending TikTok message for conversation {ConversationId}, Company {CompanyId}: {ExceptionMessage}",
                conversation.Id,
                conversation.CompanyId,
                ex.Message);

            conversationMessage.Status = MessageStatus.Failed;
            conversationMessage.ChannelStatusMessage = ex.Message;

            _conversationMeters.IncrementCounter(ChannelTypes.Tiktok, ConversationMeterOptions.SendFailed);
        }

        // Update conversation metadata
        conversation.UpdatedTime = DateTime.UtcNow;
        conversation.ModifiedAt = DateTime.UtcNow;

        // Save changes to database
        await _appDbContext.SaveChangesAsync();

        return conversationMessage;
    }

    public async ValueTask<ConversationMessage> ReceiveChannelMessageAsync(
        Conversation conversation,
        ConversationMessage conversationMessage)
    {
        // Exclude processing the message from import
        if (conversationMessage.IsFromImport)
        {
            return conversationMessage;
        }

        try
        {
            _logger.LogInformation(
                "Processing TikTok receive message for conversation {ConversationId}, company {CompanyId}",
                conversation.Id,
                conversation.CompanyId);

            // Handle TikTok image messages - convert to file message type for consistency with other channels
            if (conversationMessage.MessageType?.ToLower() == "image")
            {
                _logger.LogInformation(
                    "Converting TikTok image message to file message type for conversation {ConversationId}",
                    conversation.Id);
                conversationMessage.MessageType = "file";
            }

            // Step 1: Upsert TikTok sender (user)
            await UpsertTikTokSenderAsync(conversation, conversationMessage);

            // Step 2: Upsert conversation
            await UpsertConversationAsync(conversation, conversationMessage);

            // Step 3: Upsert user profile
            await UpsertUserProfileAsync(conversation, conversationMessage);

            // Step 4: Handle image message download if required (for file messages that are actually images)
            await HandleIncomingImageMessageAsync(conversation, conversationMessage);

            // Step 5: Update conversation message
            await UpdateConversationMessageAsync(conversation, conversationMessage);

            // Set status for received messages
            conversationMessage.Status = MessageStatus.Received;

            // Track metrics
            _conversationMeters.IncrementCounter(ChannelTypes.Tiktok, ConversationMeterOptions.ReceiveSuccess);

            // Save changes
            await _appDbContext.SaveChangesAsync();

            _logger.LogInformation(
                "Successfully processed TikTok receive message for conversation {ConversationId}",
                conversation.Id);

            return conversationMessage;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error processing TikTok receive message for conversation {ConversationId}: {ExceptionMessage}",
                conversation?.Id,
                ex.Message);

            // Track failed metrics
            _conversationMeters.IncrementCounter(ChannelTypes.Tiktok, ConversationMeterOptions.ReceiveFailed);

            throw;
        }
    }

    /// <summary>
    /// Step 1: Upsert TikTok sender (user) information
    /// </summary>
    private async Task UpsertTikTokSenderAsync(Conversation conversation, ConversationMessage conversationMessage)
    {
        try
        {
            if (conversation.TikTokUser == null)
            {
                _logger.LogWarning(
                    "TikTok user is null for conversation {ConversationId}, skipping sender upsert",
                    conversation.Id);
                return;
            }

            var tikTokSender = conversation.TikTokUser;

            // Update TikTok sender information with latest data
            tikTokSender.UpdatedAt = DateTimeOffset.UtcNow;
            tikTokSender.RemainMessageCount = 10;
            tikTokSender.ConversationWindowExpireAt = DateTimeOffset.UtcNow.AddDays(2);
            tikTokSender.CompanyId = conversation.CompanyId;
            tikTokSender.ConversationId = conversation.Id;

            // If we have user profile information, link it
            if (!string.IsNullOrEmpty(conversation.UserProfileId))
            {
                tikTokSender.UserProfileId = conversation.UserProfileId;
            }

            // Fetch additional TikTok user profile information if needed
            await FetchTikTokUserProfileAsync(conversation.CompanyId, tikTokSender);

            _logger.LogInformation(
                "Successfully upserted TikTok sender for user {UserIdentityId} in conversation {ConversationId}",
                tikTokSender.UserIdentityId,
                conversation.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error upserting TikTok sender for conversation {ConversationId}: {ExceptionMessage}",
                conversation.Id,
                ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Step 2: Upsert conversation information
    /// </summary>
    private async Task UpsertConversationAsync(Conversation conversation, ConversationMessage conversationMessage)
    {
        try
        {
            // Update conversation metadata
            conversation.UpdatedTime = DateTime.UtcNow;
            conversation.ModifiedAt = DateTime.UtcNow;
            conversation.LastChannelIdentityId =
                conversationMessage.ChannelIdentityId ?? conversation.TikTokUser?.ChannelIdentityId;
            conversation.LastMessageChannel = ChannelTypes.Tiktok;
            conversation.LastMessageId = conversationMessage.Id;

            // Set active status for received messages
            if (!conversationMessage.IsSentFromSleekflow)
            {
                conversation.ActiveStatus = ActiveStatus.Active;
            }

            _logger.LogInformation(
                "Successfully upserted conversation {ConversationId} for TikTok channel",
                conversation.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error upserting conversation {ConversationId}: {ExceptionMessage}",
                conversation.Id,
                ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Step 3: Upsert user profile information
    /// </summary>
    private async Task UpsertUserProfileAsync(Conversation conversation, ConversationMessage conversationMessage)
    {
        try
        {
            if (conversation.TikTokUser == null)
            {
                _logger.LogWarning(
                    "Missing TikTok user for conversation {ConversationId}, skipping user profile upsert",
                    conversation.Id);
                return;
            }

            // For TikTok, the user profile upserting is usually handled by ConversationResolver
            // Here we focus on updating the TikTok-specific sender information
            // and fetching additional profile data if needed
            await FetchTikTokUserProfileAsync(conversation.CompanyId, conversation.TikTokUser);

            // Update the user profile name if we have better information
            if (!string.IsNullOrEmpty(conversation.UserProfileId))
            {
                var userProfile = await _appDbContext.UserProfiles
                    .FirstOrDefaultAsync(x => x.Id == conversation.UserProfileId);

                if (userProfile != null)
                {
                    var displayName = conversation.TikTokUser.DisplayName;

                    // Update user profile name if we have better display name
                    if (!string.IsNullOrEmpty(displayName) &&
                        displayName != conversation.TikTokUser.UserIdentityId &&
                        (string.IsNullOrEmpty(userProfile.FirstName) ||
                         userProfile.FirstName == "Anonymous" ||
                         userProfile.FirstName == conversation.TikTokUser.UserIdentityId))
                    {
                        userProfile.FirstName = displayName;
                        userProfile.UpdatedAt = DateTime.UtcNow;

                        _logger.LogInformation(
                            "Updated user profile name to {DisplayName} for TikTok user {UserIdentityId}",
                            displayName,
                            conversation.TikTokUser.UserIdentityId);
                    }
                }
            }

            _logger.LogInformation(
                "Successfully processed user profile for TikTok user {UserIdentityId} in conversation {ConversationId}",
                conversation.TikTokUser.UserIdentityId,
                conversation.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error upserting user profile for TikTok conversation {ConversationId}: {ExceptionMessage}",
                conversation.Id,
                ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Step 4: Update conversation message information
    /// </summary>
    private async Task UpdateConversationMessageAsync(
        Conversation conversation,
        ConversationMessage conversationMessage)
    {
        try
        {
            // Link message to conversation and company
            conversationMessage.ConversationId = conversation.Id;
            conversationMessage.CompanyId = conversation.CompanyId;

            // Set channel information
            conversationMessage.Channel = ChannelTypes.Tiktok;
            if (string.IsNullOrEmpty(conversationMessage.ChannelIdentityId) && conversation.TikTokUser != null)
            {
                conversationMessage.ChannelIdentityId = conversation.TikTokUser.ChannelIdentityId;
            }

            // Update timestamps
            conversationMessage.UpdatedAt = DateTime.UtcNow;

            _logger.LogInformation(
                "Successfully updated conversation message {MessageId} for TikTok conversation {ConversationId}",
                conversationMessage.Id,
                conversation.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error updating conversation message for TikTok conversation {ConversationId}: {ExceptionMessage}",
                conversation.Id,
                ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Fetch TikTok user profile information and update the sender
    /// Similar to Instagram's IGFetchFacebookUserProfile method
    /// </summary>
    /// <param name="companyId">Company ID</param>
    /// <param name="tikTokSender">TikTok sender to update</param>
    private async Task FetchTikTokUserProfileAsync(string companyId, TikTokSender tikTokSender)
    {
        try
        {
            // Only fetch if display name is missing or generic
            if (!string.IsNullOrEmpty(tikTokSender.DisplayName) &&
                tikTokSender.DisplayName != tikTokSender.UserIdentityId &&
                tikTokSender.DisplayName != "TikTok User")
            {
                return;
            }

            _logger.LogInformation(
                "Fetching TikTok profile for user {UserIdentityId} in company {CompanyId}",
                tikTokSender.UserIdentityId,
                companyId);

            // TODO: Implement TikTok API call to fetch user profile
            // This would require TikTok API integration similar to how Instagram handler fetches profiles
            // For now, we'll update with basic information

            if (string.IsNullOrEmpty(tikTokSender.DisplayName) ||
                tikTokSender.DisplayName == tikTokSender.UserIdentityId)
            {
                tikTokSender.DisplayName = $"TikTok User ({tikTokSender.UserIdentityId})";
            }

            tikTokSender.UpdatedAt = DateTimeOffset.UtcNow;

            _logger.LogInformation(
                "Updated TikTok profile for user {UserIdentityId}",
                tikTokSender.UserIdentityId);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error fetching TikTok profile for user {UserIdentityId}: {ExceptionMessage}",
                tikTokSender.UserIdentityId,
                ex.Message);
        }
    }

    /// <summary>
    /// Handle downloading and storing incoming image messages from TikTok
    /// </summary>
    /// <param name="conversation">The conversation</param>
    /// <param name="conversationMessage">The conversation message</param>
    private async Task HandleIncomingImageMessageAsync(Conversation conversation, ConversationMessage conversationMessage)
    {
        try
        {
            // Check if this is a TikTok image message that requires download
            // TikTok image messages are converted to "file" message type but contain image-specific metadata
            if (conversationMessage.MessageType != "file" ||
                conversationMessage.Metadata == null)
            {
                return; // Not a file message or no metadata
            }

            // Check if this is specifically a TikTok image that needs downloading
            bool isImageMessage = conversationMessage.Metadata.ContainsKey("media_type") &&
                                 conversationMessage.Metadata["media_type"]?.ToString()?.ToLower() == "image";

            bool requiresDownload = conversationMessage.Metadata.ContainsKey("requires_image_download") &&
                                   (bool)conversationMessage.Metadata["requires_image_download"];

            // Also check for TikTok-specific image metadata
            bool hasTikTokImageMetadata = conversationMessage.Metadata.ContainsKey("image_media_id") ||
                                         conversationMessage.Metadata.ContainsKey("media_id");

            if (!isImageMessage && !requiresDownload && !hasTikTokImageMetadata)
            {
                return; // Not an image message or no download required
            }

            _logger.LogInformation(
                "Processing incoming TikTok image message for conversation {ConversationId}",
                conversation.Id);

            // Extract image metadata
            var mediaId = conversationMessage.Metadata["image_media_id"]?.ToString() ??
                         conversationMessage.Metadata["media_id"]?.ToString();
            var messageId = conversationMessage.Metadata["image_message_id"]?.ToString() ??
                           conversationMessage.Metadata["message_id"]?.ToString() ??
                           conversationMessage.MessageUniqueID;
            var tikTokConversationId = conversationMessage.Metadata["image_conversation_id"]?.ToString() ??
                                      conversationMessage.Metadata["conversation_id"]?.ToString() ??
                                      conversation.TikTokUser?.TiktokConversationId;
            var tikTokBusinessId = conversationMessage.Metadata["tiktok_business_id"]?.ToString() ??
                      conversationMessage.Metadata["business_id"]?.ToString() ??
                       conversation.TikTokUser?.ChannelIdentityId;

            if (string.IsNullOrEmpty(mediaId))
            {
                _logger.LogWarning(
                    "Missing media_id for TikTok image download in conversation {ConversationId}",
                    conversation.Id);
                return;
            }

            if (string.IsNullOrEmpty(messageId))
            {
                _logger.LogWarning(
                    "Missing message_id for TikTok image download in conversation {ConversationId}",
                    conversation.Id);
                return;
            }

            if (string.IsNullOrEmpty(tikTokConversationId))
            {
                _logger.LogWarning(
                    "Missing TikTok conversation_id for image download in conversation {ConversationId}",
                    conversation.Id);
                return;
            }

            if (string.IsNullOrEmpty(tikTokBusinessId))
            {
                _logger.LogWarning(
                    "Missing TikTok business_id for image download in conversation {ConversationId}",
                    conversation.Id);
                return;
            }

            // Get TikTok configuration to find connection ID
            var tikTokConfig = await _appDbContext.ConfigTikTokConfigs
                .FirstOrDefaultAsync(x => x.CompanyId == conversation.CompanyId &&
                                          x.ChannelIdentityId == tikTokBusinessId);

            if (tikTokConfig == null)
            {
                _logger.LogError(
                    "TikTok configuration not found for company {CompanyId}, business ID {BusinessId}",
                    conversation.CompanyId, tikTokBusinessId);
                return;
            }

            try
            {
                _logger.LogInformation(
                    "Downloading TikTok image: MediaId={MediaId}, MessageId={MessageId}, ConversationId={ConversationId}",
                    mediaId, messageId, tikTokConversationId);

                // Call TikTok download image API
                var downloadResponse = await _channelsApi.ChannelsDownloadTikTokImagePostAsync(
                    downloadTikTokImageInput: new DownloadTikTokImageInput(
                        conversation.CompanyId,
                        tikTokConfig.TikTokConfigId,
                        tikTokConversationId,
                        messageId,
                        mediaId,
                        "IMAGE"
                    ));

                if (!downloadResponse.Success || string.IsNullOrEmpty(downloadResponse.Data?.DownloadUrl))
                {
                    _logger.LogError(
                        "Failed to get TikTok image download URL for conversation {ConversationId}: {ErrorMessage}",
                        conversation.Id, downloadResponse.Data?.ErrorMessage ?? downloadResponse.Message ?? "Unknown error");
                    return;
                }

                var downloadUrl = downloadResponse.Data.DownloadUrl;

                _logger.LogInformation(
                    "Successfully obtained TikTok image download URL for conversation {ConversationId}",
                    conversation.Id);

                // Create FileURLMessage to handle the download and storage manually (avoiding circular dependency)
                var fileUrlMessage = new FileURLMessage
                {
                    FileName = $"tiktok_image_{mediaId}_{DateTime.UtcNow:yyyyMMdd_HHmmss}.jpg",
                    FileURL = downloadUrl,
                    MIMEType = "image/jpeg"
                };

                // Handle file download and storage manually using existing services
                await HandleTikTokImageDownloadAsync(conversation, conversationMessage, fileUrlMessage);

                _logger.LogInformation(
                    "Successfully processed TikTok image message for conversation {ConversationId}",
                    conversation.Id);
            }
            catch (Exception apiEx)
            {
                _logger.LogError(
                    apiEx,
                    "Error downloading TikTok image for conversation {ConversationId}: {ErrorMessage}",
                    conversation.Id, apiEx.Message);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error handling incoming TikTok image message for conversation {ConversationId}: {ErrorMessage}",
                conversation.Id, ex.Message);
        }
    }

    private async Task HandleTikTokImageDownloadAsync(Conversation conversation, ConversationMessage conversationMessage, FileURLMessage fileUrlMessage)
    {
        try
        {
            _logger.LogInformation(
                "Downloading TikTok image from URL: {FileUrl} for conversation {ConversationId}",
                fileUrlMessage.FileURL, conversation.Id);

            // Download the file using HttpClient
            using var httpClient = new HttpClient();
            var response = await httpClient.GetAsync(fileUrlMessage.FileURL);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError(
                    "Failed to download TikTok image from URL {FileUrl} for conversation {ConversationId}: {StatusCode} {ReasonPhrase}",
                    fileUrlMessage.FileURL, conversation.Id, response.StatusCode, response.ReasonPhrase);
                return;
            }

            // Get the content stream
            using var contentStream = await response.Content.ReadAsStreamAsync();
            var memoryStream = new MemoryStream();
            await contentStream.CopyToAsync(memoryStream);
            memoryStream.Position = 0;

            // Update MIME type from response if available
            var contentType = response.Content.Headers.ContentType?.MediaType ?? fileUrlMessage.MIMEType ?? "image/jpeg";

            var fileName = fileUrlMessage.FileName;
            var filePath = $"Conversation/{conversation.Id}/{DateTime.UtcNow:o}/{Guid.NewGuid()}/{fileName}";

            // Upload to blob storage using IUploadService
            var uploadFileResult = await _uploadService.UploadFileBySteam(
                conversation.CompanyId,
                filePath,
                memoryStream,
                contentType);

            if (uploadFileResult?.Url == null)
            {
                _logger.LogError(
                    "Failed to upload TikTok image to blob storage for conversation {ConversationId}",
                    conversation.Id);
                return;
            }

            // Handle image metadata extraction if it's an image
            string fileMetadata = null;
            if (contentType.Contains("image"))
            {
                try
                {
                    memoryStream.Position = 0;
                    var imageSize = await ImageHelper.GetImageSizeAsync(memoryStream);
                    var dimension = new
                    {
                        width = imageSize.Width,
                        height = imageSize.Height
                    };
                    fileMetadata = System.Text.Json.JsonSerializer.Serialize(dimension);
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "Error extracting image metadata for TikTok image in conversation {ConversationId}",
                        conversation.Id);
                }
            }

            // Create UploadedFile record
            var newUploadedFile = _mapper.Map<UploadedFile>(conversationMessage);
            newUploadedFile.Filename = filePath;
            newUploadedFile.FileSize = uploadFileResult.FileSizeInByte;
            newUploadedFile.BlobContainer = conversation.CompanyId;
            newUploadedFile.MIMEType = contentType;
            newUploadedFile.Metadata = fileMetadata;

            var domainName = _configuration.GetValue<string>("Values:DomainName");
            newUploadedFile.Url = $"{domainName}/Message/File/Private/{newUploadedFile.FileId}/{fileName}";

            // Add to conversation message
            if (conversationMessage.UploadedFiles == null)
            {
                conversationMessage.UploadedFiles = new List<UploadedFile>();
            }
            conversationMessage.UploadedFiles.Add(newUploadedFile);

            _logger.LogInformation(
                "Successfully downloaded and stored TikTok image for conversation {ConversationId}, file size: {FileSize} bytes",
                conversation.Id, uploadFileResult.FileSizeInByte);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error handling TikTok image download for conversation {ConversationId}: {ErrorMessage}",
                conversation.Id, ex.Message);
        }
    }
}
