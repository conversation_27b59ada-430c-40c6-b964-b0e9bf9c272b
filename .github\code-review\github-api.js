/**
 * GitHub API module for pull request operations using Octokit
 * Provides functionality to fetch PR information and post comments
 */

import { Octokit } from '@octokit/rest';

/**
 * Extracts owner, repo, and pull number from a GitHub pull request URL
 * @param {string} prUrl - The GitHub pull request URL
 * @returns {Object} Object containing owner, repo, and pull_number
 * @throws {Error} If the URL format is invalid
 */
function parsePullRequestUrl(prUrl) {
    const urlPattern = /https:\/\/github\.com\/([^/]+)\/([^/]+)\/pull\/(\d+)/;
    const match = prUrl.match(urlPattern);

    if (!match) {
        throw new Error(`Invalid pull request URL format: ${prUrl}`);
    }

    const [, owner, repo, pull_number] = match;
    return {
        owner,
        repo,
        pull_number: parseInt(pull_number, 10)
    };
}

/**
 * Fetches comprehensive information about a pull request including metadata and file changes
 * @param {string} prUrl - The GitHub pull request URL
 * @param {string} githubToken - GitHub personal access token for authentication
 * @returns {Promise<Object>} Object containing PR data, files, and commits information
 * @throws {Error} If the API request fails or authentication is invalid
 */
export async function fetchPRInfo(prUrl, githubToken) {
    if (!prUrl || !githubToken) {
        throw new Error('Both prUrl and githubToken are required parameters');
    }

    const { owner, repo, pull_number } = parsePullRequestUrl(prUrl);
    const octokit = new Octokit({
        auth: githubToken,
        userAgent: 'SleekFlow-CodeReview/1.0.0'
    });

    try {
        // Fetch pull request metadata
        const { data: prData } = await octokit.rest.pulls.get({
            owner,
            repo,
            pull_number
        });

        // Fetch changed files with diff information
        const { data: files } = await octokit.rest.pulls.listFiles({
            owner,
            repo,
            pull_number
        });

        // Fetch commits for additional context
        const { data: commits } = await octokit.rest.pulls.listCommits({
            owner,
            repo,
            pull_number
        });

        return {
            prData: {
                id: prData.id,
                number: prData.number,
                title: prData.title,
                body: prData.body,
                state: prData.state,
                author: prData.user.login,
                authorAvatar: prData.user.avatar_url,
                createdAt: prData.created_at,
                updatedAt: prData.updated_at,
                mergedAt: prData.merged_at,
                baseRef: prData.base.ref,
                headRef: prData.head.ref,
                baseSha: prData.base.sha,
                headSha: prData.head.sha,
                mergeable: prData.mergeable,
                additions: prData.additions,
                deletions: prData.deletions,
                changedFiles: prData.changed_files,
                reviewComments: prData.review_comments,
                comments: prData.comments,
                htmlUrl: prData.html_url
            },
            files: files.map(file => ({
                filename: file.filename,
                status: file.status,
                additions: file.additions,
                deletions: file.deletions,
                changes: file.changes,
                patch: file.patch,
                blobUrl: file.blob_url,
                rawUrl: file.raw_url,
                contentsUrl: file.contents_url,
                sha: file.sha
            })),
            commits: commits.map(commit => ({
                sha: commit.sha,
                message: commit.commit.message,
                author: commit.commit.author,
                committer: commit.commit.committer,
                htmlUrl: commit.html_url
            })),
            repository: {
                owner,
                repo,
                fullName: `${owner}/${repo}`
            }
        };
    } catch (error) {
        console.error('Error fetching PR information:', error.message);

        if (error.status === 401) {
            throw new Error('Invalid GitHub token or insufficient permissions');
        } else if (error.status === 404) {
            throw new Error('Pull request not found or repository is private');
        } else if (error.status === 403) {
            throw new Error('API rate limit exceeded or access forbidden');
        } else {
            throw new Error(`Failed to fetch pull request information: ${error.message}`);
        }
    }
}

/**
 * Posts an overall summary comment to a pull request
 * @param {string} prUrl - The GitHub pull request URL
 * @param {string} comment - The comment content (supports Markdown)
 * @param {string} githubToken - GitHub personal access token for authentication
 * @returns {Promise<Object>} The created comment data from GitHub API
 * @throws {Error} If the API request fails or parameters are invalid
 */
export async function postSummaryComment(prUrl, comment, githubToken) {
    if (!prUrl || !comment || !githubToken) {
        throw new Error('prUrl, comment, and githubToken are all required parameters');
    }

    const { owner, repo, pull_number } = parsePullRequestUrl(prUrl);
    const octokit = new Octokit({
        auth: githubToken,
        userAgent: 'SleekFlow-CodeReview/1.0.0'
    });

    try {
        const { data } = await octokit.rest.issues.createComment({
            owner,
            repo,
            issue_number: pull_number,
            body: comment
        });

        return {
            id: data.id,
            htmlUrl: data.html_url,
            body: data.body,
            createdAt: data.created_at,
            updatedAt: data.updated_at,
            author: data.user.login
        };
    } catch (error) {
        console.error('Error posting summary comment:', error.message);

        if (error.status === 401) {
            throw new Error('Invalid GitHub token or insufficient permissions');
        } else if (error.status === 404) {
            throw new Error('Pull request not found or repository is private');
        } else if (error.status === 403) {
            throw new Error('API rate limit exceeded or access forbidden');
        } else {
            throw new Error(`Failed to post summary comment: ${error.message}`);
        }
    }
}

/**
 * Posts an inline comment on a specific line of a pull request file
 * @param {string} prUrl - The GitHub pull request URL
 * @param {string} filePath - The path of the file to comment on (relative to repository root)
 * @param {number} lineNumber - The line number in the new version of the file
 * @param {string} comment - The comment content (supports Markdown)
 * @param {string} githubToken - GitHub personal access token for authentication
 * @returns {Promise<Object>} The created review comment data from GitHub API
 * @throws {Error} If the API request fails or parameters are invalid
 */
export async function postInlineComment(prUrl, filePath, lineNumber, comment, githubToken) {
    if (!prUrl || !filePath || !lineNumber || !comment || !githubToken) {
        throw new Error('All parameters (prUrl, filePath, lineNumber, comment, githubToken) are required');
    }

    if (!Number.isInteger(lineNumber) || lineNumber < 1) {
        throw new Error('lineNumber must be a positive integer');
    }

    const { owner, repo, pull_number } = parsePullRequestUrl(prUrl);
    const octokit = new Octokit({
        auth: githubToken,
        userAgent: 'SleekFlow-CodeReview/1.0.0'
    });

    try {
        // Fetch the pull request to get the head commit SHA
        const { data: prData } = await octokit.rest.pulls.get({
            owner,
            repo,
            pull_number
        });
        const commitId = prData.head.sha;

        // Fetch the list of files in the pull request
        const { data: files } = await octokit.rest.pulls.listFiles({
            owner,
            repo,
            pull_number
        });

        const file = files.find(f => f.filename === filePath);
        if (!file) {
            throw new Error(`File "${filePath}" not found in pull request changes`);
        }

        if (!file.patch) {
            throw new Error(`No diff available for file "${filePath}" (file might be binary or renamed without changes)`);
        }

        // Calculate the position in the diff for the specified line number
        const position = calculateDiffPosition(file.patch, lineNumber);
        if (position === null) {
            throw new Error(`Line ${lineNumber} not found in the diff for file "${filePath}"`);
        }

        // Post the inline comment
        const { data } = await octokit.rest.pulls.createReviewComment({
            owner,
            repo,
            pull_number,
            commit_id: commitId,
            path: filePath,
            position,
            body: comment
        });

        return {
            id: data.id,
            htmlUrl: data.html_url,
            path: data.path,
            position: data.position,
            originalPosition: data.original_position,
            commitId: data.commit_id,
            body: data.body,
            createdAt: data.created_at,
            updatedAt: data.updated_at,
            author: data.user.login,
            line: data.line,
            side: data.side
        };
    } catch (error) {
        console.error('Error posting inline comment:', error.message);

        if (error.status === 401) {
            throw new Error('Invalid GitHub token or insufficient permissions');
        } else if (error.status === 404) {
            throw new Error('Pull request, file, or line not found');
        } else if (error.status === 403) {
            throw new Error('API rate limit exceeded or access forbidden');
        } else {
            throw new Error(`Failed to post inline comment: ${error.message}`);
        }
    }
}

/**
 * Calculates the position in a diff for a given line number
 * @param {string} patch - The diff patch string
 * @param {number} targetLineNumber - The target line number in the new file
 * @returns {number|null} The position in the diff, or null if line not found
 */
function calculateDiffPosition(patch, targetLineNumber) {
    const lines = patch.split('\n');
    let position = 0;
    let newFileLineNumber = 0;
    let inHunk = false;

    for (const line of lines) {
        if (line.startsWith('@@')) {
            // Parse hunk header to get starting line number
            const match = line.match(/@@ -\d+(?:,\d+)? \+(\d+)(?:,\d+)? @@/);
            if (match) {
                newFileLineNumber = parseInt(match[1], 10) - 1; // -1 because we increment before checking
                inHunk = true;
            }
        } else if (inHunk) {
            position++;

            if (line.startsWith('-')) {
                // Deletion - don't increment new file line number
                continue;
            } else if (line.startsWith('+')) {
                // Addition - increment new file line number
                newFileLineNumber++;
            } else if (line.startsWith(' ')) {
                // Context - increment new file line number
                newFileLineNumber++;
            } else {
                // End of hunk
                inHunk = false;
                continue;
            }

            if (newFileLineNumber === targetLineNumber) {
                return position;
            }
        }
    }

    return null;
}
