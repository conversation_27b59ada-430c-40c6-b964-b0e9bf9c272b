using System;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Travis_backend.ChannelDomain.Models.LiveChatV2;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.Database;
using Travis_backend.MessageDomain.Models;
using Travis_backend.OpenTelemetry.Meters;
using Travis_backend.OpenTelemetry.Meters.Constants;
using Travis_backend.Utils;

namespace Travis_backend.MessageDomain.ChannelMessageProvider.ChannelMessageHandlers;

// [LiveChatV2]
public class LiveChatV2ChannelMessageHandler : IChannelMessageHandler
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IConversationMeters _conversationMeters;

    private readonly ILogger<LiveChatV2ChannelMessageHandler> _logger;

    public string ChannelType => ChannelTypes.LiveChatV2;

    public LiveChatV2ChannelMessageHandler(ApplicationDbContext appDbContext, IConversationMeters conversationMeters, ILogger<LiveChatV2ChannelMessageHandler> logger)
    {
        _appDbContext = appDbContext;
        _conversationMeters = conversationMeters;
        _logger = logger;
    }

    public async ValueTask<ConversationMessage> SendChannelMessageAsync(
        Conversation conversation,
        ConversationMessage conversationMessage)
    {
        conversationMessage.Status = MessageStatus.Read;

        try
        {
            var urlRegex = new Regex(@"https?://\S+");
            if (conversationMessage.MessageContent != null && urlRegex.IsMatch(conversationMessage.MessageContent))
            {
                var url = urlRegex.Match(conversationMessage.MessageContent).Value;
                _logger.LogInformation("Detected link message: {Url}", url);

                var metadata = await HtmlMetadataFetcher.GetPageMetadata(url, _logger);

                // If the title is not null, transform it to link message
                if (metadata?.Title != null && metadata.Title != string.Empty)
                {
                    var liveChatV2LinkMessage = new LiveChatV2LinkMessageObject
                    {
                        Title = metadata.Title,
                        Description = metadata.Description ?? string.Empty,
                        ImageUrl = metadata.Image ?? string.Empty,
                        Url = url
                    };

                    conversationMessage.MessageType = MessageTypes.Interactive;
                    conversationMessage.ExtendedMessagePayload = new ExtendedMessagePayload()
                    {
                        ExtendedMessageType = ExtendedMessageType.LiveChatV2LinkMessage,
                        ExtendedMessagePayloadDetail = new ExtendedMessagePayloadDetail
                        {
                            LiveChatV2MessageObject = new LiveChatV2MessageObject
                            {
                                LinkMessage = liveChatV2LinkMessage
                            }
                        }
                    };
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Can't fetch metadata for link message");
        }

        await _appDbContext.SaveChangesAsync();

        _conversationMeters.IncrementCounter(ChannelTypes.LiveChatV2, ConversationMeterOptions.SendSuccess);

        return conversationMessage;
    }

    public async ValueTask<ConversationMessage> ReceiveChannelMessageAsync(
        Conversation conversation,
        ConversationMessage conversationMessage)
    {
        conversationMessage.Status = MessageStatus.Read;
        await _appDbContext.SaveChangesAsync();

        _conversationMeters.IncrementCounter(ChannelTypes.LiveChatV2, ConversationMeterOptions.ReceiveSuccess);

        return conversationMessage;
    }
}