using System;
using Microsoft.Extensions.Configuration;
using Travis_backend.ChannelDomain.Helpers.LiveChatV2;

namespace Travis_backend.ChannelDomain.Services.LiveChatV2;

public interface ILiveChatV2AuthManager
{
    public string GenerateSignature(string senderId);
}

public class LiveChatV2AuthManager : ILiveChatV2AuthManager
{
    private readonly IConfiguration _configuration;

    public LiveChatV2AuthManager(IConfiguration configuration)
    {
        _configuration = configuration;
    }

    public string GenerateSignature(string senderId)
    {
        var secret = _configuration["LiveChat:SecretKey"];
        if (string.IsNullOrEmpty(secret))
        {
            throw new InvalidOperationException("Secret key not configured. Check environment variables.");
        }

        return LiveChatV2SignatureHelper.GenerateHmacSignature(senderId, secret);
    }
}