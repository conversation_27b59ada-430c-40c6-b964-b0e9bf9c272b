using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.Database.Services;
using Travis_backend.FlowHubs.Constants;
using Travis_backend.FlowHubs.Models;
using Travis_backend.FlowHubs.Services;
using Travis_backend.Utils;

namespace Travis_backend.FlowHubs.FieldMetadataGenerator;

public class LiveChatWebsiteUrlDetectedEventFieldMetadataGenerator
    : FlowHubEventFieldMetadataGeneratorBase,
        IFlowHubEventFieldMetadataGenerator
{
    public string EventName => TriggerIds.LiveChtWebsiteUrlDetected;

    public Dictionary<string, string> FieldPathOptionRequestPathDict { get; } = new ()
    {
        { "channel_id", "FlowHub/SelectOptions/LiveChatChannels" },
        { "lists[*].list_id", "FlowHub/SelectOptions/ContactLists" },
        { "contactOwner.id", "FlowHub/SelectOptions/Staffs" },
    };

    public LiveChatWebsiteUrlDetectedEventFieldMetadataGenerator(
        IDbContextService dbContextService,
        IFieldMetadataVariableVisibilityService fieldMetadataVariableVisibilityService)
        : base(dbContextService, fieldMetadataVariableVisibilityService)
    {
    }

    public async Task<(HashSet<FieldMetadata> FieldMetadataSet, Type EventBodyType, string ContactObjectName)> GenerateAsync(
        string companyId,
        Dictionary<string, object?>? parameters)
    {
        var (userProfileDict, customFieldOptionsRequestPathDict) = await GenerateUserProfileDictAsync(companyId);
        var eventBody = JsonUtils.GenerateSampleObjectFromType<OnLiveChatWebsiteUrlDetectedEventBody>();

        eventBody.Contact = userProfileDict;

        var eventBodyJsonString = eventBody.ToJson();
        var eventBodyJson = JObject.Parse(eventBodyJsonString);

        var jsonData = JsonUtils.SimplifyJsonData(eventBodyJson.ToString());

        var fieldMetadataSet = GenerateFilteredFieldMetadata(jsonData, EventName);

        PopulateOptionRequestPath(
            fieldMetadataSet,
            customFieldOptionsRequestPathDict,
            FieldPathOptionRequestPathDict);

        return (fieldMetadataSet, typeof(OnLiveChatWebsiteUrlDetectedEventBody), nameof(eventBody.Contact));
    }
}