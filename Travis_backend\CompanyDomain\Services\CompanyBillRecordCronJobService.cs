#nullable enable

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Helpers;
using Travis_backend.InternalDomain.Models;
using Travis_backend.InternalIntegrationHubDomain.Services;

namespace Travis_backend.CompanyDomain.Services;

public interface ICompanyBillRecordCronJobService
{
    Task ProcessDailyNoticePeriodBillRecordsAsync();
}

public class CompanyBillRecordCronJobService(
    ICompanyBillRecordService companyBillRecordService,
    ApplicationDbContext dbContext,
    ILogger<CompanyBillRecordCronJobService> logger,
    IBillRecordRevenueCalculatorService billRecordRevenueCalculatorService) : ICompanyBillRecordCronJobService
{
    public async Task ProcessDailyNoticePeriodBillRecordsAsync()
    {
        var today = DateTime.UtcNow.Date;

        logger.LogInformation("Starting ProcessDailyNoticePeriodBillRecordsAsync for date: {Today}", today);

        try
        {
            // Step 1: Get eligible bill records
            var eligibleBillRecords = await dbContext.CompanyBillRecords
                .Include(x => x.SubscriptionPlan)
                .Include(x => x.PurchaseStaff)
                .Where(x => x.IsIrregularPlan &&
                            x.SubscriptionPlanId.Contains("enterprise") &&
                            x.NoticePeriod.HasValue &&
                            x.NoticePeriod.Value > 0 &&
                            x.Status == BillStatus.Active &&
                            x.PeriodEnd > today &&
                            x.IsAutoRenewalEnabled)
                .ToListAsync();

            logger.LogInformation(
                "Found {Count} eligible Enterprise bill records with notice period",
                eligibleBillRecords.Count);

            if (eligibleBillRecords.Count == 0)
            {
                return; // Early exit if no records to process
            }

            var recordsProcessed = 0;

            // Step 2: Check if today is the notice date for each bill record
            foreach (var billRecord in eligibleBillRecords)
            {
                try
                {
                    // Get the actual period end from metadata, fallback to PeriodEnd if not available
                    var actualPeriodEnd = BillRecordRevenueCalculatorHelper.GetActualPeriodEndFromMetadata(billRecord);

                    // Calculate notice date: actual period end - notice period (days)
                    var noticeDate = actualPeriodEnd.AddDays(-billRecord.NoticePeriod!.Value).Date;

                    // Step 3: Check if today is the notice date
                    if (noticeDate != today)
                    {
                        continue;
                    }

                    logger.LogInformation(
                        "Processing notice period for BillRecord {BillRecordId}, Company {CompanyId}",
                        billRecord.Id,
                        billRecord.CompanyId);

                    // Step 4: Create a new bill record by copying the existing one
                    var newBillRecord = await CreateNoticePeriodBillRecordAsync(billRecord);

                    // Step 5: Create an invoice
                    CreateFirstInvoiceAsync(newBillRecord, today);

                    recordsProcessed++;
                }
                catch (Exception ex)
                {
                    logger.LogError(
                        ex,
                        "Error processing notice period for bill record {BillRecordId}: {ErrorMessage}",
                        billRecord.Id,
                        ex.Message);

                    // Continue processing other records even if one fails
                }
            }

            logger.LogInformation(
                "Completed ProcessDailyNoticePeriodBillRecordsAsync. Processed {RecordsProcessed} records",
                recordsProcessed);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in ProcessDailyNoticePeriodBillRecordsAsync: {ErrorMessage}", ex.Message);
            throw new InvalidOperationException("Failed to process daily notice period bill records", ex);
        }
    }

    private async Task<BillRecord> CreateNoticePeriodBillRecordAsync(BillRecord originalBillRecord)
    {
        logger.LogInformation(
            "Creating notice period bill record for BillRecord {BillRecordId}",
            originalBillRecord.Id);

        var actualPeriodEnd = BillRecordRevenueCalculatorHelper.GetActualPeriodEndFromMetadata(originalBillRecord);

        // newPeriodStart = actualPeriodEnd, unnecessary create extra var
        var newPeriodEnd = actualPeriodEnd.AddMonths(
            BillRecordRevenueCalculatorHelper.GetMonthDiff(
                originalBillRecord.PeriodStart,
                originalBillRecord.PeriodEnd));

        // Handle custom payment splits for the new period
        List<PaymentSplit>? newPaymentSplits = null;
        if (originalBillRecord is { PaymentIntervalType: PaymentIntervalType.Custom, PaymentSplits.Count: > 0 })
        {
            // Add the subscription interval to the original payment split dates
            newPaymentSplits =
            [
                .. originalBillRecord.PaymentSplits.Select(split => new PaymentSplit
                {
                    PaymentDate = split.PaymentDate.AddMonths(
                        BillRecordRevenueCalculatorHelper.GetMonthDiff(
                            originalBillRecord.PeriodStart,
                            originalBillRecord.PeriodEnd)),
                    SplitPercentage = split.SplitPercentage
                }).Where(split => split.PaymentDate >= actualPeriodEnd && split.PaymentDate <= newPeriodEnd)
            ];
        }

        // Adding grace period metadata
        var metadata = new Dictionary<string, string>();
        var newGracePeriodEnd = newPeriodEnd.AddDays(7);

        metadata["grace_period_start"] = newPeriodEnd.ToString("O");
        metadata["grace_period_end"] = newGracePeriodEnd.ToString("O");
        metadata["actual_period_start"] = actualPeriodEnd.ToString("O");
        metadata["actual_period_end"] = newPeriodEnd.ToString("O");

        // Create a new bill record by copying the original one with different period dates
        var newBillRecord = new BillRecord
        {
            // Copy basic properties
            CompanyId = originalBillRecord.CompanyId,
            SubscriptionPlanId = originalBillRecord.SubscriptionPlanId,
            SubscriptionPlan = originalBillRecord.SubscriptionPlan,
            Status = BillStatus.Active,
            PaymentStatus = originalBillRecord.PaymentStatus,
            PayAmount = originalBillRecord.PayAmount,
            PurchaseStaffId = originalBillRecord.PurchaseStaffId,
            SubscriptionTier = originalBillRecord.SubscriptionTier,
            currency = originalBillRecord.currency,
            quantity = originalBillRecord.quantity,

            // Set new period dates - start from the original period end
            PeriodStart = actualPeriodEnd,
            PeriodEnd = newGracePeriodEnd,
            PaymentSplits = newPaymentSplits,

            // Copy irregular plan properties
            IsIrregularPlan = originalBillRecord.IsIrregularPlan,
            PaymentIntervalType = originalBillRecord.PaymentIntervalType,
            PaymentTerms = originalBillRecord.PaymentTerms,
            NoticePeriod = originalBillRecord.NoticePeriod,
            IsAutoRenewalEnabled = originalBillRecord.IsAutoRenewalEnabled,

            // Copy other properties
            IsFreeTrial = false, // The new billing cycle shouldn't be a free trial
            PaidByReseller = originalBillRecord.PaidByReseller,
            IsCustomized = originalBillRecord.IsCustomized,

            // Set usage cycle if applicable
            UsageCycleStart = originalBillRecord.UsageCycleStart,
            UsageCycleEnd = originalBillRecord.UsageCycleEnd,

            // Reference to the original bill record
            ExtendFromBillRecordId = originalBillRecord.Id,

            // Add metadata with the grace period
            metadata = metadata
        };

        await companyBillRecordService.CreateBillRecordAsync(newBillRecord);

        logger.LogInformation(
            "Successfully created notice period bill record for BillRecord {BillRecordId}, new BillRecord ID: {NewBillRecordId}",
            originalBillRecord.Id,
            newBillRecord.Id);

        return newBillRecord;
    }

    private void CreateFirstInvoiceAsync(BillRecord billRecord, DateTime today)
    {
        // Skip first invoice creation for custom intervals;
        // handled by ProcessDailyIrregularPlanInvoicesAsync cron job.
        if (billRecord.PaymentIntervalType == PaymentIntervalType.Custom)
        {
            logger.LogInformation(
                "Skipping invoice creation for custom intervals; handled by ProcessDailyIrregularPlanInvoicesAsync cron job.");
            return;
        }

        logger.LogInformation("Creating invoice for notice period BillRecord {BillRecordId}", billRecord.Id);

        var invoiceNumber = $"INV-{billRecord.Id}-1";

        // Calculate the correct invoice amount based on payment interval type
        var invoiceAmount = billRecordRevenueCalculatorService.CalculateInvoiceAmount(billRecord, today);

        var jobId = BackgroundJob.Enqueue<IInternalIntegrationService>(x => x.CreateInvoiceAsync(
            invoiceNumber,
            billRecord.CompanyId,
            invoiceAmount,
            0,
            0,
            billRecord.PeriodStart,
            BillRecordRevenueCalculatorHelper.GetActualPeriodEndFromMetadata(billRecord),
            billRecord.PaymentTerms,
            billRecord.currency,
            null,
            today));

        logger.LogInformation(
            "Successfully queued NetSuite invoice {InvoiceNumber} for notice period BillRecord {BillRecordId} with amount {InvoiceAmount} as background job. JobId: {JobId}",
            invoiceNumber,
            billRecord.Id,
            invoiceAmount,
            jobId);
    }
}