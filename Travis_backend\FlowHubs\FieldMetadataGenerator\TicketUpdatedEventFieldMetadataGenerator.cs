﻿#nullable enable
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.Database.Services;
using Travis_backend.Extensions;
using Travis_backend.FlowHubs.Constants;
using Travis_backend.FlowHubs.Models;
using Travis_backend.FlowHubs.Services;
using Travis_backend.Utils;
using Newtonsoft.Json.Linq;

namespace Travis_backend.FlowHubs.FieldMetadataGenerator;

public sealed class TicketUpdatedEventFieldMetadataGenerator
    : FlowHubEventFieldMetadataGeneratorBase,
        IFlowHubEventFieldMetadataGenerator
{
    public string EventName => TriggerIds.TicketUpdated;

    public Dictionary<string, string> FieldPathOptionRequestPathDict { get; } = new()
    {
        { "updated_properties.type_id", "FlowHub/SelectOptions/TicketTypes" },
        { "updated_properties.priority_id", "FlowHub/SelectOptions/TicketPriorities" },
        { "updated_properties.assignee_id", "FlowHub/SelectOptions/TicketAssignees" },
        { "updated_properties.status_id", "FlowHub/SelectOptions/TicketStatuses" },
        { "updated_property_list[*]", "FlowHub/SelectOptions/TicketUpdatedProperties" },
        { "ticket.status_id", "FlowHub/SelectOptions/TicketStatuses" },
        { "ticket.priority_id", "FlowHub/SelectOptions/TicketPriorities" },
        { "ticket.type_id", "FlowHub/SelectOptions/TicketTypes" },
        { "ticket.channel.channel_identity_id", "FlowHub/SelectOptions/Channels" },
        { "sleekflow_staff_id", "FlowHub/SelectOptions/Staffs" },

        { "ticket.current_assignee", "FlowHub/SelectOptions/TicketAssignees" },
        { "ticket.resolution_agent", "FlowHub/SelectOptions/TicketAssignees" },
        { "ticket.first_assignee", "FlowHub/SelectOptions/TicketAssignees" },
        { "ticket.created_by", "FlowHub/SelectOptions/Staffs" },
        { "contactOwner.id", "FlowHub/SelectOptions/Staffs" },

        { "ticket.channel.channel_name", "FlowHub/SelectOptions/ChannelNames" },
        { "ticket.status_name", "FlowHub/SelectOptions/TicketStatusNames" },
        { "ticket.priority_name", "FlowHub/SelectOptions/TicketPriorityNames" },
        { "ticket.type_name", "FlowHub/SelectOptions/TicketTypeNames" },
    };

    public TicketUpdatedEventFieldMetadataGenerator(
        IDbContextService dbContextService,
        IFieldMetadataVariableVisibilityService fieldMetadataVariableVisibilityService)
        : base(dbContextService, fieldMetadataVariableVisibilityService)
    {
    }

    public async Task<(HashSet<FieldMetadata> FieldMetadataSet, Type EventBodyType, string ContactObjectName)> GenerateAsync(
        string companyId,
        Dictionary<string, object?>? parameters)
    {
        var (userProfileDict, customFieldOptionsRequestPathDict) = await GenerateUserProfileDictAsync(companyId);
        var eventBody = JsonUtils.GenerateSampleObjectFromType<OnTicketUpdatedEventBody>();
        eventBody.UpdatedPropertyList = ["sample_updated_property"];
        eventBody.UpdatedProperties = new Dictionary<string, object>()
        {
            {
                "type_id", "sample_type_id"
            },
            {
                "status_id", "sample_status_id"
            },
            {
                "priority_id", "sample_priority_id"
            },
            {
                "due_date", DateTime.Now
            },
            {
                "assignee_id", "sample_assignee_id"
            }
        };
        eventBody.Contact = userProfileDict;

        var eventBodyJsonString = eventBody.ToJson();
        var eventBodyJson = JObject.Parse(eventBodyJsonString);
        AddContactExpansionToEventBodyJson(eventBodyJson);

        var jsonData = JsonUtils.SimplifyJsonData(eventBodyJson.ToString());

        var fieldMetadataSet = GenerateFilteredFieldMetadata(jsonData, EventName);

        PopulateOptionRequestPath(
            fieldMetadataSet,
            customFieldOptionsRequestPathDict,
            FieldPathOptionRequestPathDict);

        return (fieldMetadataSet, typeof(OnTicketUpdatedEventBody), nameof(eventBody.Contact));
    }
}