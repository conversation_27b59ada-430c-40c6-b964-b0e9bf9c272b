using System.Net;
using Microsoft.AspNetCore.Http;
using Travis_backend.Helpers;

namespace Sleekflow.Core.Tests.Helpers;

[TestFixture]
public class ClientIpHelperTests
{
    private DefaultHttpContext _httpContext;

    [SetUp]
    public void Setup()
    {
        _httpContext = new DefaultHttpContext();
    }

    [Test]
    public void GetClientIp_ShouldReturnIpFromXForwardedForHeader()
    {
        // Arrange
        _httpContext.Request.Headers["X-Forwarded-For"] = "***********";

        // Act
        var result = ClientIpHelper.GetClientIp(_httpContext);

        // Assert
        Assert.AreEqual("***********", result);
    }

    [Test]
    public void GetClientIp_ShouldReturnFirstIpFromCommaSeparatedXForwardedForHeader()
    {
        // Arrange
        _httpContext.Request.Headers["X-Forwarded-For"] = "***********, ********";

        // Act
        var result = ClientIpHelper.GetClientIp(_httpContext);

        // Assert
        Assert.AreEqual("***********", result);
    }

    [Test]
    public void GetClientIp_ShouldReturnIpFromXAzureClientIpHeader()
    {
        // Arrange
        _httpContext.Request.Headers["X-Azure-ClientIP"] = "***********";

        // Act
        var result = ClientIpHelper.GetClientIp(_httpContext);

        // Assert
        Assert.AreEqual("***********", result);
    }

    [Test]
    public void GetClientIp_ShouldReturnIpFromXAzureSocketIpHeader()
    {
        // Arrange
        _httpContext.Request.Headers["X-Azure-SocketIP"] = "***********";

        // Act
        var result = ClientIpHelper.GetClientIp(_httpContext);

        // Assert
        Assert.AreEqual("***********", result);
    }

    [Test]
    public void GetClientIp_ShouldReturnIpFromXClientIpHeader()
    {
        // Arrange
        _httpContext.Request.Headers["X-Client-IP"] = "***********";

        // Act
        var result = ClientIpHelper.GetClientIp(_httpContext);

        // Assert
        Assert.AreEqual("***********", result);
    }

    [Test]
    public void GetClientIp_ShouldReturnRemoteIpAddressWhenNoHeadersArePresent()
    {
        // Arrange
        _httpContext.Connection.RemoteIpAddress = IPAddress.Parse("127.0.0.1");

        // Act
        var result = ClientIpHelper.GetClientIp(_httpContext);

        // Assert
        Assert.AreEqual("127.0.0.1", result);
    }

    [Test]
    public void GetClientIp_ShouldReturnNullWhenNoIpIsAvailable()
    {
        // Arrange
        _httpContext.Connection.RemoteIpAddress = null;

        // Act
        var result = ClientIpHelper.GetClientIp(_httpContext);

        // Assert
        Assert.IsNull(result);
    }

    [Test]
    public void GetClientIp_ShouldPrioritizeHeadersCorrectly()
    {
        // Arrange
        _httpContext.Request.Headers["X-Forwarded-For"] = "*******";
        _httpContext.Request.Headers["X-Azure-ClientIP"] = "*******";
        _httpContext.Request.Headers["X-Azure-SocketIP"] = "*******";
        _httpContext.Request.Headers["X-Client-IP"] = "*******";
        _httpContext.Connection.RemoteIpAddress = IPAddress.Parse("*******");

        // Act & Assert

        // X-Forwarded-For has top priority
        Assert.AreEqual("*******", ClientIpHelper.GetClientIp(_httpContext));

        // Then X-Azure-ClientIP
        _httpContext.Request.Headers.Remove("X-Forwarded-For");
        Assert.AreEqual("*******", ClientIpHelper.GetClientIp(_httpContext));

        // Then X-Azure-SocketIP
        _httpContext.Request.Headers.Remove("X-Azure-ClientIP");
        Assert.AreEqual("*******", ClientIpHelper.GetClientIp(_httpContext));

        // Then X-Client-IP
        _httpContext.Request.Headers.Remove("X-Azure-SocketIP");
        Assert.AreEqual("*******", ClientIpHelper.GetClientIp(_httpContext));

        // Finally RemoteIpAddress
        _httpContext.Request.Headers.Remove("X-Client-IP");
        Assert.AreEqual("*******", ClientIpHelper.GetClientIp(_httpContext));
    }
}