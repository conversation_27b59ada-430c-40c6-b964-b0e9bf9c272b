﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.ChannelDomain.Models;
using Travis_backend.ChannelDomain.ViewModels;
using Travis_backend.ChannelDomain.ViewModels.Interfaces;
using Travis_backend.ConversationServices;
using Travis_backend.ConversationServices.Models;
using Travis_backend.Enums;
using Travis_backend.MessageDomain.Services;
using Travis_backend.Models.ChatChannelConfig;

namespace Travis_backend.Controllers.MessageControllers;

[Route("MessagingChannels")]
[Authorize]
public class MessagingChannelController : Controller
{
    private readonly IMapper _mapper;
    private readonly ICoreService _coreService;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ILogger<MessagingChannelController> _logger;
    private readonly IMessagingChannelService _messagingChannelService;

    public MessagingChannelController(
        UserManager<ApplicationUser> userManager,
        ILogger<MessagingChannelController> logger,
        ICoreService coreService,
        IMessagingChannelService messagingChannelService,
        IMapper mapper)
    {
        _userManager = userManager;
        _logger = logger;
        _coreService = coreService;
        _messagingChannelService = messagingChannelService;
        _mapper = mapper;
    }

    public record GetAllMessagingChannelsOutput(List<IMessagingChannelDto> MessagingChannels);

    [HttpPost]
    [Route("GetAllMessagingChannels")]
    public async Task<ActionResult<GetAllMessagingChannelsOutput>> GetAllMessagingChannels()
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        var allChannels = await _messagingChannelService.GetAllChannelsAsync(companyUser.CompanyId);

        return Ok(new GetAllMessagingChannelsOutput(allChannels.Select(x => x.MapFromIMessagingChannel()).ToList()));
    }

    public record GetMessagingChannelOutput(IMessagingChannelDto MessagingChannel);

    public record GetMessagingChannelInput(string ChannelType, string ChannelIdentityId);

    [HttpPost]
    [Route("GetMessagingChannel")]
    public async Task<ActionResult<GetMessagingChannelOutput>> GetMessagingChannel(
        [FromBody]
        GetMessagingChannelInput input)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        var channel = await _messagingChannelService.GetChannelAsync(
            companyUser.CompanyId,
            input.ChannelType,
            input.ChannelIdentityId);

        return Ok(new GetMessagingChannelOutput(channel.MapFromIMessagingChannel()));
    }

    public class GetAvailableChannelsResults
    {
        public List<WhatsAppConfigViewModel> WhatsAppConfigs { get; }

        public List<WhatsApp360DialogConfigViewModel> WhatsApp360DialogConfigs { get; }

        public List<WhatsappCloudApiConfigViewModel> WhatsappCloudApiConfigs { get; }

        public List<FacebookConfigViewModel> FacebookConfigs { get; }

        public List<InstagramConfigResponse> InstagramConfigs { get; }

        public EmailConfigViewModel EmailConfig { get; }

        public WeChatConfigViewModel WeChatConfig { get; }

        public List<LineConfigViewModel> LineConfigs { get; }

        public List<ViberConfigResponse> ViberConfigs { get; }

        public List<TelegramConfigDto> TelegramConfigs { get; }

        public List<LiveChatV2ConfigDto> LiveChatV2Configs { get; }

        public List<SMSViewConfigViewModel> SmsConfigs { get; }

        public List<TikTokConfigViewModel> TikTokConfigs { get; }


        public GetAvailableChannelsResults(
            List<WhatsAppConfigViewModel> whatsAppConfigs,
            List<WhatsApp360DialogConfigViewModel> whatsApp360DialogConfigs,
            List<WhatsappCloudApiConfigViewModel> whatsappCloudApiConfigs,
            List<FacebookConfigViewModel> facebookConfigs,
            List<InstagramConfigResponse> instagramConfigs,
            EmailConfigViewModel emailConfig,
            WeChatConfigViewModel weChatConfig,
            List<LineConfigViewModel> lineConfigs,
            List<ViberConfigResponse> viberConfigs,
            List<TelegramConfigDto> telegramConfigs,
            List<LiveChatV2ConfigDto> liveChatV2Configs,
            List<SMSViewConfigViewModel> smsConfigs,
            List<TikTokConfigViewModel> tikTokConfigs)
        {
            WhatsAppConfigs = whatsAppConfigs;
            WhatsApp360DialogConfigs = whatsApp360DialogConfigs;
            WhatsappCloudApiConfigs = whatsappCloudApiConfigs;
            FacebookConfigs = facebookConfigs;
            InstagramConfigs = instagramConfigs;
            EmailConfig = emailConfig;
            WeChatConfig = weChatConfig;
            LineConfigs = lineConfigs;
            ViberConfigs = viberConfigs;
            TelegramConfigs = telegramConfigs;
            LiveChatV2Configs = liveChatV2Configs;
            SmsConfigs = smsConfigs;
            TikTokConfigs = tikTokConfigs;
        }
    }

    [HttpPost]
    [Route("GetAvailableChannels")]
    public async Task<ActionResult<GetAvailableChannelsResults>> GetAvailableChannels()
    {
        if (User.Identity is { IsAuthenticated: false })
        {
            return Unauthorized();
        }

        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser is null)
        {
            return Unauthorized();
        }

        var (
            whatsAppConfigs,
            whatsApp360DialogConfigs,
            whatsappCloudApiConfigs,
            facebookConfigs,
            instagramConfigs,
            emailConfig,
            weChatConfig,
            lineConfigs,
            viberConfigs,
            telegramConfigs,
            liveChatV2Configs,
            smsConfigs,
            tikTokConfigs) = await _messagingChannelService.GetAvailableChannels(companyUser);

        return Ok(
            new GetAvailableChannelsResults(
                _mapper.Map<List<WhatsAppConfigViewModel>>(whatsAppConfigs),
                _mapper.Map<List<WhatsApp360DialogConfigViewModel>>(whatsApp360DialogConfigs),
                _mapper.Map<List<WhatsappCloudApiConfigViewModel>>(whatsappCloudApiConfigs),
                _mapper.Map<List<FacebookConfigViewModel>>(facebookConfigs),
                _mapper.Map<List<InstagramConfigResponse>>(instagramConfigs),
                _mapper.Map<EmailConfigViewModel>(emailConfig),
                _mapper.Map<WeChatConfigViewModel>(weChatConfig),
                _mapper.Map<List<LineConfigViewModel>>(lineConfigs),
                _mapper.Map<List<ViberConfigResponse>>(viberConfigs),
                _mapper.Map<List<TelegramConfigDto>>(telegramConfigs),
                _mapper.Map<List<LiveChatV2ConfigDto>>(liveChatV2Configs),
                _mapper.Map<List<SMSViewConfigViewModel>>(smsConfigs),
                _mapper.Map<List<TikTokConfigViewModel>>(tikTokConfigs)));
    }
}