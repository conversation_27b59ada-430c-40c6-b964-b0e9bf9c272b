﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Security.Claims;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Hangfire;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using Stripe;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.AccountAuthenticationDomain.ViewModels;
using Travis_backend.AutomationDomain.Models;
using Travis_backend.BackgroundTaskServices.Attributes;
using Travis_backend.Cache;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.CommonDomain.Models;
using Travis_backend.CommonDomain.Repositories;
using Travis_backend.CommonDomain.Services;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.CompanyDomain.ViewModels;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Constants;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ContactDomain.Repositories;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ContactDomain.ViewModels;
using Travis_backend.CoreDomain.Models;
using Travis_backend.Database;
using Travis_backend.Database.Services;
using Travis_backend.**********************;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.FlowHubs;
using Travis_backend.FlowHubs.Models;
using Travis_backend.Helpers;
using Travis_backend.IntegrationServices;
using Travis_backend.InternalDomain.Services;
using Travis_backend.InternalIntegrationHubDomain.Services;
using Travis_backend.MessageDomain.Models;
using Travis_backend.Services.Internal;
using Travis_backend.StripeIntegrationDomain.Clients;
using Travis_backend.SubscriptionPlanDomain.Constants;
using Travis_backend.SubscriptionPlanDomain.Enums;
using Travis_backend.SubscriptionPlanDomain.Models;
using Travis_backend.SubscriptionPlanDomain.Services;
   using Travis_backend.Telemetries;
   using Travis_backend.Telemetries.Constants;
   using static Travis_backend.ContactDomain.Services.UserProfileService;
   using JsonSerializer = System.Text.Json.JsonSerializer;

   namespace Travis_backend.ConversationServices
{
    public interface ICoreFieldService
    {
        Task<CoreCustomField> GetCustomFieldByFieldName(string fieldName);
    }

    public class CoreFieldService : ICoreFieldService
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly IMapper _mapper;
        private readonly IConfiguration _configuration;

        private readonly ILogger _logger;

        public CoreFieldService(
                ApplicationDbContext appDbContext,
                IMapper mapper,
                IConfiguration configuration,
                ILogger<CoreFieldService> logger)
        {
            _appDbContext = appDbContext;
            _mapper = mapper;
            _configuration = configuration;
            _logger = logger;
        }

        public async Task<CoreCustomField> GetCustomFieldByFieldName(string fieldName)
        {
            var companyField = await _appDbContext.CoreCustomFields
                .FirstOrDefaultAsync(x => x.FieldName.ToLower() == fieldName.ToLower());

            return companyField;
        }
    }

    public interface ICoreService
    {
        Task DeleteBlankWebClientSenders();

        // Task RemoveUpdatedNotifications();
        Task AddToSleekFlowCRM(
            string userCompanyId,
            ApplicationUser userIdentity,
            string role,
            string heardFrom,
            List<ImportHeader> headers,
            List<string> fields,
            RegisterCompanyViewModel registerCompanyViewModel = null);

        Task<Staff> GetCompanyStaff(ApplicationUser user);

        Task<Staff> GetCompanyStaffByStaffId(long staffId);

        Task<Staff> GetCompanyStaff(long staffId, string companyId);

        Task<Staff> GetCompanyStaffByIdentityId(string identityId);


        Task<AuthenticationResponse> GetToken(ApplicationUser user);

        Task UpdateUserLastLoginAt(string userId, DateTime lastLoginAt);

        Task<AuthenticationResponse> GetTokenWithLoginAsSecret(CmsLoginAsSecret loginAsSecret);

        Task RemoveCompanyData(string companyId);

        Task<bool> RemoveStaffData(string companyId, long staffId);

        Task DeleteIdentityUser(string identityId);

        Task UpdateSleekFlowLeadStage(string phoneNumber, string value);

        Task CreateCompany(ApplicationUser userIdentity, RegisterCompanyViewModel registerCompanyViewModel);

        Task AddInfoToSleekFlowCRM(
            string userCompanyId,
            ApplicationUser userIdentity,
            List<ImportHeader> headers,
            List<string> fields);

        // Task DeleteFreemiumInstances();
        Task DeleteDeletedUserProfiles();

        Task RemoveUnusedBlankAutomations();

        Task DailyCleanUp();

        Task RemoveDuplicatedHashtag();

        Task<List<RedeemPromotionRecord>> GetRedeemHistory(string companyId);

        Task RedeemPromotionCode(ApplyPromotionCodeViewModel applyPromotionCode, string companyId);

        Task<Staff> UpdateStaffIsNewlyRegistered(ApplicationUser user, bool isNewlyRegistered);

        Task<List<UserProfile>> ImportStaffsIntoUserProfiles(string sourceCompanyId, string toCompanyId);

        Task<List<UserProfile>> SyncCompanyStaffsToSleekFlowContacts(string companyId);

        Task SyncCompanyStaffsToSleekFlowContactsBackground(string companyId);

        Task<List<CustomUserProfileFieldOption>> GetCountryList();

        Task RemoveAllUnusedCompanyAccounts();

        Task UpdatePlanFreeToFreemium(Staff staff);

        Task BackgroundMergeDuplicatedContacts();
    }

    public class CoreService : ICoreService
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly IMapper _mapper;
        private readonly IConfiguration _configuration;
        private readonly ILogger<CoreService> _logger;
        private readonly ICompanyService _companyService;
        private readonly IUserProfileService _userProfileService;
        private readonly ICompanyUsageService _companyUsageService;
        private readonly IAzureBlobStorageService _azureBlobStorageService;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly JwtOptions _options;
        private readonly IShopifyService _shopifyService;
        private readonly IInternalCompanyDataRepository _internalCompanyDataRepository;
        private readonly IUserProfileHooks _userProfileHooks;
        private readonly IStripeClients _stripeClients;
        private readonly ITokenService _tokenService;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ICountryService _countryService;
        private readonly IDbContextService _dbContextService;
        private readonly IUsageCycleCalculator _usageCycleCalculator;
        private readonly IDefaultSubscriptionPlanIdGetter _defaultSubscriptionPlanIdGetter;
        private readonly IContactDeletionConfig _contactDeletionConfig;
        private readonly IDistributedInvocationContextService _distributedInvocationContextService;
        private readonly IUserProfileDuplicatedLogRepository _userProfileDuplicatedLogRepository;
        private readonly IIntegrationAlertConfigRepository _integrationAlertConfigRepository;
        private readonly IUserProfileCustomFieldsDeletionService _userProfileCustomFieldsDeletionService;
        private readonly ICacheManagerService _cacheManagerService;
        private readonly IApplicationInsightsTelemetryTracer _applicationInsightsTelemetryTracer;
        private readonly ICompanyInfoCacheService _companyInfoCacheService;

        public CoreService(
            ApplicationDbContext appDbContext,
            IMapper mapper,
            IConfiguration configuration,
            ILogger<CoreService> logger,
            ICompanyService companyService,
            IUserProfileService userProfileService,
            ICompanyUsageService companyUsageService,
            IAzureBlobStorageService azureBlobStorageService,
            UserManager<ApplicationUser> userManager,
            IOptions<JwtOptions> options,
            IShopifyService shopifyService,
            IInternalCompanyDataRepository internalCompanyDataRepository,
            IStripeClients stripeClients,
            IUserProfileHooks userProfileHooks,
            ITokenService tokenService,
            IHttpClientFactory httpClientFactory,
            ICountryService countryService,
            IDbContextService dbContextService,
            IUsageCycleCalculator usageCycleCalculator,
            IDefaultSubscriptionPlanIdGetter defaultSubscriptionPlanIdGetter,
            IContactDeletionConfig contactDeletionConfig,
            IDistributedInvocationContextService distributedInvocationContextService,
            IUserProfileDuplicatedLogRepository userProfileDuplicatedLogRepository,
            IIntegrationAlertConfigRepository integrationAlertConfigRepository,
            IUserProfileCustomFieldsDeletionService userProfileCustomFieldsDeletionService,
            ICacheManagerService cacheManagerService,
            IApplicationInsightsTelemetryTracer applicationInsightsTelemetryTracer,
            ICompanyInfoCacheService companyInfoCacheService)
        {
            _userManager = userManager;
            _appDbContext = appDbContext;
            _mapper = mapper;
            _configuration = configuration;
            _logger = logger;
            _companyService = companyService;
            _userProfileService = userProfileService;
            _companyUsageService = companyUsageService;
            _azureBlobStorageService = azureBlobStorageService;
            _options = options.Value;
            _shopifyService = shopifyService;
            _internalCompanyDataRepository = internalCompanyDataRepository;
            _userProfileHooks = userProfileHooks;
            _stripeClients = stripeClients;
            _tokenService = tokenService;
            _httpClientFactory = httpClientFactory;
            _countryService = countryService;
            _dbContextService = dbContextService;
            _usageCycleCalculator = usageCycleCalculator;
            _defaultSubscriptionPlanIdGetter = defaultSubscriptionPlanIdGetter;
            _contactDeletionConfig = contactDeletionConfig;
            _distributedInvocationContextService = distributedInvocationContextService;
            _userProfileDuplicatedLogRepository = userProfileDuplicatedLogRepository;
            _integrationAlertConfigRepository = integrationAlertConfigRepository;
            _userProfileCustomFieldsDeletionService = userProfileCustomFieldsDeletionService;
            _cacheManagerService = cacheManagerService;
            _applicationInsightsTelemetryTracer = applicationInsightsTelemetryTracer;
            _companyInfoCacheService = companyInfoCacheService;
        }

        public async Task<Staff> GetCompanyStaff(ApplicationUser user)
        {
            if (user == null) return null;

            var getCompanyStaffCacheKeyPattern = new GetCompanyStaffCacheKeyPattern(user.Id);
            var cachedData = await _cacheManagerService.GetCacheAsync(getCompanyStaffCacheKeyPattern);
            var dbContext = _dbContextService.GetDbContext();

            // Return cached data directly
            if (!string.IsNullOrEmpty(cachedData))
            {
                try
                {
                    var cachedStaffDto = JsonConvert.DeserializeObject<StaffCacheDto>(cachedData);
                    // Validate cache data is still relevant
                    if (cachedStaffDto?.IdentityId == user.Id)
                    {
                        var cachedStaff = cachedStaffDto.ToStaff();
                        var userTeams = await dbContext.CompanyStaffTeams
                            .Where(x => x.Members.Any(m => m.StaffId == cachedStaff.Id))
                            .Select(x => x.Id)
                            .ToListAsync();

                        _distributedInvocationContextService.SetContext(
                            cachedStaff.CompanyId,
                            cachedStaff.Id.ToString(),
                            userTeams.Select(t => t.ToString()).ToList());

                        return cachedStaff;
                    }
                }
                catch (JsonException ex)
                {
                    _logger.LogWarning("Failed to deserialize cached staff data for user {UserId}: {Error}",
                        user.Id, ex.Message);
                    // Continue to database query
                }
            }

            var queryableCompanyUser = dbContext.UserRoleStaffs
                .Where(x => x.IdentityId == user.Id)
                .Include(x => x.Company)
                .Include(x => x.Identity)
                .Include(x => x.ProfilePicture)
                .AsNoTracking();

            if (!string.IsNullOrEmpty(user.CompanyId))
            {
                // multiple workspace
                var companyUser = await queryableCompanyUser
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == user.CompanyId &&
                            x.IdentityId == user.Id);

                if (companyUser != null)
                {
                    var dtoToCache = StaffCacheDto.FromStaff(companyUser);
                    var userTeams = await dbContext.CompanyStaffTeams
                        .Where(x => x.Members.Any(m => m.StaffId == companyUser.Id))
                        .Select(x => x.Id)
                        .ToListAsync();

                    _distributedInvocationContextService.SetContext(
                        companyUser.CompanyId,
                        companyUser.Id.ToString(),
                        userTeams.Select(t => t.ToString()).ToList());
                    await _cacheManagerService.SaveCacheAsync(getCompanyStaffCacheKeyPattern, dtoToCache);
                    return companyUser;
                }
            }

            var normalStaff = await queryableCompanyUser.FirstOrDefaultAsync(x => x.IdentityId == user.Id);

            if (user.LastLoginAt < DateTime.UtcNow.AddHours(-1))
            {
                BackgroundJob.Enqueue(() => UpdateUserLastLoginAt(user.Id, DateTime.UtcNow));
            }

            if (normalStaff != null)
            {
                var userTeams = await dbContext.CompanyStaffTeams
                    .Where(x => x.Members.Any(m => m.StaffId == normalStaff.Id))
                    .Select(x => x.Id)
                    .ToListAsync();

                _distributedInvocationContextService.SetContext(
                    normalStaff.CompanyId,
                    normalStaff.Id.ToString(),
                    userTeams.Select(t => t.ToString()).ToList());

                var dtoToCache = StaffCacheDto.FromStaff(normalStaff);
                await _cacheManagerService.SaveCacheAsync(getCompanyStaffCacheKeyPattern, dtoToCache);
            }

            return normalStaff;
        }

        public async Task<Staff> GetCompanyStaffByStaffId(long staffId)
        {
            var getCompanyStaffCacheKeyPattern = new GetCompanyStaffCacheKeyPattern(staffId.ToString());
            var cachedData = await _cacheManagerService.GetCacheAsync(getCompanyStaffCacheKeyPattern);

            // Return cached data directly
            if (!string.IsNullOrEmpty(cachedData))
            {
                try
                {
                    var cachedStaffDto = JsonConvert.DeserializeObject<StaffCacheDto>(cachedData);
                    if (cachedStaffDto?.Id == staffId)
                    {
                        return cachedStaffDto.ToStaff();
                    }
                }
                catch (JsonException ex)
                {
                    _logger.LogWarning("Failed to deserialize cached staff data for staffId {StaffId}: {Error}",
                        staffId, ex.Message);
                    // Continue to database query
                }
            }

            var dbContext = _dbContextService.GetDbContext();
            var staff = await dbContext.UserRoleStaffs
                .Where(x => x.Id == staffId)
                .Include(x => x.Company)
                .Include(x => x.Identity)
                .Include(x => x.ProfilePicture)
                .AsNoTracking()
                .FirstOrDefaultAsync();

            if (staff != null)
            {
                var dtoToCache = StaffCacheDto.FromStaff(staff);
                await _cacheManagerService.SaveCacheAsync(getCompanyStaffCacheKeyPattern, dtoToCache);
            }

            return staff;
        }

        public async Task<Staff> GetCompanyStaff(long staffId, string companyId)
        {
            var getCompanyStaffCacheKeyPattern = new GetCompanyStaffCacheKeyPattern($"{staffId}_{companyId}");
            var cachedData = await _cacheManagerService.GetCacheAsync(getCompanyStaffCacheKeyPattern);

            // Return cached data directly
            if (!string.IsNullOrEmpty(cachedData))
            {
                try
                {
                    var cachedStaffDto = JsonConvert.DeserializeObject<StaffCacheDto>(cachedData);
                    if (cachedStaffDto?.Id == staffId && cachedStaffDto?.CompanyId == companyId)
                    {
                        return cachedStaffDto.ToStaff();
                    }
                }
                catch (JsonException ex)
                {
                    _logger.LogWarning("Failed to deserialize cached staff data for staffId {StaffId}, companyId {CompanyId}: {Error}",
                        staffId, companyId, ex.Message);
                    // Continue to database query
                }
            }

            var dbContext = _dbContextService.GetDbContext();
            var staff = await dbContext.UserRoleStaffs
                .Where(x => x.Id == staffId && x.CompanyId == companyId)
                .Include(x => x.Company)
                .Include(x => x.Identity)
                .Include(x => x.ProfilePicture)
                .AsNoTracking()
                .FirstOrDefaultAsync();

            if (staff != null)
            {
                var dtoToCache = StaffCacheDto.FromStaff(staff);
                await _cacheManagerService.SaveCacheAsync(getCompanyStaffCacheKeyPattern, dtoToCache);
            }

            return staff;
        }

        public async Task<Staff> GetCompanyStaffByIdentityId(string identityId)
        {
            var getCompanyStaffCacheKeyPattern = new GetCompanyStaffCacheKeyPattern($"identity_{identityId}");
            var cachedData = await _cacheManagerService.GetCacheAsync(getCompanyStaffCacheKeyPattern);

            // Return cached data directly
            if (!string.IsNullOrEmpty(cachedData))
            {
                try
                {
                    var cachedStaffDto = JsonConvert.DeserializeObject<StaffCacheDto>(cachedData);
                    if (cachedStaffDto?.IdentityId == identityId)
                    {
                        return cachedStaffDto.ToStaff();
                    }
                }
                catch (JsonException ex)
                {
                    _logger.LogWarning("Failed to deserialize cached staff data for identityId {IdentityId}: {Error}",
                        identityId, ex.Message);
                    // Continue to database query
                }
            }

            var dbContext = _dbContextService.GetDbContext();
            var staff = await dbContext.UserRoleStaffs
                .Where(x => x.IdentityId == identityId)
                .Include(x => x.Company)
                .Include(x => x.Identity)
                .Include(x => x.ProfilePicture)
                .AsNoTracking()
                .FirstOrDefaultAsync();

            if (staff != null)
            {
                var dtoToCache = StaffCacheDto.FromStaff(staff);
                await _cacheManagerService.SaveCacheAsync(getCompanyStaffCacheKeyPattern, dtoToCache);
            }

            return staff;
        }

        public async Task UpdateUserLastLoginAt(string userId, DateTime lastLoginAt)
        {
            await _appDbContext.Users
                .Where(u => u.Id == userId)
                .ExecuteUpdateAsync(
                    rule =>
                        rule.SetProperty(x => x.LastLoginAt, lastLoginAt));
        }

        public async Task UpdatePlanFreeToFreemium(Staff staff)
        {
            // var staffId = await GetCompanyStaff(user);
            var freeBills = _appDbContext.CompanyBillRecords.Where(
                x => x.CompanyId == staff.CompanyId && x.SubscriptionPlanId == "sleekflow_free").ToList();
            if (freeBills.Count > 0)
            {
                //change all free to freemium
                foreach (var free in freeBills)
                {
                    free.SubscriptionPlanId = _defaultSubscriptionPlanIdGetter.GetDefaultStartupPlanId();
                    free.Status = BillStatus.Active;
                }

                await _appDbContext.SaveChangesAsync();
            }
        }

        public async Task BackgroundMergeDuplicatedContacts()
        {
            try
            {
                // Get top 100 duplicated company ids from WhatsappCloudApiSenders
                var whatsappCloudApiResult = await _appDbContext.WhatsappCloudApiSenders
                    .Where(
                        w =>
                            w.WhatsappId != null &&
                            _appDbContext.CompanyCompanies.Any(c => c.Id == w.CompanyId && !c.IsDeleted) &&
                            _appDbContext.UserProfiles.Any(u => u.Id == w.UserProfileId && u.PhoneNumber == null))
                    .GroupBy(w => w.CompanyId)
                    .Select(
                        g => new
                        {
                            CompanyId = g.Key, LatestCreatedAt = g.Max(w => w.CreatedAt)
                        })
                    .OrderByDescending(x => x.LatestCreatedAt)
                    .Take(100)
                    .Select(
                        r => new
                        {
                            CompanyId = r.CompanyId,
                        })
                    .ToListAsync();

                // Get top 100 duplicated company ids from SenderWhatsappSenders
                var whatsappResult = await _appDbContext.SenderWhatsappSenders
                    .Where(
                        w =>
                            w.whatsAppId != null &&
                            _appDbContext.CompanyCompanies.Any(c => c.Id == w.CompanyId && !c.IsDeleted) &&
                            _appDbContext.UserProfiles.Any(u => u.WhatsAppAccountId == w.Id && u.PhoneNumber == null))
                    .GroupBy(w => w.CompanyId)
                    .Select(
                        g => new
                        {
                            CompanyId = g.Key,
                        })
                    .Take(100)
                    .ToListAsync();

                // Combine the results
                var result = whatsappCloudApiResult
                    .Union(whatsappResult)
                    .Take(100)
                    .ToList();

                var duplicateCompanyIds = result.Select(x => x.CompanyId).ToList();
                await MergedContacts(duplicateCompanyIds);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    "[DEVS-8551] [BackgroundMergeDuplicatedContacts] Exception occurred: {Message}",
                    ex.Message);
            }

            await MergeDuplicateFbContact();
            await MergeDuplicateIgContact();
        }

        private async Task MergeDuplicateFbContact()
        {
            while (true)
            {
                var duplicateFacebookUsers = await _appDbContext.UserProfiles
                    .Join(_appDbContext.SenderFacebookSenders,
                        u => u.FacebookAccountId,
                        fb => fb.Id,
                        (u, fb) => new { u, fb })
                    .GroupBy(x => new { x.u.CompanyId, x.fb.FacebookId })
                    .Where(g => g.Count() > 1)
                    .Select(g => new
                    {
                        g.Key.CompanyId,
                        g.Key.FacebookId,
                        DuplicateCount = g.Count(),
                        EarliestCreatedAt = g.Min(x => x.u.CreatedAt),
                        LatestCreatedAt = g.Max(x => x.u.CreatedAt),
                        UserProfileIds = g.Select(x => x.u.Id).ToList()
                    })
                    .OrderByDescending(x => x.EarliestCreatedAt)
                    .Take(500)
                    .ToListAsync();

                if (!duplicateFacebookUsers.Any())
                    break;

                foreach (var fbDuplicate in duplicateFacebookUsers)
                {
                    if (fbDuplicate.UserProfileIds.Count < 2)
                        continue;

                    var userProfile1Score = await _appDbContext.UserProfileCustomFields.CountAsync(
                        x => x.CompanyId == fbDuplicate.CompanyId && x.UserProfileId == fbDuplicate.UserProfileIds[0]);

                    var userProfile2Score = await _appDbContext.UserProfileCustomFields.CountAsync(
                        x => x.CompanyId == fbDuplicate.CompanyId && x.UserProfileId == fbDuplicate.UserProfileIds[1]);

                    var isKeepFirst = userProfile1Score > userProfile2Score;

                    var request = new MergeContactRequest(
                        fbDuplicate.CompanyId,
                        isKeepFirst
                            ? fbDuplicate.UserProfileIds[0]
                            : fbDuplicate.UserProfileIds[1],
                        isKeepFirst
                            ? fbDuplicate.UserProfileIds[1]
                            : fbDuplicate.UserProfileIds[0]);

                    _logger.LogError(
                        "[DEVS-8716] [Merging Duplicated FB Contact] {CompanyId} {FacebookId} Keep: {UserProfileIdToKeep}; ToMerge: {UserProfileIdToMerge}",
                        fbDuplicate.CompanyId,
                        fbDuplicate.FacebookId,
                        request.UserProfileIdToKeep,
                        request.UserProfileIdToMerge);

                    await _userProfileDuplicatedLogRepository.CreateAsync(
                        new UserProfileDuplicatedLog()
                        {
                            CompanyId = fbDuplicate.CompanyId,
                            Channel = "Facebook",
                            ChannelIdentityId = fbDuplicate.FacebookId,
                            KeepUserProfileId = request.UserProfileIdToKeep
                        });

                    await MergeConversationAsync(request);
                }
            }
        }

        private async Task MergeDuplicateIgContact()
        {
            while (true)
            {
                var duplicateInstagrams = await _appDbContext.UserProfiles
                    .Join(_appDbContext.SenderInstagramSenders,
                        u => u.InstagramUserId,
                        ig => ig.Id,
                        (u, ig) => new { u, ig })
                    .GroupBy(x => new { x.u.CompanyId, x.ig.InstagramId })
                    .Where(g => g.Count() > 1)
                    .Select(g => new
                    {
                        g.Key.CompanyId,
                        g.Key.InstagramId,
                        DuplicateCount = g.Count(),
                        EarliestCreatedAt = g.Min(x => x.u.CreatedAt),
                        LatestCreatedAt = g.Max(x => x.u.CreatedAt),
                        UserProfileIds = g.Select(x => x.u.Id).ToList()
                    })
                    .OrderByDescending(x => x.EarliestCreatedAt)
                    .Take(500)
                    .ToListAsync();

                if (!duplicateInstagrams.Any())
                    break;

                foreach (var igDuplicate in duplicateInstagrams)
                {
                    if (igDuplicate.UserProfileIds.Count < 2)
                        continue;

                    var userProfile1Score = await _appDbContext.UserProfileCustomFields.CountAsync(
                        x => x.CompanyId == igDuplicate.CompanyId && x.UserProfileId == igDuplicate.UserProfileIds[0]);

                    var userProfile2Score = await _appDbContext.UserProfileCustomFields.CountAsync(
                        x => x.CompanyId == igDuplicate.CompanyId && x.UserProfileId == igDuplicate.UserProfileIds[1]);

                    var isKeepFirst = userProfile1Score > userProfile2Score;

                    var request = new MergeContactRequest(
                        igDuplicate.CompanyId,
                        isKeepFirst
                            ? igDuplicate.UserProfileIds[0]
                            : igDuplicate.UserProfileIds[1],
                        isKeepFirst
                            ? igDuplicate.UserProfileIds[1]
                            : igDuplicate.UserProfileIds[0]);

                    _logger.LogError(
                        "[DEVS-8394] [Merging Duplicated IG Contact] {CompanyId} {InstagramId} Keep: {UserProfileIdToKeep}; ToMerge: {UserProfileIdToMerge}",
                        igDuplicate.CompanyId,
                        igDuplicate.InstagramId,
                        request.UserProfileIdToKeep,
                        request.UserProfileIdToMerge);

                    await _userProfileDuplicatedLogRepository.CreateAsync(
                        new UserProfileDuplicatedLog()
                        {
                            CompanyId = igDuplicate.CompanyId,
                            Channel = "Instagram",
                            ChannelIdentityId = igDuplicate.InstagramId,
                            KeepUserProfileId = request.UserProfileIdToKeep
                        });

                    await MergeConversationAsync(request);
                }
            }
        }

        private async Task MergedContacts(List<string> duplicateCompanyIds)
        {
            foreach (var companyId in duplicateCompanyIds)
            {
                if (await _appDbContext.CompanyCompanies.AnyAsync(
                        x =>
                            x.Id == companyId && x.IsDeleted))
                {
                    continue;
                }

                if (await _appDbContext.WhatsappCloudApiSenders.AnyAsync(
                        x => x.CompanyId == companyId &&
                             x.WhatsappId != null &&
                             _appDbContext.UserProfiles.Any(z => z.Id == x.UserProfileId && z.PhoneNumber == null)))
                {
                    var whatsappCloudApiSenders = await _appDbContext.WhatsappCloudApiSenders
                        .Where(x => x.CompanyId == companyId &&
                                    _appDbContext.UserProfiles.Any(z => z.Id == x.UserProfileId && z.PhoneNumber == null))
                        .ToListAsync();

                    var phoneNumberCustomField =
                        await _appDbContext.CompanyCustomUserProfileFields.FirstOrDefaultAsync(
                            x => x.CompanyId == companyId && x.FieldName.ToLower() == "phonenumber");

                    foreach (var cloudApiSender in whatsappCloudApiSenders)
                    {
                        _logger.LogError(
                            "[DEVS-8551] [Missing Phone Number in UserProfile] [CloudApiSender] {CompanyId} {CloudApiSenderId} {UserProfileId} {PhoneNumber} {Name} {CreatedAt}",
                            companyId,
                            cloudApiSender.Id,
                            cloudApiSender.UserProfileId,
                            cloudApiSender.WhatsappId,
                            cloudApiSender.WhatsappUserDisplayName,
                            cloudApiSender.CreatedAt);

                        // Add phone number to user profile is missing
                        await _appDbContext.UserProfileCustomFields.AddAsync(
                            new UserProfileCustomField()
                            {
                                CompanyId = companyId,
                                CompanyDefinedFieldId = phoneNumberCustomField.Id,
                                UserProfileId = cloudApiSender.UserProfileId,
                                Value = cloudApiSender.WhatsappId
                            });

                        await _appDbContext.UserProfiles.Where(x => x.Id == cloudApiSender.UserProfileId)
                            .ExecuteUpdateAsync(
                                config =>
                                    config.SetProperty(c => c.PhoneNumber, cloudApiSender.WhatsappId));

                        await _appDbContext.SaveChangesAsync();
                    }
                }

                if (await _appDbContext.ConversationMessages
                        .AnyAsync(x => x.CompanyId == companyId && x.Channel == "whatsappcloudapi" && x.Conversation.UserProfileId == null))
                {
                    var conversationWithoutAttachedWithUserProfile = await _appDbContext.ConversationMessages
                        .Where(x => x.CompanyId == companyId && x.Channel == "whatsappcloudapi" && x.Conversation.UserProfileId == null)
                        .Take(10)
                        .ToListAsync();

                    foreach (var conversationMessage in conversationWithoutAttachedWithUserProfile)
                    {
                        if (conversationMessage.DynamicChannelSender?.UserIdentityId != null)
                        {
                            _logger.LogError(
                                "[DEVS-8551] [Missing Conversation] [CloudApiSender] {CompanyId} {UserIdentityId} {UserDisplayName}",
                                companyId,
                                conversationMessage.DynamicChannelSender.UserIdentityId,
                                conversationMessage.DynamicChannelSender.UserDisplayName);

                            var conversationId = await _appDbContext.Conversations.Where(
                                    x => x.CompanyId == companyId &&
                                         x.UserProfile.PhoneNumber ==
                                         conversationMessage.DynamicChannelSender.UserIdentityId)
                                .OrderByDescending(x => x.CreatedAt)
                                .Select(x => x.Id)
                                .FirstOrDefaultAsync();

                            if (conversationId == null)
                            {
                                _logger.LogError(
                                    "[DEVS-8551] [Missing Conversation] [CloudApiSender] missing conversationId to be saved {CompanyId} {UserIdentityId} {UserDisplayName} {ConversationId}",
                                    companyId,
                                    conversationMessage.DynamicChannelSender.UserIdentityId,
                                    conversationMessage.DynamicChannelSender.UserDisplayName,
                                    conversationId);

                                continue;
                            }

                            // Move message to the correct conversation, and then delete the old conversation downstream
                            conversationMessage.ConversationId = conversationId;
                            await _appDbContext.SaveChangesAsync();
                        }
                    }
                }

                var twilioSenders = new List<(string PhoneNumber, string UserProfileId)>();
                if (await _appDbContext.SenderWhatsappSenders.AnyAsync(
                        x => x.CompanyId == companyId &&
                             x.whatsAppId != null &&
                             _appDbContext.UserProfiles.Any(
                                 z => z.WhatsAppAccountId == x.Id && z.PhoneNumber == null)))
                {
                    var whatsappTwilioSenders = await _appDbContext.SenderWhatsappSenders
                        .Where(x => x.CompanyId == companyId
                                    && _appDbContext.UserProfiles.Any(z => z.WhatsAppAccountId == x.Id && z.PhoneNumber == null)
                                    && x.phone_number != null)
                        .ToListAsync();

                    var phoneNumberCustomField =
                        await _appDbContext.CompanyCustomUserProfileFields.FirstOrDefaultAsync(
                            x =>
                                x.CompanyId == companyId && x.FieldName.ToLower() == "phonenumber");

                    foreach (var twilioSender in whatsappTwilioSenders)
                    {
                        var userProfile = await _appDbContext.UserProfiles
                            .FirstOrDefaultAsync(x => x.WhatsAppAccountId == twilioSender.Id);

                        // if the phone number can be retrieved from WhatsappCloudApiSenders instead, skip
                        if (await _appDbContext.WhatsappCloudApiSenders.AnyAsync(
                                s => s.UserProfileId == userProfile.Id))
                        {
                            continue;
                        }

                        // skip group chats
                        if (twilioSender.phone_number.Contains("-") || twilioSender.phone_number.Length > 15)
                        {
                            continue;
                        }

                        var phoneNumber = PhoneNumberHelper.NormalizeWhatsappPhoneNumber(twilioSender.phone_number);

                        // if the phone number cannot be normalized into a valid one, skip
                        if (phoneNumber is null
                            || (!PhoneNumberHelper.IsValidE164FormatPhoneNumber(phoneNumber)
                                && !PhoneNumberHelper.IsValidPhoneNumberContainsCountryCode(phoneNumber)))
                        {
                            continue;
                        }

                        twilioSenders.Add((phoneNumber, userProfile.Id));

                        _logger.LogInformation(
                            "[DEVS-13959] [BackgroundMergeDuplicatedContacts improvement] [WhatsappSender] " +
                            "{CompanyId} {WhatsappSenderId} {UserProfileId} {PhoneNumber} {Name}",
                            companyId,
                            twilioSender.Id,
                            userProfile.Id,
                            twilioSender.whatsAppId,
                            twilioSender.name);

                        // Add phone number to the user profile missing one
                        await _appDbContext.UserProfileCustomFields.AddAsync(
                            new UserProfileCustomField()
                            {
                                CompanyId = companyId,
                                CompanyDefinedFieldId = phoneNumberCustomField.Id,
                                UserProfileId = userProfile.Id,
                                Value = phoneNumber
                            });

                        await _appDbContext.UserProfiles.Where(x => x.WhatsAppAccountId == twilioSender.Id)
                            .ExecuteUpdateAsync(
                                config =>
                                    config.SetProperty(c => c.PhoneNumber, phoneNumber));

                        await _appDbContext.SaveChangesAsync();
                    }
                }

                var userProfiles = await _appDbContext.UserProfiles
                    .Where(up => up.CompanyId == companyId)
                    .Select(
                        up => new
                        {
                            PhoneNumber = up.PhoneNumber, UserProfileId = up.Id
                        })
                    .ToListAsync();

                var cloudApiSenders = await _appDbContext.WhatsappCloudApiSenders
                    .Where(ws => ws.CompanyId == companyId && ws.UserProfileId != null)
                    .Select(
                        ws => new
                        {
                            PhoneNumber = ws.WhatsappId, UserProfileId = ws.UserProfileId
                        })
                    .ToListAsync();

                var twilioSendersConverted = twilioSenders
                    .Select(x => new
                    {
                        x.PhoneNumber, x.UserProfileId
                    })
                    .ToList();

                var duplicatePhoneNumbers = userProfiles
                    .Concat(cloudApiSenders)
                    .Concat(twilioSendersConverted)
                    .GroupBy(x => x.PhoneNumber)
                    .Where(g => g.Select(x => x.UserProfileId).Distinct().Count() > 1)
                    .ToList();

                foreach (var duplicatedPhoneNumber in duplicatePhoneNumbers)
                {
                    if (duplicatedPhoneNumber.Key == null)
                        continue;

                    var userProfileIds = await _appDbContext.UserProfiles
                        .Where(x => duplicatedPhoneNumber.Select(x => x.UserProfileId).Contains(x.Id)).Select(
                            x => new
                            {
                                x.Id, x.UpdatedAt, x.PhoneNumber
                            })
                        .ToListAsync();

                    var userProfile1 = userProfileIds[0];
                    var userProfile2 = userProfileIds[1];


                    var isKeepFirst = !string.IsNullOrEmpty(userProfile1.PhoneNumber) ||
                                      (string.IsNullOrEmpty(userProfile2.PhoneNumber) &&
                                       userProfile1.UpdatedAt > userProfile2.UpdatedAt);

                    if (!string.IsNullOrEmpty(userProfile1.PhoneNumber) &&
                        !string.IsNullOrEmpty(userProfile2.PhoneNumber))
                    {
                        var userProfile1Score = await _appDbContext.UserProfileCustomFields.CountAsync(
                            x => x.CompanyId == companyId && x.UserProfileId == userProfile1.Id);

                        var userProfile2Score = await _appDbContext.UserProfileCustomFields.CountAsync(
                            x => x.CompanyId == companyId && x.UserProfileId == userProfile2.Id);

                        isKeepFirst = userProfile1Score > userProfile2Score;
                    }

                    var request = new MergeContactRequest(
                        companyId,
                        isKeepFirst
                            ? userProfile1.Id
                            : userProfile2.Id,
                        isKeepFirst
                            ? userProfile2.Id
                            : userProfile1.Id);

                    _logger.LogError(
                        "[DEVS-8551] [Merging Duplicated Contact] {CompanyId} {PhoneNumber} {UserProfileWeKeep} {UserProfileWeMerge}",
                        companyId,
                        duplicatedPhoneNumber.Key,
                        request.UserProfileIdToKeep,
                        request.UserProfileIdToMerge);

                    await _userProfileDuplicatedLogRepository.CreateAsync(
                        new UserProfileDuplicatedLog()
                        {
                            CompanyId = companyId,
                            Channel = "Phone",
                            ChannelIdentityId = duplicatedPhoneNumber.Key,
                            KeepUserProfileId = request.UserProfileIdToKeep
                        });

                    await MergeConversationAsync(request);
                }
            }
        }

        private record MergeContactRequest(
            string CompanyId,
            string UserProfileIdToKeep,
            string UserProfileIdToMerge);

        private async Task MergeConversationAsync(MergeContactRequest mergeContactRequest)
        {
            try
            {
                var conversationToMerge = await _appDbContext.Conversations
                    .Include(x => x.UserProfile)
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == mergeContactRequest.CompanyId
                            && x.UserProfileId == mergeContactRequest.UserProfileIdToMerge);

                if (conversationToMerge == null)
                {
                    await _userProfileService.DeleteUserProfileLinked(
                        await _appDbContext.UserProfiles.FirstOrDefaultAsync(
                            x =>
                                x.CompanyId == mergeContactRequest.CompanyId
                                && x.Id == mergeContactRequest.UserProfileIdToMerge),
                        new UserProfileDeletionTriggerContext(
                            UpdateUserProfileTriggerSource.ConversationMerge,
                            null));
                    return;
                }

                var messagesToMerge = await _appDbContext.ConversationMessages
                    .Where(
                        x =>
                            x.CompanyId == mergeContactRequest.CompanyId
                            && x.ConversationId == conversationToMerge.Id)
                    .ToListAsync();

                var conversationIdToKeep = await _appDbContext.Conversations
                    .Where(
                        x =>
                            x.CompanyId == mergeContactRequest.CompanyId
                            && x.UserProfileId == mergeContactRequest.UserProfileIdToKeep)
                    .Select(x => x.Id)
                    .FirstOrDefaultAsync();

                var messagesToKeep = await _appDbContext.ConversationMessages
                    .Where(
                        x =>
                            x.CompanyId == mergeContactRequest.CompanyId
                            && x.ConversationId == conversationIdToKeep)
                    .ToListAsync();

                // DEVS-10104 If conversationIdToKeep is null, create new conversation to avoid saving message to the null conversation
                if (string.IsNullOrEmpty(conversationIdToKeep))
                {
                    _logger.LogError("[DEVS-8551] [Merging Duplicated Contact] {CompanyId} {UserProfileIdToKeep} {UserProfileIdToMerge} conversationIdToKeep is null, create new conversation",
                        mergeContactRequest.CompanyId,
                        mergeContactRequest.UserProfileIdToKeep,
                        mergeContactRequest.UserProfileIdToMerge);

                    var newConversationId = await _userProfileService.GetConversationByUserProfileId(
                        mergeContactRequest.CompanyId,
                        mergeContactRequest.UserProfileIdToKeep);

                    if (newConversationId == null)
                    {
                        _logger.LogError("[DEVS-8551] [Merging Duplicated Contact] {CompanyId} {UserProfileIdToKeep} {UserProfileIdToMerge} newConversationId is null",
                            mergeContactRequest.CompanyId,
                            mergeContactRequest.UserProfileIdToKeep,
                            mergeContactRequest.UserProfileIdToMerge);
                        return;
                    }

                    conversationIdToKeep = newConversationId.Id;
                }

                foreach (var msg in messagesToMerge)
                {
                    // support five default channels and note
                    if (msg.Channel is ChannelTypes.WhatsappTwilio or ChannelTypes.Whatsapp360Dialog
                        or ChannelTypes.WhatsappCloudApi or ChannelTypes.Instagram or ChannelTypes.Facebook
                        or ChannelTypes.Note)
                    {
                        msg.ConversationId = conversationIdToKeep;

                        switch (msg.Channel)
                        {
                            case ChannelTypes.WhatsappTwilio:

                                var whatsappSenderId = messagesToKeep
                                    .Where(
                                        x =>
                                            x.IsSentFromSleekflow
                                            && x.whatsappReceiverId != null)
                                    .Select(x => x.whatsappReceiverId)
                                    .FirstOrDefault();

                                if (msg.IsSentFromSleekflow)
                                {
                                    msg.whatsappReceiverId = whatsappSenderId;
                                }
                                else
                                {
                                    msg.whatsappSenderId = whatsappSenderId;
                                }

                                // only change the DynamicChannelSender when it is not null
                                if (msg.DynamicChannelSender != null)
                                {
                                    var userIdentityId = await _appDbContext.UserProfiles
                                        .Where(
                                            x =>
                                                x.CompanyId == mergeContactRequest.CompanyId
                                                && x.Id == mergeContactRequest.UserProfileIdToKeep)
                                        .Select(x => x.PhoneNumber)
                                        .FirstOrDefaultAsync();

                                    var whatsappDynamicChannelSender = messagesToKeep
                                        .Where(
                                            x =>
                                                x.DynamicChannelSender != null
                                                && x.DynamicChannelSender.UserIdentityId == userIdentityId)
                                        .Select(x => x.DynamicChannelSender)
                                        .FirstOrDefault();

                                    msg.DynamicChannelSender = whatsappDynamicChannelSender;

                                    _appDbContext.Entry(msg).Property(x => x.DynamicChannelSender).IsModified = true;
                                }

                                break;

                            case ChannelTypes.Whatsapp360Dialog:

                                var whatsapp360DialogSenderId = messagesToKeep
                                    .Where(
                                        x =>
                                            x.IsSentFromSleekflow
                                            && x.Whatsapp360DialogReceiverId != null)
                                    .Select(x => x.Whatsapp360DialogReceiverId)
                                    .FirstOrDefault();

                                if (msg.IsSentFromSleekflow)
                                {
                                    msg.Whatsapp360DialogReceiverId = whatsapp360DialogSenderId;
                                }
                                else
                                {
                                    msg.Whatsapp360DialogSenderId = whatsapp360DialogSenderId;
                                }

                                if (msg.DynamicChannelSender != null)
                                {
                                    var userIdentityId = await _appDbContext.UserProfiles
                                        .Where(
                                            x =>
                                                x.CompanyId == mergeContactRequest.CompanyId
                                                && x.Id == mergeContactRequest.UserProfileIdToKeep)
                                        .Select(x => x.PhoneNumber)
                                        .FirstOrDefaultAsync();

                                    var whatsapp360DialogDynamicChannelSender = messagesToKeep
                                        .Where(
                                            x =>
                                                x.DynamicChannelSender != null
                                                && x.DynamicChannelSender.UserIdentityId == userIdentityId)
                                        .Select(x => x.DynamicChannelSender)
                                        .FirstOrDefault();

                                    msg.DynamicChannelSender = whatsapp360DialogDynamicChannelSender;

                                    _appDbContext.Entry(msg).Property(x => x.DynamicChannelSender).IsModified = true;
                                }

                                break;

                            case ChannelTypes.WhatsappCloudApi:

                                if (msg.DynamicChannelSender != null)
                                {
                                    var userIdentityId = _appDbContext.UserProfiles
                                        .Where(
                                            x =>
                                                x.CompanyId == mergeContactRequest.CompanyId
                                                && x.Id == mergeContactRequest.UserProfileIdToKeep)
                                        .Select(x => x.PhoneNumber)
                                        .FirstOrDefault();

                                    var whatsappCloudApiDynamicChannelSender = messagesToKeep
                                        .Where(
                                            x =>
                                                x.DynamicChannelSender != null
                                                && x.DynamicChannelSender.ChannelIdentityId == msg.ChannelIdentityId
                                                && x.DynamicChannelSender.UserIdentityId == userIdentityId)
                                        .Select(x => x.DynamicChannelSender)
                                        .FirstOrDefault();

                                    if (whatsappCloudApiDynamicChannelSender != null)
                                    {
                                        msg.DynamicChannelSender = whatsappCloudApiDynamicChannelSender;
                                        _appDbContext.Entry(msg).Property(x => x.DynamicChannelSender).IsModified = true;
                                    }
                                }

                                break;

                            case ChannelTypes.Instagram:
                                var instagramSenderId = messagesToKeep
                                    .Where(x => x.IsSentFromSleekflow && x.InstagramReceiverId != null)
                                    .Select(x => x.InstagramReceiverId)
                                    .FirstOrDefault();

                                if (msg.IsSentFromSleekflow)
                                {
                                    msg.InstagramReceiverId = instagramSenderId;
                                }
                                else
                                {
                                    msg.InstagramSenderId = instagramSenderId;
                                }

                                break;

                            case ChannelTypes.Facebook:
                                var facebookSenderId = messagesToKeep
                                    .Where(x => x.IsSentFromSleekflow && x.facebookReceiverId != null)
                                    .Select(x => x.facebookReceiverId)
                                    .FirstOrDefault();

                                if (msg.IsSentFromSleekflow)
                                {
                                    msg.facebookReceiverId = facebookSenderId;
                                }
                                else
                                {
                                    msg.facebookSenderId = facebookSenderId;
                                }

                                break;
                        }

                        await _appDbContext.SaveChangesAsync();
                    }
                }

                _logger.LogInformation(
                    "[InternalCmsCompanyUserProfile {MethodName} endpoint] Company {CompanyId} is deleting user profile {UserProfileId}",
                    nameof(MergeConversationAsync),
                    mergeContactRequest.CompanyId,
                    conversationToMerge?.UserProfileId);

                await _userProfileService.DeleteUserProfileLinked(
                    conversationToMerge.UserProfile,
                    new UserProfileDeletionTriggerContext(
                        UpdateUserProfileTriggerSource.ConversationMerge,
                        null));
            }
            catch (Exception e)
            {
                _logger.LogError(e.Message);
            }
        }

        public async Task<AuthenticationResponse> GetToken(ApplicationUser user)
        {
            var response = _mapper.Map<AuthenticationResponse>(user);
            response.AccessToken = await _tokenService.GetUserTokenForShopifyOrEta(user);
            try
            {
                var staffId = await GetCompanyStaff(user);
                await UpdatePlanFreeToFreemium(staffId);

                response.FirstName = user.FirstName;
                response.LastName = user.LastName;
                response.SignalRGroupName = staffId.Company.SignalRGroupName;
                response.IsShopifyAccount = staffId.Company.IsShopifyAccount;

                response.AssociatedCompanyIds = await _appDbContext.UserRoleStaffs
                    .Where(x => x.IdentityId == user.Id)
                    .Select(x => x.CompanyId)
                    .ToListAsync();

                _logger.LogInformation($"Staff Login: {response.Id}, {response.Email}, {response.SignalRGroupName}");
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"User Login: {response.Id}, {response.Email}, error: {ex.ToString()}");
            }

            return response;
        }

        public async Task<AuthenticationResponse> GetTokenWithLoginAsSecret(CmsLoginAsSecret loginAsSecret)
        {
            var user = await _userManager.Users
                .FirstOrDefaultAsync(x => x.Id == loginAsSecret.StaffIdentityId);

            var utcNow = DateTime.UtcNow;

            var claims = new Claim[]
            {
                new Claim(JwtRegisteredClaimNames.Sub, user.Id),
                new Claim(JwtRegisteredClaimNames.UniqueName, user.UserName),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                new Claim(JwtRegisteredClaimNames.Iat, utcNow.ToString()),
            };

            var signingKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_options.Key));
            var signingCredentials = new SigningCredentials(signingKey, SecurityAlgorithms.HmacSha256);
            var jwt = new JwtSecurityToken(
                signingCredentials: signingCredentials,
                claims: claims,
                notBefore: utcNow,
                expires: loginAsSecret.ExpireAt,
                audience: _options.Audience,
                issuer: _options.Issuer);

            var response = _mapper.Map<AuthenticationResponse>(user);
            response.AccessToken = new JwtSecurityTokenHandler().WriteToken(jwt);

            try
            {
                var staffId = await GetCompanyStaff(user);
                var freeBills = await _appDbContext.CompanyBillRecords
                    .Where(
                        x =>
                            x.CompanyId == staffId.CompanyId &&
                            x.SubscriptionPlanId == "sleekflow_free")
                    .ToListAsync();

                if (freeBills.Count > 0)
                {
                    // change all free to freemium
                    foreach (var free in freeBills)
                    {
                        free.SubscriptionPlanId = _defaultSubscriptionPlanIdGetter.GetDefaultStartupPlanId();
                        free.Status = BillStatus.Active;
                    }

                    await _appDbContext.SaveChangesAsync();
                }

                response.FirstName = user.FirstName;
                response.LastName = user.LastName;
                response.SignalRGroupName = staffId.Company.SignalRGroupName;

                response.AssociatedCompanyIds = await _appDbContext.UserRoleStaffs
                    .Where(x => x.IdentityId == user.Id)
                    .Select(x => x.CompanyId)
                    .ToListAsync();

                _logger.LogInformation($"Staff Login: {response.Id}, {response.Email}, {response.SignalRGroupName}");
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"User Login: {response.Id}, {response.Email}, error: {ex.ToString()}");
            }

            return response;
        }

        public async Task RemoveUnusedBlankAutomations()
        {
            do
            {
                // _appDbContext.Database.SetCommandTimeout(180);

                var assignmentRulesToBeDeleted = await _appDbContext.CompanyAssignmentRules
                    .AsNoTracking()
                    .Where(
                        x =>
                            x.Status == AutomationStatus.Saved
                            && x.CreatedAt < DateTime.UtcNow.AddMinutes(-15))
                    .OrderByDescending(x => x.Id)
                    .Select(x => new { x.Id, x.AssociatedListId })
                    .Take(500)
                    .ToListAsync();

                var listIdsToBeDeleted = assignmentRulesToBeDeleted
                    .Where(x => x.AssociatedListId.HasValue)
                    .Select(x => x.AssociatedListId)
                    .ToList();

                await _appDbContext.CompanyAssignmentRules
                    .Where(
                        x =>
                            assignmentRulesToBeDeleted
                                .Select(t => t.Id)
                                .Contains(x.Id))
                    .ExecuteDeleteAsync();

                await _appDbContext.CompanyImportedUserProfiles
                    .Where(
                        x =>
                            listIdsToBeDeleted
                                .Contains(x.ImportContactHistoryId))
                    .ExecuteDeleteAsync();

                await _appDbContext.CompanyImportContactHistories
                    .Where(
                        x =>
                            listIdsToBeDeleted
                                .Contains(x.Id))
                    .ExecuteDeleteAsync();
            }
            while (await _appDbContext.CompanyAssignmentRules.AnyAsync(
                       x =>
                           x.Status == AutomationStatus.Saved &&
                           x.CreatedAt < DateTime.UtcNow.AddMinutes(-15)));
        }

        public async Task DailyCleanUp()
        {
            var pastMonth = DateTime.UtcNow.AddMonths(-1);
            var pastWeek = DateTime.UtcNow.AddDays(-7);

            _appDbContext.Database.SetCommandTimeout(120);

            // BackgroundTasks Daily CleanUp
            var dismissedBackgroundTaskCount = await _appDbContext.BackgroundTasks
                .Where(x => x.CreatedAt < pastWeek && x.IsCompleted && !x.IsDismissed)
                .ExecuteUpdateAsync(
                    backgroundTask =>
                        backgroundTask.SetProperty(t => t.IsDismissed, true));

            _logger.LogInformation(
                "[DailyCleanUp] Dismissed BackgroundTasks: {DismissedBackgroundTaskCount}",
                dismissedBackgroundTaskCount);

            var deletedBackgroundTaskCount = await _appDbContext.BackgroundTasks
                .Where(
                    x =>
                        x.CreatedAt < pastMonth &&
                        x.IsCompleted &&
                        x.IsDismissed)
                .ExecuteDeleteAsync();

            _logger.LogInformation(
                "[DailyCleanUp] Deleted BackgroundTasks: {DeletedBackgroundTaskCount}",
                deletedBackgroundTaskCount);

            // Delete ConversationUnreadRecords unused Read records
            var deletedConversationUnreadRecordsRowCount = await DeleteOldConversationUnreadRecords();

            _logger.LogInformation(
                "[DailyCleanUp] Deleted ConversationUnreadRecords: {DeletedRowCount}",
                deletedConversationUnreadRecordsRowCount);
        }

        [AutomaticRetry(
            Attempts = 1000,
            DelaysInSeconds = new[]
            {
                5
            })]
        public async Task DeleteDeletedUserProfiles()
        {
            // to avoid accidental running of this method
            if (_contactDeletionConfig.IsContactSafeDeleteEnabled)
            {
                _logger.LogError(
                    "Unexpected attempt of executing {MethodName}!",
                    nameof(DeleteDeletedUserProfiles));

                return;
            }

            _appDbContext.Database.SetCommandTimeout(180);

            while (true)
            {
                var userProfileTobeDeleted = await _appDbContext.UserProfiles
                    .Where(
                        x =>
                            x.ActiveStatus == ActiveStatus.Inactive &&
                            !x.WebClientId.HasValue)
                    .Include(x => x.UserProfilePictureFile)
                    .Take(100)
                    .ToListAsync();

                if (!userProfileTobeDeleted.Any())
                {
                    break;
                }

                var beDeletedUserProfileIds = userProfileTobeDeleted.Select(y => y.Id);

                var conversationTobeDeleted = await _appDbContext.Conversations
                    .Where(x => beDeletedUserProfileIds.Contains(x.UserProfileId))
                    .ToListAsync();

                foreach (var conversation in conversationTobeDeleted)
                {
                    var broadcastHistories = await _appDbContext.BroadcastCompaignHistories
                        .Where(x => x.ConversationId == conversation.Id)
                        .ToListAsync();

                    foreach (var broadcastHistory in broadcastHistories)
                    {
                        await _appDbContext.ConversationMessages
                            .Where(x => x.BroadcastHistoryId == broadcastHistory.Id)
                            .ExecuteUpdateAsync(
                                message =>
                                    message.SetProperty(m => m.BroadcastHistoryId, (long?) null));
                    }

                    await _appDbContext.BroadcastCompaignHistories
                        .Where(x => x.ConversationId == conversation.Id)
                        .ExecuteDeleteAsync();

                    await _appDbContext.ConversationMessageUploadedFiles
                        .Where(
                            x => _appDbContext.ConversationMessages
                                .Where(
                                    y =>
                                        y.ConversationId == conversation.Id &&
                                        y.UploadedFiles.Any())
                                .Select(y => y.Id)
                                .Contains(x.ConversationMessageId))
                        .ExecuteDeleteAsync();

                    do
                    {
                        var toBeDeletedConversationMessageIds = await _appDbContext.ConversationMessages
                            .Where(x => x.ConversationId == conversation.Id)
                            .Select(x => x.Id)
                            .Take(500)
                            .ToListAsync();

                        await _appDbContext.ConversationMessageUploadedFiles
                            .Where(
                                x => toBeDeletedConversationMessageIds.Contains(x.ConversationMessageId))
                            .ExecuteDeleteAsync();

                        await _appDbContext.ConversationMessages
                            .Where(x => toBeDeletedConversationMessageIds.Contains(x.Id))
                            .ExecuteDeleteAsync();
                    }
                    while (await _appDbContext.ConversationMessages
                               .AnyAsync(x => x.ConversationId == conversation.Id));

                    await _appDbContext.ConversationAdditionalAssignees
                        .Where(x => x.ConversationId == conversation.Id)
                        .ExecuteDeleteAsync();

                    await _appDbContext.ConversationBookmarks
                        .Where(x => x.ConversationId == conversation.Id)
                        .ExecuteDeleteAsync();

                    await _appDbContext.ConversationWhatsappHistories
                        .Where(x => x.ConversationId == conversation.Id)
                        .ExecuteDeleteAsync();

                    await _appDbContext.ConversationHashtags
                        .Where(x => x.ConversationId == conversation.Id)
                        .ExecuteDeleteAsync();

                    await _appDbContext.Conversations
                        .Where(x => x.Id == conversation.Id)
                        .ExecuteDeleteAsync();
                }

                await _appDbContext.Conversations
                    .Where(x => beDeletedUserProfileIds.Contains(x.UserProfileId))
                    .ExecuteDeleteAsync();

                await _appDbContext.CompanyImportedUserProfiles
                    .Where(x => beDeletedUserProfileIds.Contains(x.UserProfileId))
                    .ExecuteDeleteAsync();

                await _appDbContext.UserProfileCustomFields
                    .Where(x => beDeletedUserProfileIds.Contains(x.UserProfileId))
                    .ExecuteDeleteAsync();

                await _appDbContext.CrmHubEntities
                    .Where(x => beDeletedUserProfileIds.Contains(x.UserProfileId))
                    .ExecuteDeleteAsync();

                await _appDbContext.UserProfiles
                    .Where(x => beDeletedUserProfileIds.Contains(x.Id))
                    .ExecuteDeleteAsync();
            }
        }

        public async Task<bool> RemoveStaffData(string companyId, long staffId)
        {
            try
            {
                _appDbContext.Database.SetCommandTimeout(360);

                var targetedStaff = await _appDbContext.UserRoleStaffs
                    .Include(x => x.ProfilePicture)
                    .FirstOrDefaultAsync(
                        x =>
                            x.Id == staffId &&
                            x.CompanyId == companyId);

                await _appDbContext.CompanyShareableInvitations
                    .Where(x => x.GeneratedById == targetedStaff.Id)
                    .ExecuteUpdateAsync(
                        invitation =>
                            invitation
                                .SetProperty(i => i.GeneratedById, (long?)null)
                                .SetProperty(i => i.Status, ShareableLinkStatus.Disabled));

                await _appDbContext.ConversationAdditionalAssignees
                    .Where(x => x.AssigneeId == targetedStaff.Id)
                    .ExecuteDeleteAsync();

                await _appDbContext.CompanyShareableInvitationRecords
                    .Where(x => x.InvitedStaffId == targetedStaff.Id)
                    .ExecuteDeleteAsync();

                await _appDbContext.BroadcastCompaignHistories
                    .Where(x => x.BroadcastSentById == targetedStaff.Id)
                    .ExecuteUpdateAsync(
                        history =>
                            history.SetProperty(h => h.BroadcastSentById, (long?)null));

                await _appDbContext.CompanyQuickReplies
                    .Where(x => x.SavedById == targetedStaff.Id)
                    .ExecuteUpdateAsync(
                        quickReply =>
                            quickReply.SetProperty(q => q.SavedById, (long?) null));

                await _appDbContext.CompanyAssignmentRules
                    .Where(x => x.AssignedStaff.Id == targetedStaff.Id)
                    .ExecuteUpdateAsync(
                        rule =>
                            rule
                                .SetProperty(r => r.AssignedStaffId, (long?) null)
                                .SetProperty(r => r.AssignmentType, AssignmentType.Unassigned));

                await _appDbContext.CompanyAssignmentRules
                    .Where(x => x.SavedBy.Id == targetedStaff.Id)
                    .ExecuteUpdateAsync(
                        rule =>
                            rule.SetProperty(r => r.SavedById, (long?) null));

                await _appDbContext.CompanyBillRecords
                    .Where(x => x.PurchaseStaffId == targetedStaff.Id)
                    .ExecuteUpdateAsync(
                        billRecord =>
                            billRecord.SetProperty(r => r.PurchaseStaffId, (long?) null));

                await _appDbContext.CompanyAutomationActionRecords
                    .Where(x => x.AssignedStaffId == staffId)
                    .ExecuteUpdateAsync(
                        actionRecord =>
                            actionRecord.SetProperty(r => r.AssignedStaffId, (long?) null));

                await _appDbContext.CompanyAutomationActions
                    .Where(x => x.AssignedStaff.Id == targetedStaff.Id)
                    .ExecuteUpdateAsync(
                        automationAction =>
                            automationAction.SetProperty(a => a.AssignedStaffId, (long?) null));

                await _appDbContext.CampaignAutomationActions
                    .Where(x => x.AssignedStaff.Id == targetedStaff.Id)
                    .ExecuteUpdateAsync(
                        campaignAction =>
                            campaignAction.SetProperty(a => a.AssignedStaffId, (long?) null));

                await _appDbContext.CompanyImportContactHistories
                    .Where(x => x.ImportedFromId == targetedStaff.Id)
                    .ExecuteUpdateAsync(
                        importContactHistory =>
                            importContactHistory.SetProperty(h => h.ImportedFromId, (long?) null));

                await _appDbContext.CompanyAnalyticSegment
                    .Where(x => x.SavedById == targetedStaff.Id)
                    .ExecuteUpdateAsync(
                        analyticSegment =>
                            analyticSegment.SetProperty(s => s.SavedById, (long?) null));

                await _appDbContext.ConversationBookmarks
                    .Where(x => x.StaffId == targetedStaff.Id)
                    .ExecuteDeleteAsync();

                await _appDbContext.CompanyMessageTemplates
                    .Where(x => x.LastSentById == targetedStaff.Id)
                    .ExecuteUpdateAsync(
                        template =>
                            template.SetProperty(t => t.LastSentById, (long?) null));

                await _appDbContext.CompanyMessageTemplates
                    .Where(x => x.SavedById == targetedStaff.Id)
                    .ExecuteUpdateAsync(
                        template =>
                            template.SetProperty(t => t.SavedById, (long?) null));

                await _appDbContext.CompanyTeamMembers
                    .Where(x => x.StaffId == targetedStaff.Id)
                    .ExecuteDeleteAsync();

                await _appDbContext.ConversationMessageUploadedFiles
                    .Where(x => x.SenderId == targetedStaff.IdentityId)
                    .ExecuteUpdateAsync(
                        file =>
                            file.SetProperty(f => f.SenderId, (string) null));

                await _appDbContext.SaveChangesAsync();

                while (await _appDbContext.ConversationMessages
                           .AnyAsync(x => x.MessageAssigneeId == targetedStaff.Id))
                {
                    await _appDbContext.ConversationMessages
                        .Where(x => x.MessageAssigneeId == targetedStaff.Id)
                        .Take(500)
                        .ExecuteUpdateAsync(
                            message =>
                                message.SetProperty(m => m.MessageAssigneeId, (long?) null));
                }

                await _appDbContext.SaveChangesAsync();

                while (await _appDbContext.ConversationMessages.AnyAsync(
                           x =>
                               x.CompanyId == companyId &&
                               x.ReceiverId == targetedStaff.IdentityId))
                {
                    await _appDbContext.ConversationMessages
                        .Where(
                            x =>
                                x.CompanyId == companyId &&
                                x.ReceiverId == targetedStaff.IdentityId)
                        .Take(500)
                        .ExecuteUpdateAsync(
                            message =>
                                message.SetProperty(m => m.ReceiverId, (string) null));
                }

                while (await _appDbContext.ConversationMessages.AnyAsync(
                           x =>
                               x.CompanyId == companyId &&
                               x.SenderId == targetedStaff.IdentityId))
                {
                    await _appDbContext.ConversationMessages
                        .Where(
                            x =>
                                x.CompanyId == companyId &&
                                x.SenderId == targetedStaff.IdentityId)
                        .Take(500)
                        .ExecuteUpdateAsync(
                            message =>
                                message.SetProperty(m => m.SenderId, (string) null));
                }

                await _appDbContext.SaveChangesAsync();

                while (await _appDbContext.Conversations
                           .AnyAsync(x => x.AssigneeId == targetedStaff.Id))
                {
                    await _appDbContext.Conversations
                        .Where(x => x.AssigneeId == targetedStaff.Id)
                        .Take(500)
                        .ExecuteUpdateAsync(
                            conversation =>
                                conversation.SetProperty(c => c.AssigneeId, (long?) null));
                }

                var companyContactOwnerCustomFieldId = await _appDbContext.CompanyCustomUserProfileFields
                    .AsNoTracking()
                    .Where(
                        field =>
                            field.CompanyId == companyId
                            && field.FieldName.ToLower() == "contactowner")
                    .Select(field => field.Id)
                    .FirstOrDefaultAsync();

                if (!string.IsNullOrWhiteSpace(companyContactOwnerCustomFieldId))
                {
                    await _appDbContext.UserProfileCustomFields
                        .Where(
                            field =>
                                field.CompanyDefinedFieldId == companyContactOwnerCustomFieldId
                                && field.Value == targetedStaff.IdentityId)
                        .ExecuteUpdateAsync(
                            field =>
                                field.SetProperty(f => f.Value, string.Empty));
                }

                await _appDbContext.UserProfiles
                    .Where(
                        profile =>
                            profile.CompanyId == companyId
                            && profile.ContactOwnerId == targetedStaff.IdentityId)
                    .ExecuteUpdateAsync(
                        field =>
                            field.SetProperty(f => f.ContactOwnerId, (string)null));

                await _appDbContext.SaveChangesAsync();

                await _appDbContext.CompanyShareableInvitationRecords
                    .Where(x => x.InvitedStaffId == targetedStaff.Id)
                    .ExecuteDeleteAsync();

                await _appDbContext.CompanyNotificationRecords
                    .Where(x => x.NotificationStaff.IdentityId == targetedStaff.IdentityId)
                    .ExecuteDeleteAsync();

                await _appDbContext.BlastMessageTemplates
                    .Where(
                        x =>
                            x.CompanyId == companyId &&
                            x.SavedById == targetedStaff.Id)
                    .ExecuteUpdateAsync(
                        blastMessageTemplate =>
                            blastMessageTemplate.SetProperty(t => t.SavedById, (long?) null));

                await _appDbContext.BlastMessageTemplates
                    .Where(
                        x =>
                            x.CompanyId == companyId &&
                            x.LastSentById == targetedStaff.Id)
                    .ExecuteUpdateAsync(
                        blastMessageTempalte =>
                            blastMessageTempalte.SetProperty(t => t.LastSentById, (long?) null));

                // var conversationActivityLogs = _appDbContext.ConversationActivityLogs.Where(x => x.ModifiedBy.IdentityId == targettedstaff.IdentityId);
                // await conversationActivityLogs.ForEachAsync(x => x.ModifiedBy = null);

                // var conversationRemarks = _appDbContext.ConversationRemarks.Where(x => x.RemarksStaff.IdentityId == targettedstaff.IdentityId);
                // await conversationRemarks.ForEachAsync(x => x.RemarksStaff = null);

                await _appDbContext.SaveChangesAsync();

                await _appDbContext.UserRoleGuests
                    .Where(
                        x =>
                            x.CompanyId == companyId &&
                            x.IdentityId == targetedStaff.IdentityId)
                    .ExecuteDeleteAsync();

                await _appDbContext.UserRoleStaffs
                    .Where(
                        x =>
                            x.CompanyId == companyId &&
                            x.IdentityId == targetedStaff.IdentityId)
                    .ExecuteDeleteAsync();

                await _appDbContext.CompanyAnalyticSegment
                    .Where(
                        x =>
                            x.CompanyId == companyId &&
                            x.SavedById == staffId)
                    .ExecuteUpdateAsync(
                        analyticSegment =>
                            analyticSegment.SetProperty(s => s.SavedById, (long?) null));

                var whatsappApplications = _appDbContext.CmsWhatsappApplications.Where(
                    x => x.CreatedByUserId == targetedStaff.IdentityId ||
                         x.UpdatedByUserId == targetedStaff.IdentityId);

                await whatsappApplications.ForEachAsync(
                    x =>
                    {
                        if (x.CreatedByUserId == targetedStaff.IdentityId)
                        {
                            x.CreatedByUserId = null;
                        }

                        if (x.UpdatedByUserId == targetedStaff.IdentityId)
                        {
                            x.UpdatedByUserId = null;
                        }
                    });

                await _appDbContext.WhatsApp360DialogTemplateBookmarks
                    .Where(x => x.CreatedByStaff.Id == targetedStaff.Id)
                    .ExecuteUpdateAsync(
                        bookmarkTemplate =>
                            bookmarkTemplate.SetProperty(t => t.CreatedByStaffId, (long?)null));


                // if (targettedstaff.Identity.Email != null)
                //     await _appDbContext.CmsHubSpotUserContactMaps.Where(x => x.Email == targettedstaff.Identity.Email).ExecuteDeleteAsync();

                await _appDbContext.SaveChangesAsync();

                await DeleteIdentityUser(targetedStaff.IdentityId);

                // try
                // {
                //     var userIdentity = await _userManager.FindByIdAsync(targettedstaff.IdentityId);
                //     await _userManager.DeleteAsync(userIdentity);
                //     await _appDbContext.SaveChangesAsync();
                // }
                // catch (Exception ex)
                // {
                //     _logger.LogError(ex, ex.Message);
                //
                //     throw ex;
                // }
            }
            catch (Exception ex)
            {
                var exceptionLineOfCode = new System.Diagnostics.StackTrace(ex, true).GetFrame(0)?.GetFileLineNumber() ?? 0;
                _logger.LogError(
                    ex,
                    "[CoreService {MethodName}] Company {CompanyId} error for {StaffId} at line {LineNumber}. {ExceptionMessage}",
                    nameof(RemoveStaffData),
                    companyId,
                    staffId,
                    exceptionLineOfCode,
                    ex.Message);

                return false;
            }

            return true;
        }

        public async Task DeleteIdentityUser(string identityId)
        {
            try
            {
                var userIdentity = await _userManager.FindByIdAsync(identityId);

                if (userIdentity is not null)
                {
                    await _userManager.DeleteAsync(userIdentity);
                }
            }
            catch (Exception err)
            {
                _logger.LogError(
                    err,
                    "Delete identity user {IdentityId} failed with user manager, retrying",
                    identityId);
            }

            try
            {
                // Dont need SaveChangeAsync here, because ExecuteDeleteAsync will call SaveChangeAsync:
                // https://mahsan-ghasemi-dev.medium.com/bulk-update-and-bulk-delete-using-executeupdate-and-executedelete-in-ef-core7-8751e94e6953
                await _appDbContext.Users
                    .Where(x => x.Id == identityId)
                    .ExecuteDeleteAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Delete identity user {IdentityId} failed",
                    identityId);
            }
        }

        public async Task UpdateSleekFlowLeadStage(string phoneNumber, string value)
        {
            try
            {
                var companyId = _configuration.GetValue<String>("Values:SleekFlowCompanyId");

                var userProfile = await _userProfileService.GetUserProfilesByFields(
                    companyId,
                    new List<Condition>
                    {
                        new Condition
                        {
                            FieldName = "PhoneNumber",
                            ConditionOperator = SupportedOperator.Equals,
                            Values = new List<string>
                            {
                                phoneNumber
                            }
                        }
                    },
                    0,
                    1);

                if (userProfile.UserProfiles.Count > 0)
                {
                    BackgroundJob.Enqueue<IUserProfileService>(
                        x => x.SetFieldValueByFieldNameSafe(
                            userProfile.UserProfiles.FirstOrDefault().Id,
                            "LeadStage",
                            value,
                            true));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[CoreService {MethodName}] Update lead stage to {Value} failed for {PhoneNumber}. {ExceptionMessage}",
                    nameof(UpdateSleekFlowLeadStage),
                    value,
                    phoneNumber,
                    ex.Message);
            }
        }

        public async Task AddInfoToSleekFlowCRM(
            string userCompanyId,
            ApplicationUser userIdentity,
            List<ImportHeader> headers,
            List<string> fields)
        {
            var company = await _appDbContext.CompanyCompanies
                .FirstOrDefaultAsync(x => x.Id == userCompanyId);

            var companyId = _configuration.GetValue<String>("Values:SleekFlowCompanyId");

            var userProfiles = await _userProfileService.GetUserProfilesByFields(
                companyId,
                new List<Condition>
                {
                    new Condition
                    {
                        FieldName = "PhoneNumber",
                        Values = new List<string>
                        {
                            userIdentity.PhoneNumber
                        },
                        ConditionOperator = SupportedOperator.Equals
                    }
                });

            if (userProfiles.UserProfiles.FirstOrDefault() == null)
            {
                userProfiles = await _userProfileService.GetUserProfilesByFields(
                    companyId,
                    new List<Condition>
                    {
                        new Condition
                        {
                            FieldName = "Email",
                            Values = new List<string>
                            {
                                userIdentity.Email
                            },
                            ConditionOperator = SupportedOperator.Equals
                        }
                    });
            }

            var userProfile = userProfiles.UserProfiles.FirstOrDefault();

            if (userProfile == null)
            {
                return;
            }

            var importUserProfile = new ImportUserProfileObject(headers, fields);

            await _userProfileService.BulkSetFields(
                userProfile,
                null,
                importUserProfile,
                userProfileSource: UserProfileSource.CrmIntegration);

            var companySize = await _companyService.GetCustomFieldByFieldName(
                userCompanyId,
                "Company Size");

            if (companySize != null)
            {
                companySize.Value = importUserProfile.GetValueFromList("COMPANY SIZE");
                await _appDbContext.SaveChangesAsync();
            }
        }

        public async Task AddToSleekFlowCRM(
            string userCompanyId,
            ApplicationUser userIdentity,
            string role,
            string heardFrom,
            List<ImportHeader> headers,
            List<string> fields,
            RegisterCompanyViewModel registerCompanyViewModel = null)
        {
            var shouldUseApiStr = _configuration.GetValue<string>("Values:SleekFlowCompanyShouldUsePublicApi");
            if (!string.IsNullOrEmpty(shouldUseApiStr) && shouldUseApiStr == "true")
            {
                await AddToSleekFlowCRMViaApi(
                    userCompanyId,
                    userIdentity,
                    role,
                    heardFrom,
                    headers,
                    fields,
                    registerCompanyViewModel);

                return;
            }

            try
            {
                var newCreated = false;

                var company = await _appDbContext.CompanyCompanies
                    .FirstOrDefaultAsync(x => x.Id == userCompanyId);

                var companyId = _configuration.GetValue<String>("Values:SleekFlowCompanyId");

                var coreWhatsapp = await _appDbContext.ConfigWhatsAppConfigs
                    .FirstOrDefaultAsync(x => x.CompanyId == companyId);

                var email = await _appDbContext.SenderEmailSenders
                    .FirstOrDefaultAsync(
                        x =>
                            x.Email == userIdentity.Email &&
                            x.CompanyId == companyId);

                if (email == null)
                {
                    email = new EmailSender
                    {
                        Email = userIdentity.Email,
                        CompanyId = companyId
                    };
                }

                var whatsapp = await _appDbContext.SenderWhatsappSenders
                    .FirstOrDefaultAsync(
                        x =>
                            (x.whatsAppId == $"whatsapp:+{userIdentity.PhoneNumber}" ||
                             x.whatsAppId == $"{userIdentity.PhoneNumber}@c.us") &&
                            x.CompanyId == companyId);

                if (whatsapp == null)
                {
                    whatsapp = new WhatsAppSender
                    {
                        CompanyId = companyId,
                        phone_number = userIdentity.PhoneNumber,
                        whatsAppId = $"whatsapp:+{userIdentity.PhoneNumber}",
                        name = userIdentity.FirstName,
                        InstanceId = coreWhatsapp?.TwilioAccountId,
                        InstaneSender = coreWhatsapp?.WhatsAppSender
                    };
                }

                SearchUserProfileResult userProfiles = null;

                if (!string.IsNullOrEmpty(userIdentity.PhoneNumber))
                {
                    userProfiles = await _userProfileService.GetUserProfilesByFields(
                        companyId,
                        new List<Condition>
                        {
                            new Condition
                            {
                                FieldName = "PhoneNumber",
                                Values = new List<string>
                                {
                                    userIdentity.PhoneNumber
                                },
                                ConditionOperator = SupportedOperator.Equals
                            }
                        });
                }

                if (userProfiles?.UserProfiles.FirstOrDefault() == null && !string.IsNullOrEmpty(userIdentity.Email))
                {
                    userProfiles = await _userProfileService.GetUserProfilesByFields(
                        companyId,
                        new List<Condition>
                        {
                            new Condition
                            {
                                FieldName = "Email",
                                Values = new List<string>
                                {
                                    userIdentity.Email
                                },
                                ConditionOperator = SupportedOperator.Equals
                            }
                        });
                }

                var userProfile = userProfiles?.UserProfiles.FirstOrDefault();

                if (userProfile == null)
                {
                    userProfile = new UserProfile
                    {
                        FirstName = userIdentity.FirstName,
                        LastName = userIdentity.LastName,
                        WhatsAppAccount = whatsapp,
                        CompanyId = companyId
                    };
                    _appDbContext.UserProfiles.Add(userProfile);
                    newCreated = true;

                    _applicationInsightsTelemetryTracer.TraceEvent(
                        TraceEventNames.ContactCreatedWithSource,
                        new Dictionary<string, string>
                        {
                            {
                                "source", UserProfileSource.Onboarding.ToString()
                            },
                            {
                                "company_id", companyId
                            }
                        },
                        new Dictionary<string, double>
                        {
                            {
                                "count", 1
                            }
                        });
                }

                userProfile.FirstName = userIdentity.FirstName;
                userProfile.LastName = userIdentity.LastName;

                await _appDbContext.SaveChangesAsync();

                if (headers == null)
                {
                    headers = new List<ImportHeader>();
                }

                if (fields == null)
                {
                    fields = new List<string>();
                }

                if (!string.IsNullOrEmpty(userProfile.WhatsAppAccount?.phone_number))
                {
                    headers.Add(
                        new ImportHeader
                        {
                            HeaderName = "PhoneNumber"
                        });

                    fields.Add(userProfile.WhatsAppAccount.phone_number);
                }

                headers.Add(
                    new ImportHeader
                    {
                        HeaderName = "Email"
                    });

                fields.Add(userIdentity.Email);

                if (company != null)
                {
                    headers.Add(
                        new ImportHeader
                        {
                            HeaderName = "CompanyName"
                        });

                    fields.Add(company.CompanyName);
                }

                headers.Add(
                    new ImportHeader
                    {
                        HeaderName = "Subscriber"
                    });

                fields.Add("true");

                if (registerCompanyViewModel != null)
                {
                    headers.Add(
                        new ImportHeader
                        {
                            HeaderName = "Company Size"
                        });

                    fields.Add(registerCompanyViewModel.CompanySize);
                }

                if (company != null)
                {
                    var lastBill = await _appDbContext.CompanyBillRecords
                        .OrderByDescending(x => x.PeriodStart)
                        .FirstOrDefaultAsync(
                            x =>
                                x.CompanyId == userCompanyId &&
                                ValidSubscriptionPlan.PaidPlan.Contains(x.SubscriptionPlanId) &&
                                x.PayAmount > 0);

                    if (lastBill != null)
                    {
                        if (lastBill.PaymentStatus == PaymentStatus.Paid &&
                            !string.IsNullOrEmpty(lastBill.stripe_subscriptionId) &&
                            lastBill.PayAmount > 0)
                        {
                            if (!string.IsNullOrEmpty(role))
                            {
                                if (role == "User")
                                {
                                    headers.Add(
                                        new ImportHeader
                                        {
                                            HeaderName = "LeadStage"
                                        });
                                    fields.Add("Invited User");

                                    var staffId = await _appDbContext.UserRoleStaffs
                                        .Where(x => x.CompanyId == companyId)
                                        .OrderBy(x => x.Order)
                                        .ThenBy(x => x.Id)
                                        .Select(x => x.IdentityId)
                                        .FirstOrDefaultAsync();
                                }
                                else
                                {
                                    headers.Add(
                                        new ImportHeader
                                        {
                                            HeaderName = "LeadStage"
                                        });

                                    fields.Add("Customers");
                                }
                            }

                            switch (lastBill.SubscriptionPlanId)
                            {
                                case "sleekflow_v2_premium":
                                    headers.Add(
                                        new ImportHeader
                                        {
                                            HeaderName = "Plan"
                                        });

                                    fields.Add("Premium");
                                    break;
                                case "sleekflow_v2_pro":
                                    headers.Add(
                                        new ImportHeader
                                        {
                                            HeaderName = "Plan"
                                        });

                                    fields.Add("Pro");
                                    break;
                                case "sleekflow_v2_standard":
                                    headers.Add(
                                        new ImportHeader
                                        {
                                            HeaderName = "Plan"
                                        });

                                    fields.Add("Standard");
                                    break;
                            }
                        }
                        else
                        {
                            headers.Add(
                                new ImportHeader
                                {
                                    HeaderName = "LeadStage"
                                });

                            fields.Add("Free Trial");

                            headers.Add(
                                new ImportHeader
                                {
                                    HeaderName = "Trial Started"
                                });

                            fields.Add(lastBill.PeriodStart.ToString("o"));

                            headers.Add(
                                new ImportHeader
                                {
                                    HeaderName = "Trial Ended"
                                });

                            fields.Add(lastBill.PeriodEnd.ToString("o"));
                        }
                    }
                    else
                    {
                        var lastBillFreeTrial = await _appDbContext.CompanyBillRecords
                            .OrderByDescending(x => x.PeriodStart)
                            .FirstOrDefaultAsync(
                                x =>
                                    x.CompanyId == userCompanyId &&
                                    ValidSubscriptionPlan.AllPaidPlans.Contains(x.SubscriptionPlanId));

                        if (!string.IsNullOrEmpty(role))
                        {
                            if (role == "User")
                            {
                                headers.Add(
                                    new ImportHeader
                                    {
                                        HeaderName = "LeadStage"
                                    });

                                fields.Add("Invited User");
                            }
                            else
                            {
                                headers.Add(
                                    new ImportHeader
                                    {
                                        HeaderName = "LeadStage"
                                    });

                                fields.Add("Sandbox");
                            }
                        }
                    }
                }

                if (!string.IsNullOrEmpty(role))
                {
                    headers.Add(
                        new ImportHeader
                        {
                            HeaderName = "Role"
                        });

                    fields.Add(role);
                }

                if (!string.IsNullOrEmpty(heardFrom))
                {
                    if (await _appDbContext.CompanyCustomUserProfileFields
                            .AnyAsync(
                                x =>
                                    x.CompanyId == companyId &&
                                    x.CustomUserProfileFieldOptions
                                        .Any(option => option.Value.ToLower() == heardFrom.ToLower())))
                    {
                        headers.Add(
                            new ImportHeader
                            {
                                HeaderName = "LeadSource"
                            });

                        fields.Add(heardFrom);
                    }
                }

                if (!string.IsNullOrEmpty(company?.ReferralCode))
                {
                    headers.Add(
                        new ImportHeader
                        {
                            HeaderName = "Referral Code"
                        });

                    fields.Add(company?.ReferralCode);
                }

                var importUserProfile = new ImportUserProfileObject(headers, fields);

                userProfile = await _userProfileService.BulkSetFields(
                    userProfile,
                    null,
                    importUserProfile,
                    userProfileSource: UserProfileSource.CrmIntegration);

                if (newCreated)
                {
                    BackgroundJob.Enqueue<IAutomationService>(
                        x => x.NewContactTrigger(userProfile.Id));

                    await _userProfileHooks.OnUserProfileCreatedAsync(
                        companyId,
                        userProfile.Id,
                        null,
                        () => Task.FromResult(new OnUserProfileCreatedData(userProfile)));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[CoreService {MethodName}] Company {CompanyId} error for {StaffIdentityId}. {ExceptionMessage}",
                    nameof(RemoveStaffData),
                    userCompanyId,
                    userIdentity.Id,
                    ex.Message);
            }
        }

        public async Task CheckSubscriptionExpiration()
        {
            var companies = await _appDbContext.CompanyCompanies
                .Where(
                    x =>
                        !x.IsDeleted &&
                        x.BillRecords.Any() &&
                        x.BillRecords
                            .Where(y => ValidSubscriptionPlan.SubscriptionPlan.Contains(y.SubscriptionPlanId))
                            .OrderByDescending(y => y.Id)
                            .FirstOrDefault().SubscriptionPlanId != "sleekflow_free")
                .OrderBy(x => x.CreatedAt)
                .Select(x => x.Id)
                .ToListAsync();

            foreach (var companyId in companies)
            {
                _logger.LogInformation(
                    "[{MethodName}] Checking for company {CompanyId}",
                    nameof(CheckSubscriptionExpiration),
                    companyId);

                var usage = await _companyUsageService.GetCompanyUsage(companyId);
            }

            _logger.LogInformation(
                "[{MethodName}] Completed subscription expiration checking",
                nameof(CheckSubscriptionExpiration));
        }

        [AutomaticRetry(
            Attempts = 1000,
            DelaysInSeconds = new[]
            {
                5
            })]
        public async Task RemoveAllUnusedCompanyAccounts()
        {
            _logger.LogInformation(
                "[{MethodName}] Started",
                nameof(RemoveAllUnusedCompanyAccounts));

            try
            {
                // Select all sleekflow_free and 90+ no login account
                var freeCompanies = await _appDbContext.CompanyCompanies
                    .Where(
                        x =>
                            !x.IsDeleted &&
                            x.CompanyType == CompanyType.DirectClient &&
                            x.BillRecords.Any() &&
                            x.BillRecords
                                .OrderByDescending(y => y.Id)
                                .FirstOrDefault().SubscriptionPlanId == "sleekflow_free" &&
                            x.Staffs
                                .OrderByDescending(y => y.Identity.LastLoginAt)
                                .FirstOrDefault()
                                .Identity
                                .LastLoginAt < DateTime.UtcNow.AddDays(-90))
                    .OrderBy(x => x.CreatedAt)
                    .Select(x => x.Id)
                    .ToListAsync();

                await CheckAndDeleteCompanyAccounts(freeCompanies);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Error: {ExceptionMessage}",
                    nameof(RemoveAllUnusedCompanyAccounts),
                    ex.Message);
                throw;
            }

            _logger.LogInformation(
                "[{MethodName}] Completed",
                nameof(RemoveAllUnusedCompanyAccounts));
        }

        [AutomaticRetry(
            Attempts = 1000,
            DelaysInSeconds = new[]
            {
                5
            })]
        public async Task CheckAndDeleteCompanyAccounts(List<string> freeCompanyIds)
        {
            var companyInfos = await _appDbContext
                .CmsCompanyAdditionalInfos
                .AsNoTracking()
                .Where(x => freeCompanyIds.Contains(x.CompanyId))
                .ToListAsync();

            foreach (var companyInfo in companyInfos)
            {
                if (companyInfo.AllTimeRevenueAnalyticData.TotalRevenue > 0)
                {
                    freeCompanyIds.Remove(companyInfo.CompanyId);
                }
            }

            var companyIdsToRemove = new List<string>();

            foreach (var companyId in freeCompanyIds)
            {
                // If already deleted continue
                if (await _appDbContext.CompanyCompanies
                        .AnyAsync(
                            x =>
                                x.Id == companyId &&
                                x.IsDeleted))
                {
                    continue;
                }

                var usage = await _companyUsageService.GetCompanyUsage(companyId);

                if (usage.billingPeriodUsages.Count <= 0)
                {
                    continue;
                }

                if (!ValidSubscriptionPlan.FreeTier.Contains(
                        usage.billingPeriodUsages.FirstOrDefault().BillRecord.SubscriptionPlan.Id))
                {
                    continue;
                }

                _logger.LogInformation($"No usage account pending to be deleted: {companyId}");

                // Last login is 90 days before
                var lastLoginAt = await _appDbContext.UserRoleStaffs
                    .Where(x => x.CompanyId == companyId)
                    .OrderByDescending(x => x.Identity.LastLoginAt)
                    .Select(x => x.Identity.LastLoginAt)
                    .FirstOrDefaultAsync();

                if (lastLoginAt < DateTime.UtcNow.AddDays(-90))
                {
                    companyIdsToRemove.Add(companyId);
                }
            }

            if (companyIdsToRemove.Any())
            {
                var maxThread = 10;
                foreach (var companyIdsToRemoveChuck in companyIdsToRemove.Chunk(companyIdsToRemove.Count / maxThread))
                {
                    BackgroundJob.Enqueue(() => RemoveCompanyAccountsDataAsync(companyIdsToRemoveChuck));
                }
            }
        }

        public async Task RemoveCompanyAccountsDataAsync(string[] companyIds)
        {
            foreach (var companyId in companyIds)
            {
                await RemoveCompanyData(companyId);
            }
        }

        private async Task DisconnectChannels(string companyId)
        {
            var isRemovedChannels = false;

            var facebookConfigs = await _appDbContext.ConfigFacebookConfigs
                .Where(x => x.CompanyId == companyId)
                .ToListAsync();

            if (facebookConfigs.Count > 0)
            {
                try
                {
                    foreach (var facebookConfig in facebookConfigs)
                    {
                        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

                        facebookConfig.SubscribedFields = string.Empty;

                        try
                        {
                            var subscribe = await httpClient.DeleteAsync(
                                $"https://graph.facebook.com/{facebookConfig.PageId}/subscribed_apps?" +
                                $"access_token={facebookConfig.PageAccessToken}" +
                                $"&subscribed_fields={facebookConfig.SubscribedFields}");

                            var subscribeResponse = await httpClient.GetStringAsync(
                                $"https://graph.facebook.com/{facebookConfig.PageId}/subscribed_apps?" +
                                $"access_token={facebookConfig.PageAccessToken}" +
                                $"&subscribed_fields={facebookConfig.SubscribedFields}");
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "[CoreService {MethodName}] Disconnect company {CompanyId} Facebook error, config id: {FacebookConfigId}. {ExceptionMessage}",
                                nameof(DisconnectChannels),
                                companyId,
                                facebookConfig.Id,
                                ex.Message);
                        }

                        var leadAdsNotificationConfig = await _appDbContext.FacebookLeadAdsNotificationConfigs
                                .FirstOrDefaultAsync(x =>
                                    x.CompanyId == facebookConfig.CompanyId
                                    && x.FacebookConfigId == facebookConfig.Id);

                        if (leadAdsNotificationConfig is not null)
                        {
                            _appDbContext.FacebookLeadAdsNotificationConfigs.Remove(leadAdsNotificationConfig);
                        }

                        _appDbContext.ConfigFacebookConfigs.RemoveRange(facebookConfig);
                        await _appDbContext.SaveChangesAsync();

                        isRemovedChannels = true;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[CoreService {MethodName}] Disconnect company {CompanyId} Facebook general exception. {ExceptionMessage}",
                        nameof(DisconnectChannels),
                        companyId,
                        ex.Message);
                }
            }

            var instagramConfigs = await _appDbContext.ConfigInstagramConfigs
                .Where(x => x.CompanyId == companyId)
                .ToListAsync();

            if (instagramConfigs.Count > 0)
            {
                try
                {
                    foreach (var instagramConfig in instagramConfigs)
                    {
                        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);
                        instagramConfig.SubscribedFields = string.Empty;

                        try
                        {
                            var subscribe = await httpClient.DeleteAsync(
                                $"https://graph.facebook.com/{instagramConfig.PageId}/subscribed_apps?" +
                                $"access_token={instagramConfig.PageAccessToken}" +
                                $"&subscribed_fields={instagramConfig.SubscribedFields}");

                            var subscribeResponse = await httpClient.GetStringAsync(
                                $"https://graph.facebook.com/{instagramConfig.PageId}/subscribed_apps?" +
                                $"access_token={instagramConfig.PageAccessToken}" +
                                $"&subscribed_fields={instagramConfig.SubscribedFields}");
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "[CoreService {MethodName}] Disconnect company {CompanyId} Instagram error, config id: {FacebookConfigId}. {ExceptionMessage}",
                                nameof(DisconnectChannels),
                                companyId,
                                instagramConfig.Id,
                                ex.Message);
                        }

                        _appDbContext.ConfigInstagramConfigs.RemoveRange(instagramConfig);
                        await _appDbContext.SaveChangesAsync();

                        isRemovedChannels = true;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[CoreService {MethodName}] Disconnect company {CompanyId} Instagram general exception. {ExceptionMessage}",
                        nameof(DisconnectChannels),
                        companyId,
                        ex.Message);
                }
            }

            var twilioConfigs = await _appDbContext.ConfigWhatsAppConfigs
                .Where(x => x.CompanyId == companyId)
                .ToListAsync();

            if (twilioConfigs.Count > 0)
            {
                try
                {
                    _appDbContext.ConfigWhatsAppConfigs.RemoveRange(twilioConfigs);
                    await _appDbContext.SaveChangesAsync();

                    isRemovedChannels = true;
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[CoreService {MethodName}] Disconnect company {CompanyId} WhatsApp Twilio general exception. {ExceptionMessage}",
                        nameof(DisconnectChannels),
                        companyId,
                        ex.Message);
                }
            }

            var whatsapp360DialogConfigs = await _appDbContext.ConfigWhatsApp360DialogConfigs
                .Where(x => x.CompanyId == companyId)
                .ToListAsync();

            if (whatsapp360DialogConfigs.Count > 0)
            {
                try
                {
                    foreach (var whatsapp360DialogConfig in whatsapp360DialogConfigs)
                    {
                        BackgroundJob.Enqueue<IWhatsApp360DialogService>(x => x.RemoveChannel(companyId, whatsapp360DialogConfig.Id));
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[CoreService {MethodName}] Disconnect company {CompanyId} WhatsApp 360Dialog channel from background general exception. {ExceptionMessage}",
                        nameof(DisconnectChannels),
                        companyId,
                        ex.Message);
                }
            }

            var whatsappCloudApiConfigs = await _appDbContext.ConfigWhatsappCloudApiConfigs
                .Where(x => x.CompanyId == companyId)
                .ToListAsync();

            if (whatsappCloudApiConfigs.Count > 0)
            {
                try
                {
                    foreach (var whatsappCloudApiConfig in whatsappCloudApiConfigs)
                    {
                        BackgroundJob.Enqueue<IWhatsappCloudApiService>(
                            x => x.DisconnectWhatsappCloudApiChannelWebhookAsync(
                                companyId,
                                whatsappCloudApiConfig.MessagingHubWabaId,
                                whatsappCloudApiConfig.MessagingHubWabaPhoneNumberId));
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[CoreService {MethodName}] Disconnect company {CompanyId} WhatsApp Cloud API channel from background general exception. {ExceptionMessage}",
                        nameof(DisconnectChannels),
                        companyId,
                        ex.Message);
                }
            }

            var lineConfigs = await _appDbContext.ConfigLineConfigs
                .Where(x => x.CompanyId == companyId)
                .ToListAsync();

            if (lineConfigs.Count > 0)
            {
                try
                {
                    _appDbContext.ConfigLineConfigs.RemoveRange(lineConfigs);
                    await _appDbContext.SaveChangesAsync();

                    isRemovedChannels = true;
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[CoreService {MethodName}] Disconnect company {CompanyId} LINE general exception. {ExceptionMessage}",
                        nameof(DisconnectChannels),
                        companyId,
                        ex.Message);
                }
            }

            var wechatConfig = await _appDbContext.CompanyCompanies
                .Include(x => x.WeChatConfig)
                .FirstOrDefaultAsync(x => x.Id == companyId);

            if (wechatConfig.WeChatConfigId.HasValue)
            {
                try
                {
                    _appDbContext.ConfigWeChatConfigs.RemoveRange(wechatConfig.WeChatConfig);
                    await _appDbContext.SaveChangesAsync();

                    isRemovedChannels = true;
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[CoreService {MethodName}] Disconnect company {CompanyId} WeChat general exception. {ExceptionMessage}",
                        nameof(DisconnectChannels),
                        companyId,
                        ex.Message);
                }
            }

            var shopifyConfigs = await _appDbContext.ConfigShopifyConfigs
                .Where(x => x.CompanyId == companyId)
                .ToListAsync();

            foreach (var shopifyConfig in shopifyConfigs)
            {
                try
                {
                    await _shopifyService.RemoveWebhooks(
                        shopifyConfig.CompanyId,
                        shopifyConfig.Id);
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[CoreService {MethodName}] Remove company {CompanyId} Shopify webhook general exception. {ExceptionMessage}",
                        nameof(DisconnectChannels),
                        companyId,
                        ex.Message);
                }

                _appDbContext.ConfigShopifyConfigs.RemoveRange(shopifyConfig);
                await _appDbContext.SaveChangesAsync();

                isRemovedChannels = true;
            }

            var shoplineConfigs = await _appDbContext.ConfigShoplineConfigs
                .Where(x => x.CompanyId == companyId)
                .ToListAsync();

            foreach (var shoplineConfig in shoplineConfigs)
            {
                _appDbContext.ConfigShoplineConfigs.RemoveRange(shoplineConfig);
                await _appDbContext.SaveChangesAsync();

                isRemovedChannels = true;
            }

            var recurringJobs = await _appDbContext.CompanyAssignmentRules
                .Where(
                    x =>
                        x.CompanyId == companyId &&
                        x.AutomationType == AutomationType.RecurringJob &&
                        x.Status == AutomationStatus.Live)
                .ToListAsync();

            foreach (var recurringJob in recurringJobs)
            {
                RecurringJob.RemoveIfExists(recurringJob.AssignmentId);
                recurringJob.Status = AutomationStatus.Draft;

                await _appDbContext.SaveChangesAsync();
            }

            if (isRemovedChannels)
            {
                var company = await _appDbContext.CompanyCompanies
                    .FirstOrDefaultAsync(x => x.Id == companyId);

                company.IsRemovedChannels = true;

                await _appDbContext.SaveChangesAsync();
            }
        }

        [AutomaticRetry(
            Attempts = 100,
            DelaysInSeconds = new[]
            {
                5
            })]
        public async Task RemoveCompanyData(string companyId)
        {
            var companyName = await _appDbContext.CompanyCompanies
                .Where(x => x.Id == companyId)
                .Select(x => x.CompanyName)
                .FirstOrDefaultAsync();

            _logger.LogWarning(
                "Removing company data: [{CompanyId}] [{CompanyName}]",
                companyId,
                companyName);

            await DisconnectChannels(companyId);

            try
            {
                // await using var transaction = await _appDbContext.Database.BeginTransactionAsync();

                // notification
                _appDbContext.Database.SetCommandTimeout(120);

                await _appDbContext.CompanyNotificationRecords
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                await _appDbContext.StripePaymentRecords
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                // assignment
                var assignments = await _appDbContext.CompanyAssignmentRules
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                await _appDbContext.CompanyAssignmentQueues
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                await _appDbContext.CompanyAssignmentRules
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                var automations = await _appDbContext.CompanyAutomationActions
                    .Where(x => x.CompanyId == companyId)
                    .ToListAsync();

                await _appDbContext.CompanyAssignmentUploadedFiles
                    .Where(x => automations
                        .Select(y => y.Id)
                        .Contains(x.AutomationActionId))
                    .ExecuteDeleteAsync();

                await _appDbContext.CompanyAutomationActions
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                var campaignAutomations = await _appDbContext.CampaignAutomationActions
                    .Where(x => x.CompanyId == companyId)
                    .ToListAsync();

                await _appDbContext.CampaignAutomationUploadedFiles
                    .Where(x => campaignAutomations
                        .Select(y => y.Id)
                        .Contains(x.CampaignAutomationActionId))
                    .ExecuteDeleteAsync();

                await _appDbContext.CompanyAutomationActions
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                // remove import
                await _appDbContext.CompanyImportedUserProfiles
                    .Where(
                        x => _appDbContext.CompanyImportContactHistories
                            .Where(x => x.CompanyId == companyId)
                            .Select(y => y.Id)
                            .Contains(x.ImportContactHistoryId))
                    .ExecuteDeleteAsync();

                await _appDbContext.CompanyImportContactHistories
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                // remove broadcast history
                // var broadcastsIds = await _appDbContext.CompanyMessageTemplates.Where(x => x.CompanyId == companyId).Select(y => y.Id).ToListAsync();

                await _appDbContext.ConversationHashtags.Where(
                    x => _appDbContext.Conversations
                        .Where(c => c.CompanyId == companyId)
                        .Select(c => c.Id)
                        .Contains(x.ConversationId))
                    .ExecuteDeleteAsync();

                await _appDbContext.ConversationAdditionalAssignees
                    .Where(x => _appDbContext.Conversations
                        .Where(c => c.CompanyId == companyId)
                        .Select(c => c.Id)
                        .Contains(x.ConversationId))
                    .ExecuteDeleteAsync();

                while (await _appDbContext.ConversationMessageUploadedFiles
                           .AnyAsync(x => x.BlobContainer == companyId))
                {
                    await _appDbContext.ConversationMessageUploadedFiles
                        .Where(x => x.BlobContainer == companyId)
                        .Take(500)
                        .ExecuteDeleteAsync();
                }

                await _appDbContext.Whatsapp360DialogExtendedMessagePayload
                    .Where(x => _appDbContext.ConversationMessages
                        .Where(c => c.CompanyId == companyId)
                        .Select(c => c.Id)
                        .Contains(x.ConversationMessageId))
                    .ExecuteDeleteAsync();

                while (await _appDbContext.ConversationMessages
                           .AnyAsync(x => x.CompanyId == companyId))
                {
                    await _appDbContext.ConversationMessages
                        .Where(x => x.CompanyId == companyId)
                        .Take(500)
                        .ExecuteDeleteAsync();
                }

                await _appDbContext.ConversationBookmarks
                    .Where(x => _appDbContext.Conversations
                        .Where(c => c.CompanyId == companyId)
                        .Select(c => c.Id)
                        .Contains(x.ConversationId))
                    .ExecuteDeleteAsync();

                await _appDbContext.ConversationWhatsappHistories
                    .Where(x => _appDbContext.Conversations
                        .Where(c => c.CompanyId == companyId)
                        .Select(c => c.Id)
                        .Contains(x.ConversationId))
                    .ExecuteDeleteAsync();

                await _appDbContext.CampaignUploadedFiles
                    .Where(x => x.BlobContainer == companyId)
                    .ExecuteDeleteAsync();

                await _appDbContext.BroadcastCompaignHistories
                    .Where(x => _appDbContext.CompanyMessageTemplates
                        .Where(x => x.CompanyId == companyId)
                        .Select(y => y.Id)
                        .Contains(x.BroadcastCampaignId))
                    .ExecuteDeleteAsync();

                await _appDbContext.CampaignChannelMessages
                    .Where(x => _appDbContext.CompanyMessageTemplates
                        .Where(x => x.CompanyId == companyId)
                        .Select(y => y.Id)
                        .Contains(x.CompanyMessageTemplateId))
                    .ExecuteDeleteAsync();

                await _appDbContext.CampaignAutomationActions
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                await _appDbContext.CompanyMessageTemplates
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                while (await _appDbContext.Conversations
                           .AnyAsync(x => x.CompanyId == companyId))
                {
                    await _appDbContext.Conversations
                        .Where(x => x.CompanyId == companyId)
                        .Take(500)
                        .ExecuteDeleteAsync();
                }

                await _userProfileCustomFieldsDeletionService.DeleteCustomUserProfileFieldsByCompanyIdAsync(companyId);

                await _appDbContext.CompanyCustomFieldFieldLinguals
                    .Where(x => _appDbContext.CompanyCompanyCustomFields
                        .Where(x => x.CompanyId == companyId)
                        .Select(x => x.Id)
                        .Contains(x.CompanyCustomFieldId))
                    .ExecuteDeleteAsync();

                await _appDbContext.CompanyCompanyCustomFields
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                do
                {
                    await _appDbContext.CrmHubEntities
                        .Where(x => x.UserProfile.CompanyId == companyId)
                        .Take(500)
                        .ExecuteDeleteAsync();
                }
                while (await _appDbContext.CrmHubEntities.AnyAsync(x => x.UserProfile.CompanyId == companyId));

                do
                {
                    await _appDbContext.UserProfiles
                        .Where(x => x.CompanyId == companyId)
                        .Take(500)
                        .ExecuteDeleteAsync();
                }
                while (await _appDbContext.UserProfiles.AnyAsync(x => x.CompanyId == companyId));

                await _appDbContext.SenderEmailSenders
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                await _appDbContext.SenderLineSender
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                await _appDbContext.SenderSandboxSenders
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                await _appDbContext.SenderSMSSenders
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                do
                {
                    await _appDbContext.SenderWebClientIPAddressInfos
                        .Where(x => _appDbContext.SenderWebClientSenders
                            .Where(y => y.CompanyId == companyId)
                            .Select(y => y.Id)
                            .Contains(x.WebClientSenderId))
                        .Take(1000)
                        .ExecuteDeleteAsync();
                }
                while (await _appDbContext.SenderWebClientIPAddressInfos
                           .AnyAsync(x => _appDbContext.SenderWebClientSenders
                               .Where(y => y.CompanyId == companyId)
                               .Select(y => y.Id)
                               .Contains(x.WebClientSenderId)));

                do
                {
                    await _appDbContext.SenderWebClientSenders
                        .Where(x => x.CompanyId == companyId)
                        .Take(1000)
                        .ExecuteDeleteAsync();
                }
                while (await _appDbContext.SenderWebClientSenders
                           .AnyAsync(x => x.CompanyId == companyId));

                await _appDbContext.SenderWeChatSenders
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                await _appDbContext.SenderWhatsApp360DialogSenders
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                await _appDbContext.CompanySandboxes
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                await _appDbContext.UserProfilePictureFiles
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                await _appDbContext.ConversationHashtags
                    .Where(x => _appDbContext.CompanyDefinedHashtags
                        .Where(c => c.CompanyId == companyId)
                        .Select(c => c.Id)
                        .Contains(x.HashtagId))
                    .ExecuteDeleteAsync();

                await _appDbContext.CompanyDefinedHashtags
                    .Where(x => x.CompanyId == companyId)
                    .ToListAsync();

                var quickReply = await _appDbContext.CompanyQuickReplies
                    .Where(x => x.CompanyId == companyId)
                    .Include(x => x.CompanyQuickReplyLinguals)
                    .Include(x => x.QuickReplyFile)
                    .ToListAsync();

                if (quickReply.Any())
                {
                    _appDbContext.CompanyQuickReplies.RemoveRange(quickReply);
                }

                await _appDbContext.CompanyShareableInvitations
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                // remove integrations
                await _appDbContext.ConfigFacebookConfigs
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                await _appDbContext.ConfigWhatsAppConfigs
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                await _appDbContext.ConfigLineConfigs
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                await _appDbContext.ConfigSMSConfigs
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                await _appDbContext.WhatsApp360DialogMediaFiles
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                await _appDbContext.WhatsApp360DialogTemplateBookmarks
                    .Where(x => x.CompanyId == companyId)
                    .ToListAsync();

                await _appDbContext.ConfigWhatsApp360DialogConfigs
                    .Where(x => x.CompanyId == companyId)
                    .ToListAsync();

                await _appDbContext.ConfigInstagramConfigs
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                await _appDbContext.ConfigStorageConfigs
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                try
                {
                    await _azureBlobStorageService.DeleteContainer(companyId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[CoreService {MethodName}] Remove company {CompanyId} blob container error. {ExceptionMessage}",
                        nameof(RemoveCompanyData),
                        companyId,
                        ex.Message);
                }

                await _appDbContext.CompanyStaffTeams
                    .Where(x => x.CompanyId == companyId)
                    .Include(x => x.Members)
                    .ExecuteDeleteAsync();

                await _appDbContext.CoreRedeemPromotionRecords
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                await _appDbContext.CorePromotions
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                await _appDbContext.CompanyRequestChannels
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                await _appDbContext.CoreRedeemPromotionRecords
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                await _appDbContext.CompanyIconFiles
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                await _appDbContext.CompanyBillRecords
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteUpdateAsync(
                        billRecord =>
                            billRecord.SetProperty(r => r.PurchaseStaffId, (long?) null));

                await _appDbContext.CompanyAnalyticSegment
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                var company = await _appDbContext.CompanyCompanies
                    .FirstOrDefaultAsync(x => x.Id == companyId);

                company.IsDeleted = true;
                company.UpdatedAt = DateTime.UtcNow;

                await _appDbContext.SaveChangesAsync();

                var identityIds = await _appDbContext.UserRoleStaffs
                    .Where(x => x.CompanyId == companyId)
                    .Select(x => x.IdentityId)
                    .ToListAsync();

                await _appDbContext.UserRoleStaffs
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                await _appDbContext.UserStaffProfilePictures
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                await _appDbContext.UserRoleGuests
                    .Where(x => identityIds.Contains(x.IdentityId))
                    .ExecuteDeleteAsync();

                await _appDbContext.CoreZapierPollingRecord
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                await _appDbContext.CompanyAPIKeys
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                // await _appDbContext.CompanyWhatsapp360DialogTopUpConfigs.Where(x => x.CompanyId == companyId).ExecuteDeleteAsync();
                // await _appDbContext.CmsWhatsappApplications.Where(x => x.CompanyId == companyId).ExecuteDeleteAsync();
                // await _appDbContext.CompanyQuickReplyFiles.Where(x => _appDbContext.CompanyQuickReplies.Where(c => c.CompanyId == companyId).Select(c => c.Id).Contains(x.QuickReplyFileId)).ExecuteDeleteAsync();
                // await _appDbContext.CompanyWhatsApp360DialogUsageTransactionLogs.Where(x => x.CompanyId == companyId).ExecuteDeleteAsync();
                // await _appDbContext.CompanyWhatsApp360DialogUsageRecords.Where(x => x.CompanyId == companyId).ExecuteDeleteAsync();
                // await _appDbContext.CompanyTwilioUsageRecords.Where(x => x.CompanyId == companyId).ExecuteDeleteAsync();
                // await _appDbContext.CompanyPaymentFailedLogs.Where(x => x.CompanyId == companyId).ExecuteDeleteAsync();
                // await _appDbContext.CmsHubSpotCompanyMaps.Where(x => x.CompanyId == companyId).ExecuteDeleteAsync();

                await _appDbContext.UserProfileShopifyAbandonedCarts
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                await _appDbContext.UserProfileShopifyOrders
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                await _appDbContext.ConfigShopifyConfigs
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                await _appDbContext.ConfigShoplineConfigs
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                await _appDbContext.BlastMessageTemplates
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                await _appDbContext.CompanyQuickReplies
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                await _appDbContext.CompanyQuickReplyFiles
                    .Where(x => x.BlobContainer == companyId)
                    .ExecuteDeleteAsync();

                if (company.EmailConfigId.HasValue)
                {
                    company.EmailConfigId = null;

                    await _appDbContext.ConfigEmailConfigs
                        .Where(x => x.Id == company.EmailConfigId)
                        .ExecuteDeleteAsync();
                }

                if (company.WeChatConfigId.HasValue)
                {
                    company.WeChatConfigId = null;

                    await _appDbContext.ConfigWeChatConfigs
                        .Where(x => x.Id == company.WeChatConfigId)
                        .ExecuteDeleteAsync();
                }

                // await transaction.CommitAsync();

                foreach (var identityId in identityIds)
                {
                    try
                    {
                        var user = await _userManager.FindByIdAsync(identityId);
                        await _userManager.DeleteAsync(user);
                        await _appDbContext.SaveChangesAsync();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[CoreService {MethodName}] Remove company {CompanyId} user {UserIdentityId} error. {ExceptionMessage}",
                            nameof(RemoveCompanyData),
                            companyId,
                            identityId,
                            ex.Message);
                    }
                }

                await _appDbContext.SenderWhatsappSenders
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                await _appDbContext.SenderFacebookSenders
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                await _appDbContext.IntegrationAlertConfigs
                    .Where(x => x.CompanyId == companyId)
                    .ExecuteDeleteAsync();

                BackgroundJob.Enqueue<IInternalHubSpotService>(
                    x => x.MarkCompanyAsDeleted(companyId));
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[CoreService {MethodName}] Remove company {CompanyId} general exception. {ExceptionMessage}",
                    nameof(RemoveCompanyData),
                    companyId,
                    ex.Message);

                throw;
            }
        }

        /// <summary>
        /// Extract from <see cref="DeleteInvalidUserProfileCustomFields"/>
        /// </summary>
        /// <see cref="DeleteInvalidUserProfileCustomFields"/>
        /// <returns>Task.</returns>
        public async Task DeleteInvalidUserProfileCustomFields()
        {
            try
            {
                await _appDbContext.UserProfileCustomFields
                    .Where(x => x.UserProfileId == null)
                    .ExecuteDeleteAsync();

                await _appDbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Error deleting orphan rows in UserProfileCustomFields. {ExceptionMessage}",
                    nameof(DeleteInvalidUserProfileCustomFields),
                    ex.Message);
            }
        }

        /// <summary>
        /// Removes unused blank WebClientSenders that don't have any associated ConversationMessages,
        /// along with their related UserProfiles and Conversations. Limited to run for 2 hours.
        /// </summary>
        /// <returns>Task.</returns>
        [AutomaticRetry(Attempts = 1)]
        public async Task DeleteBlankWebClientSenders()
        {
            _logger.LogInformation("[DeleteBlankWebClientSenders] Starting cleanup of unused WebClientSenders");

            const int batchSize = 1000;
            const int processCount = 500000;

            long lastWebClientId = 4830391;

            var savedLastWebClientId = await _cacheManagerService.GetCacheWithConstantKeyAsync(
                "DeleteBlankWebClientSenders");

            if (savedLastWebClientId != null)
                long.TryParse(savedLastWebClientId, out lastWebClientId);

            try
            {
                _appDbContext.Database.SetCommandTimeout(180); // Set a longer timeout for this operation

                var totalDeletedSenders = 0;
                var totalDeletedUserProfiles = 0;
                var totalDeletedConversations = 0;
                var hasMoreRecords = true;

                while (hasMoreRecords && totalDeletedSenders < processCount)
                {
                    // Find WebClientSenders that don't have any associated ConversationMessages
                    var id = lastWebClientId;

                    var unusedSenders = await _appDbContext.SenderWebClientSenders
                        .Where(x => x.Id > id)
                        .Take(batchSize)
                        .OrderBy(x => x.Id)
                        .ToListAsync();

                    if (unusedSenders.Count == 0)
                    {
                        hasMoreRecords = false;

                        continue;
                    }

                    foreach (var unusedSender in unusedSenders)
                    {
                        var shouldDelete = false;

                        var userProfileIds = await _appDbContext.UserProfiles
                            .Where(
                                up => up.WebClientId.HasValue &&
                                      up.WebClientId == unusedSender.Id)
                            .Select(up => new { up.Id, up.ActiveStatus})
                            .ToListAsync();

                        if (userProfileIds.Any(x => x.ActiveStatus == ActiveStatus.Active))
                        {
                            continue;
                        }

                        lastWebClientId = unusedSender.Id;

                        List<string> conversationIds = null;

                        if (userProfileIds.Any())
                        {
                            // Get Conversations associated with these UserProfiles
                            conversationIds = await _appDbContext.Conversations
                                .Where(c => userProfileIds.Select(x => x.Id).Contains(c.UserProfileId))
                                .Select(c => c.Id)
                                .ToListAsync();

                            if (!conversationIds.Any())
                                shouldDelete = true; // no conversation

                            if (conversationIds.Any() &&
                                !_appDbContext.ConversationMessages.Any(
                                    x => conversationIds.Contains(x.ConversationId)))
                            {
                                shouldDelete = true;
                            }
                        }
                        else
                        {
                            // No user profile attached remove
                            shouldDelete = true;
                        }

                        if (!shouldDelete)
                            continue;

                        if (_appDbContext.ConversationMessages.Any(
                                x => x.WebClientSenderId == unusedSender.Id ||
                                     x.WebClientReceiverId == unusedSender.Id))
                            shouldDelete = false;

                        if (!shouldDelete)
                            continue;

                        if (conversationIds != null && conversationIds.Any())
                        {
                            await _appDbContext.BroadcastCompaignHistories
                                .Where(c => conversationIds.Contains(c.ConversationId))
                                .ExecuteDeleteAsync();

                            await _appDbContext.Conversations
                                .Where(c => conversationIds.Contains(c.Id))
                                .ExecuteDeleteAsync();

                            totalDeletedConversations += conversationIds.Count;
                        }

                        if (userProfileIds.Any())
                        {
                            // Delete UserProfileCustomFields
                            await _appDbContext.UserProfileCustomFields
                                .Where(upcf => userProfileIds.Select(x => x.Id).Contains(upcf.UserProfileId))
                                .ExecuteDeleteAsync();

                            await _appDbContext.CompanyImportedUserProfiles
                                .Where(x => userProfileIds.Select(x => x.Id).Contains(x.UserProfileId))
                                .ExecuteDeleteAsync();

                            // Delete UserProfiles
                            await _appDbContext.UserProfiles
                                .Where(up => userProfileIds.Select(x => x.Id).Contains(up.Id))
                                .ExecuteDeleteAsync();

                            totalDeletedUserProfiles += userProfileIds.Count;
                        }

                        // Delete IP address info records associated with these senders
                        await _appDbContext.SenderWebClientIPAddressInfos
                            .Where(ip => ip.WebClientSenderId == unusedSender.Id)
                            .ExecuteDeleteAsync();

                        // Delete the WebClientSenders
                        await _appDbContext.SenderWebClientSenders
                            .Where(s => s.Id == unusedSender.Id)
                            .ExecuteDeleteAsync();

                        totalDeletedSenders++;
                    }

                    // Check if we've reached the time limit
                    if (totalDeletedSenders > processCount)
                    {
                        _logger.LogInformation(
                            $"[DeleteBlankWebClientSenders] Delete count of {processCount} reached. Stopping cleanup process.");

                        break;
                    }

                    await _cacheManagerService.SaveCacheWithConstantKeyAsync(
                        "DeleteBlankWebClientSenders",
                        lastWebClientId,
                        TimeSpan.FromDays(2));
                }

                _logger.LogInformation(
                    "[DeleteBlankWebClientSenders] Completed. Total deleted: {TotalDeletedSenders} WebClientSenders, {TotalDeletedUserProfiles} UserProfiles, {TotalDeletedConversations} Conversations",
                    totalDeletedSenders,
                    totalDeletedUserProfiles,
                    totalDeletedConversations);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[DeleteBlankWebClientSenders] Error while deleting unused WebClientSenders: {ErrorMessage}",
                    ex.Message);

                await _cacheManagerService.SaveCacheWithConstantKeyAsync(
                    "DeleteBlankWebClientSenders",
                    lastWebClientId + 1, // Continue from the last processed ID
                    TimeSpan.FromDays(2));

                throw;
            }
        }

        /// <summary>
        /// Deletes old ConversationUnreadRecords with Read status using ID-based iteration for performance.
        /// </summary>
        /// <param name="pastWeek">The cutoff date for determining old records</param>
        /// <returns>Total count of deleted records</returns>
        private async Task<int> DeleteOldConversationUnreadRecords()
        {
            _logger.LogInformation("[DeleteOldConversationUnreadRecords] Starting cleanup of old ConversationUnreadRecords");

            const int batchSize = 5000;
            const int processCount = 15000000;

            long lastProcessedId = 0;

            try
            {
                _appDbContext.Database.SetCommandTimeout(180);

                var totalDeletedRecords = 0;
                var hasMoreRecords = true;

                while (totalDeletedRecords < processCount)
                {
                    var recordsToDelete = await _appDbContext.ConversationUnreadRecords
                        .Where(x => x.Id > lastProcessedId)
                        .OrderBy(x => x.Id)
                        .Take(batchSize)
                        .AsNoTracking()
                        .ToListAsync();

                    if (recordsToDelete.Count == 0)
                        break;

                    var readRecords = recordsToDelete
                        .Where(x => x.NotificationDeliveryStatus == NotificationDeliveryStatus.Read)
                        .Select(x => x.Id)
                        .ToList();

                    // Delete all read records
                    var deletedReadRecords = await _appDbContext.ConversationUnreadRecords
                        .Where(x => readRecords.Contains(x.Id))
                        .ExecuteDeleteAsync();

                    var unreadRecords = recordsToDelete
                        .Where(x => x.NotificationDeliveryStatus != NotificationDeliveryStatus.Read &&
                                    x.CreatedAt < DateTime.UtcNow.AddDays(-90))
                        .Select(x => x.Id)
                        .ToList();

                    // Keep unread records older than 90 days
                    var deletedUnreadRecords = await _appDbContext.ConversationUnreadRecords
                        .Where(x => unreadRecords.Contains(x.Id))
                        .ExecuteDeleteAsync();

                    totalDeletedRecords += deletedReadRecords + deletedUnreadRecords;

                    // Check if we've reached the process limit
                    if (totalDeletedRecords >= processCount)
                    {
                        _logger.LogInformation(
                            "[DeleteOldConversationUnreadRecords] Delete count of {ProcessCount} reached. Stopping cleanup process.",
                            processCount);
                        break;
                    }

                    lastProcessedId = recordsToDelete.LastOrDefault()!.Id;
                }

                _logger.LogInformation(
                    "[DeleteOldConversationUnreadRecords] Completed. Total deleted: {TotalDeletedRecords} ConversationUnreadRecords",
                    totalDeletedRecords);

                return totalDeletedRecords;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[DeleteOldConversationUnreadRecords] Error while deleting old ConversationUnreadRecords: {ErrorMessage}",
                    ex.Message);

                throw;
            }
        }

        public async Task CreateCompany(ApplicationUser userIdentity, RegisterCompanyViewModel registerCompanyViewModel)
        {
            var companyStaff = await _appDbContext.UserRoleStaffs
                .Include(x => x.Company)
                .FirstOrDefaultAsync(x => x.IdentityId == userIdentity.Id);

            if (companyStaff != null)
            {
                throw new Exception($"You already in company: {companyStaff.Company.CompanyName}");
            }

            // return BadRequest(new ResponseViewModel { message = $"You already in company: {companyStaff.Company.CompanyName}" });

            if (string.IsNullOrEmpty(registerCompanyViewModel.TimeZoneInfoId))
            {
                registerCompanyViewModel.TimeZoneInfoId = "GMT Standard Time";
            }
            else
            {
                if (TimeZoneHelper.GetTimeZoneByIdOrDefault(registerCompanyViewModel.TimeZoneInfoId) == null)
                {
                    throw new Exception($"Invalid TimeZoneInfoId: {registerCompanyViewModel.TimeZoneInfoId}");
                }
            }

            var subscriptionPlan = await _appDbContext.CoreSubscriptionPlans
                .FirstOrDefaultAsync(x => x.Id == registerCompanyViewModel.SubscriptionPlanId);

            if (subscriptionPlan == null)
            {
                throw new Exception($"Invalid subscription Id: {registerCompanyViewModel.SubscriptionPlanId}");
            }

            // return BadRequest(new ResponseViewModel { message = $"Invalid subscription Id: {registerCompanyViewModel.SubscriptionPlanId}" });

            userIdentity.UserRole = "staff";

            var company = new Company
            {
                Id = registerCompanyViewModel.Id ?? Guid.NewGuid().ToString(),
                CompanyName = registerCompanyViewModel.CompanyName,
                BillRecords = await GetInitialBillRecords(subscriptionPlan),
                TimeZoneInfoId = registerCompanyViewModel.TimeZoneInfoId,
                lmref = registerCompanyViewModel.lmref,
                IsShopifyAccount = registerCompanyViewModel.IsShopifyAccount,
                CompanyType = registerCompanyViewModel.CompanyType,
                MaximumAgents = subscriptionPlan.IncludedAgents,
                MaximumAutomations = subscriptionPlan.MaximumAutomation,
                UsageLimitOffsetProfile = new CompanyUsageLimitOffsetProfile
                {
                    IsEnabled = subscriptionPlan.PlanTypeCode == PlanTypeCodes.BasePlan && subscriptionPlan.SubscriptionTier != SubscriptionTier.Free
                }
            };
            company.StorageConfig = new StorageConfig()
            {
                ContainerName = company.Id
            };

            if (subscriptionPlan.Id == "sleekflow_freemium")
            {
                company.MaximumNumberOfChannel = 3;
            }

            var customFields = new List<CompanyCustomField>()
            {
                new CompanyCustomField
                {
                    Category = "CompanyInfo",
                    FieldName = "CompanySize",
                    Type = FieldDataType.SingleLineText,
                    CompanyCustomFieldFieldLinguals = new List<CompanyCustomFieldFieldLingual>
                    {
                        new CompanyCustomFieldFieldLingual
                        {
                            DisplayName = "公司人數", Language = "zh-HK"
                        },
                        new CompanyCustomFieldFieldLingual
                        {
                            DisplayName = "Company Size", Language = "en"
                        }
                    },
                    Value = registerCompanyViewModel.CompanySize
                },
                new CompanyCustomField
                {
                    Category = "CompanyInfo",
                    FieldName = "PhoneNumber",
                    Type = FieldDataType.SingleLineText,
                    CompanyCustomFieldFieldLinguals = new List<CompanyCustomFieldFieldLingual>
                    {
                        new CompanyCustomFieldFieldLingual
                        {
                            DisplayName = "公司電話", Language = "zh-HK"
                        },
                        new CompanyCustomFieldFieldLingual
                        {
                            DisplayName = "Company Phone", Language = "en"
                        }
                    },
                    Value = registerCompanyViewModel.PhoneNumber
                },
                new CompanyCustomField
                {
                    Category = "CompanyInfo",
                    FieldName = "Industry",
                    Type = FieldDataType.SingleLineText,
                    CompanyCustomFieldFieldLinguals = new List<CompanyCustomFieldFieldLingual>
                    {
                        new CompanyCustomFieldFieldLingual
                        {
                            DisplayName = "行業", Language = "zh-HK"
                        },
                        new CompanyCustomFieldFieldLingual
                        {
                            DisplayName = "Company Industry", Language = "en"
                        }
                    },
                    Value = registerCompanyViewModel.Industry
                },
                new CompanyCustomField
                {
                    Category = "CompanyInfo",
                    FieldName = "OnlineShopSystem",
                    Type = FieldDataType.SingleLineText,
                    CompanyCustomFieldFieldLinguals = new List<CompanyCustomFieldFieldLingual>
                    {
                        new CompanyCustomFieldFieldLingual
                        {
                            DisplayName = "網上商店系統", Language = "zh-HK"
                        },
                        new CompanyCustomFieldFieldLingual
                        {
                            DisplayName = "Online Shop System", Language = "en"
                        }
                    },
                    Value = registerCompanyViewModel.OnlineShopSystem
                },
                new CompanyCustomField
                {
                    Category = "CompanyInfo",
                    FieldName = "CompanyWebsite",
                    Type = FieldDataType.SingleLineText,
                    CompanyCustomFieldFieldLinguals = new List<CompanyCustomFieldFieldLingual>
                    {
                        new CompanyCustomFieldFieldLingual
                        {
                            DisplayName = "公司網頁", Language = "zh-HK"
                        },
                        new CompanyCustomFieldFieldLingual
                        {
                            DisplayName = "Company Website", Language = "en"
                        }
                    },
                    Value = registerCompanyViewModel.CompanyWebsite
                },
                new CompanyCustomField
                {
                    Category = "CompanyInfo",
                    FieldName = "PlatformUsageIntent",
                    Type = FieldDataType.SingleLineText,
                    CompanyCustomFieldFieldLinguals = new List<CompanyCustomFieldFieldLingual>
                    {
                        new CompanyCustomFieldFieldLingual
                        {
                            DisplayName = "Platform Usage Purpose", Language = "en"
                        }
                    },
                    Value = registerCompanyViewModel.PlatformUsageIntent
                }
            };

            _logger?.LogInformation(
                "Create company: registerCompanyViewModel.PlatformUsageIntent: {PlatformUsageIntent}",
                registerCompanyViewModel.PlatformUsageIntent);

            company.CompanyCustomFields = customFields;
            company.CommunicationTools = registerCompanyViewModel.CommunicationTools;

            var staff = new Staff
            {
                CompanyId = company.Id,
                IdentityId = userIdentity.Id,
                TimeZoneInfoId = registerCompanyViewModel.TimeZoneInfoId,
                RoleType = StaffUserRole.Admin,
                NotificationSettingId = 1
            };
            company.SignalRGroupName = company.Id;

            company = AddDefaultUserFiels(company);
            company = AddDefaultQuickReply(company);
            company = AddDefaultLabel(company);
            company = AddDefaultCompanyList(company);

            try
            {
                var phoneNumberUtil = PhoneNumbers.PhoneNumberUtil.GetInstance();
                var phonenumber = phoneNumberUtil.Parse($"+{userIdentity.PhoneNumber}", null);
                var countCode = PhoneNumberHelper.GetCountryCode(phonenumber);
                string countryName = _countryService.GetCountryEnglishNameByCountryCode(countCode);

                company.CompanyCountry = countryName;
            }
            catch (Exception ex)
            {
                _logger.LogInformation("country ignore" + ex.Message);
            }

            _appDbContext.CompanyCompanies.Add(company);
            _appDbContext.UserRoleStaffs.Add(staff);

            var assignmentRule = new AssignmentRule
            {
                AssignmentRuleName = "Default",
                AssignmentType = AssignmentType.SpecificPerson,
                AssignedStaff = staff,
                CompanyId = company.Id
            };

            await _appDbContext.CompanyAssignmentRules.AddAsync(assignmentRule);
            await _appDbContext.SaveChangesAsync();

            await AddDefaultAutomationRules(company);

            ////Add Contact to SleekFlow
            // BackgroundJob.Enqueue<ICoreService>(x => x.AddToSleekFlowCRM(company.Id, userIdentity, "Owner", registerCompanyViewModel.HeardFrom, null, null));
            // BackgroundJob.Enqueue<ICompanyService>(x => x.CreateSandbox(company.Id, company.CompanyName));

            // var verificationResult = await _userManager.GenerateEmailConfirmationTokenAsync(userIdentity);

            // default whatsapp instance
            // if (subscriptionPlan.Id != "sleekflow_free")
            //    company.MaximumWhatsappInstance = 1;

            if (!string.IsNullOrEmpty(registerCompanyViewModel.PromotionCode))
            {
                try
                {
                    await RedeemPromotionCode(
                        new ApplyPromotionCodeViewModel
                        {
                            PromotionCode = registerCompanyViewModel.PromotionCode
                        },
                        company.Id);
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "Redeem promotion code {PromotionCode} for company {CompanyId} error. {ExceptionMessage}",
                        registerCompanyViewModel.PromotionCode,
                        company.Id,
                        ex.Message);
                }
            }

            var newPromotion = new Promotion
            {
                CompanyId = company.Id,
                PromotionCode = ConversationHelper.RandomString(8),
                ExpiryDate = DateTime.MaxValue,
                ExtraAgents = 1,
                ExtraAutomatedMessages = 0,
                ExtraContacts = 0
            };
            if (await _appDbContext.CorePromotions
                    .AnyAsync(
                        x =>
                            x.PromotionCode == newPromotion.PromotionCode &&
                            x.CompanyId == company.Id))
            {
                company.ReferralCode = newPromotion.PromotionCode;
            }
            else if (await _appDbContext.CorePromotions
                         .AnyAsync(x => x.PromotionCode == newPromotion.PromotionCode))
            {
                newPromotion.PromotionCode = $"{ConversationHelper.RandomString(8)}";
                _appDbContext.CorePromotions.Add(newPromotion);
            }
            else
            {
                _appDbContext.CorePromotions.Add(newPromotion);
            }

            company.ReferralCode = newPromotion.PromotionCode;
            company.IsFreeTrial = true;
            await _appDbContext.SaveChangesAsync();

            var integrationAlertConfig = new IntegrationAlertConfig(
                company.Id,
                new List<string>(),
                new List<string>());

            await _integrationAlertConfigRepository.CreateConfigAsync(integrationAlertConfig);

            // Add Contact to SleekFlow
            BackgroundJob.Enqueue<ICoreService>(
                x => x.AddToSleekFlowCRM(
                    company.Id,
                    userIdentity,
                    "Owner",
                    registerCompanyViewModel.HeardFrom,
                    null,
                    null,
                    registerCompanyViewModel));

            BackgroundJob.Enqueue<ICompanyService>(
                x => x.CreateSandbox(
                    company.Id,
                    company.CompanyName));

            // HubSpot
            BackgroundJob.Schedule<IInternalHubSpotService>(
                x => x.CreateNewSignupCompanyAndStaff(
                    company.Id,
                    userIdentity.Email),
                TimeSpan.FromMinutes(2));

            // Sync to NetSuite
            BackgroundJob.Schedule<IInternalIntegrationService>(
                x => x.CreateCustomerAsync(
                    company.Id,
                    company.CompanyName,
                    company.CompanyCountry,
                    userIdentity.Email,
                    userIdentity.PhoneNumber,
                    null),
                TimeSpan.FromMinutes(2));

            // Sync PartnerStack Customer Key From HubSpot Contact
            BackgroundJob.Schedule<IInternalPartnerStackService>(
                x => x.SyncPartnerStackCustomerKeyFromHubSpotContact(
                    company.Id,
                    userIdentity.Email),
                TimeSpan.FromMinutes(4));

            if (!string.IsNullOrEmpty(registerCompanyViewModel.Referral))
            {
                var stripeClient = _stripeClients.GetStripeClient();

                var metaData = new Dictionary<string, string>()
                {
                    {
                        "referral", registerCompanyViewModel.Referral
                    }
                };

                var options = new CustomerCreateOptions
                {
                    Email = userIdentity.Email,
                    Metadata = metaData,
                    Description = $"{userIdentity.FirstName} {userIdentity.LastName}"
                };

                var service = new CustomerService(stripeClient);
                var customer = await service.CreateAsync(options);

                company.AffiliateCustomerId = customer.Id;
                await _appDbContext.SaveChangesAsync();
            }

            await _companyInfoCacheService.RemoveCompanyInfoCache(company.Id);
        }

        private async Task<List<BillRecord>> GetInitialBillRecords(SubscriptionPlan subscriptionPlan)
        {
            var initialBaseBillRecord = GetInitialBaseBillRecord(subscriptionPlan);

            var billRecords = new List<BillRecord> { initialBaseBillRecord };

            // Add whatsapp_phone_number addon bill record
            var whatsappPhoneNumberAddonBillRecord = await GetInitialWhatsAppPhoneNumberAddonBillRecord(
                subscriptionPlan, initialBaseBillRecord);
            if (whatsappPhoneNumberAddonBillRecord != null)
            {
                billRecords.Add(whatsappPhoneNumberAddonBillRecord);
            }

            return billRecords;
        }

        private BillRecord GetInitialBaseBillRecord(SubscriptionPlan subscriptionPlan)
        {
            var periodEnd = DateTime.UtcNow.AddMonths(1);
            if (ValidSubscriptionPlan.FreePlans.ContainsIgnoreCase(subscriptionPlan.Id) == false)
            {
                periodEnd = DateTime.UtcNow.AddDays(7);
            }

            var billRecord = new BillRecord
            {
                SubscriptionPlan = subscriptionPlan,
                PayAmount = 0,
                Status = BillStatus.Active,
                PaymentStatus = PaymentStatus.FreeOfCharge,
                PeriodStart = DateTime.UtcNow,
                PeriodEnd = periodEnd
            };

            var usageCycle = _usageCycleCalculator.GetUsageCycle(billRecord.PeriodStart, billRecord.PeriodEnd);
            billRecord.UsageCycleStart = usageCycle.From;
            billRecord.UsageCycleEnd = usageCycle.To;
            return billRecord;
        }

        private async Task<BillRecord> GetInitialWhatsAppPhoneNumberAddonBillRecord(
            SubscriptionPlan subscriptionPlan, BillRecord initialBaseBillRecord)
        {
            try
            {
                // Determine interval from subscription plan
               const string interval = "monthly";

                // Get the whatsapp_phone_number addon plan with same version, country tier, currency, and interval
               var whatsappPhoneNumberAddonPlan = await _appDbContext.CoreSubscriptionPlans
                        .FirstOrDefaultAsync(x =>
                        x.Id.Contains("whatsapp_phone_number") &&
                        !x.Id.Contains("pro") &&
                        !x.Id.Contains("premium") &&
                        !x.Id.Contains("enterprise") &&
                        x.Id.Contains(interval) &&
                        x.SubscriptionTier == SubscriptionTier.AddOn &&
                        x.CountryTier == nameof(SubscriptionCountryTier.Tier1) &&
                        x.Currency == "USD" &&
                        x.Version == subscriptionPlan.Version);

               if (whatsappPhoneNumberAddonPlan == null)
               {
                    _logger.LogWarning(
                        "WhatsApp phone number addon plan not found for subscription plan {SubscriptionPlanId} with interval {Interval}, country tier {CountryTier}, currency {Currency}, version {Version}",
                        subscriptionPlan.Id, interval, subscriptionPlan.CountryTier, subscriptionPlan.Currency, subscriptionPlan.Version);
                    return null;
               }

               var addonBillRecord = new BillRecord
                {
                    SubscriptionPlan = whatsappPhoneNumberAddonPlan,
                    PayAmount = 0,
                    Status = BillStatus.Active,
                    PaymentStatus = PaymentStatus.FreeOfCharge,
                    PeriodStart = initialBaseBillRecord.PeriodStart,
                    PeriodEnd = initialBaseBillRecord.PeriodEnd.AddYears(99),
                    UsageCycleStart = initialBaseBillRecord.UsageCycleStart,
                    UsageCycleEnd = initialBaseBillRecord.UsageCycleEnd?.AddYears(99),
                    SubscriptionTier = whatsappPhoneNumberAddonPlan.SubscriptionTier,
                    quantity = 3
                };

               return addonBillRecord;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating WhatsApp phone number addon bill record for subscription plan {SubscriptionPlanId}", subscriptionPlan.Id);
                return null;
            }
        }

        public async Task<List<RedeemPromotionRecord>> GetRedeemHistory(string companyId)
        {
            var histories = await _appDbContext.CoreRedeemPromotionRecords
                    .Where(
                        x =>
                            x.CompanyId == companyId &&
                            x.Status == TopupStatus.Redeemed)
                    .Include(x => x.Promotion)
                    .ToListAsync();

            return histories;
        }

        public async Task RedeemPromotionCode(ApplyPromotionCodeViewModel applyPromotionCode, string companyId)
        {
            var company = await _appDbContext.CompanyCompanies
                .FirstOrDefaultAsync(x => x.Id == companyId);

            // This company has promotion code
            var promotionCode = await _appDbContext.CorePromotions
                .FirstOrDefaultAsync(
                    x =>
                        x.PromotionCode == applyPromotionCode.PromotionCode &&
                        (x.ExpiryDate.HasValue && DateTime.UtcNow < x.ExpiryDate.Value));

            if (promotionCode == null)
            {
                throw new Exception("Promo code not found expired");
            }

            if (await _appDbContext.CoreRedeemPromotionRecords
                    .AnyAsync(
                        x =>
                            x.PromotionId == promotionCode.Id &&
                            x.CompanyId == companyId &&
                            x.Status == TopupStatus.Redeemed))
            {
                throw new Exception("You have redeemed this promotion");
            }

            var pendingPromotionCode = new RedeemPromotionRecord
            {
                CompanyId = company.Id,
                PromotionId = promotionCode.Id,
            };

            _appDbContext.CoreRedeemPromotionRecords.Add(pendingPromotionCode);
            await _appDbContext.SaveChangesAsync();

            var companyUsage = await _companyUsageService.GetCompanyUsage(companyId);

            if (pendingPromotionCode.Promotion.ExtraAutomatedMessages > 0)
            {
                company.MaximumWhAutomatedMessages = companyUsage.MaximumAutomatedMessages +
                                                     ((companyUsage.billingPeriodUsages.FirstOrDefault().BillRecord
                                                         .SubscriptionPlan.Id.Contains("yearly"))
                                                         ? pendingPromotionCode.Promotion.ExtraAutomatedMessages * 12
                                                         : pendingPromotionCode.Promotion.ExtraAutomatedMessages);
            }

            if (pendingPromotionCode.Promotion.ExtraContacts > 0)
            {
                company.MaximumContacts = companyUsage.MaximumContacts + pendingPromotionCode.Promotion.ExtraContacts;
            }

            if (pendingPromotionCode.Promotion.ExtraAgents > 0)
            {
                company.MaximumAgents = companyUsage.MaximumAgents + pendingPromotionCode.Promotion.ExtraAgents;
            }

            pendingPromotionCode.Status = TopupStatus.Redeemed;
            pendingPromotionCode.RedeemedAt = DateTime.UtcNow;
            await _appDbContext.SaveChangesAsync();
        }

        private Company AddDefaultCompanyList(Company company)
        {
            company.ImportContactHistories = new List<ImportContactHistory>();

            company.ImportContactHistories.Add(
                new ImportContactHistory
                {
                    ImportName = "[Template] Welcome"
                });
            company.ImportContactHistories.Add(
                new ImportContactHistory
                {
                    ImportName = "[Template] Campaign"
                });
            company.ImportContactHistories.Add(
                new ImportContactHistory
                {
                    ImportName = "[Template] Leads"
                });
            company.ImportContactHistories.Add(
                new ImportContactHistory
                {
                    ImportName = "[Template] Customers"
                });
            company.ImportContactHistories.Add(
                new ImportContactHistory
                {
                    ImportName = "[Template] Pending"
                });
            company.ImportContactHistories.Add(
                new ImportContactHistory
                {
                    ImportName = "[Template] VIP"
                });

            return company;
        }

        private async Task AddDefaultAutomationRules(Company company)
        {
            var welcomeListId = await _appDbContext.CompanyImportContactHistories
                .Where(x => x.CompanyId == company.Id && x.ImportName == "[Template] Welcome")
                .Select(x => x.Id)
                .FirstAsync();

            var campaignListId = await _appDbContext.CompanyImportContactHistories
                .Where(x => x.CompanyId == company.Id && x.ImportName == "[Template] Campaign")
                .Select(x => x.Id)
                .FirstAsync();

            var leadListId = await _appDbContext.CompanyImportContactHistories
                .Where(x => x.CompanyId == company.Id && x.ImportName == "[Template] Leads")
                .Select(x => x.Id)
                .FirstAsync();

            var userId = await _appDbContext.UserRoleStaffs
                .Where(x => x.CompanyId == company.Id && x.Id != 1)
                .Select(x => x.Id)
                .FirstAsync();

            var assignmentRuleWelcome = new AssignmentRule()
            {
                AutomationType = AutomationType.MessageReceived,
                Status = AutomationStatus.Draft,
                AssignmentRuleName = "[Demo] Welcome Message",
                IsContinue = false,
                CompanyId = company.Id,
                Conditions = new List<Condition>()
                {
                    new ()
                    {
                        FieldName = "ContactOwner",
                        NextOperator = SupportedNextOperator.And,
                        ConditionOperator = SupportedOperator.IsNull,
                        Values = new List<string>()
                    }
                },
                AutomationActions = new List<AutomationAction>()
                {
                    new ()
                    {
                        AutomatedTriggerType = AutomatedTriggerType.SendMessage,
                        AssignmentType = AssignmentType.Unassigned,
                        MessageContent =
                            "Thank you for your enquiry! Please send us the number to continue.\n[1] Visit our website\n[2] Contact our sales",
                        Order = 0,
                        AddAdditionalAssigneeIds = new (),
                        TargetedChannelWithIds = new (),
                        MessageParams = new (),
                        ActionAddedToGroupIds = new (),
                        ActionRemoveFromGroupIds = new (),
                        ActionUpdateCustomFields = new (),
                        ActionAddConversationHashtags = new (),
                        ActionAddConversationRemarks = new (),
                    },
                    new ()
                    {
                        AutomatedTriggerType = AutomatedTriggerType.AddToList,
                        AssignmentType = AssignmentType.Unassigned,
                        Order = 1,
                        AddAdditionalAssigneeIds = new (),
                        TargetedChannelWithIds = new (),
                        MessageParams = new (),
                        ActionAddedToGroupIds = new ()
                        {
                            welcomeListId
                        },
                        ActionRemoveFromGroupIds = new (),
                        ActionUpdateCustomFields = new (),
                        ActionAddConversationHashtags = new (),
                        ActionAddConversationRemarks = new (),
                    }
                }
            };

            var assignmentRuleWelcomeAction1 = new AssignmentRule()
            {
                AutomationType = AutomationType.MessageReceived,
                Status = AutomationStatus.Draft,
                AssignmentRuleName = "[Demo] Automated Reply (Welcome-1)",
                IsContinue = false,
                CompanyId = company.Id,
                Conditions = new List<Condition>()
                {
                    new ()
                    {
                        FieldName = "Message",
                        NextOperator = SupportedNextOperator.And,
                        ConditionOperator = SupportedOperator.Equals,
                        Values = new List<string>()
                        {
                            "1"
                        }
                    },
                    new ()
                    {
                        FieldName = "importfrom",
                        NextOperator = SupportedNextOperator.And,
                        ConditionOperator = SupportedOperator.ContainsAll,
                        Values = new List<string>()
                        {
                            welcomeListId.ToString()
                        }
                    }
                },
                AutomationActions = new List<AutomationAction>()
                {
                    new ()
                    {
                        AutomatedTriggerType = AutomatedTriggerType.SendMessage,
                        AssignmentType = AssignmentType.Unassigned,
                        MessageContent =
                            "Thanks for choosing option 1. You may visit our website via this link: https://sleekflow.io !",
                        Order = 0,
                        AddAdditionalAssigneeIds = new (),
                        TargetedChannelWithIds = new (),
                        MessageParams = new (),
                        ActionAddedToGroupIds = new (),
                        ActionRemoveFromGroupIds = new (),
                        ActionUpdateCustomFields = new (),
                        ActionAddConversationHashtags = new (),
                        ActionAddConversationRemarks = new (),
                    },
                    new ()
                    {
                        AutomatedTriggerType = AutomatedTriggerType.RemoveFromList,
                        AssignmentType = AssignmentType.Unassigned,
                        Order = 1,
                        AddAdditionalAssigneeIds = new (),
                        TargetedChannelWithIds = new (),
                        MessageParams = new (),
                        ActionAddedToGroupIds = new (),
                        ActionRemoveFromGroupIds = new ()
                        {
                            welcomeListId
                        },
                        ActionUpdateCustomFields = new (),
                        ActionAddConversationHashtags = new (),
                        ActionAddConversationRemarks = new (),
                    }
                }
            };

            var assignmentRuleWelcomeAction2 = new AssignmentRule()
            {
                AutomationType = AutomationType.MessageReceived,
                Status = AutomationStatus.Draft,
                AssignmentRuleName = "[Demo] Automated Reply (Welcome-2)",
                IsContinue = false,
                CompanyId = company.Id,
                Conditions = new List<Condition>()
                {
                    new ()
                    {
                        FieldName = "Message",
                        NextOperator = SupportedNextOperator.And,
                        ConditionOperator = SupportedOperator.Equals,
                        Values = new List<string>()
                        {
                            "2"
                        }
                    },
                    new ()
                    {
                        FieldName = "importfrom",
                        NextOperator = SupportedNextOperator.And,
                        ConditionOperator = SupportedOperator.ContainsAll,
                        Values = new List<string>()
                        {
                            campaignListId.ToString()
                        }
                    }
                },
                AutomationActions = new List<AutomationAction>()
                {
                    new ()
                    {
                        AutomatedTriggerType = AutomatedTriggerType.SendMessage,
                        AssignmentType = AssignmentType.Unassigned,
                        MessageContent = "Thanks for choosing option 2. Our sales will reach out to you shortly!",
                        Order = 0,
                        AddAdditionalAssigneeIds = new (),
                        TargetedChannelWithIds = new (),
                        MessageParams = new (),
                        ActionAddedToGroupIds = new (),
                        ActionRemoveFromGroupIds = new (),
                        ActionUpdateCustomFields = new (),
                        ActionAddConversationHashtags = new (),
                        ActionAddConversationRemarks = new (),
                    },
                    new ()
                    {
                        AutomatedTriggerType = AutomatedTriggerType.Assignment,
                        AssignmentType = AssignmentType.SpecificPerson,
                        AssignedStaffId = userId,
                        Order = 1,
                        AddAdditionalAssigneeIds = new (),
                        TargetedChannelWithIds = new (),
                        MessageParams = new (),
                        ActionAddedToGroupIds = new (),
                        ActionRemoveFromGroupIds = new (),
                        ActionUpdateCustomFields = new (),
                        ActionAddConversationHashtags = new (),
                        ActionAddConversationRemarks = new (),
                    },
                    new ()
                    {
                        AutomatedTriggerType = AutomatedTriggerType.RemoveFromList,
                        AssignmentType = AssignmentType.Unassigned,
                        Order = 2,
                        AddAdditionalAssigneeIds = new (),
                        TargetedChannelWithIds = new (),
                        MessageParams = new (),
                        ActionAddedToGroupIds = new (),
                        ActionRemoveFromGroupIds = new ()
                        {
                            welcomeListId
                        },
                        ActionUpdateCustomFields = new (),
                        ActionAddConversationHashtags = new (),
                        ActionAddConversationRemarks = new (),
                    }
                }
            };

            var assignmentRuleCampaign = new AssignmentRule()
            {
                AutomationType = AutomationType.MessageReceived,
                Status = AutomationStatus.Draft,
                AssignmentRuleName = "[Demo] Campaign",
                IsContinue = false,
                CompanyId = company.Id,
                Conditions = new List<Condition>()
                {
                    new ()
                    {
                        FieldName = "Message",
                        NextOperator = SupportedNextOperator.And,
                        ConditionOperator = SupportedOperator.Contains,
                        Values = new List<string>()
                        {
                            "Know more"
                        }
                    },
                    new ()
                    {
                        FieldName = "importfrom",
                        NextOperator = SupportedNextOperator.And,
                        ConditionOperator = SupportedOperator.ContainsAll,
                        Values = new List<string>()
                        {
                            campaignListId.ToString()
                        }
                    }
                },
                AutomationActions = new List<AutomationAction>()
                {
                    new ()
                    {
                        AutomatedTriggerType = AutomatedTriggerType.SendMessage,
                        AssignmentType = AssignmentType.Unassigned,
                        MessageContent =
                            "Hi there! \nWant to know more about our products? You can visit us via this link: https://sleekflow.io",
                        Order = 0,
                        AddAdditionalAssigneeIds = new (),
                        TargetedChannelWithIds = new (),
                        MessageParams = new (),
                        ActionAddedToGroupIds = new (),
                        ActionRemoveFromGroupIds = new (),
                        ActionUpdateCustomFields = new (),
                        ActionAddConversationHashtags = new (),
                        ActionAddConversationRemarks = new (),
                    },
                    new ()
                    {
                        AutomatedTriggerType = AutomatedTriggerType.RemoveFromList,
                        AssignmentType = AssignmentType.Unassigned,
                        Order = 1,
                        AddAdditionalAssigneeIds = new (),
                        TargetedChannelWithIds = new (),
                        MessageParams = new (),
                        ActionAddedToGroupIds = new (),
                        ActionRemoveFromGroupIds = new ()
                        {
                            campaignListId
                        },
                        ActionUpdateCustomFields = new (),
                        ActionAddConversationHashtags = new (),
                        ActionAddConversationRemarks = new (),
                    },
                    new ()
                    {
                        AutomatedTriggerType = AutomatedTriggerType.AddToList,
                        AssignmentType = AssignmentType.Unassigned,
                        Order = 2,
                        AddAdditionalAssigneeIds = new (),
                        TargetedChannelWithIds = new (),
                        MessageParams = new (),
                        ActionAddedToGroupIds = new ()
                        {
                            leadListId
                        },
                        ActionRemoveFromGroupIds = new (),
                        ActionUpdateCustomFields = new (),
                        ActionAddConversationHashtags = new (),
                        ActionAddConversationRemarks = new (),
                    }
                }
            };

            _appDbContext.CompanyAssignmentRules.Add(assignmentRuleWelcome);
            _appDbContext.CompanyAssignmentRules.Add(assignmentRuleWelcomeAction1);
            _appDbContext.CompanyAssignmentRules.Add(assignmentRuleWelcomeAction2);
            _appDbContext.CompanyAssignmentRules.Add(assignmentRuleCampaign);

            await _appDbContext.SaveChangesAsync();
        }

        private Company AddDefaultLabel(Company company)
        {
            company.CompanyHashtags = new List<CompanyHashtag>();

            company.CompanyHashtags.Add(
                new CompanyHashtag
                {
                    Hashtag = "New customer",
                    HashTagColor = HashTagColor.Cyan
                });
            company.CompanyHashtags.Add(
                new CompanyHashtag
                {
                    Hashtag = "New order",
                    HashTagColor = HashTagColor.Yellow
                });
            company.CompanyHashtags.Add(
                new CompanyHashtag
                {
                    Hashtag = "Pending payment",
                    HashTagColor = HashTagColor.Pink
                });
            company.CompanyHashtags.Add(
                new CompanyHashtag
                {
                    Hashtag = "Paid",
                    HashTagColor = HashTagColor.Purple
                });
            company.CompanyHashtags.Add(
                new CompanyHashtag
                {
                    Hashtag = "Order complete",
                    HashTagColor = HashTagColor.Green
                });
            company.CompanyHashtags.Add(
                new CompanyHashtag
                {
                    Hashtag = "VIP",
                    HashTagColor = HashTagColor.Blue
                });
            company.CompanyHashtags.Add(
                new CompanyHashtag
                {
                    Hashtag = "Issue",
                    HashTagColor = HashTagColor.Red
                });

            return company;
        }

        private Company AddDefaultUserFiels(Company company)
        {
            List<CompanyCustomUserProfileField> companyCustomUserProfiles = new List<CompanyCustomUserProfileField>()
            {
                new CompanyCustomUserProfileField
                {
                    FieldName = "Labels",
                    Type = FieldDataType.Labels,
                    Order = 0,
                    CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                    {
                        new CustomUserProfileFieldLingual
                        {
                            DisplayName = "Labels", Language = "en"
                        },
                        new CustomUserProfileFieldLingual
                        {
                            DisplayName = "標籤", Language = "zh-hk"
                        }
                    },
                    IsDeletable = false,
                    IsEditable = false,
                    IsDefault = true,
                    FieldsCategory = FieldsCategory.Segmentation
                },
                new CompanyCustomUserProfileField
                {
                    FieldName = "Lists",
                    Type = FieldDataType.Lists,
                    Order = 1,
                    CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                    {
                        new CustomUserProfileFieldLingual
                        {
                            DisplayName = "Lists", Language = "en"
                        },
                        new CustomUserProfileFieldLingual
                        {
                            DisplayName = "名單", Language = "zh-hk"
                        }
                    },
                    IsDeletable = false,
                    IsEditable = false,
                    IsDefault = true,
                    FieldsCategory = FieldsCategory.Segmentation
                },
                new CompanyCustomUserProfileField
                {
                    FieldName = "Email",
                    Type = FieldDataType.Email,
                    Order = 2,
                    CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                    {
                        new CustomUserProfileFieldLingual
                        {
                            DisplayName = "Email", Language = "en"
                        },
                        new CustomUserProfileFieldLingual
                        {
                            DisplayName = "電郵地址", Language = "zh-hk"
                        }
                    },
                    IsDeletable = false
                },
                new CompanyCustomUserProfileField
                {
                    FieldName = "PhoneNumber",
                    Type = FieldDataType.PhoneNumber,
                    Order = 3,
                    CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                    {
                        new CustomUserProfileFieldLingual
                        {
                            DisplayName = "Phone Number", Language = "en"
                        },
                        new CustomUserProfileFieldLingual
                        {
                            DisplayName = "電話號碼", Language = "zh-hk"
                        }
                    },
                    IsDeletable = false
                },
                new CompanyCustomUserProfileField
                {
                    FieldName = "CompanyName",
                    Type = FieldDataType.SingleLineText,
                    Order = 4,
                    CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                    {
                        new CustomUserProfileFieldLingual
                        {
                            DisplayName = "Company Name", Language = "en"
                        },
                        new CustomUserProfileFieldLingual
                        {
                            DisplayName = "公司名稱", Language = "zh-hk"
                        }
                    }
                },
                new CompanyCustomUserProfileField
                {
                    FieldName = "JobTitle",
                    Type = FieldDataType.SingleLineText,
                    Order = 5,
                    CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                    {
                        new CustomUserProfileFieldLingual
                        {
                            DisplayName = "Job Title", Language = "en"
                        },
                        new CustomUserProfileFieldLingual
                        {
                            DisplayName = "職稱", Language = "zh-hk"
                        }
                    }
                },
                new CompanyCustomUserProfileField
                {
                    FieldName = "ContactOwner",
                    Type = FieldDataType.TravisUser,
                    Order = 6,
                    CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                    {
                        new CustomUserProfileFieldLingual
                        {
                            DisplayName = "Contact Owner", Language = "en"
                        },
                        new CustomUserProfileFieldLingual
                        {
                            DisplayName = "聯絡負責人", Language = "zh-hk"
                        }
                    },
                    IsDeletable = false,
                    IsEditable = true,
                    IsDefault = true,
                    FieldsCategory = FieldsCategory.SleekFlowUser
                },
                new CompanyCustomUserProfileField
                {
                    FieldName = "LeadStage",
                    Type = FieldDataType.Options,
                    Order = 7,
                    CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                    {
                        new CustomUserProfileFieldLingual
                        {
                            DisplayName = "Lead Stage", Language = "en"
                        },
                        new CustomUserProfileFieldLingual
                        {
                            DisplayName = "階段", Language = "zh-hk"
                        }
                    },
                    CustomUserProfileFieldOptions = new List<CustomUserProfileFieldOption>()
                    {
                        new CustomUserProfileFieldOption
                        {
                            Value = "Prospect",
                            CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                            {
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "Prospect", Language = "en"
                                },
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "Prospect", Language = "zh-HK"
                                }
                            },
                            Order = 0
                        },
                        new CustomUserProfileFieldOption
                        {
                            Value = "Contacted",
                            CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                            {
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "Contacted", Language = "en"
                                },
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "Contacted", Language = "zh-HK"
                                }
                            },
                            Order = 1
                        },
                        new CustomUserProfileFieldOption
                        {
                            Value = "Lead",
                            CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                            {
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "Lead", Language = "en"
                                },
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "Lead", Language = "zh-HK"
                                }
                            },
                            Order = 2
                        },
                        new CustomUserProfileFieldOption
                        {
                            Value = "Opportunity",
                            CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                            {
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "Opportunity", Language = "en"
                                },
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "Opportunity", Language = "zh-HK"
                                }
                            },
                            Order = 3
                        },
                        new CustomUserProfileFieldOption
                        {
                            Value = "Customer",
                            CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                            {
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "Customer", Language = "en"
                                },
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "Customer", Language = "zh-HK"
                                }
                            },
                            Order = 4
                        },
                        new CustomUserProfileFieldOption
                        {
                            Value = "Lost",
                            CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                            {
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "Lost", Language = "en"
                                },
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "Lost", Language = "zh-HK"
                                }
                            },
                            Order = 5
                        },
                        new CustomUserProfileFieldOption
                        {
                            Value = "Inactive",
                            CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                            {
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "Inactive / to be reviewed", Language = "en"
                                },
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "Inactive / to be reviewed", Language = "zh-HK"
                                }
                            },
                            Order = 6
                        }
                    },
                    IsDeletable = false,
                    IsEditable = true,
                    IsDefault = true
                },
                new CompanyCustomUserProfileField
                {
                    FieldName = "LeadSource",
                    Type = FieldDataType.Options,
                    Order = 8,
                    CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                    {
                        new CustomUserProfileFieldLingual
                        {
                            DisplayName = "Lead Source", Language = "en"
                        },
                        new CustomUserProfileFieldLingual
                        {
                            DisplayName = "資料來源", Language = "zh-hk"
                        }
                    },
                    CustomUserProfileFieldOptions = new List<CustomUserProfileFieldOption>()
                    {
                        new CustomUserProfileFieldOption
                        {
                            Value = "Organic search",
                            CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                            {
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "Organic search", Language = "en"
                                },
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "網站", Language = "zh-HK"
                                }
                            },
                            Order = 0
                        },
                        new CustomUserProfileFieldOption
                        {
                            Value = "Referrals",
                            CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                            {
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "Referrals", Language = "en"
                                },
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "推薦", Language = "zh-HK"
                                }
                            },
                            Order = 1
                        },
                        new CustomUserProfileFieldOption
                        {
                            Value = "Social media",
                            CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                            {
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "Social media", Language = "en"
                                },
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "社交媒體", Language = "zh-HK"
                                }
                            },
                            Order = 2
                        },
                        new CustomUserProfileFieldOption
                        {
                            Value = "Email marketing",
                            CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                            {
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "Email marketing", Language = "en"
                                },
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "社交媒體", Language = "zh-HK"
                                }
                            },
                            Order = 3
                        },
                        new CustomUserProfileFieldOption
                        {
                            Value = "Paid search",
                            CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                            {
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "Paid search", Language = "en"
                                },
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "付費搜索", Language = "zh-HK"
                                }
                            },
                            Order = 4
                        },
                        new CustomUserProfileFieldOption
                        {
                            Value = "Paid social",
                            CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                            {
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "Paid social", Language = "en"
                                },
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "付費社交媒體", Language = "zh-HK"
                                }
                            },
                            Order = 5
                        },
                        new CustomUserProfileFieldOption
                        {
                            Value = "Direct traffic",
                            CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                            {
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "Direct traffic", Language = "en"
                                },
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "直接流量", Language = "zh-HK"
                                }
                            },
                            Order = 6
                        },
                        new CustomUserProfileFieldOption
                        {
                            Value = "Events and seminar",
                            CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                            {
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "Events and seminar", Language = "en"
                                },
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "研討會", Language = "zh-HK"
                                }
                            },
                            Order = 7
                        },
                        new CustomUserProfileFieldOption
                        {
                            Value = "Other offline sources",
                            CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                            {
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "Other offline sources", Language = "en"
                                },
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "其他活動", Language = "zh-HK"
                                }
                            },
                            Order = 8
                        },
                        new CustomUserProfileFieldOption
                        {
                            Value = "Others",
                            CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                            {
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "Others", Language = "en"
                                },
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "其他", Language = "zh-HK"
                                }
                            },
                            Order = 9
                        }
                    },
                    IsDeletable = false,
                    IsEditable = true,
                    IsDefault = true
                },
                new CompanyCustomUserProfileField
                {
                    FieldName = "Priority",
                    Type = FieldDataType.Options,
                    Order = 9,
                    CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                    {
                        new CustomUserProfileFieldLingual
                        {
                            DisplayName = "Priority", Language = "en"
                        },
                        new CustomUserProfileFieldLingual
                        {
                            DisplayName = "優先度", Language = "zh-hk"
                        }
                    },
                    CustomUserProfileFieldOptions = new List<CustomUserProfileFieldOption>()
                    {
                        new CustomUserProfileFieldOption
                        {
                            Value = "High",
                            CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                            {
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "High", Language = "en"
                                },
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "高", Language = "zh-HK"
                                }
                            },
                            Order = 0
                        },
                        new CustomUserProfileFieldOption
                        {
                            Value = "Medium",
                            CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                            {
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "Medium", Language = "en"
                                },
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "中", Language = "zh-HK"
                                }
                            },
                            Order = 1
                        },
                        new CustomUserProfileFieldOption
                        {
                            Value = "Low",
                            CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                            {
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "Low", Language = "en"
                                },
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "低", Language = "zh-HK"
                                }
                            },
                            Order = 2
                        }
                    }
                },
                new CompanyCustomUserProfileField
                {
                    FieldName = "Country",
                    Type = FieldDataType.Options,
                    Order = 10,
                    CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                    {
                        new CustomUserProfileFieldLingual
                        {
                            DisplayName = "Country", Language = "en"
                        },
                        new CustomUserProfileFieldLingual
                        {
                            DisplayName = "國家", Language = "zh-hk"
                        },
                    },
                    CustomUserProfileFieldOptions = GetCountryList().Result,
                    IsDefault = true,
                    IsDeletable = false,
                    IsEditable = false
                },
                new CompanyCustomUserProfileField
                {
                    FieldName = "Subscriber",
                    Type = FieldDataType.Boolean,
                    Order = 11,
                    CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                    {
                        new CustomUserProfileFieldLingual
                        {
                            DisplayName = "Subscriber", Language = "en"
                        },
                        new CustomUserProfileFieldLingual
                        {
                            DisplayName = "訂閱者", Language = "zh-hk"
                        }
                    },
                    IsDeletable = false,
                    IsEditable = true,
                    IsDefault = true
                },
                new CompanyCustomUserProfileField
                {
                    FieldName = "LastChannel",
                    Type = FieldDataType.Channel,
                    Order = 12,
                    CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                    {
                        new CustomUserProfileFieldLingual
                        {
                            DisplayName = "Last Channel", Language = "en"
                        },
                        new CustomUserProfileFieldLingual
                        {
                            DisplayName = "最後聯繫頻道", Language = "zh-hk"
                        }
                    },
                    IsEditable = false,
                    IsDeletable = false,
                    FieldsCategory = FieldsCategory.Message
                },
                new CompanyCustomUserProfileField
                {
                    FieldName = "LastContact",
                    Type = FieldDataType.DateTime,
                    Order = 13,
                    CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                    {
                        new CustomUserProfileFieldLingual
                        {
                            DisplayName = "Last Contact From You", Language = "en"
                        },
                        new CustomUserProfileFieldLingual
                        {
                            DisplayName = "最後聯絡時間", Language = "zh-hk"
                        }
                    },
                    IsEditable = false,
                    IsDeletable = false,
                    FieldsCategory = FieldsCategory.Message
                },
                new CompanyCustomUserProfileField
                {
                    FieldName = "LastContactFromCustomers",
                    Type = FieldDataType.DateTime,
                    Order = 14,
                    CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                    {
                        new CustomUserProfileFieldLingual
                        {
                            DisplayName = "Last Contact From Customers", Language = "en"
                        },
                        new CustomUserProfileFieldLingual
                        {
                            DisplayName = "客戶的最後聯絡時間", Language = "zh-hk"
                        }
                    },
                    IsEditable = false,
                    IsDeletable = false,
                    FieldsCategory = FieldsCategory.Message
                },
                // new CompanyCustomUserProfileField { FieldName = "Collaborators", Type = FieldDataType.Collaborators, Order = 15, CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>() {
                //         new CustomUserProfileFieldLingual { DisplayName = "Collaborators", Language = "en" },
                //         new CustomUserProfileFieldLingual { DisplayName = "協作人", Language = "zh-hk" }
                //     }
                // },
            };

            company.CustomUserProfileFields = new List<CompanyCustomUserProfileField>();
            foreach (var customFeild in companyCustomUserProfiles)
            {
                company.CustomUserProfileFields.Add(customFeild);
            }

            return company;
        }

        // public async Task AddCountry(string companyId)
        // {
        //    var userProfileField = await _appDbContext.CompanyCustomUserProfileFields.Where(x => x.CompanyId == companyId && x.FieldName == "Country").Include(x => x.CustomUserProfileFieldOptions).FirstOrDefaultAsync();
        //    userProfileField.CustomUserProfileFieldOptions = GetCountryList().Result;
        //    await _appDbContext.SaveChangesAsync();
        // }

        private Company AddDefaultQuickReply(Company company)
        {
            company.QuickReplies = new List<CompanyQuickReply>()
            {
                new CompanyQuickReply
                {
                    Value = "Welcome",
                    CompanyQuickReplyLinguals = new List<CompanyQuickReplyLingual>
                    {
                        new CompanyQuickReplyLingual
                        {
                            Language = "en", Value = "Hello. Thank you for contacting. How may we help you today?"
                        }
                    }
                },
                new CompanyQuickReply
                {
                    Value = "Thanks",
                    CompanyQuickReplyLinguals = new List<CompanyQuickReplyLingual>
                    {
                        new CompanyQuickReplyLingual
                        {
                            Language = "en",
                            Value = "Thank you for your business! We look forward to working with you again."
                        }
                    }
                },
                new CompanyQuickReply
                {
                    Value = "Away",
                    CompanyQuickReplyLinguals = new List<CompanyQuickReplyLingual>
                    {
                        new CompanyQuickReplyLingual
                        {
                            Language = "en",
                            Value =
                                "Hello. Thank you for your message. We're not here right now, but will respond as soon as we return."
                        }
                    }
                },
                new CompanyQuickReply
                {
                    Value = "Close",
                    CompanyQuickReplyLinguals = new List<CompanyQuickReplyLingual>
                    {
                        new CompanyQuickReplyLingual
                        {
                            Language = "en",
                            Value = "Let me know if you have any further questions. Our team is always here for you."
                        }
                    }
                }
            };

            return company;
        }

        public async Task<List<CustomUserProfileFieldOption>> GetCountryList()
        {
            var result = await _appDbContext.CoreCountries.ToListAsync();

            if (result.Count > 0)
            {
                var response = _mapper.Map<List<CustomUserProfileFieldOption>>(result);
                return response;
            }
            else
            {
                List<Country> cultureList = new List<Country>();

                CultureInfo[] getCultureInfo = CultureInfo.GetCultures(CultureTypes.SpecificCultures);

                foreach (CultureInfo getCulture in getCultureInfo)
                {
                    try
                    {
                        RegionInfo getRegionInfo = new RegionInfo(getCulture.LCID);
                        if (!cultureList
                                .Select(x => x.EnglishName)
                                .Contains(getRegionInfo.EnglishName))
                        {
                            cultureList.Add(
                                new Country
                                {
                                    Id = getRegionInfo.TwoLetterISORegionName,
                                    EnglishName = getRegionInfo.EnglishName,
                                    DisplayName = getRegionInfo.DisplayName
                                });
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogInformation(ex.Message);
                    }
                }

                await _appDbContext.CoreCountries.AddRangeAsync(cultureList);
                await _appDbContext.SaveChangesAsync();

                var response = _mapper.Map<List<CustomUserProfileFieldOption>>(cultureList);
                return response;
            }
        }

        public async Task RemoveDuplicatedHashtag()
        {
            return;
            do
            {
                var duplicateConversations = await _appDbContext.ConversationHashtags.GroupBy(
                        x => new
                        {
                            x.ConversationId, x.HashtagId
                        })
                    .Where(g => g.Count() > 1)
                    .Select(y => y.Key)
                    .Take(100)
                    .ToListAsync();

                foreach (var duplicateConversation in duplicateConversations)
                {
                    var conversationHashtags = await _appDbContext.ConversationHashtags.Where(
                        x => x.ConversationId == duplicateConversation.ConversationId &&
                             x.HashtagId == duplicateConversation.HashtagId).ToListAsync();
                    if (conversationHashtags.Count > 0)
                    {
                        _appDbContext.ConversationHashtags.RemoveRange(conversationHashtags.LastOrDefault());
                    }
                }

                await _appDbContext.SaveChangesAsync();
            }
            while (await _appDbContext.ConversationHashtags.GroupBy(
                       x => new
                       {
                           x.ConversationId, x.HashtagId
                       }).AnyAsync(g => g.Count() > 1));
        }

        public async Task<Staff> UpdateStaffIsNewlyRegistered(ApplicationUser user, bool isNewlyRegistered)
        {
            var staff = await _appDbContext.UserRoleStaffs
                .FirstOrDefaultAsync(x => x.IdentityId == user.Id);

            staff.IsNewlyRegistered = isNewlyRegistered;
            await _appDbContext.SaveChangesAsync();

            return staff;
        }

        public async Task<List<UserProfile>> ImportStaffsIntoUserProfiles(string sourceCompanyId, string toCompanyId)
        {
            var staffsToImport = await _internalCompanyDataRepository.GetCmsCompanyStaffList(
                new List<string>
                {
                    sourceCompanyId
                });

            var userProfiles = new List<UserProfile>();

            // Avoid duplicate email and phone number in the same company that repeat import & update internal user profile
            var addedPhoneNumbers = new List<string>();
            var addedEmails = new List<string>();

            foreach (var staffToImport in staffsToImport)
            {
                var newProfileViewModel = new NewProfileViewModel
                {
                    FirstName = staffToImport.FirstName,
                    LastName = staffToImport.LastName,
                    Email = staffToImport.Email?.Trim(),
                    PhoneNumber = staffToImport.PhoneNumber?.Trim(),
                    UserProfileFields = new List<AddCustomFieldsViewModel>
                    {
                        new AddCustomFieldsViewModel
                        {
                            CustomFieldName = "CompanyName",
                            CustomValue = staffToImport.CompanyName
                        },
                        new AddCustomFieldsViewModel
                        {
                            CustomFieldName = "Subscription Plan",
                            CustomValue = staffToImport.SubscriptionPlanId
                        }
                    }
                };

                if (string.IsNullOrEmpty(newProfileViewModel.Email) &&
                    string.IsNullOrEmpty(newProfileViewModel.WhatsAppPhoneNumber))
                {
                    continue;
                }

                // check if the email or phone number is already added
                if (!string.IsNullOrEmpty(newProfileViewModel.Email) && addedEmails.Contains(newProfileViewModel.Email))
                {
                   continue;
                }

                if (!string.IsNullOrEmpty(newProfileViewModel.WhatsAppPhoneNumber) && addedPhoneNumbers.Contains(newProfileViewModel.WhatsAppPhoneNumber))
                {
                    continue;
                }

                try
                {
                    userProfiles.AddRange(
                        await _userProfileService.AddOrUpdateUserProfile(
                            toCompanyId,
                            newProfileViewModel,
                            userProfileSource: UserProfileSource.InternalStaffImport));
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "Error during ImportStaffsIntoUserProfiles, from company: {SourceCompanyId}, to company: {DestinationCompanyId}," +
                        " Email:{Email}, PhoneNumber:{WhatsAppPhoneNumber}",
                        sourceCompanyId,
                        toCompanyId,
                        newProfileViewModel.Email,
                        newProfileViewModel.WhatsAppPhoneNumber);
                }

                if (!string.IsNullOrEmpty(newProfileViewModel.Email))
                {
                    addedEmails.Add(newProfileViewModel.Email);
                }

                if (!string.IsNullOrEmpty(newProfileViewModel.WhatsAppPhoneNumber))
                {
                    addedPhoneNumbers.Add(newProfileViewModel.WhatsAppPhoneNumber);
                }
            }

            return userProfiles;
        }

        [AutomaticRetry(Attempts = 2, OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [HangfireJobExpirationTimeout(timeoutInMinutes: 1)]
        public async Task<List<UserProfile>> SyncCompanyStaffsToSleekFlowContacts(string companyId)
        {
            if (companyId == null)
            {
                return null;
            }

            var sleekflowCompanyId = _configuration.GetValue<String>("Values:SleekFlowCompanyId");

            var userProfiles = await ImportStaffsIntoUserProfiles(companyId, sleekflowCompanyId);

            return userProfiles;
        }

        [AutomaticRetry(Attempts = 2, OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [HangfireJobExpirationTimeout(timeoutInMinutes: 60)]
        public async Task SyncCompanyStaffsToSleekFlowContactsBackground(string companyId)
        {
            await SyncCompanyStaffsToSleekFlowContacts(companyId);
        }

        // public async Task RemoveUpdatedNotifications()
        // {
        //    do
        //    {
        //        var notifcations = await _appDbContext.CompanyNotificationRecords.Where(x => DateTime.UtcNow.AddDays(-14) > x.CreatedAt).Take(100).ToListAsync();
        //        _appDbContext.CompanyNotificationRecords.RemoveRange(notifcations);
        //        await _appDbContext.SaveChangesAsync();
        //    } while (_appDbContext.CompanyNotificationRecords.Where(x => DateTime.UtcNow.AddDays(-14) > x.CreatedAt).Count() > 0);
        // }

        public async Task AddToSleekFlowCRMViaApi(
            string userCompanyId,
            ApplicationUser userIdentity,
            string role,
            string heardFrom,
            List<ImportHeader> headers,
            List<string> fields,
            RegisterCompanyViewModel registerCompanyViewModel = null)
        {
            try
            {
                var companyId = _configuration.GetValue<string>("Values:SleekFlowCompanyId");
                var apiKey = _configuration.GetValue<string>("Values:SleekFlowPublicApiKey", "t0MPHYgy390Ou32mOlab2wQfdQm8IVLXKOSQkoROLPg");
                var apiUrl = _configuration.GetValue<string>("Values:SleekFlowPublicApiUrl", "https://api.sleekflow.io");

                var company = await _appDbContext.CompanyCompanies
                    .FirstOrDefaultAsync(x => x.Id == userCompanyId);

                // Prepare the userProfileFields collection to send to the API
                var userProfileFields = new List<object>();

                // Add email field
                userProfileFields.Add(new
                {
                    customFieldName = "Email",
                    customValue = userIdentity.Email
                });

                // Add phone number field if available
                if (!string.IsNullOrEmpty(userIdentity.PhoneNumber))
                {
                    userProfileFields.Add(new
                    {
                        customFieldName = "PhoneNumber",
                        customValue = userIdentity.PhoneNumber
                    });
                }

                // Add company name if available
                if (company != null)
                {
                    userProfileFields.Add(new
                    {
                        customFieldName = "CompanyName",
                        customValue = company.CompanyName
                    });
                }

                // Add Subscriber field
                userProfileFields.Add(new
                {
                    customFieldName = "Subscriber",
                    customValue = "true"
                });

                // Add company size if available from registerCompanyViewModel
                if (registerCompanyViewModel != null && !string.IsNullOrEmpty(registerCompanyViewModel.CompanySize))
                {
                    userProfileFields.Add(new
                    {
                        customFieldName = "Company Size",
                        customValue = registerCompanyViewModel.CompanySize
                    });
                }

                // Add role field if provided
                if (!string.IsNullOrEmpty(role))
                {
                    userProfileFields.Add(new
                    {
                        customFieldName = "Role",
                        customValue = role
                    });

                    // Set LeadStage based on role
                    if (role == "User")
                    {
                        userProfileFields.Add(new
                        {
                            customFieldName = "LeadStage",
                            customValue = "Invited User"
                        });
                    }
                    else if (company != null && await HasPaidSubscription(userCompanyId))
                    {
                        userProfileFields.Add(new
                        {
                            customFieldName = "LeadStage",
                            customValue = "Customers"
                        });
                    }
                    else
                    {
                        userProfileFields.Add(new
                        {
                            customFieldName = "LeadStage",
                            customValue = "Sandbox"
                        });
                    }
                }

                // Add subscription plan info if available
                if (company != null)
                {
                    var lastBill = await _appDbContext.CompanyBillRecords
                        .OrderByDescending(x => x.PeriodStart)
                        .FirstOrDefaultAsync(
                            x => x.CompanyId == userCompanyId &&
                                ValidSubscriptionPlan.PaidPlan.Contains(x.SubscriptionPlanId) &&
                                x.PayAmount > 0);

                    if (lastBill != null && lastBill.PaymentStatus == PaymentStatus.Paid &&
                        !string.IsNullOrEmpty(lastBill.stripe_subscriptionId) && lastBill.PayAmount > 0)
                    {
                        // Add Plan information based on subscription
                        switch (lastBill.SubscriptionPlanId)
                        {
                            case "sleekflow_v2_premium":
                                userProfileFields.Add(new
                                {
                                    customFieldName = "Plan",
                                    customValue = "Premium"
                                });
                                break;
                            case "sleekflow_v2_pro":
                                userProfileFields.Add(new
                                {
                                    customFieldName = "Plan",
                                    customValue = "Pro"
                                });
                                break;
                            case "sleekflow_v2_standard":
                                userProfileFields.Add(new
                                {
                                    customFieldName = "Plan",
                                    customValue = "Standard"
                                });
                                break;
                        }
                    }
                    else
                    {
                        // Free trial information
                        var lastBillFreeTrial = await _appDbContext.CompanyBillRecords
                            .OrderByDescending(x => x.PeriodStart)
                            .FirstOrDefaultAsync(
                                x => x.CompanyId == userCompanyId &&
                                    ValidSubscriptionPlan.AllPaidPlans.Contains(x.SubscriptionPlanId));

                        if (lastBillFreeTrial != null)
                        {
                            userProfileFields.Add(new
                            {
                                customFieldName = "LeadStage",
                                customValue = "Free Trial"
                            });

                            userProfileFields.Add(new
                            {
                                customFieldName = "Trial Started",
                                customValue = lastBillFreeTrial.PeriodStart.ToString("o")
                            });

                            userProfileFields.Add(new
                            {
                                customFieldName = "Trial Ended",
                                customValue = lastBillFreeTrial.PeriodEnd.ToString("o")
                            });
                        }
                    }
                }

                // Add lead source if provided
                if (!string.IsNullOrEmpty(heardFrom))
                {
                    if (await _appDbContext.CompanyCustomUserProfileFields
                        .AnyAsync(
                            x => x.CompanyId == companyId &&
                                x.CustomUserProfileFieldOptions
                                    .Any(option => option.Value.ToLower() == heardFrom.ToLower())))
                    {
                        userProfileFields.Add(new
                        {
                            customFieldName = "LeadSource",
                            customValue = heardFrom
                        });
                    }
                }

                // Add referral code if available
                if (!string.IsNullOrEmpty(company?.ReferralCode))
                {
                    userProfileFields.Add(new
                    {
                        customFieldName = "Referral Code",
                        customValue = company.ReferralCode
                    });
                }

                // Process any additional custom fields from headers and fields
                if (headers?.Count > 0 && fields?.Count > 0)
                {
                    for (int i = 0; i < Math.Min(headers.Count, fields.Count); i++)
                    {
                        userProfileFields.Add(new
                        {
                            customFieldName = headers[i].HeaderName,
                            customValue = fields[i]
                        });
                    }
                }

                // Create the contact request payload
                var contactPayload = new[]
                {
                    new
                    {
                        firstName = userIdentity.FirstName,
                        lastName = userIdentity.LastName,
                        phoneNumber = userIdentity.PhoneNumber,
                        email = userIdentity.Email,
                        userProfileFields = userProfileFields
                    }
                };

                // Convert the payload to JSON
                var jsonContent = JsonSerializer.Serialize(contactPayload);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                // Create HTTP client and make the request
                using var httpClient = _httpClientFactory.CreateClient();
                httpClient.DefaultRequestHeaders.Add("X-Sleekflow-Api-Key", apiKey);
                httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                var response = await httpClient.PostAsync(apiUrl + "/api/contact/addOrUpdate", content);

                // Handle the response
                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError(
                        "[CoreService {MethodName}] Error calling SleekFlow API for company {CompanyId}. Status: {StatusCode}. Response: {Response}",
                        nameof(AddToSleekFlowCRMViaApi),
                        userCompanyId,
                        response.StatusCode,
                        errorContent);

                    // Consider retrying or other fallback mechanism here
                }
                else
                {
                    _logger.LogInformation(
                        "[CoreService {MethodName}] Successfully added/updated contact in SleekFlow for company {CompanyId}",
                        nameof(AddToSleekFlowCRMViaApi),
                        userCompanyId);

                    // If we need to handle the response, we can parse it here
                    // var responseContent = await response.Content.ReadAsStringAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[CoreService {MethodName}] Error adding contact to SleekFlow for company {CompanyId}. {ExceptionMessage}",
                    nameof(AddToSleekFlowCRMViaApi),
                    userCompanyId,
                    ex.Message);
            }
        }

        // Helper method to check if a company has a paid subscription
        private async Task<bool> HasPaidSubscription(string companyId)
        {
            return await _appDbContext.CompanyBillRecords
                .AnyAsync(x =>
                    x.CompanyId == companyId &&
                    ValidSubscriptionPlan.PaidPlan.Contains(x.SubscriptionPlanId) &&
                    x.PaymentStatus == PaymentStatus.Paid &&
                    !string.IsNullOrEmpty(x.stripe_subscriptionId) &&
                    x.PayAmount > 0);
        }
    }
}