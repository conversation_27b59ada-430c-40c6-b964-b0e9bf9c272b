using System.Net.Mime;
using System.Text;
using GraphApi.Client.Const.WhatsappCloudApi;
using Hangfire;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Sleekflow.Apis.MessagingHub.Model;
using Sleekflow.Powerflow.Apis.ViewModels;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.Cache;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.Configuration;
using Travis_backend.ConversationServices;
using Travis_backend.Data.WhatsappCloudApi;
using Travis_backend.Exceptions;
using Travis_backend.Filters;
using Travis_backend.InternalDomain.Services;
using AddBackWhatsappCloudApiSenderToUserProfileRequest =
    Sleekflow.Powerflow.Apis.ViewModels.AddBackWhatsappCloudApiSenderToUserProfileRequest;
using CmsCacheKeyHelper = Sleekflow.Powerflow.Apis.Helpers.CmsCacheKeyHelper;
using CreateWhatsappCloudApiTransactionLogRequest =
    Sleekflow.Powerflow.Apis.ViewModels.CreateWhatsappCloudApiTransactionLogRequest;
using CreateWhatsappCloudApiTransactionLogResponse =
    Sleekflow.Powerflow.Apis.ViewModels.CreateWhatsappCloudApiTransactionLogResponse;
using DeregisterWhatsAppPhoneNumberRequest = Sleekflow.Powerflow.Apis.ViewModels.DeregisterWhatsAppPhoneNumberRequest;
using GetAllWhatsAppCloudApiBalancesRequest = Sleekflow.Powerflow.Apis.ViewModels.GetAllWhatsAppCloudApiBalancesRequest;
using GetAllWhatsAppCloudApiBalancesResponse =
    Sleekflow.Powerflow.Apis.ViewModels.GetAllWhatsAppCloudApiBalancesResponse;
using GetAllWhatsappCloudApiConversationUsageAnalyticsRequest =
    Sleekflow.Powerflow.Apis.ViewModels.GetAllWhatsappCloudApiConversationUsageAnalyticsRequest;
using GetAllWhatsappCloudApiWabasRequest = Sleekflow.Powerflow.Apis.ViewModels.GetAllWhatsappCloudApiWabasRequest;
using GetAllWhatsappCloudApiWabasResponse = Sleekflow.Powerflow.Apis.ViewModels.GetAllWhatsappCloudApiWabasResponse;
using GetConnectedWabasByCompanyIdRequest = Sleekflow.Powerflow.Apis.ViewModels.GetConnectedWabasByCompanyIdRequest;
using GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogsRequest =
    Sleekflow.Powerflow.Apis.ViewModels.GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogsRequest;
using GetWhatsappCloudApiConversationUsageAnalyticRequest =
    Sleekflow.Powerflow.Apis.ViewModels.GetWhatsappCloudApiConversationUsageAnalyticRequest;
using GetWhatsAppCloudApiUsageRecordsRequest =
    Sleekflow.Powerflow.Apis.ViewModels.GetWhatsAppCloudApiUsageRecordsRequest;
using GetWhatsappCloudApiUsageRecordsResponse =
    Sleekflow.Powerflow.Apis.ViewModels.GetWhatsappCloudApiUsageRecordsResponse;
using PatchBusinessBalanceMarkupProfile = Sleekflow.Powerflow.Apis.ViewModels.PatchBusinessBalanceMarkupProfile;
using RegisterWhatsappPhoneNumberRequest = Sleekflow.Powerflow.Apis.ViewModels.RegisterWhatsappPhoneNumberRequest;
using SuccessResponse = Sleekflow.Powerflow.Apis.ViewModels.SuccessResponse;
using TriggerWhatsappPhoneNumberOtpRequest = Sleekflow.Powerflow.Apis.ViewModels.TriggerWhatsappPhoneNumberOtpRequest;
using TriggerWhatsAppPhoneNumberOtpResponse = Sleekflow.Powerflow.Apis.ViewModels.TriggerWhatsAppPhoneNumberOtpResponse;
using UpdateWhatsappCloudApiBusinessBalanceMarkupProfileRequest =
    Sleekflow.Powerflow.Apis.ViewModels.UpdateWhatsappCloudApiBusinessBalanceMarkupProfileRequest;
using VerifyWhatsappPhoneNumberOtpRequest = Sleekflow.Powerflow.Apis.ViewModels.VerifyWhatsappPhoneNumberOtpRequest;

namespace Sleekflow.Powerflow.Apis.Controllers;

[Route("/internal/whatsappcloudapi/[action]")]
[TypeFilter(typeof(MessagingHubExceptionFilter))]
[TypeFilter(typeof(ModelStateValidationFilter))]
[Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)] // Basic Rolese Requirement
public class InternalWhatsappCloudApiController : InternalControllerBase
{
    private readonly ILogger<InternalWhatsappCloudApiController> _logger;
    private readonly IWhatsappCloudApiService _whatsappCloudApiService;
    private readonly IInternalWhatsappCloudApiService _internalWhatsappCloudApiService;
    private readonly ICacheManagerService _cacheManagerService;

    public InternalWhatsappCloudApiController(
        UserManager<ApplicationUser> userManager,
        ILogger<InternalWhatsappCloudApiController> logger,
        IWhatsappCloudApiService whatsappCloudApiService,
        IInternalWhatsappCloudApiService internalWhatsappCloudApiService,
        ICacheManagerService cacheManagerService)
        : base(userManager)
    {
        _logger = logger;
        _whatsappCloudApiService = whatsappCloudApiService;
        _internalWhatsappCloudApiService = internalWhatsappCloudApiService;
        _cacheManagerService = cacheManagerService;
    }

    /// <summary>
    /// Create Business Balance Transaction Log.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<CreateWhatsappCloudApiTransactionLogResponse>> CreateTransactionLog(
        [FromBody]
        CreateWhatsappCloudApiTransactionLogRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var user = await GetCurrentValidInternalUser(
            new List<string>
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (user == null)
        {
            return Unauthorized();
        }

        Money credit = new Money("USD", request.AddCreditValue);

        var uniqueId = string.Join("/", request.FacebookBusinessId, user.Id, DateTimeOffset.Now.ToUnixTimeSeconds());

        var response = await _whatsappCloudApiService.TopUp(
            request.CompanyId,
            uniqueId,
            request.FacebookBusinessId,
            WhatsappCloudApiTopUpPaymentMethods.Internal,
            credit,
            user.Id,
            user.DisplayName,
            resellerTransactionLogDetail: request.ResellerTransactionLogDetail,
            metadata: request.Metadata);

        if (!response.Success)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = response.Message
                });
        }

        var result = new CreateWhatsappCloudApiTransactionLogResponse
        {
            WhatsappCloudApiBusinessBalanceTransactionLog = response.Data.WabaBalanceTransactionLog
        };

        var cacheKey = CmsCacheKeyHelper.GetAllWhatsAppCloudApiBalance();
        await _cacheManagerService.DeleteCacheWithConstantKeyAsync(cacheKey);

        return Ok(result);
    }

    /// <summary>
    /// Get Business Balances of the company.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetWhatsappCloudApiUsageRecordsResponse>> GetWhatsAppCloudApiUsageRecords(
        [FromBody]
        GetWhatsAppCloudApiUsageRecordsRequest request)
    {
        var response = await _internalWhatsappCloudApiService.GetInternalBusinessBalances(request.CompanyId);

        var result = new GetWhatsappCloudApiUsageRecordsResponse
        {
            WhatsappCloudApiUsageRecords = response.BusinessBalances
        };

        return Ok(result);
    }

    /// <summary>
    /// Get All Business Balances.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetAllWhatsAppCloudApiBalancesResponse>> GetAllWhatsAppCloudApiBalances(
        [FromBody]
        GetAllWhatsAppCloudApiBalancesRequest request)
    {
        var cacheKey = CmsCacheKeyHelper.GetAllWhatsAppCloudApiBalance();

        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);

            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var response = await _internalWhatsappCloudApiService.GetAllWhatsappCloudApiBusinessBalances();

        var result = new GetAllWhatsAppCloudApiBalancesResponse(response.BusinessBalances);

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get All Wabas.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetAllWhatsappCloudApiWabasResponse>> GetAllWhatsappCloudApiWabas(
        [FromBody]
        GetAllWhatsappCloudApiWabasRequest request)
    {
        var cacheKey = CmsCacheKeyHelper.GetAllWhatsappCloudApiWabas();

        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);

            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var response = await _internalWhatsappCloudApiService.GetAllWhatsappCloudApiWabas();

        var result = new GetAllWhatsappCloudApiWabasResponse(response.Wabas);

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    [HttpPost]
    [Route("auto-top-up/{companyId}/{facebookBusinessId}")]
    public async Task<ActionResult<GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileOutput>>
        GetBusinessBalanceAutoTopUpProfile([FromRoute] string companyId,
            [FromRoute] string facebookBusinessId)
    {
        var result = await _whatsappCloudApiService.GetBusinessBalanceAutoTopUpProfile(
            companyId,
            facebookBusinessId);

        return Ok(result);
    }

    [HttpPost]
    [Route("auto-top-up/waba")]
    public async Task<ActionResult<GetWabaBalanceAutoTopUpProfileOutputOutput>>
        GetWabaBalanceAutoTopUpProfile(
            [FromBody]
            GetWabaBalanceAutoTopUpProfileRequest request,
            CancellationToken cancellationToken)
    {
        var result = await _whatsappCloudApiService.GetWabaBalanceAutoTopUpProfileAsync(
            request.Input,
            cancellationToken);

        return Ok(result);
    }

    /// <summary>
    /// Update Markup Profile.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetAllWhatsappCloudApiWabasResponse>>
        UpdateWhatsappCloudApiBusinessBalanceMarkupProfile(
            [FromBody]
            UpdateWhatsappCloudApiBusinessBalanceMarkupProfileRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var user = await GetCurrentValidInternalUser(
            new List<string>
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (user == null)
        {
            return Unauthorized();
        }

        var response = await _internalWhatsappCloudApiService.UpdateWhatsappCloudApiBusinessBalanceMarkupProfile(
            request.BusinessBalanceId,
            request.MarkupProfile,
            request.LastRecalculateBusinessBalanceTransactionLogDateTime,
            user.Id);

        if (!response.Success)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = response.Message
                });
        }

        return Ok(
            new ResponseViewModel()
            {
                message = "Markup profile updated, the markup will be re-calculate soon"
            });
    }

    /// <summary>
    /// Add back Whatsapp cloud api sender to UserProfile.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> AddBackWhatsappCloudApiSenderToUserProfile(
        [FromBody]
        AddBackWhatsappCloudApiSenderToUserProfileRequest request)
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (request.IsDoInBackground)
        {
            BackgroundJob.Enqueue<IWhatsappCloudApiService>(
                x => x.AddBackWhatsappCloudApiSenderToUserProfile(request.CompanyId));

            return Ok(
                new ResponseViewModel()
                {
                    message = "Add Back Whatsapp CloudApi Sender To UserProfile job queue in background"
                });
        }

        await _whatsappCloudApiService.AddBackWhatsappCloudApiSenderToUserProfile(request.CompanyId);

        return Ok(
            new ResponseViewModel()
            {
                message = "Added Back Whatsapp CloudApi Sender To UserProfile "
            });
    }

    /// <summary>
    /// Disconnect Whatsapp CloudApi Channel Webhook.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> DisconnectWhatsappCloudApiChannelWebhook(
        [FromBody]
        DisconnectWhatsappCloudApiChannelWebhookRequest request)
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (user == null)
        {
            return Unauthorized();
        }

        await _whatsappCloudApiService.DisconnectWhatsappCloudApiChannelWebhookAsync(
            request.CompanyId,
            request.FacebookWabaId,
            request.FacebookPhoneNumberId);

        return Ok(
            new ResponseViewModel()
            {
                message = "Disconnected Whatsapp Cloud Api Channel Webhook"
            });
    }

    /// <summary>
    /// Reconnect Whatsapp Cloud Api Channel webhook.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> ReconnectWhatsappCloudApiChannelWebhook(
        [FromBody]
        ReconnectWhatsappCloudApiChannelWebhookRequest request)
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (user == null)
        {
            return Unauthorized();
        }

        await _whatsappCloudApiService.ReconnectWabaPhoneNumber(
            request.CompanyId,
            request.FacebookWabaId,
            request.FacebookPhoneNumberId);

        return Ok(
            new ResponseViewModel()
            {
                message = "Reconnected Whatsapp Cloud Api Channel Phone Number webhook"
            });
    }

    /// <summary>
    /// Patch selective business balance mark up profile with an effective date.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<List<string>> PatchBusinessBalanceMarkUpProfile(
        [FromBody]
        PatchBusinessBalanceMarkupProfile patchBusinessBalanceMarkupProfile)
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        return await _internalWhatsappCloudApiService.PatchBusinessBalanceMarkupProfile(
            patchBusinessBalanceMarkupProfile.WhiteListSleekflowCompanyIds,
            patchBusinessBalanceMarkupProfile.MarkupProfile,
            patchBusinessBalanceMarkupProfile.EffectiveDate,
            user.Id);
    }

    [HttpPost]
    public async Task<ActionResult<GetManagementWhatsappCloudApiConversationUsageAnalyticOutput>>
        GetWhatsappCloudApiConversationUsageAnalytic(
            [FromBody]
            GetWhatsappCloudApiConversationUsageAnalyticRequest request)
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        return Ok(
            await _internalWhatsappCloudApiService.GetWhatsappCloudApiConversationUsageAnalytic(
                request.FacebookBusinessId,
                request.Start,
                request.End,
                request.Granularity));
    }

    [HttpPost]
    public async Task<ActionResult>
        GetAllWhatsappCloudApiConversationUsageAnalytics(
            [FromBody]
            GetAllWhatsappCloudApiConversationUsageAnalyticsRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid request parameters"
                });
        }

        var user = await GetCurrentValidInternalUser(
            [ApplicationUserRole.InternalCmsSuperUser]);

        if (user == null)
        {
            return Unauthorized();
        }

        try
        {
            var results = await _internalWhatsappCloudApiService
                .GetAllWhatsappCloudApiConversationUsageAnalyticsInternal(
                    request.Start,
                    request.End,
                    request.Granularity);

            return Ok(
                new GetAllWhatsappCloudApiConversationUsageAnalyticsV2Response
                {
                    ByBusinessAnalytics = results.ByBusiness, ByWabaAnalytics = results.ByWaba
                });
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error retrieving WhatsApp Cloud API conversation usage analytics for user {UserId}",
                user.Id);

            return BadRequest(
                new ResponseViewModel
                {
                    message = "An error occurred while retrieving the analytics data"
                });
        }
    }

    /// <summary>
    /// Get All WhatsApp Cloud API Conversation Usage Analytics V2.
    /// </summary>
    /// <param name="request">The request containing start and end dates for analytics.</param>
    /// <returns>A <see cref="Task{ActionResult}"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult> GetAllWhatsappCloudApiConversationUsageAnalyticsV2(
        [FromBody]
        GetAllWhatsappCloudApiConversationUsageAnalyticsRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid request parameters"
                });
        }

        var user = await GetCurrentValidInternalUser(
            [ApplicationUserRole.InternalCmsSuperUser]);

        if (user == null)
        {
            return Unauthorized();
        }

        try
        {
            var results = await _internalWhatsappCloudApiService
                .GetInternalWhatsappCloudApiConversationUsageAnalytics(
                    request.Start,
                    request.End);

            return Ok(new GetAllWhatsappCloudApiConversationUsageAnalyticsV2Response
            {
                ByBusinessAnalytics = results.ByBusiness,
                ByWabaAnalytics = results.ByWaba
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error retrieving WhatsApp Cloud API conversation usage analytics V2 for user {UserId}",
                user.Id);

            return BadRequest(
                new ResponseViewModel
                {
                    message = "An error occurred while retrieving the analytics data"
                });
        }
    }

    [HttpPost]
    public async Task<ActionResult<GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogsOutput>>
        GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogs(
            [FromBody]
            GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogsRequest request)
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        return Ok(
            await _internalWhatsappCloudApiService.GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogs(
                request.BusinessBalanceTransactionLogFilter,
                request.Limit,
                request.ContinuationToken));
    }

    [HttpPost]
    public async Task<ActionResult<TriggerWhatsAppPhoneNumberOtpResponse>> RequestWhatsAppPhoneNumberOtp(
        [FromBody]
        TriggerWhatsappPhoneNumberOtpRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var user = await GetCurrentValidInternalUser(
            new List<string>
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (user == null)
        {
            return Unauthorized();
        }

        TriggerWhatsAppPhoneNumberOtpResponse response = new TriggerWhatsAppPhoneNumberOtpResponse();

        try
        {
            var phoneNumberVerificationCodeRequestDto =
                await _whatsappCloudApiService.RequestPhoneNumberVerificationCode(
                    request.CompanyId,
                    request.FacebookWabaId,
                    request.FacebookPhoneNumberId,
                    request.FacebookPhoneNumber,
                    request.CodeMethod,
                    request.Language);

            response.CodeSent = phoneNumberVerificationCodeRequestDto.CodeSent;

            if (response.CodeSent)
            {
                return Ok(new SuccessResponse(response.CodeSent));
            }

            return BadRequest(new SuccessResponse(false));
        }
        catch (SleekflowUiException ex)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = ex.Message
                });
        }
    }

    [HttpPost]
    public async Task<ActionResult<SuccessResponse>> VerifyWhatsAppPhoneNumberOtp(
        [FromBody]
        VerifyWhatsappPhoneNumberOtpRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var user = await GetCurrentValidInternalUser(
            new List<string>
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (user == null)
        {
            return Unauthorized();
        }

        try
        {
            if ((await _whatsappCloudApiService.PhoneNumberWabaMigrationVerificationCode(
                    request.CompanyId,
                    request.FacebookWabaId,
                    request.FacebookPhoneNumberId,
                    request.FacebookPhoneNumber,
                    request.Code)).Success)
            {
                return new SuccessResponse(true);
            }

            return BadRequest(new SuccessResponse(false));
        }
        catch (SleekflowUiException ex)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = ex.Message
                });
        }
    }

    [HttpPost]
    public async Task<ActionResult<SuccessResponse>> RegisterWhatsAppPhoneNumber(
        [FromBody]
        RegisterWhatsappPhoneNumberRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var user = await GetCurrentValidInternalUser(
            new List<string>
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (user == null)
        {
            return Unauthorized();
        }

        try
        {
            if ((await _whatsappCloudApiService.RegisterWhatsAppPhoneNumberAsync(
                    request.CompanyId,
                    request.FacebookWabaId,
                    request.FacebookPhoneNumberId,
                    request.FacebookPhoneNumber,
                    request.Pin)).Success)
            {
                return new SuccessResponse(true);
            }

            return BadRequest(new SuccessResponse(false));
        }
        catch (SleekflowUiException ex)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = ex.Message
                });
        }
    }

    [HttpPost]
    public async Task<ActionResult<SuccessResponse>> DeregisterWhatsAppPhoneNumber(
        [FromBody]
        DeregisterWhatsAppPhoneNumberRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var user = await GetCurrentValidInternalUser(
            new List<string>
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (user == null)
        {
            return Unauthorized();
        }

        try
        {
            var wabaDto = await _whatsappCloudApiService.GetWabaDtoFromFacebookWabaPhoneNumberId(
                request.CompanyId,
                request.FacebookWabaId,
                request.FacebookPhoneNumberId);

            if (wabaDto == null || wabaDto.WabaDtoPhoneNumbers == null ||
                wabaDto.WabaDtoPhoneNumbers.All(x => x.FacebookPhoneNumberId != request.FacebookPhoneNumberId))
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "unable to find this waba id and phone number id connected to sleekflow"
                    });
            }

            if ((await _whatsappCloudApiService.DeRegisterWhatsAppPhoneNumberAsync(
                    request.CompanyId,
                    wabaDto.Id,
                    wabaDto.WabaDtoPhoneNumbers.Where(x => x.FacebookPhoneNumberId == request.FacebookPhoneNumberId)
                        .Select(x => x.Id).FirstOrDefault())).Success)
            {
                return Ok(new SuccessResponse(true));
            }

            return BadRequest(new SuccessResponse(false));
        }
        catch (SleekflowUiException ex)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = ex.Message
                });
        }
    }

    [HttpPost]
    public async Task<ActionResult<List<WabaDto>>> GetConnectedWabasByCompanyId(
        [FromBody]
        GetConnectedWabasByCompanyIdRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var user = await GetCurrentValidInternalUser(
            new List<string>
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (user == null)
        {
            return Unauthorized();
        }

        var shouldRefresh = !request.AllowCache;
        var cacheKey = CmsCacheKeyHelper.GetConnectedWabasByCompanyId(request.CompanyId);

        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);

            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }

            shouldRefresh = false;
        }

        try
        {
            var wabaDtos = await _whatsappCloudApiService.GetConnectWaba(request.CompanyId, shouldRefresh);

            await _cacheManagerService.SaveCacheWithConstantKeyAsync(
                cacheKey,
                wabaDtos,
                TimeSpan.FromHours(1),
                _jsonSerializerSettings);

            return Ok(wabaDtos);
        }
        catch (SleekflowUiException ex)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = ex.Message
                });
        }
    }

    /// <summary>
    /// Disassociates a Facebook Business Account from a company.
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<DisassociateFacebookBusinessAccountFromCompanyOutputOutput>>
        DisassociateFacebookBusinessAccountFromCompany(
            [FromBody]
            DisassociateFacebookBusinessAccountFromCompanyRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var user = await GetCurrentValidInternalUser(
            new List<string>
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (user == null)
        {
            return Unauthorized();
        }

        try
        {
            return await _internalWhatsappCloudApiService.DisassociateFacebookBusinessAccountFromCompany(
                request.CompanyId,
                request.FacebookBusinessId,
                user.Id);
        }
        catch (Exception ex)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = ex.Message
                });
        }
    }

    [HttpPost]
    public async Task<ActionResult<GetWhatsappPhoneNumberBusinessProfileResponse>>
        GetWhatsappPhoneNumberBusinessProfile([FromBody] GetWhatsappPhoneNumberBusinessProfileRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var user = await GetCurrentValidInternalUser(
            new List<string>
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (user == null)
        {
            return Unauthorized();
        }

        var getPhoneNumberWhatsappBusinessProfileOutputOutput =
            await _internalWhatsappCloudApiService.GetPhoneNumberWhatsappBusinessProfileAsync(
                request.CompanyId,
                request.MessagingWabaId,
                request.MessagingPhoneNumberId);

        return Ok(
            new GetWhatsappPhoneNumberBusinessProfileResponse(
                getPhoneNumberWhatsappBusinessProfileOutputOutput.Data.WhatsappBusinessProfile));
    }

    [HttpPost]
    public async Task<ActionResult<GetWhatsappPhoneNumberBusinessProfileResponse>> UpdateWhatsappPhoneNumberBusinessProfile(
        [FromBody]
        UpdateWhatsappPhoneNumberBusinessProfileRequest request)
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (user == null)
        {
            return Unauthorized();
        }

        var updatePhoneNumberWhatsappBusinessProfileOutputOutput =
            await _internalWhatsappCloudApiService.UpdatePhoneNumberWhatsappBusinessProfileAsync(
                request.CompanyId,
                request.MessagingHubWabaId,
                request.MessagingHubPhoneNumberId,
                user.Id,
                request.UpdatePhoneNumberBusinessProfile);

        return Ok(
            new GetWhatsappPhoneNumberBusinessProfileResponse(
                updatePhoneNumberWhatsappBusinessProfileOutputOutput.Data.WhatsappBusinessProfile));
    }


    public record GetAuditLogsRequest(
        List<string> AuditingOperations,
        string SleekflowCompanyId,
        bool? HasCloudApiAuditsException,
        string ContinuationToken,
        int Limit = 100);

    [HttpPost]
    public async Task<ActionResult<GetWhatsappPhoneNumberBusinessProfileResponse>> GetAuditLogs([FromBody] GetAuditLogsRequest request)
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (user == null)
        {
            return Unauthorized();
        }

        var output =
            await _internalWhatsappCloudApiService.GetAuditLogsAsync(
                request.AuditingOperations,
                request.SleekflowCompanyId,
                request.HasCloudApiAuditsException,
                request.ContinuationToken,
                request.Limit);

        return Ok(output);
    }

    public record UpdatePhoneNumberSettingsRequest(
        string SleekflowCompanyId,
        string MessagingHubWabaId,
        string MessagingHubPhoneNumberId,
        StorageConfiguration StorageConfiguration);

    [HttpPost]
    public async Task<ActionResult<GetWhatsappPhoneNumberBusinessProfileResponse>> UpdatePhoneNumberSettings([FromBody] UpdatePhoneNumberSettingsRequest request)
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (user == null)
        {
            return Unauthorized();
        }

        var output =
            await _internalWhatsappCloudApiService.UpdatePhoneNumberSettingsAsync(
                request.SleekflowCompanyId,
                request.MessagingHubWabaId,
                request.MessagingHubPhoneNumberId,
                request.StorageConfiguration,
                user.Id);

        return Ok(output);
    }

    /// <summary>
    ///     Get Monthly Whatsapp Conversation Usage Analytics.
    /// </summary>
    /// <returns>A <see cref="Task" /> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<List<GetMonthlyWhatsappCloudApiConversationUsageAnalyticsResponse>>>
        GetMonthlyWhatsappCloudApiConversationUsageAnalytics(
            [FromBody]
            GetMonthlyWhatsappCloudApiConversationUsageAnalyticsRequest request)
    {
        var user = await GetCurrentValidInternalUser(
            [ApplicationUserRole.InternalCmsSuperUser]);

        if (user == null)
        {
            return Unauthorized();
        }

        var results = new List<GetMonthlyWhatsappCloudApiConversationUsageAnalyticsResponse>();

        for (var i = (request.Start.Year * 12) + request.Start.Month - 1;
             i < (request.End.Year * 12) + request.End.Month;
             i++)
        {
            var year = i / 12;
            var month = (i % 12) + 1;
            var monthEndDate = new DateTime(year, month, DateTime.DaysInMonth(year, month));
            var isNotFullMonth = monthEndDate > request.End;
            var endDate = isNotFullMonth ? request.End : monthEndDate;
            var response = await _internalWhatsappCloudApiService.GetAllWhatsappCloudApiConversationUsageAnalytics(
                new DateTime(year, month, 1),
                endDate,
                isNotFullMonth
                    ? WhatsappConversationAnalyticGranularityConst.DAILY
                    : WhatsappConversationAnalyticGranularityConst.MONTHLY);

            foreach (var item in response.WhatsappCloudApiConversationUsageAnalytics)
            {
                var foundExist = false;
                foreach (var exist in results.Where(exist => exist.FacebookBusinessId.Equals(item.FacebookBusinessId)))
                {
                    exist.MonthlySummarizedConversationUsageAnalytics.Add(
                        new MonthlySummarizedConversationUsageAnalytic
                        {
                            Date = endDate.ToString("yyyy-MM-dd"),
                            TotalMarkup = item.SummarizedConversationUsageAnalytic.TotalMarkup,
                            TotalUsed = item.SummarizedConversationUsageAnalytic.TotalUsed,
                            TotalTransactionHandlingFee =
                                item.SummarizedConversationUsageAnalytic.TotalTransactionHandlingFee
                        });
                    foundExist = true;
                    break;
                }

                if (foundExist)
                {
                    continue;
                }

                var result = new GetMonthlyWhatsappCloudApiConversationUsageAnalyticsResponse
                {
                    FacebookBusinessId = item.FacebookBusinessId,
                    FacebookBusinessName = item.FacebookBusinessName,
                    MonthlySummarizedConversationUsageAnalytics =
                    [
                        new MonthlySummarizedConversationUsageAnalytic
                        {
                            Date = endDate.ToString("yyyy-MM-dd"),
                            TotalMarkup = item.SummarizedConversationUsageAnalytic.TotalMarkup,
                            TotalUsed = item.SummarizedConversationUsageAnalytic.TotalUsed,
                            TotalTransactionHandlingFee =
                                item.SummarizedConversationUsageAnalytic.TotalTransactionHandlingFee
                        }
                    ]
                };
                results.Add(result);
            }
        }

        return Ok(results);
    }

    /// <summary>
    /// Get Monthly WhatsApp Cloud API Conversation Usage Analytics V2.
    /// </summary>
    /// <returns>A <see cref="Task" /> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult> GetMonthlyWhatsappCloudApiConversationUsageAnalyticsV2(
        [FromBody]
        GetMonthlyWhatsappCloudApiConversationUsageAnalyticsRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid request parameters"
                });
        }

        var user = await GetCurrentValidInternalUser(
            [ApplicationUserRole.InternalCmsSuperUser]);

        if (user == null)
        {
            return Unauthorized();
        }

        try
        {
            var results = await _internalWhatsappCloudApiService
                .GetMonthlyWhatsappCloudApiConversationUsageAnalytics(
                    request.Start,
                    request.End);

            return Ok(results);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error retrieving monthly WhatsApp Cloud API conversation usage analytics V2 for user {UserId}",
                user.Id);

            return BadRequest(
                new ResponseViewModel
                {
                    message = "An error occurred while retrieving the monthly analytics data"
                });
        }
    }
}
