﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using Force.DeepCloner;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.Cache;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ConversationDomain.ConversationAccessControl;
using Travis_backend.ConversationDomain.ConversationResolver;
using Travis_backend.ConversationDomain.Services;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.FileDomain.Models;
using Travis_backend.MessageDomain.ChannelMessageProvider;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.Services;
using Travis_backend.MessageDomain.ViewModels;
using Travis_backend.PiiMasking.Models;
using Travis_backend.PiiMaskingDomain.Services;
using Travis_backend.StripeIntegrationDomain.Services;
using Travis_backend.TenantHubDomain.Services;

namespace Travis_backend.Controllers.MessageControllers;

[Authorize]
[Route("ConversationMessages")]
[Consumes("application/json")]
[Produces("application/json")]
public class ConversationMessageController : Controller
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly IMapper _mapper;
    private readonly ILogger<ConversationMessageController> _logger;
    private readonly IConfiguration _configuration;
    private readonly IConversationMessageService _conversationMessageService;
    private readonly ICacheManagerService _cacheManagerService;
    private readonly IConversationMessageV2Service _conversationMessageV2Service;
    private readonly ICoreService _coreService;
    private readonly ISleekPayService _sleekPayService;
    private readonly IConversationResolver _conversationResolver;
    private readonly IChannelMessageValidator _channelMessageValidator;
    private readonly IConversationValidator _conversationValidator;
    private readonly IUserProfileService _userProfileService;
    private readonly IMessagingChannelService _messagingChannelService;
    private readonly IPiiMaskingService _piiMaskingService;
    private readonly IRbacService _rbacService;
    private readonly IAccessControlAggregationService _accessControlAggregationService;
    private readonly IRbacConversationPermissionManager _rbacConversationPermissionManager;

    public ConversationMessageController(
        ApplicationDbContext appDbContext,
        UserManager<ApplicationUser> userManager,
        IMapper mapper,
        IConfiguration configuration,
        ILogger<ConversationMessageController> logger,
        ICoreService coreService,
        ISleekPayService sleekPayService,
        IConversationResolver conversationResolver,
        IChannelMessageValidator channelMessageValidator,
        IConversationMessageService conversationMessageService,
        ICacheManagerService cacheManagerService,
        IConversationMessageV2Service conversationMessageV2Service,
        IConversationValidator conversationValidator,
        IUserProfileService userProfileService,
        IMessagingChannelService messagingChannelService,
        IPiiMaskingService piiMaskingService,
        IServiceProvider serviceProvider)
    {
        _userManager = userManager;
        _appDbContext = appDbContext;
        _mapper = mapper;
        _configuration = configuration;
        _logger = logger;
        _coreService = coreService;
        _sleekPayService = sleekPayService;
        _conversationResolver = conversationResolver;
        _channelMessageValidator = channelMessageValidator;
        _conversationMessageService = conversationMessageService;
        _cacheManagerService = cacheManagerService;
        _conversationMessageV2Service = conversationMessageV2Service;
        _conversationValidator = conversationValidator;
        _userProfileService = userProfileService;
        _messagingChannelService = messagingChannelService;
        _piiMaskingService = piiMaskingService;
        _rbacService = serviceProvider.GetRequiredService<IRbacService>();
        _accessControlAggregationService = serviceProvider.GetRequiredService<IAccessControlAggregationService>();
        _rbacConversationPermissionManager = serviceProvider.GetRequiredService<IRbacConversationPermissionManager>();
    }

    [HttpPost]
    [Route("SendMessage")]
    public async Task<ActionResult<List<ConversationMessageResponseViewModel>>> SendMessage(
        [FromBody]
        ExtendedConversationMessageViewModel input)
    {
        var companyUser = await _coreService.GetCompanyStaff(
            await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        var isRbacEnabled = _rbacService.IsRbacEnabled();
        if (isRbacEnabled)
        {
            return await RbacSendMessage(input, companyUser);
        }

        return await DefaultSendMessage(input);
    }

    private async Task<ActionResult<List<ConversationMessageResponseViewModel>>> DefaultSendMessage(ExtendedConversationMessageViewModel input)
    {
        try
        {
            var user = await _userManager.GetUserAsync(User);
            var companyStaff = await _coreService.GetCompanyStaff(user);

            if (companyStaff == null || user == null)
            {
                return Unauthorized();
            }

            _logger.LogInformation(
                "Send message start for StaffIdentityId: {IdentityId} CompanyId: {CompanyId} ConversationId: {ConversationId} MessageChecksum: {MessageChecksum} " +
                "Channel: {Channel} ChannelIdentityId: {ChannelIdentityId} MessageContent: {MessageContent} MessageType: {MessageType}",
                companyStaff.IdentityId,
                companyStaff.CompanyId,
                input.ConversationId,
                input.MessageChecksum,
                input.Channel,
                input.ChannelIdentityId,
                input.MessageContent,
                input.MessageType);

            // Validate staff permission
            var (isValidateConversationStaffHaveSendMessagePermission,
                    validateConversationStaffHaveSendMessagePermissionErrorMessage) =
                await _conversationValidator.ValidateConversationStaffHaveSendMessagePermission(
                    input.ConversationId,
                    companyStaff);

            if (!isValidateConversationStaffHaveSendMessagePermission)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = validateConversationStaffHaveSendMessagePermissionErrorMessage
                    });
            }

            var conversationMessage = new ConversationMessage
            {
                CompanyId = companyStaff.CompanyId,
                ConversationId = input.ConversationId,
                MessageChecksum = input.MessageChecksum,
                Channel = input.Channel,
                ReceiverDeviceUUID = input.ReceiverDeviceUUID,
                SenderId = user.Id,
                Sender = user,
                ReceiverId = input.ReceiverId,
                EmailTo = input.EmailTo,
                EmailCC = input.EmailCC,
                Subject = input.Subject,
                MessageType = input.MessageType,
                MessageContent = input.MessageContent,
                DeliveryType = DeliveryType.Normal,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                LocalTimestamp = input.LocalTimestamp,
                Status = MessageStatus.Sending,
                IsSentFromSleekflow = true,
                QuotedMsgId = input.QuotedMsgId,
                MessageTag = input.MessageTag,
                ScheduleSentAt = input.ScheduleSentAt,
                ChannelIdentityId = input.ChannelIdentityId,
                AnalyticTags = input.AnalyticTags,
                Whatsapp360DialogExtendedMessagePayload =
                    _mapper.Map<Whatsapp360DialogExtendedMessagePayload>(input.Whatsapp360DialogExtendedMessagePayload),
                ExtendedMessagePayload = _mapper.Map<ExtendedMessagePayload>(input.ExtendedMessagePayload),
            };

            var conversation = await _conversationResolver.GetConversationByChannelAsync(
                companyStaff.CompanyId,
                input.ConversationId,
                input.Channel,
                input.ChannelIdentityId);

            if (!conversation.IsSandbox)
            {
                // Validate Channel
                var (isValidateConversationMessageChannelValid, validateConversationMessageChannelErrorCode, validateConversationMessageChannelErrorMessage) =
                    await _channelMessageValidator.ValidateConversationMessageChannel(conversationMessage, companyStaff);

                if (!isValidateConversationMessageChannelValid)
                {
                    _logger.LogError(
                        "ValidateConversationMessageChannel {ValidateConversationMessageChannelErrorCode} {ValidateConversationMessageChannelErrorMessage}",
                        validateConversationMessageChannelErrorCode,
                        validateConversationMessageChannelErrorMessage);
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            code = validateConversationMessageChannelErrorCode,
                            message = validateConversationMessageChannelErrorMessage
                        });
                }

                // Valid message
                var (isConversationMessageValid, validateConversationMessageErrorMessage) =
                    await _channelMessageValidator.ValidateConversationMessage(conversationMessage);

                if (!isConversationMessageValid)
                {
                    _logger.LogError(
                        "ValidateConversationMessage {ValidateConversationMessageErrorMessage}",
                        validateConversationMessageErrorMessage);

                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = validateConversationMessageErrorMessage
                        });
                }
            }

            // Link SleekPay Record
            if (!string.IsNullOrEmpty(input.PaymentIntentId))
            {
                conversationMessage.SleekPayRecordId = await _appDbContext.StripePaymentRecords
                    .Where(
                        x =>
                            x.CompanyId == companyStaff.CompanyId
                            && x.StripePaymentIntentId == input.PaymentIntentId)
                    .Select(x => x.Id)
                    .FirstOrDefaultAsync();

                conversationMessage.DeliveryType = DeliveryType.PaymentLink;
            }


            // Fill conversationMessage Receiver
            switch (conversationMessage.Channel)
            {
                case ChannelTypes.WhatsappTwilio:
                    conversationMessage.whatsappReceiver = conversation.WhatsappUser;

                    break;
                case ChannelTypes.Whatsapp360Dialog:
                    conversationMessage.Whatsapp360DialogReceiver = conversation.WhatsApp360DialogUser;

                    break;
                case ChannelTypes.WhatsappCloudApi:
                    conversationMessage.WhatsappCloudApiReceiver = conversation.WhatsappCloudApiUser;

                    break;
                case "naive":
                case ChannelTypes.Email:
                    conversationMessage.Channel = ChannelTypes.Email;
                    conversationMessage.EmailFrom = conversation.EmailAddress;

                    break;
                case ChannelTypes.LiveChat:
                    conversationMessage.WebClientReceiver = conversation.WebClient;

                    break;
                case ChannelTypes.LiveChatV2:
                    // Since LiveChatV2 enables users to have multiple live chat channels, new senders should not directly reference the conversation.
                    var liveChatV2Sender =
                        await _appDbContext.SenderWebClientSenders
                            .Where(
                                sender => sender.CompanyId == companyStaff.CompanyId &&
                                          sender.ConversationId == conversation.Id &&
                                          sender.ChannelIdentityId == conversationMessage.ChannelIdentityId)
                            .FirstOrDefaultAsync();

                    conversationMessage.WebClientReceiver = liveChatV2Sender;
                    break;

                case ChannelTypes.Wechat:
                    conversationMessage.WeChatReceiver = conversation.WeChatUser;

                    break;
                case ChannelTypes.Line:
                    conversationMessage.LineReceiver = conversation.LineUser;

                    break;
                case ChannelTypes.Viber:
                    conversationMessage.ViberReceiver = conversation.ViberUser;

                    break;
                case ChannelTypes.Telegram:
                    conversationMessage.TelegramReceiver = conversation.TelegramUser;

                    break;
                case ChannelTypes.Sms:
                    conversationMessage.SMSReceiver = conversation.SMSUser;

                    break;
                case ChannelTypes.Facebook:
                    conversationMessage.facebookReceiver = conversation.facebookUser;

                    break;
                case ChannelTypes.Instagram:
                    conversationMessage.InstagramReceiver = conversation.InstagramUser;

                    break;
            }

            // Quick Reply Text - File Handling
            try
            {
                List<ConversationMessage> results = new List<ConversationMessage>();

                var textConversationMessage = new ConversationMessage
                {
                    Id = 0,
                    CompanyId = companyStaff.CompanyId,
                    ConversationId = input.ConversationId,
                    Conversation = conversation,
                    MessageChecksum = null,
                    Channel = input.Channel,
                    ReceiverDeviceUUID = input.ReceiverDeviceUUID,
                    SenderId = user.Id,
                    Sender = user,
                    ReceiverId = input.ReceiverId,
                    EmailTo = input.EmailTo,
                    EmailCC = input.EmailCC,
                    Subject = input.Subject,
                    MessageType = "text",
                    MessageContent = input.MessageContent,
                    DeliveryType = DeliveryType.Normal,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                    LocalTimestamp = input.LocalTimestamp,
                    Status = MessageStatus.Sending,
                    IsSentFromSleekflow = true,
                    QuotedMsgId = input.QuotedMsgId,
                    MessageTag = input.MessageTag,
                    ScheduleSentAt = input.ScheduleSentAt,
                    ChannelIdentityId = input.ChannelIdentityId,
                    AnalyticTags = input.AnalyticTags,
                    UploadedFiles = new List<UploadedFile>(),
                    Whatsapp360DialogExtendedMessagePayload =
                        _mapper.Map<Whatsapp360DialogExtendedMessagePayload>(
                            input.Whatsapp360DialogExtendedMessagePayload),
                    ExtendedMessagePayload = _mapper.Map<ExtendedMessagePayload>(input.ExtendedMessagePayload),
                };

                if (input.QuickReplyId.HasValue)
                {
                    var quickReply = await _appDbContext.CompanyQuickReplies
                        .Include(x => x.QuickReplyFile)
                        .Include(x => x.CompanyQuickReplyLinguals)
                        .FirstOrDefaultAsync(
                            x =>
                                x.Id == input.QuickReplyId.Value
                                && x.CompanyId == companyStaff.CompanyId);

                    if (quickReply.QuickReplyFile != null)
                    {
                        conversationMessage.DeliveryType = DeliveryType.QuickReply;
                        conversationMessage.MessageType = "file";

                        var provider = new FileExtensionContentTypeProvider();
                        string contentType;

                        var domainName = _configuration.GetValue<string>("Values:DomainName");

                        var url =
                            $"{domainName}/company/quickReply/attachment/private/{quickReply.QuickReplyFile.QuickReplyFileId}";

                        var fileURLMessages = new List<FileURLMessage>();

                        if (!provider.TryGetContentType(url, out contentType))
                        {
                            contentType = "application/octet-stream";
                        }

                        switch (conversationMessage.Channel)
                        {
                            case ChannelTypes.Facebook:
                            case ChannelTypes.Instagram:
                            case ChannelTypes.Wechat:
                                conversationMessage.MessageContent = null;

                                break;
                        }

                        fileURLMessages.Add(
                            new FileURLMessage
                            {
                                FileName = Path.GetFileName(quickReply.QuickReplyFile.Filename),
                                FileURL = url,
                                MIMEType = contentType
                            });

                        results = (await _conversationMessageService.SendFileMessageByFBURL(
                                conversation,
                                conversationMessage,
                                fileURLMessages))
                            .ToList();
                    }
                    else
                    {
                        textConversationMessage = conversationMessage;
                    }

                    IEnumerable<ConversationMessage> textMessageResult;


                    if (!string.IsNullOrEmpty(textConversationMessage.MessageContent))
                    {
                        switch (conversationMessage.Channel)
                        {
                            case ChannelTypes.Facebook:
                                textConversationMessage.facebookReceiver = conversation.facebookUser;

                                textMessageResult = (await _conversationMessageService.SendMessage(
                                        conversation,
                                        textConversationMessage))
                                    .ToList();

                                results.AddRange(textMessageResult);

                                break;
                            case ChannelTypes.Instagram:
                                textConversationMessage.InstagramReceiver = conversation.InstagramUser;

                                textMessageResult = (await _conversationMessageService.SendMessage(
                                        conversation,
                                        textConversationMessage))
                                    .AsEnumerable();

                                results.AddRange(textMessageResult);

                                break;
                            case ChannelTypes.Wechat:
                                textConversationMessage.WeChatReceiver = conversation.WeChatUser;

                                textMessageResult = (await _conversationMessageService.SendMessage(
                                        conversation,
                                        textConversationMessage))
                                    .ToList();

                                results.AddRange(textMessageResult);

                                break;
                        }
                    }
                }
                else if (input.fileURLs?.Count > 0)
                {
                    conversationMessage.MessageType = "file";

                    var provider = new FileExtensionContentTypeProvider();
                    string contentType;

                    var fileURLMessages = new List<FileURLMessage>();

                    for (var i = 0; i < input.fileURLs.Count; i++)
                    {
                        var url = input.fileURLs[i];
                        var fileName = input.fileNames[i];

                        if (!provider.TryGetContentType(url, out contentType))
                        {
                            contentType = "application/octet-stream";
                        }

                        fileURLMessages.Add(
                            new FileURLMessage
                            {
                                FileName = fileName,
                                FileURL = url,
                                MIMEType = contentType
                            });
                    }

                    switch (conversationMessage.Channel)
                    {
                        case ChannelTypes.Facebook:
                        case ChannelTypes.Instagram:
                            conversationMessage.MessageContent = null;

                            break;
                    }

                    results = (await _conversationMessageService.SendFileMessageByFBURL(
                            conversation,
                            conversationMessage,
                            fileURLMessages))
                        .ToList();

                    if (!string.IsNullOrEmpty(textConversationMessage.MessageContent))
                    {
                        switch (conversationMessage.Channel)
                        {
                            case ChannelTypes.Facebook:
                                textConversationMessage.MessageUniqueID = null;
                                textConversationMessage.Id = 0;
                                textConversationMessage.MessageType = "text";

                                textConversationMessage.SenderId = user.Id;
                                textConversationMessage.Sender = user;
                                textConversationMessage.IsSentFromSleekflow = true;

                                textConversationMessage.facebookReceiver = conversation.facebookUser;
                                textConversationMessage.ConversationId = conversation.Id;
                                textConversationMessage.Conversation = conversation;

                                var results2 = (await _conversationMessageService.SendMessage(
                                        conversation,
                                        textConversationMessage))
                                    .ToList();

                                results.AddRange(results2);

                                break;
                            case ChannelTypes.Instagram:
                                break;
                        }
                    }
                }
                else
                {
                    results = (await _conversationMessageService.SendMessage(
                            conversation,
                            conversationMessage))
                        .ToList();
                }

                var conversationMessagesVM = _mapper.Map<List<ConversationMessageResponseViewModel>>(results);
                await _sleekPayService.AddSleekPayRecord(conversationMessagesVM);

                return Ok(conversationMessagesVM);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Message/Send (inner) exception: {ExceptionMessage}, Request payload: {RequestPayload}",
                    ex.Message,
                    JsonConvert.SerializeObject(input));

                return BadRequest(ex.Message);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Message/Send (outer) exception: {ExceptionMessage}, Request payload: {RequestPayload}",
                ex.Message,
                JsonConvert.SerializeObject(input));

            return BadRequest(
                new ResponseViewModel
                {
                    message = $"{ex.ToString()}"
                });
        }
    }

    [HttpPost]
    [Route("SendFileMessage")]
    [Consumes("multipart/form-data")]
    [Produces("application/json")]
    public async Task<ActionResult<List<ConversationMessageResponseViewModel>>> V2SendFileMessage(
        [FromForm]
        ConversationMessageViewModel input)
    {
        var companyUser = await _coreService.GetCompanyStaff(
            await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        var isRbacEnabled = _rbacService.IsRbacEnabled();
        if (isRbacEnabled)
        {
            return await RbacV2SendFileMessage(input, companyUser);
        }

        return await DefaultV2SendFileMessage(input);
    }

    private async Task<ActionResult<List<ConversationMessageResponseViewModel>>> DefaultV2SendFileMessage(ConversationMessageViewModel input)
    {
        var user = await _userManager.GetUserAsync(User);
        var companyStaff = await _coreService.GetCompanyStaff(user);

        if (companyStaff == null || user == null)
        {
            return Unauthorized();
        }

        _logger.LogInformation(
            "Send file message start for StaffIdentityId: {IdentityId} CompanyId: {CompanyId} ConversationId: {ConversationId} MessageChecksum: {MessageChecksum} " +
            "Channel: {Channel} ChannelIdentityId: {ChannelIdentityId} MessageContent: {MessageContent} MessageType: {MessageType}",
            companyStaff.IdentityId,
            companyStaff.CompanyId,
            input.ConversationId,
            input.MessageChecksum,
            input.Channel,
            input.ChannelIdentityId,
            input.MessageContent,
            input.MessageType);

        // Validate staff permission
        var (isValidateConversationStaffHaveSendMessagePermission,
                validateConversationStaffHaveSendMessagePermissionErrorMessage) =
            await _conversationValidator.ValidateConversationStaffHaveSendMessagePermission(
                input.ConversationId,
                companyStaff);

        if (!isValidateConversationStaffHaveSendMessagePermission)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = validateConversationStaffHaveSendMessagePermissionErrorMessage
                });
        }

        var conversationMessage = new ConversationMessage
        {
            CompanyId = companyStaff.CompanyId,
            ConversationId = input.ConversationId,
            MessageChecksum = input.MessageChecksum,
            Channel = input.Channel,
            ReceiverDeviceUUID = input.ReceiverDeviceUUID,
            SenderId = user.Id,
            Sender = user,
            ReceiverId = input.ReceiverId,
            EmailTo = input.EmailTo,
            EmailCC = input.EmailCC,
            Subject = input.Subject,
            MessageType = input.MessageType,
            MessageContent = input.MessageContent,
            DeliveryType = DeliveryType.Normal,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
            LocalTimestamp = input.LocalTimestamp,
            Status = MessageStatus.Sending,
            IsSentFromSleekflow = true,
            QuotedMsgId = input.QuotedMsgId,
            MessageTag = input.MessageTag,
            ScheduleSentAt = input.ScheduleSentAt,
            ChannelIdentityId = input.ChannelIdentityId,
        };

        var conversation = await _conversationResolver.GetConversationByChannelAsync(
            companyStaff.CompanyId,
            input.ConversationId,
            input.Channel,
            input.ChannelIdentityId);

        if (!conversation.IsSandbox)
        {
            // Validate Channel
            var (isValidateConversationMessageChannelValid, validateConversationMessageChannelErrorCode, validateConversationMessageChannelErrorMessage) =
                await _channelMessageValidator.ValidateConversationMessageChannel(conversationMessage, companyStaff);

            if (!isValidateConversationMessageChannelValid)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        code = validateConversationMessageChannelErrorCode,
                        message = validateConversationMessageChannelErrorMessage
                    });
            }

            // Valid message
            var (isConversationMessageValid, validateConversationMessageErrorMessage) =
                await _channelMessageValidator.ValidateConversationMessage(conversationMessage);

            if (!isConversationMessageValid)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = validateConversationMessageErrorMessage
                    });
            }
        }

        // Link SleekPay Record
        if (!string.IsNullOrEmpty(input.PaymentIntentId))
        {
            conversationMessage.SleekPayRecordId = await _appDbContext.StripePaymentRecords
                .Where(
                    x =>
                        x.CompanyId == companyStaff.CompanyId
                        && x.StripePaymentIntentId == input.PaymentIntentId)
                .Select(x => x.Id)
                .FirstOrDefaultAsync();

            conversationMessage.DeliveryType = DeliveryType.PaymentLink;
        }

        switch (conversationMessage.Channel)
        {
            case ChannelTypes.WhatsappTwilio:
                conversationMessage.whatsappReceiver = conversation.WhatsappUser;

                break;
            case ChannelTypes.Whatsapp360Dialog:
                conversationMessage.Whatsapp360DialogReceiver = conversation.WhatsApp360DialogUser;

                break;
            case ChannelTypes.WhatsappCloudApi:
                conversationMessage.WhatsappCloudApiReceiver = conversation.WhatsappCloudApiUser;

                break;
            case "naive":
            case ChannelTypes.Email:
                conversationMessage.Channel = ChannelTypes.Email;
                conversationMessage.EmailFrom = conversation.EmailAddress;

                break;
            case ChannelTypes.LiveChat:
                conversationMessage.WebClientReceiver = conversation.WebClient;

                break;
            case ChannelTypes.Wechat:
                conversationMessage.WeChatReceiver = conversation.WeChatUser;

                break;
            case ChannelTypes.Line:
                conversationMessage.LineReceiver = conversation.LineUser;

                break;
            case ChannelTypes.Viber:
                conversationMessage.ViberReceiver = conversation.ViberUser;

                break;
            case ChannelTypes.Telegram:
                conversationMessage.TelegramReceiver = conversation.TelegramUser;

                break;
            case ChannelTypes.Sms:
                conversationMessage.SMSReceiver = conversation.SMSUser;

                break;
            case ChannelTypes.Facebook:
                conversationMessage.facebookReceiver = conversation.facebookUser;

                break;
            case ChannelTypes.Instagram:
                conversationMessage.InstagramReceiver = conversation.InstagramUser;

                break;
            case ChannelTypes.LiveChatV2:
                // Since LiveChatV2 enables users to have multiple live chat channels, new senders should not directly reference the conversation.
                var liveChatV2Sender =
                    await _appDbContext.SenderWebClientSenders
                        .Where(
                            sender => sender.CompanyId == companyStaff.CompanyId&&
                                      sender.ConversationId == conversation.Id &&
                                      sender.ChannelIdentityId == conversationMessage.ChannelIdentityId)
                        .FirstOrDefaultAsync();
                conversationMessage.WebClientReceiver = liveChatV2Sender;
                break;
        }

        try
        {
            List<ConversationMessage> results = new List<ConversationMessage>();
            List<ConversationMessage> textMessageResult = new List<ConversationMessage>();

            if (conversationMessage.Channel == ChannelTypes.Whatsapp360Dialog)
            {
                var files = input.files;
                input.files = null;

                for (var index = 0; index < files.Count; index++)
                {
                    var file = files[index];

                    // Retrieve the conversation again, and plug that again
                    var whatsapp360ConversationMessageViewModel = input.DeepClone();

                    var whatsapp360ConversationMessage = new ConversationMessage
                    {
                        CompanyId = companyStaff.CompanyId,
                        ConversationId = input.ConversationId,
                        MessageChecksum = input.MessageChecksum,
                        Channel = input.Channel,
                        ReceiverDeviceUUID = input.ReceiverDeviceUUID,
                        SenderId = user.Id,
                        Sender = user,
                        ReceiverId = input.ReceiverId,
                        MessageType = input.MessageType,
                        MessageContent = input.MessageContent,
                        DeliveryType = DeliveryType.Normal,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow,
                        Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                        LocalTimestamp = input.LocalTimestamp,
                        Status = MessageStatus.Sending,
                        IsSentFromSleekflow = true,
                        QuotedMsgId = input.QuotedMsgId,
                        MessageTag = input.MessageTag,
                        ScheduleSentAt = input.ScheduleSentAt,
                        ChannelIdentityId = input.ChannelIdentityId,
                    };

                    whatsapp360ConversationMessage.MessageChecksum = index == 0
                        ? input.MessageChecksum
                        : Guid.NewGuid().ToString();

                    whatsapp360ConversationMessage.MessageContent =
                        index == 0 ? input.MessageContent : null;

                    whatsapp360ConversationMessage.SenderId = conversationMessage.SenderId;

                    whatsapp360ConversationMessage.Whatsapp360DialogReceiver =
                        conversation.WhatsApp360DialogUser;

                    whatsapp360ConversationMessageViewModel.files = new List<IFormFile>
                    {
                        file
                    };

                    var whatsapp360Results = await _conversationMessageService.SendFileMessage(
                        conversation,
                        whatsapp360ConversationMessage,
                        whatsapp360ConversationMessageViewModel);

                    foreach (var whatsapp360Result in whatsapp360Results)
                    {
                        results.Add(whatsapp360Result);
                    }
                }
            }
            else if (conversationMessage.Channel == ChannelTypes.WhatsappCloudApi)
            {
                var files = input.files;
                input.files = null;

                for (var index = 0; index < files.Count; index++)
                {
                    var file = files[index];
                    var whatsappCloudApiConversationMessageViewModel = input.DeepClone();

                    var whatsappCloudApiConversationMessage = new ConversationMessage
                    {
                        CompanyId = companyStaff.CompanyId,
                        ConversationId = input.ConversationId,
                        MessageChecksum = input.MessageChecksum,
                        Channel = input.Channel,
                        ReceiverDeviceUUID = input.ReceiverDeviceUUID,
                        SenderId = user.Id,
                        Sender = user,
                        ReceiverId = input.ReceiverId,
                        MessageType = input.MessageType,
                        MessageContent = input.MessageContent,
                        DeliveryType = DeliveryType.Normal,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow,
                        Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                        LocalTimestamp = input.LocalTimestamp,
                        Status = MessageStatus.Sending,
                        IsSentFromSleekflow = true,
                        QuotedMsgId = input.QuotedMsgId,
                        MessageTag = input.MessageTag,
                        ScheduleSentAt = input.ScheduleSentAt,
                        ChannelIdentityId = input.ChannelIdentityId,
                    };

                    whatsappCloudApiConversationMessage.MessageChecksum = index == 0
                        ? input.MessageChecksum
                        : Guid.NewGuid().ToString();

                    whatsappCloudApiConversationMessage.MessageContent =
                        index == 0 ? input.MessageContent : null;

                    whatsappCloudApiConversationMessage.SenderId = conversationMessage.SenderId;

                    whatsappCloudApiConversationMessage.WhatsappCloudApiReceiver =
                        conversation.WhatsappCloudApiUser;

                    whatsappCloudApiConversationMessageViewModel.files = new List<IFormFile>
                    {
                        file
                    };

                    var whatsappCloudApiResults = await _conversationMessageService.SendFileMessage(
                        conversation,
                        whatsappCloudApiConversationMessage,
                        whatsappCloudApiConversationMessageViewModel);

                    foreach (var whatsappCloudApiResult in whatsappCloudApiResults)
                    {
                        results.Add(whatsappCloudApiResult);
                    }
                }
            }
            else if (conversationMessage.Channel == ChannelTypes.Facebook)
            {
                conversationMessage.MessageContent = null;

                results = (await _conversationMessageService.SendFileMessage(
                    conversation,
                    conversationMessage,
                    input)).ToList();

                if (!string.IsNullOrEmpty(input.MessageContent))
                {
                    var textConversationMessage = new ConversationMessage
                    {
                        CompanyId = companyStaff.CompanyId,
                        ConversationId = input.ConversationId,
                        MessageChecksum = input.MessageChecksum,
                        Channel = input.Channel,
                        ReceiverDeviceUUID = input.ReceiverDeviceUUID,
                        SenderId = user.Id,
                        Sender = user,
                        ReceiverId = input.ReceiverId,
                        MessageType = input.MessageType,
                        MessageContent = input.MessageContent,
                        DeliveryType = DeliveryType.Normal,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow,
                        Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                        LocalTimestamp = input.LocalTimestamp,
                        Status = MessageStatus.Sending,
                        IsSentFromSleekflow = true,
                        QuotedMsgId = input.QuotedMsgId,
                        MessageTag = input.MessageTag,
                        ScheduleSentAt = input.ScheduleSentAt,
                        ChannelIdentityId = input.ChannelIdentityId,
                    };

                    try
                    {
                        textConversationMessage.MessageUniqueID = null;
                        textConversationMessage.Id = 0;
                        textConversationMessage.MessageChecksum = Guid.NewGuid().ToString();
                        textConversationMessage.UploadedFiles = new List<UploadedFile>(); // Empty the files?
                        textConversationMessage.MessageType = "text";

                        textConversationMessage.SenderId = user.Id;
                        textConversationMessage.Sender = user;
                        textConversationMessage.IsSentFromSleekflow = true;

                        textConversationMessage.facebookReceiver = conversation.facebookUser;
                        textConversationMessage.Conversation = conversation;

                        textMessageResult = (await _conversationMessageService.SendMessage(
                                conversation,
                                textConversationMessage))
                            .ToList();

                        results.AddRange(textMessageResult);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[{MethodName} endpoint] Send message to Facebook error: {ExceptionMessage}",
                            nameof(V2SendFileMessage),
                            ex.Message);
                    }
                }
            }
            else if (conversationMessage.Channel == ChannelTypes.Instagram)
            {
                conversationMessage.MessageContent = null;

                results = (await _conversationMessageService.SendFileMessage(
                    conversation,
                    conversationMessage,
                    input)).ToList();

                if (!string.IsNullOrEmpty(input.MessageContent))
                {
                    var textConversationMessage = new ConversationMessage
                    {
                        CompanyId = companyStaff.CompanyId,
                        ConversationId = input.ConversationId,
                        MessageChecksum = input.MessageChecksum,
                        Channel = input.Channel,
                        ReceiverDeviceUUID = input.ReceiverDeviceUUID,
                        SenderId = user.Id,
                        Sender = user,
                        ReceiverId = input.ReceiverId,
                        MessageType = input.MessageType,
                        MessageContent = input.MessageContent,
                        DeliveryType = DeliveryType.Normal,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow,
                        Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                        LocalTimestamp = input.LocalTimestamp,
                        Status = MessageStatus.Sending,
                        IsSentFromSleekflow = true,
                        QuotedMsgId = input.QuotedMsgId,
                        MessageTag = input.MessageTag,
                        ScheduleSentAt = input.ScheduleSentAt,
                        ChannelIdentityId = input.ChannelIdentityId,
                    };

                    try
                    {
                        textConversationMessage.MessageUniqueID = null;
                        textConversationMessage.Id = 0;
                        textConversationMessage.MessageChecksum = Guid.NewGuid().ToString();
                        textConversationMessage.UploadedFiles = new List<UploadedFile>();
                        textConversationMessage.MessageType = "text";

                        textConversationMessage.InstagramReceiver = conversation.InstagramUser;
                        textConversationMessage.ConversationId = conversation.Id;
                        textConversationMessage.Conversation = conversation;

                        var testMessageResult = (await _conversationMessageService.SendMessage(
                                conversation,
                                textConversationMessage))
                            .AsEnumerable();

                        results.AddRange(testMessageResult);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[{MethodName} endpoint] Send message to Instagram error: {ExceptionMessage}",
                            nameof(V2SendFileMessage),
                            ex.Message);
                    }
                }
            }
            else if (conversationMessage.Channel == ChannelTypes.Wechat)
            {
                // send file first
                conversationMessage.MessageContent = null;

                results = (await _conversationMessageService.SendFileMessage(
                    conversation,
                    conversationMessage,
                    input)).ToList();

                // send text if any
                var textConversationMessage = new ConversationMessage
                {
                    CompanyId = companyStaff.CompanyId,
                    ConversationId = input.ConversationId,
                    MessageChecksum = input.MessageChecksum,
                    Channel = input.Channel,
                    ReceiverDeviceUUID = input.ReceiverDeviceUUID,
                    SenderId = user.Id,
                    Sender = user,
                    ReceiverId = input.ReceiverId,
                    MessageType = input.MessageType,
                    MessageContent = input.MessageContent,
                    DeliveryType = DeliveryType.Normal,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                    LocalTimestamp = input.LocalTimestamp,
                    Status = MessageStatus.Sending,
                    IsSentFromSleekflow = true,
                    QuotedMsgId = input.QuotedMsgId,
                    MessageTag = input.MessageTag,
                    ScheduleSentAt = input.ScheduleSentAt,
                    ChannelIdentityId = input.ChannelIdentityId,
                };

                if (textConversationMessage.MessageContent != null)
                {
                    try
                    {
                        textConversationMessage.MessageUniqueID = null;
                        textConversationMessage.Id = 0;
                        textConversationMessage.MessageChecksum = Guid.NewGuid().ToString();
                        textConversationMessage.UploadedFiles = new List<UploadedFile>();
                        textConversationMessage.MessageType = "text";
                        textConversationMessage.SenderId = user.Id;
                        textConversationMessage.Sender = user;
                        textConversationMessage.IsSentFromSleekflow = true;

                        textConversationMessage.WeChatReceiver = conversation.WeChatUser;
                        textConversationMessage.ConversationId = conversation.Id;
                        textConversationMessage.Conversation = conversation;

                        textMessageResult = (await _conversationMessageService.SendMessage(
                                conversation,
                                textConversationMessage))
                            .ToList();

                        results.AddRange(textMessageResult);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[{MethodName} endpoint] Send message to WeChat error: {ExceptionMessage}",
                            nameof(V2SendFileMessage),
                            ex.Message);
                    }
                }
            }
            else
            {
                results = (await _conversationMessageService.SendFileMessage(
                        conversation,
                        conversationMessage,
                        input))
                    .ToList();
            }

            var conversationMessagesVM = _mapper.Map<List<ConversationMessageResponseViewModel>>(results);

            return Ok(conversationMessagesVM);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName} endpoint] error: {ExceptionMessage}",
                nameof(V2SendFileMessage),
                ex.Message);

            return BadRequest(ex.Message);
        }
    }

    public class GetConversationMessagesInput
    {
        [Required]
        public string ConversationId { get; set; }

        public int Offset { get; set; } = 0;

        public int Limit { get; set; } = 10;

        public long? BeforeMessageId { get; set; } = null;

        public long? AfterMessageId { get; set; } = null;

        public long? AfterTimestamp { get; set; } = null;

        public long? BeforeTimestamp { get; set; } = null;

        public List<ChannelMessageFilter> ChannelMessageFilters { get; set; } = new List<ChannelMessageFilter>();

        public bool? IsFromUser { get; set; } = null;

        public bool? IsFromImport { get; set; } = null;

        public string MessageType { get; set; } = null;

        public string MessageContent { get; set; } = null;

        // Message Status Example Values - Sending,Sent,Received,Read,Failed,Undelivered,Deleted,OutOfCredit,Scheduled, PaymentLinkPending = 90, PaymentLinkPaid = 91,PaymentLinkCanceled = 92.
        public string? MessageStatus { get; set; } = null;

        public string Order { get; set; } = "desc";
    }

    public class GetConversationMessagesOutput
    {
        public List<ConversationMessageResponseViewModel> Messages { get; set; }
    }

    [HttpPost]
    [Route("GetMessages")]
    public async Task<ActionResult<GetConversationMessagesOutput>> GetConversationMessages(
        [FromBody]
        GetConversationMessagesInput input,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
        }

        var companyUser = await _coreService.GetCompanyStaff(
            await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        // Validate staff permission
        var (isConversationStaffPermissionValid, validateConversationStaffPermissionErrorMessage) =
            await _conversationMessageV2Service.ValidateConversationStaffPermission(
                companyUser,
                input.ConversationId,
                cancellationToken);

        if (!isConversationStaffPermissionValid)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = validateConversationStaffPermissionErrorMessage
                });
        }

        var getConversationMessagesCacheKeyPattern = new GetConversationMessagesCacheKeyPattern(
            companyUser.Id,
            input.ConversationId,
            input.Offset,
            input.Limit,
            input.BeforeMessageId,
            input.AfterMessageId,
            input.AfterTimestamp,
            input.BeforeTimestamp,
            string.Join("_", input.ChannelMessageFilters.Select(x => x.ChannelIdentityId)),
            input.IsFromUser,
            input.Order,
            input.MessageType,
            input.MessageContent,
            input.MessageStatus);

        var data = await _cacheManagerService.GetCacheAsync(getConversationMessagesCacheKeyPattern);

        // if cache data exists, return it
        if ((input.BeforeTimestamp.HasValue
             || input.AfterTimestamp.HasValue
             || !input.IsFromUser.HasValue)
            && !string.IsNullOrEmpty(data))
        {
            return Ok(
                new GetConversationMessagesOutput
                {
                    Messages = JsonConvert.DeserializeObject<List<ConversationMessageResponseViewModel>>(data)
                });
        }

        try
        {
            _logger.LogInformation(
                "Company {CompanyId} user {CompanyUserId} GetConversationMessages: ConversationId {ConversationId}",
                companyUser.CompanyId,
                companyUser.Id,
                input.ConversationId);

            var conversationMessagesQueryable =
                await _conversationMessageV2Service.FilterConversationMessageWithDefaultChannelQueryableAsync(
                    companyUser.CompanyId,
                    companyUser.Id,
                    companyUser.RoleType,
                    input.ConversationId,
                    input.BeforeMessageId,
                    input.AfterMessageId,
                    input.BeforeTimestamp,
                    input.AfterTimestamp,
                    input.ChannelMessageFilters,
                    input.MessageType,
                    input.MessageContent,
                    input.MessageStatus,
                    input.IsFromUser,
                    input.IsFromImport,
                    input.Order);

            var messageIds = await conversationMessagesQueryable
                .Skip(input.Offset)
                .Take(input.Limit)
                .Select(x => x.Id)
                .ToListAsync(cancellationToken: cancellationToken);

            var response = await conversationMessagesQueryable
                .Where(x => messageIds.Contains(x.Id))
                .ProjectTo<ConversationMessageResponseViewModel>(_mapper.ConfigurationProvider)
                .ToListAsync(cancellationToken: cancellationToken);

            await _sleekPayService.AddSleekPayRecord(response);

            // reset unread message
            if (input.Offset == 0 &&
                (await _appDbContext.Conversations
                     .AnyAsync(
                         x =>
                             x.Id == input.ConversationId
                             && x.UnreadMessageCount > 0,
                         cancellationToken: cancellationToken)
                 || await _appDbContext.ConversationUnreadRecords
                     .AnyAsync(
                         x =>
                             x.CompanyId == companyUser.CompanyId
                             && x.ConversationId == input.ConversationId
                             && x.StaffId == companyUser.Id
                             && x.NotificationDeliveryStatus != NotificationDeliveryStatus.Read,
                         cancellationToken: cancellationToken)))
            {
                await _conversationMessageService.ReadConversation(
                    input.ConversationId,
                    companyUser,
                    companyUser.CompanyId == "21da963c-a525-478d-8ec7-d473791a482e");
            }

            if (await _piiMaskingService.IsConfiguredAsync(companyUser.CompanyId)
                && Enum.TryParse(companyUser.RoleType.ToString(), out MaskingRoles maskingRole))
            {
                foreach (var message in response)
                {
                    if (message is not null
                        && !message.IsSentFromSleekflow
                        && message.MessageContent is not null)
                    {
                        message.MessageContent = await _piiMaskingService.MaskPiiIfEnabledAsync(
                            companyUser.CompanyId,
                            message.MessageContent,
                            MaskingLocations.IncomingMessage,
                            maskingRole);
                    }
                }
            }

            var result = new GetConversationMessagesOutput
            {
                Messages = response
            };

            if (input.BeforeTimestamp.HasValue || input.AfterTimestamp.HasValue || !input.IsFromUser.HasValue)
            {
                await _conversationMessageService.AddGetConversationMessagesCache(
                    input.ConversationId,
                    getConversationMessagesCacheKeyPattern,
                    response);
            }

            return Ok(result);
        }
        catch (TaskCanceledException)
        {
            return StatusCode(499);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName} endpoint] error: {ExceptionMessage}",
                nameof(GetConversationMessages),
                ex.Message);

            return BadRequest(
                new ResponseViewModel
                {
                    message = "error"
                });
        }
    }

    public class GetMessageByMessageIdInput
    {
        [Required]
        public string ConversationId { get; set; }

        public long? MessageId { get; set; }

        public string MessageUniqueID { get; set; }
    }

    public class GetConversationMessageOutput
    {
        public ConversationMessageResponseViewModel Message { get; set; }
    }

    [HttpPost]
    [Route("GetMessage")]
    public async Task<ActionResult<GetConversationMessageOutput>> GetConversationMessage(
        [FromBody]
        GetMessageByMessageIdInput input,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
        }

        var companyUser = await _coreService.GetCompanyStaff(
            await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        // Validate staff permission
        var (isConversationStaffPermissionValid, validateConversationStaffPermissionErrorMessage) =
            await _conversationMessageV2Service.ValidateConversationStaffPermission(companyUser, input.ConversationId);

        if (!isConversationStaffPermissionValid)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = validateConversationStaffPermissionErrorMessage
                });
        }

        try
        {
            IQueryable<ConversationMessage> conversationMessagesQueryable;
            if (!string.IsNullOrEmpty(input.MessageUniqueID))
            {
                conversationMessagesQueryable =
                    _conversationMessageV2Service.GetConversationMessageByMessageUniqueIdQueryable(
                        companyUser.CompanyId,
                        input.ConversationId,
                        input.MessageUniqueID);
            }
            else if (input.MessageId.HasValue)
            {
                conversationMessagesQueryable =
                    _conversationMessageV2Service.GetConversationMessageByMessageIdQueryable(
                        companyUser.CompanyId,
                        input.ConversationId,
                        input.MessageId.Value);
            }
            else
            {
                // Not found
                return Ok(
                    new GetConversationMessageOutput
                    {
                        Message = null
                    });
            }

            var response = await conversationMessagesQueryable
                .Take(1)
                .AsNoTracking()
                .ProjectTo<ConversationMessageResponseViewModel>(_mapper.ConfigurationProvider)
                .ToListAsync(cancellationToken: cancellationToken);

            await _sleekPayService.AddSleekPayRecord(response);

            return Ok(
                new GetConversationMessageOutput
                {
                    Message = response.FirstOrDefault()
                });
        }
        catch (TaskCanceledException)
        {
            return StatusCode(499);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName} endpoint] error: {ExceptionMessage}",
                nameof(GetConversationMessage),
                ex.Message);

            return BadRequest(
                new ResponseViewModel
                {
                    message = "error"
                });
        }
    }

    [HttpPost("ForwardMessage")]
    public async Task<ActionResult<List<ConversationMessageResponseViewModel>>> ForwardMessages(
        [FromBody]
        ForwardMessageInput input)
    {
        var companyUser = await _coreService.GetCompanyStaff(
            await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        var isRbacEnabled = _rbacService.IsRbacEnabled();
        if (isRbacEnabled)
        {
            return await RbacForwardMessages(input, companyUser);
        }

        return await DefaultForwardMessages(input);
    }

    private async Task<ActionResult<List<ConversationMessageResponseViewModel>>> DefaultForwardMessages(ForwardMessageInput input)
    {
        try
        {
            var user = await _userManager.GetUserAsync(User);
            var companyStaff = await _coreService.GetCompanyStaff(user);

            if (companyStaff == null || user == null)
            {
                return Unauthorized();
            }

            var forwardedCount = 0;
            var domainName = _configuration.GetValue<String>("Values:DomainName");

            foreach (var forwardConversationMessage in input.ForwardConversationMessages)
            {
                // Validate staff permission
                var (isValidateConversationStaffHaveSendMessagePermission,
                        validateConversationStaffHaveSendMessagePermissionErrorMessage) =
                    await _conversationValidator.ValidateConversationStaffHaveSendMessagePermission(
                        forwardConversationMessage.ConversationId,
                        companyStaff);

                if (!isValidateConversationStaffHaveSendMessagePermission)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = validateConversationStaffHaveSendMessagePermissionErrorMessage
                        });
                }

                var messagingChannel = await _messagingChannelService.GetChannelAsync(
                    companyStaff.CompanyId,
                    forwardConversationMessage.ChannelType,
                    forwardConversationMessage.ChannelIdentityId);

                if (messagingChannel == null)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message =
                                $"{forwardConversationMessage.ChannelType} {forwardConversationMessage.ChannelIdentityId} messaging channel not found."
                        });
                }
            }

            foreach (var forwardConversationMessage in input.ForwardConversationMessages)
            {
                var fileUrls = new List<string>();
                var fileNames = new List<string>();

                var conversation = await _appDbContext.Conversations
                    .Include(x => x.facebookUser)
                    .Include(x => x.WhatsappUser)
                    .Include(x => x.EmailAddress)
                    .Include(x => x.WeChatUser)
                    .Include(x => x.WebClient)
                    .Include(x => x.LineUser)
                    .Include(x => x.SMSUser)
                    .Include(x => x.InstagramUser)
                    .Include(x => x.ViberUser)
                    .Include(x => x.TelegramUser)
                    .Include(x => x.WhatsApp360DialogUser)
                    .Include(x => x.WhatsappCloudApiUser)
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == companyStaff.CompanyId
                            && (x.Id == forwardConversationMessage.ConversationId
                                || x.UserProfileId == forwardConversationMessage.ConversationId));

                if (conversation == null)
                {
                    try
                    {
                        conversation = await _userProfileService.GetConversationByUserProfileId(
                            companyStaff.CompanyId,
                            forwardConversationMessage.ConversationId,
                            "closed",
                            false);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[{MethodName} endpoint] Error getting conversation {ConversationOrUserProfileId}. {ExceptionMessage}",
                            nameof(ForwardMessages),
                            forwardConversationMessage.ConversationId,
                            ex.Message);

                        continue;
                    }
                }

                var messageTobeForwarded = await _appDbContext.ConversationMessages
                    .Include(x => x.UploadedFiles)
                    .Include(x => x.ExtendedMessagePayload)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == companyStaff.CompanyId
                            && x.Id == forwardConversationMessage.MessageId);

                if (messageTobeForwarded == null)
                {
                    continue;
                }

                if (messageTobeForwarded.UploadedFiles.Any())
                {
                    foreach (var file in messageTobeForwarded.UploadedFiles)
                    {
                        fileUrls.Add(
                            $"{domainName}/Message/File/Private/{file.FileId}");
                        fileNames.Add(Path.GetFileName(file.Filename));
                    }
                }

                if (messageTobeForwarded.Channel.Equals(ChannelTypes.Note, StringComparison.OrdinalIgnoreCase))
                {
                    var note = new ConversationNoteViewModel
                    {
                        MessageGroupName = companyStaff.CompanyId,
                        MessageContent = messageTobeForwarded.MessageContent,
                        ConversationId = conversation.Id,
                        MessageChecksum = Guid.NewGuid().ToString(),
                        MessageType = messageTobeForwarded.MessageType,
                        fileUrls = fileUrls.Any() ? fileUrls : null,
                        fileNames = fileNames.Any() ? fileNames : null,
                    };
                    var noteMessage = _mapper.Map<ConversationMessage>(note);

                    await _conversationMessageService.SendConversationNote(
                        companyStaff.CompanyId,
                        conversation.Id,
                        companyStaff.IdentityId,
                        noteMessage,
                        note);
                    forwardedCount += 1;
                    continue;
                }

                var conversationMessageViewModel = new ExtendedConversationMessageViewModel
                {
                    MessageContent = messageTobeForwarded.MessageContent,
                    MessageType = messageTobeForwarded.MessageType,
                    ConversationId = conversation.Id,
                    MessageGroupName = companyStaff.CompanyId,
                    Channel = forwardConversationMessage.ChannelType,
                    ChannelIdentityId = forwardConversationMessage.ChannelIdentityId,
                    fileURLs = fileUrls.Any() ? fileUrls : null,
                    fileNames = fileNames.Any() ? fileNames : null,
                };

                if (messageTobeForwarded.ExtendedMessagePayload is not null)
                {
                    conversationMessageViewModel.ExtendedMessagePayload = new ExtendedMessagePayloadViewModel
                    {
                        Channel = messageTobeForwarded.Channel,
                        ExtendedMessageType = messageTobeForwarded.ExtendedMessagePayload.ExtendedMessageType,
                        FacebookOTNTopicId = messageTobeForwarded.ExtendedMessagePayload.FacebookOTNTopicId,
                        ExtendedMessagePayloadDetail = messageTobeForwarded.ExtendedMessagePayload.ExtendedMessagePayloadDetail,
                    };
                }

                await SendMessage(conversationMessageViewModel);

                forwardedCount += 1;
            }

            return Ok(
                new ResponseViewModel
                {
                    message = $"Forwarded to {forwardedCount} people"
                });
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Message/Forward exception: {ExceptionMessage}, RequestPayload: {RequestPayload}",
                ex.Message,
                JsonConvert.SerializeObject(input));

            return BadRequest(
                new ResponseViewModel()
                {
                    message = ex.Message
                });
        }
    }

    #region Rbac methods

    private async Task<ActionResult<List<ConversationMessageResponseViewModel>>> RbacSendMessage(ExtendedConversationMessageViewModel input, Staff companyUser)
    {
        try
        {
            var user = await _userManager.FindByIdAsync(companyUser.IdentityId);

            var staff = await _accessControlAggregationService.GetStaffAccessControlAggregateAsync(companyUser);

            _logger.LogInformation(
                "Send message start for StaffIdentityId: {IdentityId} CompanyId: {CompanyId} ConversationId: {ConversationId} MessageChecksum: {MessageChecksum} " +
                "Channel: {Channel} ChannelIdentityId: {ChannelIdentityId} MessageContent: {MessageContent} MessageType: {MessageType}",
                companyUser.IdentityId,
                staff.CompanyId,
                input.ConversationId,
                input.MessageChecksum,
                input.Channel,
                input.ChannelIdentityId,
                input.MessageContent,
                input.MessageType);

            var conversation = await _conversationResolver.GetConversationByChannelAsync(
                companyUser.CompanyId,
                input.ConversationId,
                input.Channel,
                input.ChannelIdentityId);

            var conversationMessage = new ConversationMessage
            {
                CompanyId = companyUser.CompanyId,
                ConversationId = input.ConversationId,
                MessageChecksum = input.MessageChecksum,
                Channel = input.Channel,
                ReceiverDeviceUUID = input.ReceiverDeviceUUID,
                SenderId = user?.Id,
                Sender = user,
                ReceiverId = input.ReceiverId,
                EmailTo = input.EmailTo,
                EmailCC = input.EmailCC,
                Subject = input.Subject,
                MessageType = input.MessageType,
                MessageContent = input.MessageContent,
                DeliveryType = DeliveryType.Normal,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                LocalTimestamp = input.LocalTimestamp,
                Status = MessageStatus.Sending,
                IsSentFromSleekflow = true,
                QuotedMsgId = input.QuotedMsgId,
                MessageTag = input.MessageTag,
                ScheduleSentAt = input.ScheduleSentAt,
                ChannelIdentityId = input.ChannelIdentityId,
                AnalyticTags = input.AnalyticTags,
                Whatsapp360DialogExtendedMessagePayload =
                    _mapper.Map<Whatsapp360DialogExtendedMessagePayload>(input.Whatsapp360DialogExtendedMessagePayload),
                ExtendedMessagePayload = _mapper.Map<ExtendedMessagePayload>(input.ExtendedMessagePayload),
            };

            var aggregatedConversation = await _accessControlAggregationService.GetAggregatedConversationAsync(conversation);

            var canSend = _rbacConversationPermissionManager.CanSend(staff, aggregatedConversation, conversationMessage);

            if (!canSend)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "You do not have permission to send message to this conversation."
                    });
            }

            if (!conversation.IsSandbox)
            {
                // Validate Channel
                var (isValidateConversationMessageChannelValid, validateConversationMessageChannelErrorCode, validateConversationMessageChannelErrorMessage) =
                    await _channelMessageValidator.ValidateConversationMessageChannel(conversationMessage, companyUser);

                if (!isValidateConversationMessageChannelValid)
                {
                    _logger.LogError(
                        "Validate Conversation Message Channel Failed: {ErrorMessage}",
                        validateConversationMessageChannelErrorMessage);

                    return BadRequest(
                        new ResponseViewModel()
                        {
                            code = validateConversationMessageChannelErrorCode,
                            message = validateConversationMessageChannelErrorMessage
                        });
                }

                // Valid message
                var (isConversationMessageValid, validateConversationMessageErrorMessage) =
                    await _channelMessageValidator.ValidateConversationMessage(conversationMessage);

                if (!isConversationMessageValid)
                {
                    _logger.LogError(
                        "Validate Conversation Message Failed: {ErrorMessage}",
                        validateConversationMessageErrorMessage);

                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = validateConversationMessageErrorMessage
                        });
                }
            }

            // Link SleekPay Record
            if (!string.IsNullOrEmpty(input.PaymentIntentId))
            {
                conversationMessage.SleekPayRecordId = await _appDbContext.StripePaymentRecords
                    .Where(
                        x =>
                            x.CompanyId == companyUser.CompanyId
                            && x.StripePaymentIntentId == input.PaymentIntentId)
                    .Select(x => x.Id)
                    .FirstOrDefaultAsync();

                conversationMessage.DeliveryType = DeliveryType.PaymentLink;
            }


            // Fill conversationMessage Receiver
            switch (conversationMessage.Channel)
            {
                case ChannelTypes.WhatsappTwilio:
                    conversationMessage.whatsappReceiver = conversation.WhatsappUser;

                    break;
                case ChannelTypes.Whatsapp360Dialog:
                    conversationMessage.Whatsapp360DialogReceiver = conversation.WhatsApp360DialogUser;

                    break;
                case ChannelTypes.WhatsappCloudApi:
                    conversationMessage.WhatsappCloudApiReceiver = conversation.WhatsappCloudApiUser;

                    break;
                case "naive":
                case ChannelTypes.Email:
                    conversationMessage.Channel = ChannelTypes.Email;
                    conversationMessage.EmailFrom = conversation.EmailAddress;

                    break;
                case ChannelTypes.LiveChat:
                    conversationMessage.WebClientReceiver = conversation.WebClient;

                    break;
                case ChannelTypes.Wechat:
                    conversationMessage.WeChatReceiver = conversation.WeChatUser;

                    break;
                case ChannelTypes.Line:
                    conversationMessage.LineReceiver = conversation.LineUser;

                    break;
                case ChannelTypes.Viber:
                    conversationMessage.ViberReceiver = conversation.ViberUser;

                    break;
                case ChannelTypes.Telegram:
                    conversationMessage.TelegramReceiver = conversation.TelegramUser;

                    break;
                case ChannelTypes.Sms:
                    conversationMessage.SMSReceiver = conversation.SMSUser;

                    break;
                case ChannelTypes.Facebook:
                    conversationMessage.facebookReceiver = conversation.facebookUser;

                    break;
                case ChannelTypes.Instagram:
                    conversationMessage.InstagramReceiver = conversation.InstagramUser;

                    break;
                case ChannelTypes.LiveChatV2:
                    // Since LiveChatV2 enables users to have multiple live chat channels, new senders should not directly reference the conversation.
                    var liveChatV2Sender =
                        await _appDbContext.SenderWebClientSenders
                            .Where(
                                sender => sender.CompanyId == companyUser.CompanyId &&
                                          sender.ConversationId == conversation.Id &&
                                          sender.ChannelIdentityId == conversationMessage.ChannelIdentityId)
                            .FirstOrDefaultAsync();

                    conversationMessage.WebClientReceiver = liveChatV2Sender;
                    break;
            }

            // Quick Reply Text - File Handling
            try
            {
                List<ConversationMessage> results = new List<ConversationMessage>();

                var textConversationMessage = new ConversationMessage
                {
                    Id = 0,
                    CompanyId = companyUser.CompanyId,
                    ConversationId = input.ConversationId,
                    Conversation = conversation,
                    MessageChecksum = null,
                    Channel = input.Channel,
                    ReceiverDeviceUUID = input.ReceiverDeviceUUID,
                    SenderId = user?.Id,
                    Sender = user,
                    ReceiverId = input.ReceiverId,
                    EmailTo = input.EmailTo,
                    EmailCC = input.EmailCC,
                    Subject = input.Subject,
                    MessageType = "text",
                    MessageContent = input.MessageContent,
                    DeliveryType = DeliveryType.Normal,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                    LocalTimestamp = input.LocalTimestamp,
                    Status = MessageStatus.Sending,
                    IsSentFromSleekflow = true,
                    QuotedMsgId = input.QuotedMsgId,
                    MessageTag = input.MessageTag,
                    ScheduleSentAt = input.ScheduleSentAt,
                    ChannelIdentityId = input.ChannelIdentityId,
                    AnalyticTags = input.AnalyticTags,
                    UploadedFiles = new List<UploadedFile>(),
                    Whatsapp360DialogExtendedMessagePayload =
                        _mapper.Map<Whatsapp360DialogExtendedMessagePayload>(
                            input.Whatsapp360DialogExtendedMessagePayload),
                    ExtendedMessagePayload = _mapper.Map<ExtendedMessagePayload>(input.ExtendedMessagePayload),
                };

                if (input.QuickReplyId.HasValue)
                {
                    var quickReply = await _appDbContext.CompanyQuickReplies
                        .Include(x => x.QuickReplyFile)
                        .Include(x => x.CompanyQuickReplyLinguals)
                        .FirstOrDefaultAsync(
                            x =>
                                x.Id == input.QuickReplyId.Value
                                && x.CompanyId == staff.CompanyId);

                    if (quickReply.QuickReplyFile != null)
                    {
                        conversationMessage.DeliveryType = DeliveryType.QuickReply;
                        conversationMessage.MessageType = "file";

                        var provider = new FileExtensionContentTypeProvider();
                        string contentType;

                        var domainName = _configuration.GetValue<string>("Values:DomainName");

                        var url =
                            $"{domainName}/company/quickReply/attachment/private/{quickReply.QuickReplyFile.QuickReplyFileId}";

                        var fileURLMessages = new List<FileURLMessage>();

                        if (!provider.TryGetContentType(url, out contentType))
                        {
                            contentType = "application/octet-stream";
                        }

                        switch (conversationMessage.Channel)
                        {
                            case ChannelTypes.Facebook:
                            case ChannelTypes.Instagram:
                            case ChannelTypes.Wechat:
                                conversationMessage.MessageContent = null;

                                break;
                        }

                        fileURLMessages.Add(
                            new FileURLMessage
                            {
                                FileName = Path.GetFileName(quickReply.QuickReplyFile.Filename),
                                FileURL = url,
                                MIMEType = contentType
                            });

                        results = (await _conversationMessageService.SendFileMessageByFBURL(
                                conversation,
                                conversationMessage,
                                fileURLMessages))
                            .ToList();
                    }
                    else
                    {
                        textConversationMessage = conversationMessage;
                    }

                    IEnumerable<ConversationMessage> textMessageResult;


                    if (!string.IsNullOrEmpty(textConversationMessage.MessageContent))
                    {
                        switch (conversationMessage.Channel)
                        {
                            case ChannelTypes.Facebook:
                                textConversationMessage.facebookReceiver = conversation.facebookUser;

                                textMessageResult = (await _conversationMessageService.SendMessage(
                                        conversation,
                                        textConversationMessage))
                                    .ToList();

                                results.AddRange(textMessageResult);

                                break;
                            case ChannelTypes.Instagram:
                                textConversationMessage.InstagramReceiver = conversation.InstagramUser;

                                textMessageResult = (await _conversationMessageService.SendMessage(
                                        conversation,
                                        textConversationMessage))
                                    .AsEnumerable();

                                results.AddRange(textMessageResult);

                                break;
                            case ChannelTypes.Wechat:
                                textConversationMessage.WeChatReceiver = conversation.WeChatUser;

                                textMessageResult = (await _conversationMessageService.SendMessage(
                                        conversation,
                                        textConversationMessage))
                                    .ToList();

                                results.AddRange(textMessageResult);

                                break;
                        }
                    }
                }
                else if (input.fileURLs?.Count > 0)
                {
                    conversationMessage.MessageType = "file";

                    var provider = new FileExtensionContentTypeProvider();
                    string contentType;

                    var fileURLMessages = new List<FileURLMessage>();

                    for (var i = 0; i < input.fileURLs.Count; i++)
                    {
                        var url = input.fileURLs[i];
                        var fileName = input.fileNames[i];

                        if (!provider.TryGetContentType(url, out contentType))
                        {
                            contentType = "application/octet-stream";
                        }

                        fileURLMessages.Add(
                            new FileURLMessage
                            {
                                FileName = fileName,
                                FileURL = url,
                                MIMEType = contentType
                            });
                    }

                    switch (conversationMessage.Channel)
                    {
                        case ChannelTypes.Facebook:
                        case ChannelTypes.Instagram:
                            conversationMessage.MessageContent = null;

                            break;
                    }

                    results = (await _conversationMessageService.SendFileMessageByFBURL(
                            conversation,
                            conversationMessage,
                            fileURLMessages))
                        .ToList();

                    if (!string.IsNullOrEmpty(textConversationMessage.MessageContent))
                    {
                        switch (conversationMessage.Channel)
                        {
                            case ChannelTypes.Facebook:
                                textConversationMessage.MessageUniqueID = null;
                                textConversationMessage.Id = 0;
                                textConversationMessage.MessageType = "text";

                                textConversationMessage.SenderId = user.Id;
                                textConversationMessage.Sender = user;
                                textConversationMessage.IsSentFromSleekflow = true;

                                textConversationMessage.facebookReceiver = conversation.facebookUser;
                                textConversationMessage.ConversationId = conversation.Id;
                                textConversationMessage.Conversation = conversation;

                                var results2 = (await _conversationMessageService.SendMessage(
                                        conversation,
                                        textConversationMessage))
                                    .ToList();

                                results.AddRange(results2);

                                break;
                            case ChannelTypes.Instagram:
                                break;
                        }
                    }
                }
                else
                {
                    results = (await _conversationMessageService.SendMessage(
                            conversation,
                            conversationMessage))
                        .ToList();
                }

                var conversationMessagesVM = _mapper.Map<List<ConversationMessageResponseViewModel>>(results);
                await _sleekPayService.AddSleekPayRecord(conversationMessagesVM);

                return Ok(conversationMessagesVM);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Message/Send (inner) exception: {ExceptionMessage}, Request payload: {RequestPayload}",
                    ex.Message,
                    JsonConvert.SerializeObject(input));

                return BadRequest(ex.Message);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Message/Send (outer) exception: {ExceptionMessage}, Request payload: {RequestPayload}",
                ex.Message,
                JsonConvert.SerializeObject(input));

            return BadRequest(
                new ResponseViewModel
                {
                    message = $"{ex.ToString()}"
                });
        }
    }

    private async Task<ActionResult<List<ConversationMessageResponseViewModel>>> RbacV2SendFileMessage(ConversationMessageViewModel input, Staff companyUser)
    {
        var user = await _userManager.FindByIdAsync(companyUser.IdentityId);

        var staff = await _accessControlAggregationService.GetStaffAccessControlAggregateAsync(companyUser);

        _logger.LogInformation(
            "Send file message start for StaffIdentityId: {IdentityId} CompanyId: {CompanyId} ConversationId: {ConversationId} MessageChecksum: {MessageChecksum} " +
            "Channel: {Channel} ChannelIdentityId: {ChannelIdentityId} MessageContent: {MessageContent} MessageType: {MessageType}",
            companyUser.IdentityId,
            companyUser.CompanyId,
            input.ConversationId,
            input.MessageChecksum,
            input.Channel,
            input.ChannelIdentityId,
            input.MessageContent,
            input.MessageType);

        var conversation = await _conversationResolver.GetConversationByChannelAsync(
            companyUser.CompanyId,
            input.ConversationId,
            input.Channel,
            input.ChannelIdentityId);

        var aggregatedConversation = await _accessControlAggregationService.GetAggregatedConversationAsync(conversation);

        var canSend = _rbacConversationPermissionManager.CanSend(staff, aggregatedConversation);

        if (!canSend)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "You do not have permission to send message to this conversation."
                });
        }

        var conversationMessage = new ConversationMessage
        {
            CompanyId = companyUser.CompanyId,
            ConversationId = input.ConversationId,
            MessageChecksum = input.MessageChecksum,
            Channel = input.Channel,
            ReceiverDeviceUUID = input.ReceiverDeviceUUID,
            SenderId = user?.Id,
            Sender = user,
            ReceiverId = input.ReceiverId,
            EmailTo = input.EmailTo,
            EmailCC = input.EmailCC,
            Subject = input.Subject,
            MessageType = input.MessageType,
            MessageContent = input.MessageContent,
            DeliveryType = DeliveryType.Normal,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
            LocalTimestamp = input.LocalTimestamp,
            Status = MessageStatus.Sending,
            IsSentFromSleekflow = true,
            QuotedMsgId = input.QuotedMsgId,
            MessageTag = input.MessageTag,
            ScheduleSentAt = input.ScheduleSentAt,
            ChannelIdentityId = input.ChannelIdentityId,
        };

        if (!conversation.IsSandbox)
        {
            // Validate Channel
            var (isValidateConversationMessageChannelValid, validateConversationMessageChannelErrorCode, validateConversationMessageChannelErrorMessage) =
                await _channelMessageValidator.ValidateConversationMessageChannel(conversationMessage, companyUser);

            if (!isValidateConversationMessageChannelValid)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        code = validateConversationMessageChannelErrorCode,
                        message = validateConversationMessageChannelErrorMessage
                    });
            }

            // Valid message
            var (isConversationMessageValid, validateConversationMessageErrorMessage) =
                await _channelMessageValidator.ValidateConversationMessage(conversationMessage);

            if (!isConversationMessageValid)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = validateConversationMessageErrorMessage
                    });
            }
        }

        // Link SleekPay Record
        if (!string.IsNullOrEmpty(input.PaymentIntentId))
        {
            conversationMessage.SleekPayRecordId = await _appDbContext.StripePaymentRecords
                .Where(
                    x =>
                        x.CompanyId == companyUser.CompanyId
                        && x.StripePaymentIntentId == input.PaymentIntentId)
                .Select(x => x.Id)
                .FirstOrDefaultAsync();

            conversationMessage.DeliveryType = DeliveryType.PaymentLink;
        }

        switch (conversationMessage.Channel)
        {
            case ChannelTypes.WhatsappTwilio:
                conversationMessage.whatsappReceiver = conversation.WhatsappUser;

                break;
            case ChannelTypes.Whatsapp360Dialog:
                conversationMessage.Whatsapp360DialogReceiver = conversation.WhatsApp360DialogUser;

                break;
            case ChannelTypes.WhatsappCloudApi:
                conversationMessage.WhatsappCloudApiReceiver = conversation.WhatsappCloudApiUser;

                break;
            case "naive":
            case ChannelTypes.Email:
                conversationMessage.Channel = ChannelTypes.Email;
                conversationMessage.EmailFrom = conversation.EmailAddress;

                break;
            case ChannelTypes.LiveChat:
                conversationMessage.WebClientReceiver = conversation.WebClient;

                break;
            case ChannelTypes.Wechat:
                conversationMessage.WeChatReceiver = conversation.WeChatUser;

                break;
            case ChannelTypes.Line:
                conversationMessage.LineReceiver = conversation.LineUser;

                break;
            case ChannelTypes.Viber:
                conversationMessage.ViberReceiver = conversation.ViberUser;

                break;
            case ChannelTypes.Telegram:
                conversationMessage.TelegramReceiver = conversation.TelegramUser;

                break;
            case ChannelTypes.Sms:
                conversationMessage.SMSReceiver = conversation.SMSUser;

                break;
            case ChannelTypes.Facebook:
                conversationMessage.facebookReceiver = conversation.facebookUser;

                break;
            case ChannelTypes.Instagram:
                conversationMessage.InstagramReceiver = conversation.InstagramUser;

                break;
            case ChannelTypes.LiveChatV2:
                // Since LiveChatV2 enables users to have multiple live chat channels, new senders should not directly reference the conversation.
                var liveChatV2Sender =
                    await _appDbContext.SenderWebClientSenders
                        .Where(
                            sender => sender.CompanyId == companyUser.CompanyId &&
                                      sender.ConversationId == conversation.Id &&
                                      sender.ChannelIdentityId == conversationMessage.ChannelIdentityId)
                        .FirstOrDefaultAsync();
                conversationMessage.WebClientReceiver = liveChatV2Sender;
                break;
        }

        try
        {
            List<ConversationMessage> results = new List<ConversationMessage>();
            List<ConversationMessage> textMessageResult = new List<ConversationMessage>();

            if (conversationMessage.Channel == ChannelTypes.Whatsapp360Dialog)
            {
                var files = input.files;
                input.files = null;

                for (var index = 0; index < files.Count; index++)
                {
                    var file = files[index];

                    // Retrieve the conversation again, and plug that again
                    var whatsapp360ConversationMessageViewModel = input.DeepClone();

                    var whatsapp360ConversationMessage = new ConversationMessage
                    {
                        CompanyId = companyUser.CompanyId,
                        ConversationId = input.ConversationId,
                        MessageChecksum = input.MessageChecksum,
                        Channel = input.Channel,
                        ReceiverDeviceUUID = input.ReceiverDeviceUUID,
                        SenderId = user.Id,
                        Sender = user,
                        ReceiverId = input.ReceiverId,
                        MessageType = input.MessageType,
                        MessageContent = input.MessageContent,
                        DeliveryType = DeliveryType.Normal,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow,
                        Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                        LocalTimestamp = input.LocalTimestamp,
                        Status = MessageStatus.Sending,
                        IsSentFromSleekflow = true,
                        QuotedMsgId = input.QuotedMsgId,
                        MessageTag = input.MessageTag,
                        ScheduleSentAt = input.ScheduleSentAt,
                        ChannelIdentityId = input.ChannelIdentityId,
                    };

                    whatsapp360ConversationMessage.MessageChecksum = index == 0
                        ? input.MessageChecksum
                        : Guid.NewGuid().ToString();

                    whatsapp360ConversationMessage.MessageContent =
                        index == 0 ? input.MessageContent : null;

                    whatsapp360ConversationMessage.SenderId = conversationMessage.SenderId;

                    whatsapp360ConversationMessage.Whatsapp360DialogReceiver =
                        conversation.WhatsApp360DialogUser;

                    whatsapp360ConversationMessageViewModel.files = new List<IFormFile>
                    {
                        file
                    };

                    var whatsapp360Results = await _conversationMessageService.SendFileMessage(
                        conversation,
                        whatsapp360ConversationMessage,
                        whatsapp360ConversationMessageViewModel);

                    foreach (var whatsapp360Result in whatsapp360Results)
                    {
                        results.Add(whatsapp360Result);
                    }
                }
            }
            else if (conversationMessage.Channel == ChannelTypes.WhatsappCloudApi)
            {
                var files = input.files;
                input.files = null;

                for (var index = 0; index < files.Count; index++)
                {
                    var file = files[index];
                    var whatsappCloudApiConversationMessageViewModel = input.DeepClone();

                    var whatsappCloudApiConversationMessage = new ConversationMessage
                    {
                        CompanyId = companyUser.CompanyId,
                        ConversationId = input.ConversationId,
                        MessageChecksum = input.MessageChecksum,
                        Channel = input.Channel,
                        ReceiverDeviceUUID = input.ReceiverDeviceUUID,
                        SenderId = user.Id,
                        Sender = user,
                        ReceiverId = input.ReceiverId,
                        MessageType = input.MessageType,
                        MessageContent = input.MessageContent,
                        DeliveryType = DeliveryType.Normal,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow,
                        Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                        LocalTimestamp = input.LocalTimestamp,
                        Status = MessageStatus.Sending,
                        IsSentFromSleekflow = true,
                        QuotedMsgId = input.QuotedMsgId,
                        MessageTag = input.MessageTag,
                        ScheduleSentAt = input.ScheduleSentAt,
                        ChannelIdentityId = input.ChannelIdentityId,
                    };

                    whatsappCloudApiConversationMessage.MessageChecksum = index == 0
                        ? input.MessageChecksum
                        : Guid.NewGuid().ToString();

                    whatsappCloudApiConversationMessage.MessageContent =
                        index == 0 ? input.MessageContent : null;

                    whatsappCloudApiConversationMessage.SenderId = conversationMessage.SenderId;

                    whatsappCloudApiConversationMessage.WhatsappCloudApiReceiver =
                        conversation.WhatsappCloudApiUser;

                    whatsappCloudApiConversationMessageViewModel.files = new List<IFormFile>
                    {
                        file
                    };

                    var whatsappCloudApiResults = await _conversationMessageService.SendFileMessage(
                        conversation,
                        whatsappCloudApiConversationMessage,
                        whatsappCloudApiConversationMessageViewModel);

                    foreach (var whatsappCloudApiResult in whatsappCloudApiResults)
                    {
                        results.Add(whatsappCloudApiResult);
                    }
                }
            }
            else if (conversationMessage.Channel == ChannelTypes.Facebook)
            {
                conversationMessage.MessageContent = null;

                results = (await _conversationMessageService.SendFileMessage(
                    conversation,
                    conversationMessage,
                    input)).ToList();

                if (!string.IsNullOrEmpty(input.MessageContent))
                {
                    var textConversationMessage = new ConversationMessage
                    {
                        CompanyId = companyUser.CompanyId,
                        ConversationId = input.ConversationId,
                        MessageChecksum = input.MessageChecksum,
                        Channel = input.Channel,
                        ReceiverDeviceUUID = input.ReceiverDeviceUUID,
                        SenderId = user.Id,
                        Sender = user,
                        ReceiverId = input.ReceiverId,
                        MessageType = input.MessageType,
                        MessageContent = input.MessageContent,
                        DeliveryType = DeliveryType.Normal,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow,
                        Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                        LocalTimestamp = input.LocalTimestamp,
                        Status = MessageStatus.Sending,
                        IsSentFromSleekflow = true,
                        QuotedMsgId = input.QuotedMsgId,
                        MessageTag = input.MessageTag,
                        ScheduleSentAt = input.ScheduleSentAt,
                        ChannelIdentityId = input.ChannelIdentityId,
                    };

                    try
                    {
                        textConversationMessage.MessageUniqueID = null;
                        textConversationMessage.Id = 0;
                        textConversationMessage.MessageChecksum = Guid.NewGuid().ToString();
                        textConversationMessage.UploadedFiles = new List<UploadedFile>(); // Empty the files?
                        textConversationMessage.MessageType = "text";

                        textConversationMessage.SenderId = user.Id;
                        textConversationMessage.Sender = user;
                        textConversationMessage.IsSentFromSleekflow = true;

                        textConversationMessage.facebookReceiver = conversation.facebookUser;
                        textConversationMessage.Conversation = conversation;

                        textMessageResult = (await _conversationMessageService.SendMessage(
                                conversation,
                                textConversationMessage))
                            .ToList();

                        results.AddRange(textMessageResult);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[{MethodName} endpoint] Send message to Facebook error: {ExceptionMessage}",
                            nameof(V2SendFileMessage),
                            ex.Message);
                    }
                }
            }
            else if (conversationMessage.Channel == ChannelTypes.Instagram)
            {
                conversationMessage.MessageContent = null;

                results = (await _conversationMessageService.SendFileMessage(
                    conversation,
                    conversationMessage,
                    input)).ToList();

                if (!string.IsNullOrEmpty(input.MessageContent))
                {
                    var textConversationMessage = new ConversationMessage
                    {
                        CompanyId = companyUser.CompanyId,
                        ConversationId = input.ConversationId,
                        MessageChecksum = input.MessageChecksum,
                        Channel = input.Channel,
                        ReceiverDeviceUUID = input.ReceiverDeviceUUID,
                        SenderId = user?.Id,
                        Sender = user,
                        ReceiverId = input.ReceiverId,
                        MessageType = input.MessageType,
                        MessageContent = input.MessageContent,
                        DeliveryType = DeliveryType.Normal,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow,
                        Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                        LocalTimestamp = input.LocalTimestamp,
                        Status = MessageStatus.Sending,
                        IsSentFromSleekflow = true,
                        QuotedMsgId = input.QuotedMsgId,
                        MessageTag = input.MessageTag,
                        ScheduleSentAt = input.ScheduleSentAt,
                        ChannelIdentityId = input.ChannelIdentityId,
                    };

                    try
                    {
                        textConversationMessage.MessageUniqueID = null;
                        textConversationMessage.Id = 0;
                        textConversationMessage.MessageChecksum = Guid.NewGuid().ToString();
                        textConversationMessage.UploadedFiles = new List<UploadedFile>();
                        textConversationMessage.MessageType = "text";

                        textConversationMessage.InstagramReceiver = conversation.InstagramUser;
                        textConversationMessage.ConversationId = conversation.Id;
                        textConversationMessage.Conversation = conversation;

                        var testMessageResult = (await _conversationMessageService.SendMessage(
                                conversation,
                                textConversationMessage))
                            .AsEnumerable();

                        results.AddRange(testMessageResult);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[{MethodName} endpoint] Send message to Instagram error: {ExceptionMessage}",
                            nameof(V2SendFileMessage),
                            ex.Message);
                    }
                }
            }
            else if (conversationMessage.Channel == ChannelTypes.Wechat)
            {
                // send file first
                conversationMessage.MessageContent = null;

                results = (await _conversationMessageService.SendFileMessage(
                    conversation,
                    conversationMessage,
                    input)).ToList();

                // send text if any
                var textConversationMessage = new ConversationMessage
                {
                    CompanyId = companyUser.CompanyId,
                    ConversationId = input.ConversationId,
                    MessageChecksum = input.MessageChecksum,
                    Channel = input.Channel,
                    ReceiverDeviceUUID = input.ReceiverDeviceUUID,
                    SenderId = user?.Id,
                    Sender = user,
                    ReceiverId = input.ReceiverId,
                    MessageType = input.MessageType,
                    MessageContent = input.MessageContent,
                    DeliveryType = DeliveryType.Normal,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                    LocalTimestamp = input.LocalTimestamp,
                    Status = MessageStatus.Sending,
                    IsSentFromSleekflow = true,
                    QuotedMsgId = input.QuotedMsgId,
                    MessageTag = input.MessageTag,
                    ScheduleSentAt = input.ScheduleSentAt,
                    ChannelIdentityId = input.ChannelIdentityId,
                };

                if (textConversationMessage.MessageContent != null)
                {
                    try
                    {
                        textConversationMessage.MessageUniqueID = null;
                        textConversationMessage.Id = 0;
                        textConversationMessage.MessageChecksum = Guid.NewGuid().ToString();
                        textConversationMessage.UploadedFiles = new List<UploadedFile>();
                        textConversationMessage.MessageType = "text";
                        textConversationMessage.SenderId = user.Id;
                        textConversationMessage.Sender = user;
                        textConversationMessage.IsSentFromSleekflow = true;

                        textConversationMessage.WeChatReceiver = conversation.WeChatUser;
                        textConversationMessage.ConversationId = conversation.Id;
                        textConversationMessage.Conversation = conversation;

                        textMessageResult = (await _conversationMessageService.SendMessage(
                                conversation,
                                textConversationMessage))
                            .ToList();

                        results.AddRange(textMessageResult);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[{MethodName} endpoint] Send message to WeChat error: {ExceptionMessage}",
                            nameof(V2SendFileMessage),
                            ex.Message);
                    }
                }
            }
            else
            {
                results = (await _conversationMessageService.SendFileMessage(
                        conversation,
                        conversationMessage,
                        input))
                    .ToList();
            }

            var conversationMessagesVM = _mapper.Map<List<ConversationMessageResponseViewModel>>(results);

            return Ok(conversationMessagesVM);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName} endpoint] error: {ExceptionMessage}",
                nameof(V2SendFileMessage),
                ex.Message);

            return BadRequest(ex.Message);
        }
    }

    private async Task<ActionResult<List<ConversationMessageResponseViewModel>>> RbacForwardMessages(ForwardMessageInput input, Staff companyUser)
    {
        try
        {
            var staff = await _accessControlAggregationService.GetStaffAccessControlAggregateAsync(companyUser);

            var forwardedCount = 0;

            foreach (var forwardConversationMessage in input.ForwardConversationMessages)
            {
                var conversation = await _conversationResolver.GetConversationByChannelAsync(
                    companyUser.CompanyId,
                    forwardConversationMessage.ConversationId,
                    forwardConversationMessage.ChannelType,
                    forwardConversationMessage.ChannelIdentityId);

                var aggregatedConversation = await _accessControlAggregationService.GetAggregatedConversationAsync(conversation);

                var canSend = _rbacConversationPermissionManager.CanSend(staff, aggregatedConversation);

                if (!canSend)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "You do not have permission to send message to this conversation."
                        });
                }

                var messagingChannel = await _messagingChannelService.GetChannelAsync(
                    companyUser.CompanyId,
                    forwardConversationMessage.ChannelType,
                    forwardConversationMessage.ChannelIdentityId);

                if (messagingChannel == null)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message =
                                $"{forwardConversationMessage.ChannelType} {forwardConversationMessage.ChannelIdentityId} messaging channel not found."
                        });
                }
            }

            foreach (var forwardConversationMessage in input.ForwardConversationMessages)
            {
                var conversation = await _appDbContext.Conversations
                    .Include(x => x.facebookUser)
                    .Include(x => x.WhatsappUser)
                    .Include(x => x.EmailAddress)
                    .Include(x => x.WeChatUser)
                    .Include(x => x.WebClient)
                    .Include(x => x.LineUser)
                    .Include(x => x.SMSUser)
                    .Include(x => x.InstagramUser)
                    .Include(x => x.ViberUser)
                    .Include(x => x.TelegramUser)
                    .Include(x => x.WhatsApp360DialogUser)
                    .Include(x => x.WhatsappCloudApiUser)
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == companyUser.CompanyId
                            && (x.Id == forwardConversationMessage.ConversationId
                                || x.UserProfileId == forwardConversationMessage.ConversationId));

                if (conversation == null)
                {
                    try
                    {
                        conversation = await _userProfileService.GetConversationByUserProfileId(
                            companyUser.CompanyId,
                            forwardConversationMessage.ConversationId,
                            "closed",
                            false);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[{MethodName} endpoint] Error getting conversation {ConversationOrUserProfileId}. {ExceptionMessage}",
                            nameof(ForwardMessages),
                            forwardConversationMessage.ConversationId,
                            ex.Message);

                        continue;
                    }
                }

                var messageTobeForwarded = await _appDbContext.ConversationMessages
                    .Include(x => x.UploadedFiles)
                    .Include(x => x.ExtendedMessagePayload)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == companyUser.CompanyId
                            && x.Id == forwardConversationMessage.MessageId);

                if (messageTobeForwarded == null)
                {
                    continue;
                }

                var conversationMessageViewModel = new ExtendedConversationMessageViewModel
                {
                    MessageContent = messageTobeForwarded.MessageContent,
                    MessageType = messageTobeForwarded.MessageType,
                    ConversationId = conversation.Id,
                    MessageGroupName = companyUser.CompanyId,
                    Channel = forwardConversationMessage.ChannelType,
                    ChannelIdentityId = forwardConversationMessage.ChannelIdentityId,
                };

                if (messageTobeForwarded.UploadedFiles?.Count > 0)
                {
                    conversationMessageViewModel.fileURLs = new List<string>();
                    conversationMessageViewModel.fileNames = new List<string>();

                    var domainName = _configuration.GetValue<String>("Values:DomainName");

                    foreach (var file in messageTobeForwarded.UploadedFiles)
                    {
                        conversationMessageViewModel.fileURLs.Add(
                            $"{domainName}/Message/File/Private/{file.FileId}");
                        var fileName = Path.GetFileName(file.Filename);
                        conversationMessageViewModel.fileNames.Add(fileName);
                    }
                }

                if (messageTobeForwarded.ExtendedMessagePayload is not null)
                {
                    conversationMessageViewModel.ExtendedMessagePayload = new ExtendedMessagePayloadViewModel
                    {
                        Channel = messageTobeForwarded.Channel,
                        ExtendedMessageType = messageTobeForwarded.ExtendedMessagePayload.ExtendedMessageType,
                        FacebookOTNTopicId = messageTobeForwarded.ExtendedMessagePayload.FacebookOTNTopicId,
                        ExtendedMessagePayloadDetail = messageTobeForwarded.ExtendedMessagePayload.ExtendedMessagePayloadDetail,
                    };
                }

                await SendMessage(conversationMessageViewModel);

                forwardedCount += 1;
            }

            return Ok(
                new ResponseViewModel
                {
                    message = $"Forwarded to {forwardedCount} people"
                });
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Message/Forward exception: {ExceptionMessage}, RequestPayload: {RequestPayload}",
                ex.Message,
                JsonConvert.SerializeObject(input));

            return BadRequest(
                new ResponseViewModel()
                {
                    message = ex.Message
                });
        }
    }

    #endregion
}