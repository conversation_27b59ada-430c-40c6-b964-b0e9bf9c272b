using System.Collections.Generic;
using Newtonsoft.Json;
using Travis_backend.ChannelDomain.Models.LiveChatV2;
using Travis_backend.ContactDomain.ViewModels;

namespace Travis_backend.ChannelDomain.Extensions.LiveChatV2Extensions;

public static class LiveChatV2ContactFormExtensions
{
    /// <summary>
    /// Gets the value of the 'name' field from the contact form.
    /// </summary>
    /// <param name="formObject">The contact form instance.</param>
    /// <returns>The value of the name field, or null if not found.</returns>
    public static string GetName(this LiveChatV2ContactFormObject formObject)
    {
        return formObject?.Fields?.Find(field => field.Id == "name")?.Value;
    }

    /// <summary>
    /// Gets the value of the 'email' field from the contact form.
    /// </summary>
    /// <param name="formObject">The contact form instance.</param>
    /// <returns>The value of the email field, or null if not found.</returns>
    public static string GetEmail(this LiveChatV2ContactFormObject formObject)
    {
        return formObject?.Fields?.Find(field => field.Id == "email")?.Value;
    }

    /// <summary>
    /// Gets the value of the 'phone' field from the contact form.
    /// </summary>
    /// <param name="formObject">The contact form instance.</param>
    /// <returns>The value of the phone field, or null if not found.</returns>
    public static string GetPhone(this LiveChatV2ContactFormObject formObject)
    {
        return formObject?.Fields?.Find(field => field.Id == "phone")?.Value;
    }

    public static bool GetDuplicatedContactFlag(this LiveChatV2ContactFormObject formObject)
    {
        return formObject?.Fields?.Find(field => field.Id == "duplicated_contact")?.Value == "true";
    }

    public static void SetDuplicatedContactFlag(this LiveChatV2ContactFormObject formObject, bool isDuplicatedContact)
    {
        var duplicatedContactField = new LiveChatV2ContactFormField
        {
            DefaultValue = "false",
            Value = isDuplicatedContact ? "true" : "false",
            Description = null,
            Id = "duplicated_contact",
            Label = "duplicated_contact",
            Type = "boolean",
            Required = false,
            Placeholder = null,
        };

        formObject.Fields.Add(duplicatedContactField);
    }

    public static Dictionary<string, object> ToMetadata(this LiveChatV2ContactFormObject formObject)
    {
        var metadata = new Dictionary<string, object>();

        if (formObject == null)
        {
            return metadata;
        }

        var jsonString = JsonConvert.SerializeObject(formObject);
        var formAsDictionary = JsonConvert.DeserializeObject<Dictionary<string, object>>(jsonString);
        metadata["contact_form"] = formAsDictionary;

        return metadata;
    }

    public static List<AddCustomFieldsViewModel> ToAddCustomFieldsViewModels(this LiveChatV2ContactFormObject formObject)
    {
        var customFieldsViewModels = new List<AddCustomFieldsViewModel>();
        var phone = formObject.GetPhone();
        var email = formObject.GetEmail();
        var name = formObject.GetName();

        if (!string.IsNullOrEmpty(name))
        {
            customFieldsViewModels.Add(new AddCustomFieldsViewModel
            {
                CustomFieldName = "firstname",
                CustomValue = formObject.GetName()
            });
        }

        if (!string.IsNullOrEmpty(email))
        {
            customFieldsViewModels.Add(new AddCustomFieldsViewModel
            {
                CustomFieldName = "email",
                CustomValue = email
            });
        }

        if (!string.IsNullOrEmpty(phone))
        {
            customFieldsViewModels.Add(new AddCustomFieldsViewModel
            {
                CustomFieldName = "phone",
                CustomValue = phone.TrimStart('+')
            });
        }

        return customFieldsViewModels;
    }
}