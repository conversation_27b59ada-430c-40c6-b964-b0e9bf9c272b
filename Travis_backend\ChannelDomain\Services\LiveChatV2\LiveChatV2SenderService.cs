using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Sleekflow.Apis.UserEventHub.Api;
using Sleekflow.Apis.UserEventHub.Model;
using Travis_backend.Cache;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.ChannelDomain.Extensions.LiveChatV2Extensions;
using Travis_backend.ChannelDomain.Models.LiveChatV2;
using Travis_backend.ChannelDomain.Repositories;
using Travis_backend.ChannelDomain.ViewModels.LiveChatV2;
using Travis_backend.Database.Services;
using Travis_backend.**********************;
using Travis_backend.MessageDomain.Models;
using Travis_backend.Services;

namespace Travis_backend.ChannelDomain.Services.LiveChatV2;

public interface ILiveChatV2SenderService
{
    public Task<CreateLiveChatV2SenderOutput> CreateSenderAsync(CreateLiveChatV2SenderInput input);

    public Task<UpdateLiveChatV2SenderOutput> UpdateSenderAsync(UpdateLiveChatV2SenderInput input);

    public Task<GetLiveChatV2SenderOutput> GetPublicSenderAsync(string id);

    public Task<GetLiveChatV2SenderOutput> GetSenderAsync(GetLiveChatV2SenderInput input);

}

public class LiveChatV2SenderService : ILiveChatV2SenderService
{
    private readonly IDbContextService _dbContextService;
    private readonly ILiveChatV2SenderReadOnlyRepository _readOnlyRepository;
    private readonly IIpLocationService _ipLocationService;
    private readonly ICacheManagerService _cacheManagerService;
    private readonly ILiveChatTrackingEventsApi _liveChatTrackingEventsApi;
    private readonly ILogger<LiveChatV2SenderService> _logger;

    public LiveChatV2SenderService(
        IDbContextService dbContextService,
        ILiveChatV2SenderReadOnlyRepository readOnlyRepository,
        IIpLocationService ipLocationService,
        ICacheManagerService cacheManagerService,
        ILiveChatTrackingEventsApi liveChatTrackingEventsApi,
        ILogger<LiveChatV2SenderService> logger)
    {
        _dbContextService = dbContextService;
        _readOnlyRepository = readOnlyRepository;
        _ipLocationService = ipLocationService;
        _cacheManagerService = cacheManagerService;
        _liveChatTrackingEventsApi = liveChatTrackingEventsApi;
        _logger = logger;
    }

    public async Task<CreateLiveChatV2SenderOutput> CreateSenderAsync(CreateLiveChatV2SenderInput input)
    {
        var dbContext = _dbContextService.GetDbContext();

        var company = await dbContext.CompanyCompanies.Where(company => company.Id == input.CompanyId).FirstOrDefaultAsync();

        if (company is null)
        {
            return new CreateLiveChatV2SenderOutput
            {
                ExitCode = ExitCode.Failure, Message = $"Company {input.CompanyId} not found"
            };
        }

        var webClientUuid = Guid.NewGuid().ToString();

        var sender = new WebClientSender
        {
            IPAddress = input.IpAddress,
            WebClientUUID = webClientUuid,
            ChannelIdentityId = input.LiveChatId,
            CompanyId = input.CompanyId,
            Metadata = input.Metadata,
            Name = $"Visitor #{webClientUuid[^ Math.Min(webClientUuid.Length, 4)..]}",
            locale = null,
        };

        dbContext.SenderWebClientSenders.Add(sender);
        await dbContext.SaveChangesAsync();
        return new CreateLiveChatV2SenderOutput
        {
            Id = sender.WebClientUUID,
            LiveChatId = sender.ChannelIdentityId,
            CompanyId = sender.CompanyId,
            Metadata = sender.Metadata
        };
    }

    public async Task<GetLiveChatV2SenderOutput> GetPublicSenderAsync(string id)
    {
        var sender = await _readOnlyRepository.FindSenderBySenderIdAsync(id);

        if (sender is null)
        {
            return new GetLiveChatV2SenderOutput
            {
                ExitCode = ExitCode.Failure,
                Message = $"Sender {id} not found"
            };
        }

        return new GetLiveChatV2SenderOutput
        {
            Sender = sender.ToDto(),
        };
    }

    public async Task<GetLiveChatV2SenderOutput> GetSenderAsync(GetLiveChatV2SenderInput input)
    {
        var dbContext = _dbContextService.GetDbContext();

        var company = await dbContext.CompanyCompanies.Where(company => company.Id == input.CompanyId).FirstOrDefaultAsync();

        if (company is null)
        {
            return new GetLiveChatV2SenderOutput
            {
                ExitCode = ExitCode.Failure,
                Message = $"Company {input.CompanyId} not found"
            };
        }

        var sender = await _readOnlyRepository.FindSenderBySenderIdAsync(input.SenderId, input.CompanyId);

        if (sender is null)
        {
            return new GetLiveChatV2SenderOutput
            {
                ExitCode = ExitCode.Failure,
                Message = $"Sender {input.SenderId} not found"
            };
        }

        if (sender.IPAddress is not null)
        {
            var cacheKeyPattern = new LiveChatSenderGeoLocationCacheKeyPattern(sender.IPAddress);

            var data = await _cacheManagerService.GetAndSaveCacheAsync(
                cacheKeyPattern,
                async () =>
                {
                    try
                    {
                        return await _ipLocationService.GetLocationInfoByIpaddressAsync(sender.IPAddress);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "IP location service error for IP address {IPAddress}: {ErrorMessage}",
                            sender.IPAddress,
                            ex.Message);

                        // Return null or a simple fallback - adjust based on your needs
                        return null;
                    }
                });


            var geoLocation = new LiveChatV2SenderGeoLocation
            {
                CountryCode2 = data?.CountryCode2,
                CountryCode3 = data?.CountryCode3,
                CountryName = data?.CountryName
            };

            sender.SetGeoLocation(geoLocation);
        }

        var events = (await _liveChatTrackingEventsApi.LiveChatTrackingEventsGetLiveChatTrackingEventsPostAsync(
            getLiveChatTrackingEventsInput: new GetLiveChatTrackingEventsInput(
                sender.CompanyId,
                sender.WebClientUUID,
                sender.ChannelIdentityId)))?.Data?.Events ?? [];

        return new GetLiveChatV2SenderOutput
        {
            Sender = sender.ToDto(),
            TrackingEvents = events.Select(e => e.ToDto()).ToList()
        };
    }

    public async Task<UpdateLiveChatV2SenderOutput> UpdateSenderAsync(UpdateLiveChatV2SenderInput input)
    {
        var dbContext = _dbContextService.GetDbContext();

        var sender = await dbContext.SenderWebClientSenders
            .Where(sender => sender.WebClientUUID == input.SenderId)
            .FirstOrDefaultAsync();

        if (sender is null)
        {
            return new UpdateLiveChatV2SenderOutput
            {
                ExitCode = ExitCode.Failure,
                Message = $"Sender {input.SenderId} not found"
            };
        }

        // Update metadata using the extension method
        sender.SetMetadata(input.Metadata);
        sender.UpdatedAt = DateTime.UtcNow;

        // Mark the metadata property as modified explicitly
        dbContext.Entry(sender).Property(x => x.Metadata).IsModified = true;
        dbContext.Entry(sender).Property(x => x.UpdatedAt).IsModified = true;

        await dbContext.SaveChangesAsync();

        return new UpdateLiveChatV2SenderOutput
        {
            Id = sender.WebClientUUID,
            LiveChatId = sender.ChannelIdentityId,
            CompanyId = sender.CompanyId,
            Metadata = sender.Metadata
        };
    }
}