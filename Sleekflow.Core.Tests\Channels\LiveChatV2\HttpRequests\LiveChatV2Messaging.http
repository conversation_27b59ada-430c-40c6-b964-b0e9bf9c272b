### Login

POST https://localhost:5000/auth0/account/getusertoken
content-type: application/json-patch+json

{
  "username": "",
  "password": ""
}

> {% client.global.set("token", response.body.accessToken); %}

### Get message from end user (Not from sleekflow)
GET https://{{host}}/LiveChatV2/Public/Senders/cce04ba1-1366-436f-98db-fda734bc6e6d/Channels/ce2f0c3c-82d1-40cd-b8cc-46b55ab60643/Messages?offset=0&limit=20&order=asc
content-type: application/json
x-sleekflow-livechat-signature: VAJicrwF2rr30evIk5C8PR9djaHgCPPktCBwfUnhGP4=

### Send message from SleekFlow
POST https://localhost:5000/ConversationMessages/SendMessage
content-type: application/json
Authorization: Bearer {{token}}

{
    "conversationId": "0f9f468e-5b77-43a0-a7d7-b89896da0157",
    "messageChecksum": {{$randomUUID}},
    "channel": "livechat_v2",
    "channelIdentityId": "ce2f0c3c-82d1-40cd-b8cc-46b55ab60643",
    "messageType": "text",
    "extendedMessagePayload": {
        "channel": "whatsappcloudapi",
        "extendedMessageType": 101,
        "extendedMessagePayloadDetail": {
            "whatsappCloudApiTemplateMessageObject": {
                "language": "zh_HK",
                "templateName": "default_opt_in",
                "components": []
            }
        }
    },
    "messageContent": "*新訊息通知*\n你好! 你收到來自Official的新訊息。"
}


