#!/usr/bin/env node

/**
 * Verification script to check if the automated code review application is set up correctly
 * This script validates the setup without making actual API calls
 */

import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function verifySetup() {
    console.log('🔍 Verifying automated code review application setup...\n');

    let allGood = true;

    // 1. Check required files exist
    console.log('📁 Checking required files...');
    const requiredFiles = [
        'index.js',
        'github-api.js',
        'package.json',
        'prompts/overall-summary.txt',
        'prompts/inline-comments.txt'
    ];

    for (const file of requiredFiles) {
        const filePath = path.join(__dirname, file);
        try {
            await fs.access(filePath);
            console.log(`  ✅ ${file}`);
        } catch (error) {
            console.log(`  ❌ ${file} - NOT FOUND`);
            allGood = false;
        }
    }

    // 2. Check package.json has required dependencies
    console.log('\n📦 Checking dependencies...');
    try {
        const packageJson = JSON.parse(await fs.readFile(path.join(__dirname, 'package.json'), 'utf-8'));
        const requiredDeps = [
            '@langchain/google-genai',
            'langchain',
            'commander',
            '@octokit/rest'
        ];

        for (const dep of requiredDeps) {
            if (packageJson.dependencies && packageJson.dependencies[dep]) {
                console.log(`  ✅ ${dep}: ${packageJson.dependencies[dep]}`);
            } else {
                console.log(`  ❌ ${dep} - NOT FOUND in dependencies`);
                allGood = false;
            }
        }
    } catch (error) {
        console.log(`  ❌ Failed to read package.json: ${error.message}`);
        allGood = false;
    }

    // 3. Check environment variables
    console.log('\n🔑 Checking environment variables...');
    const requiredEnvVars = ['GEMINI_API_KEY'];

    for (const envVar of requiredEnvVars) {
        if (process.env[envVar]) {
            console.log(`  ✅ ${envVar} is set`);
        } else {
            console.log(`  ⚠️  ${envVar} is not set (required for runtime)`);
        }
    }

    // 4. Validate prompt files content
    console.log('\n📝 Checking prompt files...');
    try {
        const overallPrompt = await fs.readFile(path.join(__dirname, 'prompts/overall-summary.txt'), 'utf-8');
        const inlinePrompt = await fs.readFile(path.join(__dirname, 'prompts/inline-comments.txt'), 'utf-8');

        if (overallPrompt.length > 100) {
            console.log('  ✅ overall-summary.txt has content');
        } else {
            console.log('  ❌ overall-summary.txt is too short');
            allGood = false;
        }

        if (inlinePrompt.length > 100) {
            console.log('  ✅ inline-comments.txt has content');
        } else {
            console.log('  ❌ inline-comments.txt is too short');
            allGood = false;
        }
    } catch (error) {
        console.log(`  ❌ Failed to read prompt files: ${error.message}`);
        allGood = false;
    }

    // 5. Check if node_modules exists
    console.log('\n📚 Checking installation...');
    try {
        await fs.access(path.join(__dirname, 'node_modules'));
        console.log('  ✅ node_modules directory exists');
    } catch (error) {
        console.log('  ⚠️  node_modules not found - run "npm install" to install dependencies');
    }

    // 6. Test basic import
    console.log('\n🧪 Testing imports...');
    try {
        // Dynamic import to avoid top-level issues
        const { CodeReviewApp } = await import('./index.js');
        console.log('  ✅ CodeReviewApp class can be imported');

        // Try to create an instance (this will fail without GEMINI_API_KEY, but we can catch that)
        const app = new CodeReviewApp();
        console.log('  ✅ CodeReviewApp instance can be created');
    } catch (error) {
        console.log(`  ❌ Import test failed: ${error.message}`);
        allGood = false;
    }

    // Summary
    console.log('\n' + '='.repeat(50));
    if (allGood) {
        console.log('🎉 Setup verification PASSED!');
        console.log('\n🚀 Ready to use the automated code review application!');
        console.log('\nNext steps:');
        console.log('1. Set GEMINI_API_KEY environment variable');
        console.log('2. Run: node index.js --github-token <token> --pr-url <url>');
    } else {
        console.log('❌ Setup verification FAILED!');
        console.log('\n🔧 Please fix the issues above before using the application.');
    }
    console.log('='.repeat(50));
}

// Run verification
verifySetup().catch(console.error);
