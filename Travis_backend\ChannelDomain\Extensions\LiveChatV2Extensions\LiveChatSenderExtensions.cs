using System.Collections.Generic;
using Travis_backend.ChannelDomain.Models.LiveChatV2;
using Travis_backend.ChannelDomain.ViewModels.LiveChatV2.DTO;
using Travis_backend.Constants;
using Travis_backend.MessageDomain.Models;

namespace Travis_backend.ChannelDomain.Extensions.LiveChatV2Extensions;

public static class LiveChatSenderExtensions
{
    /// <summary>
    /// Merges a dictionary of metadata into the WebClientSender's Metadata property.
    /// It initializes the property if null and overrides existing keys.
    /// </summary>
    /// <param name="webClientSender">The WebClientSender instance to update.</param>
    /// <param name="metadataToMerge">The dictionary of metadata to merge in.</param>
    public static void SetMetadata(this WebClientSender webClientSender, Dictionary<string, object> metadataToMerge)
    {
        // Step 1: Ensure the sender's metadata dictionary is not null.
        // If it is, create a new empty one.
        webClientSender.Metadata ??= new Dictionary<string, object>();

        // If the new metadata is null or empty, there's nothing to do.
        if (metadataToMerge == null || metadataToMerge.Count == 0)
        {
            return;
        }

        // Step 2: Loop through the new metadata and merge it.
        // The dictionary indexer `[]` handles both adding new keys and
        // overriding the values of existing keys.
        foreach (var entry in metadataToMerge)
        {
            webClientSender.Metadata[entry.Key] = entry.Value;
        }
    }

    public static LiveChatSenderDto ToDto(this WebClientSender sender)
    {
        var liveChatSenderDto = new LiveChatSenderDto
        {
            Id = sender.WebClientUUID,
            ChannelIdentityId = sender.ChannelIdentityId,
            ConversationId = sender.ConversationId,
            UserProfileId = sender.UserProfileId,
            Metadata = sender.Metadata
        };

        return liveChatSenderDto;
    }

    public static bool HasGeoLocation(this WebClientSender sender)
    {

        // 1. First, guard against a null sender or a null metadata dictionary.
        // This prevents NullReferenceExceptions.
        if (sender?.Metadata == null)
        {
            return false;
        }

        // 2. Check if the key exists and its associated value is not null.
        // This is the most robust check, as it handles cases where the key might
        // exist but its value is explicitly set to null.
        return sender.Metadata.ContainsKey(LiveChatMetadataKeys.GeoLocation) && sender.Metadata[LiveChatMetadataKeys.GeoLocation] != null;
    }

    public static WebClientSender SetGeoLocation(this WebClientSender sender, LiveChatV2SenderGeoLocation geoLocation)
    {
        // 1. Guard against invalid input. We cannot set a null value, nor can we
        // set it on a null sender.
        if (sender == null || geoLocation == null)
        {
            return sender;
        }

        // 2. If the metadata dictionary doesn't exist yet, initialize it.
        // This makes the method more robust and prevents NullReferenceExceptions.
        sender.Metadata ??= new Dictionary<string, object>();

        // 3. Use the dictionary's indexer to perform the set operation.
        // This will cleanly add the key if it doesn't exist, or
        // overwrite the value if the key already exists.
        sender.Metadata[LiveChatMetadataKeys.GeoLocation] = geoLocation;

        return sender;
    }

}