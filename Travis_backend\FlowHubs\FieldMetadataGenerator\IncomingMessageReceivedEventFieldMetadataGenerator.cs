﻿#nullable enable
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.Database.Services;
using Travis_backend.FlowHubs.Constants;
using Travis_backend.FlowHubs.Models;
using Travis_backend.FlowHubs.Services;
using Travis_backend.Utils;
using Newtonsoft.Json.Linq;

namespace Travis_backend.FlowHubs.FieldMetadataGenerator;

public sealed class IncomingMessageReceivedEventFieldMetadataGenerator :
    FlowHubEventFieldMetadataGeneratorBase,
    IFlowHubEventFieldMetadataGenerator
{
    public string EventName => TriggerIds.IncomingMessageReceived;

    public Dictionary<string, string> FieldPathOptionRequestPathDict { get; } = new()
    {
        { "contact.labels[*].LabelValue", "FlowHub/SelectOptions/Labels" },
        { "channel_id", "FlowHub/SelectOptions/Channels" },
        { "lists[*].id", "FlowHub/SelectOptions/ContactLists" },
        { "message.message_type", "FlowHub/SelectOptions/MessageTypes" },
        { "contact.ContactOwner", "FlowHub/SelectOptions/Staffs" },
        { "contactOwner.id", "FlowHub/SelectOptions/Staffs" },
        { "contact.LastChannel", "FlowHub/SelectOptions/ChannelTypes" }
    };

    public IncomingMessageReceivedEventFieldMetadataGenerator(
        IDbContextService dbContextService,
        IFieldMetadataVariableVisibilityService fieldMetadataVariableVisibilityService)
        : base(dbContextService, fieldMetadataVariableVisibilityService)
    {
    }

    public async Task<(HashSet<FieldMetadata> FieldMetadataSet, Type EventBodyType, string ContactObjectName)> GenerateAsync(
        string companyId,
        Dictionary<string, object?>? parameters)
    {
        var (userProfileDict, customFieldOptionsRequestPathDict) = await GenerateUserProfileDictAsync(companyId);
        var eventBody = JsonUtils.GenerateSampleObjectFromType<OnMessageReceivedEventBody>();

        eventBody.Contact = userProfileDict;
        eventBody.ConversationId = userProfileDict["conversation_id"].ToString()!;

        eventBody.Message.MessageBody = new MessageBody
        {
            AudioMessage = new AudioMessageObject
            {
                Link = eventBody.Message.MessageBody.AudioMessage?.Link,
                Filename = eventBody.Message.MessageBody.AudioMessage?.Filename
            },
            VideoMessage = new VideoMessageObject
            {
                Link = eventBody.Message.MessageBody.VideoMessage?.Link,
                Filename = eventBody.Message.MessageBody.VideoMessage?.Filename
            },
            ImageMessage = new ImageMessageObject
            {
                Link = eventBody.Message.MessageBody.ImageMessage?.Link,
                Filename = eventBody.Message.MessageBody.ImageMessage?.Filename
            },
            DocumentMessage = new DocumentMessageObject
            {
                Link = eventBody.Message.MessageBody.DocumentMessage?.Link,
                Filename = eventBody.Message.MessageBody.DocumentMessage?.Filename
            },
            OrderMessage = new OrderMessageObject
            {
                CatalogId = eventBody.Message.MessageBody.OrderMessage?.CatalogId,
                ProductItemsJson = eventBody.Message.MessageBody.OrderMessage?.ProductItemsJson
            },
        };

        var eventBodyJsonString = eventBody.ToJson();
        var eventBodyJson = JObject.Parse(eventBodyJsonString);
        AddContactExpansionToEventBodyJson(eventBodyJson);

        var jsonData = JsonUtils.SimplifyJsonData(eventBodyJson.ToString());

        var fieldMetadataSet = GenerateFilteredFieldMetadata(jsonData, EventName);

        PopulateOptionRequestPath(
            fieldMetadataSet,
            customFieldOptionsRequestPathDict,
            FieldPathOptionRequestPathDict);

        return (fieldMetadataSet, typeof(OnMessageReceivedEventBody), nameof(eventBody.Contact));
    }
}