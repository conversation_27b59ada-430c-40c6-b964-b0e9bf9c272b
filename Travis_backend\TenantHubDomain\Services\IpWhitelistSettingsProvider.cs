using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.Apis.TenantHub.Api;
using Sleekflow.Apis.TenantHub.Model;
using Travis_backend.Cache;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.Constants;
using Travis_backend.TenantHubDomain.Models;

namespace Travis_backend.TenantHubDomain.Services;

public interface IIpWhitelistSettingsProvider
{
    Task<IpWhitelistSettings?> GetSettingsAsync(string companyId);
}

public class IpWhitelistSettingsProvider : IIpWhitelistSettingsProvider
{
    private readonly ICacheManagerService _cacheManagerService;
    private readonly IEnabledFeaturesService _enabledFeaturesService;
    private readonly IManagementIpWhitelistsApi _ipWhitelistsApi;
    private readonly ILogger<IpWhitelistSettingsProvider> _logger;

    public IpWhitelistSettingsProvider(
        ICacheManagerService cacheManagerService,
        IEnabledFeaturesService enabledFeaturesService,
        IManagementIpWhitelistsApi ipWhitelistsApi,
        ILogger<IpWhitelistSettingsProvider> logger)
    {
        _cacheManagerService = cacheManagerService;
        _enabledFeaturesService = enabledFeaturesService;
        _ipWhitelistsApi = ipWhitelistsApi;
        _logger = logger;
    }

    public async Task<IpWhitelistSettings?> GetSettingsAsync(string companyId)
    {
        var cacheKey = new IpWhitelistCacheKeyPattern(companyId);
        var cacheString = await _cacheManagerService.GetCacheAsync(cacheKey);

        if (cacheString != null)
        {
            return JsonConvert.DeserializeObject<IpWhitelistSettings>(cacheString);
        }

        // Cache miss, check feature status first
        var isEnabled = await _enabledFeaturesService.GetIsFeatureEnabledForCompany(companyId, TenantHubFeatures.IpWhitelist);
        if (!isEnabled)
        {
            _logger.LogInformation("IP Whitelist feature is disabled for company {CompanyId}. Caching disabled status.", companyId);
            var disabledSettings = new IpWhitelistSettings(); // Represents disabled state
            await _cacheManagerService.SaveCacheAsync(cacheKey, JsonConvert.SerializeObject(disabledSettings), TimeSpan.FromDays(1));
            return disabledSettings;
        }

        // Feature is enabled, fetch settings from TenantHub
        try
        {
            _logger.LogWarning("IP whitelist settings cache miss for enabled feature on company {CompanyId}. Fetching from TenantHub.", companyId);
            var settingsResponse = await _ipWhitelistsApi.ManagementIpWhitelistsGetIpWhitelistSettingsPostAsync(
                managementGetIpWhitelistSettingsInput: new ManagementGetIpWhitelistSettingsInput(companyId));
            var ipSettings = settingsResponse?.Data?.IpWhitelistSettings;

            await _cacheManagerService.SaveCacheAsync(
                cacheKey,
                JsonConvert.SerializeObject(ipSettings ?? new IpWhitelistSettings()),
                TimeSpan.FromDays(1));

            return ipSettings;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to fetch IP whitelist settings for company {CompanyId}. No settings will be enforced for this request.", companyId);
            return null; // Return null on failure, middleware will allow request
        }
    }
}
