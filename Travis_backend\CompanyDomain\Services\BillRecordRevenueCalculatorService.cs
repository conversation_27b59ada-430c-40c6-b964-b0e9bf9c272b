using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.Helpers;
using Travis_backend.InternalDomain.Models;
using Travis_backend.InternalDomain.ViewModels;

namespace Travis_backend.CompanyDomain.Services
{
    public interface IBillRecordRevenueCalculatorService
    {
        List<CmsDailyAnalyticDto> GetDailyAnalytics<T>(
            List<T> companies,
            DateTime start,
            DateTime end)
            where T : CmsCompanyAnalyticDto;

        List<CmsDailyMonthlyRecurringRevenueAnalyticDto> GetDailyMonthlyRecurringRevenueAnalytics<T>(
            List<T> companies,
            DateTime start,
            DateTime end)
            where T : CmsCompanyAnalyticDto;

        List<CmsDailyRevenueAnalyticDto> GetDailyRevenueAnalytics<T>(
            List<T> companies,
            DateTime start,
            DateTime end)
            where T : CmsCompanyAnalyticDto;

        List<CmsDailyPlanDistributionAnalyticDto> GetDailyPlanDistributionAnalytics<T>(
            List<T> companies,
            DateTime start,
            DateTime end)
            where T : CmsCompanyAnalyticDto;

        List<CmsDailyAccruedRevenueAnalyticDto> GetDailyAccruedAnalytics(
            List<CmsCompanyAnalyticDto> companies,
            List<CmsSleekPayCompanyAnalyticDto> sleekPayCompanies,
            List<CmsWhatsApp360DialogUsageTopUpTransactionLogDto> whatsapp360DialogTopUpLogs,
            List<CmsTwilioTopUpLogDto> twilioTopUpLogs,
            List<CmsWhatsappCloudApiTopUpDto> whatsappCloudApiTopUps,
            DateTime start,
            DateTime end);

        List<CmsDailyDistributionAnalyticDto> GetDailyDistributionAnalytics<T>(
            List<T> companies,
            DateTime start,
            DateTime end)
            where T : CmsCompanyAnalyticDto;

        List<string> GetPaymentBreakDowns(
            CmsBillRecordDto billingPeriodUsage,
            List<BillRecord> allValidBillingPeriodUsages,
            string timeZoneInfoId);

        decimal SumMonthlyRecurringRevenueWithTimezone(
            List<BillRecord> cmsBillingPeriodUsages,
            string timezoneInfoId,
            DateTime validDate = default);

        CmsCompanyAllTimeRevenueAnalyticData GetCompanyAllTimeRevenueAnalyticData(
            string companyId,
            List<CmsDailyAnalyticDto> dailyRevenueAnalytics);

        decimal CalculateInvoiceAmount(BillRecord billRecord, DateTime today);

        PaymentSplit FindMatchingPaymentSplit(BillRecord billRecord, DateTime today);
    }

    /// <summary>
    /// Service for bill record revenue calculation operations.
    /// </summary>
    public class BillRecordRevenueCalculatorService : IBillRecordRevenueCalculatorService
    {
        private readonly ILogger<BillRecordRevenueCalculatorService> _logger;
        private readonly ITimezoneAwareMrrCalculationService _timezoneAwareMrrService;

        /// <summary>
        /// Initializes a new instance of the <see cref="BillRecordRevenueCalculatorService"/> class.
        /// </summary>
        /// <param name="logger">The logger for tracking operations.</param>
        /// <param name="timezoneAwareMrrService">The timezone-aware MRR calculation service.</param>
        public BillRecordRevenueCalculatorService(
            ILogger<BillRecordRevenueCalculatorService> logger,
            ITimezoneAwareMrrCalculationService timezoneAwareMrrService)
        {
            _logger = logger;
            _timezoneAwareMrrService = timezoneAwareMrrService;
        }

        /// <inheritdoc />
        public List<CmsDailyAnalyticDto> GetDailyAnalytics<T>(
            List<T> companies,
            DateTime start,
            DateTime end)
            where T : CmsCompanyAnalyticDto
        {
            List<CmsDailyAnalyticDto> cmsDailyRevenueAnalytics = [];

            for (var date = start; date <= end; date = date.Add(TimeSpan.FromDays(1)))
            {
                var currentDate = date;
                var dailyAnalytic = new CmsDailyAnalyticDto
                {
                    Date = date.ToString("yyyy-MM-dd"),
                    MonthlyRecurringRevenue = 0,
                    CompanyRevenueBreakDowns = []
                };

                // Parallel Loop
                ConcurrentBag<DetailMonthlyRecurringRevenueDto> revenueDetails = [];
                ConcurrentBag<RevenueBreakdown> dailyRevenues = [];
                ConcurrentBag<CmsCompanyRevenueBreakDownDto> companyRevenueBreakDowns = [];

                Parallel.ForEach(
                    companies,
                    new ParallelOptions
                    {
                        MaxDegreeOfParallelism = 10
                    },
                    company =>
                    {
                        try
                        {
                            if (company.CreatedAt > currentDate)
                            {
                                return;
                            }

                            var revenueDetail = GetDetailedMonthlyRecurringRevenue(
                                company.BillRecords,
                                currentDate,
                                company.Id,
                                company.TimeZoneInfoId);

                            if (revenueDetail == null)
                            {
                                return;
                            }

                            var dailyRevenue = GetDailyRevenue(company.BillRecords, currentDate);

                            if (revenueDetail.MonthlyRecurringRevenue > 0)
                            {
                                companyRevenueBreakDowns.Add(
                                    new CmsCompanyRevenueBreakDownDto
                                    {
                                        CompanyId = company.Id,
                                        CompanyName = company.CompanyName,
                                        SubscriptionPlanId = revenueDetail.SubscriptionPlanRevenue?.PlanId,
                                        MonthlyRecurringRevenue = revenueDetail.MonthlyRecurringRevenue,
                                    });
                            }

                            revenueDetails.Add(revenueDetail);
                            dailyRevenues.Add(dailyRevenue);
                        }
                        catch (Exception e)
                        {
                            _logger.LogError(e, "CMS Analytic Error Company Id: {CompanyId}", company.Id);
                        }
                    });

                for (var i = 0; i < revenueDetails.Count; i++)
                {
                    var revenueDetail = revenueDetails.ElementAt(i);
                    var dailyRevenue = dailyRevenues.ElementAt(i);

                    dailyAnalytic.MonthlyRecurringRevenue += revenueDetail.MonthlyRecurringRevenue;
                    dailyAnalytic.DailyRevenue += dailyRevenue.TotalRevenue;
                    dailyAnalytic.SubscriptionPlanRevenue += dailyRevenue.SubscriptionPlanRevenue;
                    dailyAnalytic.OneTimeSetupFeeRevenue += dailyRevenue.OneTimeSetupFeeRevenue;
                    dailyAnalytic.OneTimeSetupFeeRevenueBasedOnPaidAt +=
                        dailyRevenue.OneTimeSetupFeeRevenueBasedOnPaidAt;
                    dailyAnalytic.MarkupRevenue += dailyRevenue.MarkupRevenue;
                }

                dailyAnalytic.CompanyRevenueBreakDowns = companyRevenueBreakDowns.ToList();

                cmsDailyRevenueAnalytics.Add(dailyAnalytic);
            }

            return cmsDailyRevenueAnalytics;
        }

        /// <inheritdoc />
        public List<CmsDailyMonthlyRecurringRevenueAnalyticDto> GetDailyMonthlyRecurringRevenueAnalytics<T>(
            List<T> companies,
            DateTime start,
            DateTime end)
            where T : CmsCompanyAnalyticDto
        {
            List<CmsDailyMonthlyRecurringRevenueAnalyticDto> cmsDailyRevenueAnalytics = [];

            for (var date = start; date <= end; date = date.Add(TimeSpan.FromDays(1)))
            {
                var currentDate = date;
                var dailyAnalytic = new CmsDailyMonthlyRecurringRevenueAnalyticDto
                {
                    Date = date.ToString("yyyy-MM-dd"),
                    MonthlyRecurringRevenue = 0,
                    CompanyRevenueBreakDowns = []
                };

                // Parallel Loop
                ConcurrentBag<CmsCompanyRevenueBreakDownDto> companyRevenueBreakDowns = [];

                Parallel.ForEach(
                    companies,
                    new ParallelOptions
                    {
                        MaxDegreeOfParallelism = 10
                    },
                    company =>
                    {
                        try
                        {
                            if (company.CreatedAt > currentDate)
                            {
                                return;
                            }

                            var revenueDetail = GetDetailedMonthlyRecurringRevenue(
                                company.BillRecords,
                                currentDate,
                                company.Id,
                                company.TimeZoneInfoId);

                            if (revenueDetail == null)
                            {
                                return;
                            }

                            if (revenueDetail.MonthlyRecurringRevenue > 0)
                            {
                                companyRevenueBreakDowns.Add(
                                    new CmsCompanyRevenueBreakDownDto
                                    {
                                        CompanyId = company.Id,
                                        CompanyName = company.CompanyName,
                                        SubscriptionPlanId = revenueDetail.SubscriptionPlanRevenue?.PlanId,
                                        MonthlyRecurringRevenue = revenueDetail.MonthlyRecurringRevenue,
                                    });
                            }
                        }
                        catch (Exception e)
                        {
                            _logger.LogError(
                                e,
                                "CMS Daily Monthly Recurring Revenue Analytic Error Company Id: {CompanyId}",
                                company.Id);
                        }
                    });

                dailyAnalytic.MonthlyRecurringRevenue = companyRevenueBreakDowns.Sum(x => x.MonthlyRecurringRevenue);
                dailyAnalytic.CompanyRevenueBreakDowns = companyRevenueBreakDowns.ToList();

                cmsDailyRevenueAnalytics.Add(dailyAnalytic);
            }

            return cmsDailyRevenueAnalytics;
        }

        /// <inheritdoc />
        public List<CmsDailyRevenueAnalyticDto> GetDailyRevenueAnalytics<T>(
            List<T> companies,
            DateTime start,
            DateTime end)
            where T : CmsCompanyAnalyticDto
        {
            List<CmsDailyRevenueAnalyticDto> cmsDailyRevenueAnalytics = [];

            for (var date = start; date <= end; date = date.Add(TimeSpan.FromDays(1)))
            {
                var currentDate = date;
                var dailyAnalytic = new CmsDailyRevenueAnalyticDto
                {
                    Date = date.ToString("yyyy-MM-dd")
                };

                // Parallel Loop
                ConcurrentBag<RevenueBreakdown> dailyRevenues = [];

                Parallel.ForEach(
                    companies,
                    new ParallelOptions
                    {
                        MaxDegreeOfParallelism = 10
                    },
                    company =>
                    {
                        try
                        {
                            if (company.CreatedAt > currentDate)
                            {
                                return;
                            }

                            var dailyRevenue = GetDailyRevenue(company.BillRecords, currentDate);

                            dailyRevenues.Add(dailyRevenue);
                        }
                        catch (Exception e)
                        {
                            _logger.LogError(e, "CMS Revenue Analytic Error Company Id: {CompanyId}", company.Id);
                        }
                    });

                foreach (var dailyRevenue in dailyRevenues)
                {
                    dailyAnalytic.DailyRevenue += dailyRevenue.TotalRevenue;
                    dailyAnalytic.SubscriptionPlanRevenue += dailyRevenue.SubscriptionPlanRevenue;
                    dailyAnalytic.OneTimeSetupFeeRevenue += dailyRevenue.OneTimeSetupFeeRevenue;
                    dailyAnalytic.OneTimeSetupFeeRevenueBasedOnPaidAt +=
                        dailyRevenue.OneTimeSetupFeeRevenueBasedOnPaidAt;
                    dailyAnalytic.MarkupRevenue += dailyRevenue.MarkupRevenue;
                }

                cmsDailyRevenueAnalytics.Add(dailyAnalytic);
            }

            return cmsDailyRevenueAnalytics;
        }

        /// <inheritdoc />
        public List<CmsDailyPlanDistributionAnalyticDto> GetDailyPlanDistributionAnalytics<T>(
            List<T> companies,
            DateTime start,
            DateTime end)
            where T : CmsCompanyAnalyticDto
        {
            List<CmsDailyPlanDistributionAnalyticDto> cmsDailyRevenueAnalytics = [];

            for (var date = start; date <= end; date = date.Add(TimeSpan.FromDays(1)))
            {
                var currentDate = date;
                var dailyAnalytic = new CmsDailyPlanDistributionAnalyticDto
                {
                    Date = date.ToString("yyyy-MM-dd"),
                    SubscriptionPlanDistribution = [],
                    AddOnPlanDistribution = []
                };

                ConcurrentBag<CmsPlanDistributionDto> subscriptionPlanDistribution = [];
                ConcurrentBag<CmsPlanDistributionDto> addOnPlanDistribution = [];

                Parallel.ForEach(
                    companies,
                    new ParallelOptions
                    {
                        MaxDegreeOfParallelism = 10
                    },
                    company =>
                    {
                        try
                        {
                            if (company.CreatedAt > currentDate)
                            {
                                return;
                            }

                            var revenueDetail = GetDetailedMonthlyRecurringRevenue(
                                company.BillRecords,
                                currentDate,
                                company.Id,
                                company.TimeZoneInfoId);

                            if (revenueDetail == null)
                            {
                                return;
                            }

                            revenueDetail.AddOnRevenues.ForEach(addOnPlanRevenue =>
                            {
                                addOnPlanDistribution.Add(
                                    new CmsPlanDistributionDto
                                    {
                                        PlanId = addOnPlanRevenue.PlanId,
                                        Count = 1,
                                        MonthlyRecurringRevenue = addOnPlanRevenue.MonthlyRecurringRevenue
                                    });
                            });

                            if (revenueDetail.SubscriptionPlanRevenue == null)
                            {
                                return;
                            }

                            subscriptionPlanDistribution.Add(
                                new CmsPlanDistributionDto
                                {
                                    PlanId = revenueDetail.SubscriptionPlanRevenue?.PlanId,
                                    Count = 1,
                                    MonthlyRecurringRevenue =
                                        revenueDetail.SubscriptionPlanRevenue?.MonthlyRecurringRevenue ?? 0
                                });
                        }
                        catch (Exception e)
                        {
                            _logger.LogError(
                                e,
                                "CMS Daily Plan Distribution Analytic Error Company Id: {CompanyId}",
                                company.Id);
                        }
                    });

                dailyAnalytic.SubscriptionPlanDistribution = subscriptionPlanDistribution
                    .GroupBy(x => x.PlanId)
                    .Select(x => new CmsPlanDistributionDto
                    {
                        PlanId = x.Key,
                        Count = x.Sum(y => y.Count),
                        MonthlyRecurringRevenue = x.Sum(y => y.MonthlyRecurringRevenue)
                    })
                    .ToList();

                dailyAnalytic.AddOnPlanDistribution = addOnPlanDistribution
                    .GroupBy(x => x.PlanId)
                    .Select(x => new CmsPlanDistributionDto
                    {
                        PlanId = x.Key,
                        Count = x.Sum(y => y.Count),
                        MonthlyRecurringRevenue = x.Sum(y => y.MonthlyRecurringRevenue)
                    })
                    .ToList();

                cmsDailyRevenueAnalytics.Add(dailyAnalytic);
            }

            return cmsDailyRevenueAnalytics;
        }

        /// <inheritdoc />
        public List<CmsDailyAccruedRevenueAnalyticDto> GetDailyAccruedAnalytics(
            List<CmsCompanyAnalyticDto> companies,
            List<CmsSleekPayCompanyAnalyticDto> sleekPayCompanies,
            List<CmsWhatsApp360DialogUsageTopUpTransactionLogDto> whatsapp360DialogTopUpLogs,
            List<CmsTwilioTopUpLogDto> twilioTopUpLogs,
            List<CmsWhatsappCloudApiTopUpDto> whatsappCloudApiTopUps,
            DateTime start,
            DateTime end)
        {
            var result = new List<CmsDailyAccruedRevenueAnalyticDto>();

            for (var date = start; date <= end; date = date.Add(TimeSpan.FromDays(1)))
            {
                var dailyAnalytic = new CmsDailyAccruedRevenueAnalyticDto()
                {
                    Date = date.ToString("yyyy-MM-dd"),
                    CmsAccruedRevenueBreakDowns = new List<CmsAccruedRevenueBreakDownDto>()
                };

                companies.ForEach(company =>
                {
                    try
                    {
                        var dailyRevenue =
                            GetDailyAccruedSubscriptionAndOneTimeSetupRevenue(company.BillRecords, date);

                        if (dailyRevenue.SubscriptionPlanRevenue > 0)
                        {
                            dailyAnalytic.CmsAccruedRevenueBreakDowns.Add(
                                new CmsAccruedRevenueBreakDownDto()
                                {
                                    CompanyId = company.Id,
                                    CompanyName = company.CompanyName,
                                    Type = "Subscription",
                                    DailyAccruedRevenue = dailyRevenue.SubscriptionPlanRevenue
                                });

                            dailyAnalytic.SubscriptionPlanRevenue += dailyRevenue.SubscriptionPlanRevenue;
                        }

                        if (dailyRevenue.OneTimeSetupFeeRevenue > 0)
                        {
                            dailyAnalytic.CmsAccruedRevenueBreakDowns.Add(
                                new CmsAccruedRevenueBreakDownDto()
                                {
                                    CompanyId = company.Id,
                                    CompanyName = company.CompanyName,
                                    Type = "One-Time Revenue",
                                    DailyAccruedRevenue = dailyRevenue.OneTimeSetupFeeRevenue
                                });

                            dailyAnalytic.OneTimeSetupFeeRevenue += dailyRevenue.OneTimeSetupFeeRevenue;
                        }
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(
                            e,
                            "CMS Accrued Analytic Subscription & One-Time Revenue Error Company Id: {CompanyId}",
                            company.Id);
                    }
                });

                sleekPayCompanies.ForEach(company =>
                {
                    try
                    {
                        var sleekPayDailyRevenue = GetDailyAccruedSleekPayRevenue(
                            company.CmsSleekPayReportDatas,
                            date);

                        if (sleekPayDailyRevenue != decimal.MinValue)
                        {
                            dailyAnalytic.CmsAccruedRevenueBreakDowns.Add(
                                new CmsAccruedRevenueBreakDownDto()
                                {
                                    CompanyId = company.CompanyId,
                                    CompanyName = company.CompanyName,
                                    Type = "SleekPay",
                                    DailyAccruedRevenue = sleekPayDailyRevenue
                                });

                            dailyAnalytic.SleekPayRevenue += sleekPayDailyRevenue;
                        }
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(
                            e,
                            "CMS Accrued Analytic SleekPay Error Company Id: {CompanyId}",
                            company.CompanyId);
                    }
                });

                var removeCount = 0;

                foreach (var topUp in whatsapp360DialogTopUpLogs)
                {
                    try
                    {
                        if (topUp.CreatedAt.Date > date.Date)
                        {
                            break;
                        }

                        var whatsapp360DialogDailyRevenue = CurrencyConverter.ConvertToUsd(
                            topUp.Total,
                            topUp.Currency);

                        var existWhatsapp360DialogTopUpRevenueBreakDownDto
                            = dailyAnalytic.CmsAccruedRevenueBreakDowns
                                .Find(bd => bd.CompanyId == topUp.CompanyId
                                            && bd.Type == "Top-up"
                                            && bd.TopUpType == "360 Dialog");

                        if (existWhatsapp360DialogTopUpRevenueBreakDownDto != null)
                        {
                            existWhatsapp360DialogTopUpRevenueBreakDownDto.DailyAccruedRevenue +=
                                whatsapp360DialogDailyRevenue;
                        }
                        else
                        {
                            dailyAnalytic.CmsAccruedRevenueBreakDowns.Add(
                                new CmsAccruedRevenueBreakDownDto()
                                {
                                    CompanyId = topUp.CompanyId,
                                    CompanyName = topUp.CompanyName,
                                    Type = "Top-up",
                                    TopUpType = "360 Dialog",
                                    DailyAccruedRevenue = whatsapp360DialogDailyRevenue
                                });
                        }

                        dailyAnalytic.TopUpRevenue += whatsapp360DialogDailyRevenue;

                        removeCount++;
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(
                            e,
                            "CMS Accrued Analytic 360 Dialog Error Company Id: {CompanyId}",
                            topUp.CompanyId);
                    }
                }

                if (removeCount > 0)
                {
                    whatsapp360DialogTopUpLogs.RemoveRange(0, removeCount);
                }

                removeCount = 0;

                foreach (var topUp in twilioTopUpLogs)
                {
                    try
                    {
                        if (topUp.CreatedAt.Date > date.Date)
                        {
                            break;
                        }

                        var twilioDailyRevenue = CurrencyConverter.ConvertToUsd(
                            topUp.TopUpAmount,
                            topUp.Currency);

                        var existTwilioTopUpRevenueBreakDownDto
                            = dailyAnalytic.CmsAccruedRevenueBreakDowns
                                .Find(bd => bd.CompanyId == topUp.CompanyId
                                            && bd.Type == "Top-up"
                                            && bd.TopUpType == "Twilio");

                        if (existTwilioTopUpRevenueBreakDownDto != null)
                        {
                            existTwilioTopUpRevenueBreakDownDto.DailyAccruedRevenue +=
                                twilioDailyRevenue;
                        }
                        else
                        {
                            dailyAnalytic.CmsAccruedRevenueBreakDowns.Add(
                                new CmsAccruedRevenueBreakDownDto()
                                {
                                    CompanyId = topUp.CompanyId,
                                    CompanyName = topUp.CompanyName,
                                    Type = "Top-up",
                                    TopUpType = "Twilio",
                                    DailyAccruedRevenue = twilioDailyRevenue
                                });
                        }

                        dailyAnalytic.TopUpRevenue += twilioDailyRevenue;

                        removeCount++;
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(
                            e,
                            "CMS Accrued Analytic Twilio Error Company Id: {CompanyId}",
                            topUp.CompanyId);
                    }
                }

                if (removeCount > 0)
                {
                    twilioTopUpLogs.RemoveRange(0, removeCount);
                }

                removeCount = 0;

                foreach (var topUp in whatsappCloudApiTopUps)
                {
                    try
                    {
                        if (topUp.CreatedAt.Date > date.Date)
                        {
                            break;
                        }

                        var whatsappCloudApiDailyRevenue = CurrencyConverter.ConvertToUsd(
                            topUp.CreditAmount,
                            topUp.Currency);

                        var existWhatsappCloudApiTopUpRevenueBreakDownDto
                            = dailyAnalytic.CmsAccruedRevenueBreakDowns
                                .Find(bd => bd.FacebookBusinessId == topUp.FacebookBusinessId
                                            && bd.Type == "Top-up"
                                            && bd.TopUpType == "WhatsApp Cloud Api");

                        if (existWhatsappCloudApiTopUpRevenueBreakDownDto != null)
                        {
                            existWhatsappCloudApiTopUpRevenueBreakDownDto.DailyAccruedRevenue +=
                                whatsappCloudApiDailyRevenue;
                        }
                        else
                        {
                            dailyAnalytic.CmsAccruedRevenueBreakDowns.Add(
                                new CmsAccruedRevenueBreakDownDto()
                                {
                                    FacebookBusinessId = topUp.FacebookBusinessId,
                                    FacebookBusinessName = topUp.FacebookBusinessName,
                                    Type = "Top-up",
                                    TopUpType = "WhatsApp Cloud Api",
                                    DailyAccruedRevenue = whatsappCloudApiDailyRevenue,
                                    SleekFlowCompanyIds = topUp.SleekFlowCompanyIds,
                                    SleekFlowCompanyNames = topUp.SleekFlowCompanyNames
                                });
                        }

                        dailyAnalytic.TopUpRevenue += whatsappCloudApiDailyRevenue;

                        removeCount++;
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(
                            e,
                            "CMS Accrued Analytic WhatsApp Cloud Api Error Facebook Business Id: {FacebookBusinessId}",
                            topUp.FacebookBusinessId);
                    }
                }

                if (removeCount > 0)
                {
                    whatsappCloudApiTopUps.RemoveRange(0, removeCount);
                }

                result.Add(dailyAnalytic);
            }

            return result;
        }

        /// <inheritdoc />
        public List<CmsDailyDistributionAnalyticDto> GetDailyDistributionAnalytics<T>(
            List<T> companies,
            DateTime start,
            DateTime end)
            where T : CmsCompanyAnalyticDto
        {
            var result = new List<CmsDailyDistributionAnalyticDto>();

            for (var date = start; date <= end; date = date.Add(TimeSpan.FromDays(1)))
            {
                var dailyAnalytic = new CmsDailyDistributionAnalyticDto()
                {
                    Date = date.ToString("yyyy-MM-dd"),
                    MonthlyRecurringRevenue = 0,
                    CountryDistribution = new List<CmsAnalyticDistributionDto>(),
                    LeadSourceDistribution = new List<CmsAnalyticDistributionDto>(),
                    IndustryDistribution = new List<CmsAnalyticDistributionDto>(),
                };

                companies.ForEach(company =>
                {
                    try
                    {
                        var revenueDetail = GetDetailedMonthlyRecurringRevenue(
                            company.BillRecords,
                            date,
                            company.Id,
                            company.TimeZoneInfoId);

                        if (revenueDetail == null)
                        {
                            return;
                        }

                        // Countries
                        var countriesDistribution =
                            dailyAnalytic.CountryDistribution.Find(d => d.AnalyticSourceName == company.CompanyCountry);

                        if (countriesDistribution == null)
                        {
                            countriesDistribution = new CmsAnalyticDistributionDto()
                            {
                                AnalyticSourceName = company.CompanyCountry
                            };
                            dailyAnalytic.CountryDistribution.Add(countriesDistribution);
                        }

                        countriesDistribution.Count++;
                        countriesDistribution.MonthlyRecurringRevenue += revenueDetail.MonthlyRecurringRevenue;

                        // Lead Source
                        company.CmsLeadSource = string.IsNullOrEmpty(company.CmsLeadSource)
                            ? "Not Set"
                            : company.CmsLeadSource;
                        var leadSourceDistribution =
                            dailyAnalytic.LeadSourceDistribution.Find(d =>
                                d.AnalyticSourceName == company.CmsLeadSource);

                        if (leadSourceDistribution == null)
                        {
                            leadSourceDistribution = new CmsAnalyticDistributionDto()
                            {
                                AnalyticSourceName = company.CmsLeadSource
                            };
                            dailyAnalytic.LeadSourceDistribution.Add(leadSourceDistribution);
                        }

                        leadSourceDistribution.Count++;
                        leadSourceDistribution.MonthlyRecurringRevenue += revenueDetail.MonthlyRecurringRevenue;

                        // Industry
                        company.CmsCompanyIndustry = string.IsNullOrEmpty(company.CmsCompanyIndustry)
                            ? "Not Set"
                            : company.CmsCompanyIndustry;
                        var companyIndustryDistribution =
                            dailyAnalytic.IndustryDistribution.Find(d =>
                                d.AnalyticSourceName == company.CmsCompanyIndustry);

                        if (companyIndustryDistribution == null)
                        {
                            companyIndustryDistribution = new CmsAnalyticDistributionDto()
                            {
                                AnalyticSourceName = company.CmsCompanyIndustry
                            };
                            dailyAnalytic.IndustryDistribution.Add(companyIndustryDistribution);
                        }

                        companyIndustryDistribution.Count++;
                        companyIndustryDistribution.MonthlyRecurringRevenue +=
                            revenueDetail.MonthlyRecurringRevenue;

                        dailyAnalytic.MonthlyRecurringRevenue += revenueDetail.MonthlyRecurringRevenue;
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(e, "CMS Analytic Error Company Id: {CompanyId}", company.Id);
                    }
                });

                result.Add(dailyAnalytic);
            }

            return result;
        }

        /// <inheritdoc />
        public List<string> GetPaymentBreakDowns(
            CmsBillRecordDto billingPeriodUsage,
            List<BillRecord> allValidBillingPeriodUsages,
            string timeZoneInfoId)
        {
            var inputPaymentBreakDowns = new List<string>();

            if (billingPeriodUsage == null)
            {
                return inputPaymentBreakDowns;
            }

            // Calculate Stripe Pay Amount
            var total = CurrencyConverter.ConvertToUsd(
                (decimal) billingPeriodUsage.PayAmount,
                billingPeriodUsage.Currency);

            // Calculate Cms Payment Input Amount
            var totalInputPaymentSubscriptionFee =
                billingPeriodUsage.CmsSalesPaymentRecords.Sum(x =>
                    CurrencyConverter.ConvertToUsd(x.SubscriptionFee, x.Currency));

            // totalInputPaymentSubscriptionFee += billingPeriodUsage.CmsSalesPaymentRecords.Sum(x => CurrencyConverter.ConvertToUsd(x.OneTimeSetupFee, x.Currency));
            inputPaymentBreakDowns.AddRange(
                billingPeriodUsage.CmsSalesPaymentRecords
                    .Where(x => x.SubscriptionFee != 0)
                    .Select(x =>
                        $"Input Payment Subscription Fee: ${Math.Round(x.SubscriptionFee, 2)} {x.Currency.ToUpper()}"));

            // inputPaymentBreakDowns.AddRange(cmsBillingPeriodUsage.CmsSalesPaymentRecords
            //     .Where(x => x.OneTimeSetupFee != 0)
            //     .Select(x => $"Input Payment One Time Setup Fee: ${x.OneTimeSetupFee} {x.Currency.ToUpper()}"));
            var previousBillRecordTotalPrice = 0M;

            if (billingPeriodUsage.UpgradeFromBillRecordId.HasValue)
            {
                previousBillRecordTotalPrice = CalculateMonthlyRecurringRevenue(
                    allValidBillingPeriodUsages.Find(x => x.Id == billingPeriodUsage.UpgradeFromBillRecordId),
                    timeZoneInfoId);
            }
            else if (billingPeriodUsage.DowngradeFromBillRecordId.HasValue)
            {
                previousBillRecordTotalPrice = CalculateMonthlyRecurringRevenue(
                    allValidBillingPeriodUsages.Find(x => x.Id == billingPeriodUsage.DowngradeFromBillRecordId),
                    timeZoneInfoId);
            }

            if (previousBillRecordTotalPrice != 0)
            {
                inputPaymentBreakDowns.Add(
                    $"Previous Bill Remaining Payment MRR: ${Math.Round(previousBillRecordTotalPrice, 2)} USD");
            }

            if (!billingPeriodUsage.IsIrregularPlan)
            {
                total += totalInputPaymentSubscriptionFee;
                total += previousBillRecordTotalPrice;
            }

            inputPaymentBreakDowns.Insert(0, $"Total: $ {Math.Round(total, 2)} USD");

            return inputPaymentBreakDowns;
        }

        /// <inheritdoc />
        public decimal SumMonthlyRecurringRevenueWithTimezone(
            List<BillRecord> cmsBillingPeriodUsages,
            string timezoneInfoId,
            DateTime validDate = default)
        {
            if (cmsBillingPeriodUsages == null || cmsBillingPeriodUsages.Count == 0)
            {
                return 0M;
            }

            decimal mrr = 0;

            if (validDate == default)
            {
                validDate = DateTime.UtcNow;
            }

            var allCmsBillingPeriodUsages = cmsBillingPeriodUsages
                .Where(x => x.PeriodStart.Date <= validDate && x.PeriodEnd >= validDate.Date).ToList();

            var realValidDate = validDate.Date.AddDays(1);

            if (realValidDate > DateTime.UtcNow)
            {
                realValidDate = DateTime.UtcNow;
            }

            var realValidBillingPeriodUsages = cmsBillingPeriodUsages
                .Where(x => x.PeriodStart <= realValidDate && x.PeriodEnd >= realValidDate).ToList();

            // Fix the double count issue
            cmsBillingPeriodUsages = allCmsBillingPeriodUsages.Count > realValidBillingPeriodUsages.Count
                ? realValidBillingPeriodUsages
                : allCmsBillingPeriodUsages;

            var currentSubscriptionBillRecord = cmsBillingPeriodUsages.Where(b =>
                    ValidSubscriptionPlan.SubscriptionPlan.Contains(b.SubscriptionPlanId))
                .OrderByDescending(br => br.created)
                .ThenByDescending(br => br.PayAmount)
                .FirstOrDefault();

            // Sum Subscription Bill Record
            mrr += CalculateMonthlyRecurringRevenue(currentSubscriptionBillRecord, timezoneInfoId);

            // Sum All Add-On MRR
            cmsBillingPeriodUsages.Where(b =>
                    ValidSubscriptionPlan.CmsMrrAllAddOnWithoutRevenuePlan.Contains(b.SubscriptionPlanId))
                .ToList()
                .ForEach(billRecord => { mrr += CalculateMonthlyRecurringRevenue(billRecord, timezoneInfoId); });

            return mrr;
        }

        /// <inheritdoc />
        public CmsCompanyAllTimeRevenueAnalyticData GetCompanyAllTimeRevenueAnalyticData(
            string companyId,
            List<CmsDailyAnalyticDto> dailyRevenueAnalytics)
        {
            var result = new CmsCompanyAllTimeRevenueAnalyticData()
            {
                CompanyId = companyId,
            };

            // Make sure the dailyRevenueAnalyticDto only contains data for one company, cover company's creation date and dailyRevenueAnalytics is order by date
            foreach (var data in dailyRevenueAnalytics)
            {
                if (data.DailyRevenue > 0 && result.InitialPaidDate == null)
                {
                    result.InitialPaidDate = data.Date;
                    result.InitialPaidSubscriptionPlanId = data.CompanyRevenueBreakDowns
                        .Find(x => x.CompanyId == companyId)?.SubscriptionPlanId;
                    result.InitialMonthlyRecurringRevenue = decimal.Round(data.MonthlyRecurringRevenue, 2);
                }

                result.TotalSubscriptionPlanRevenue += decimal.Round(data.SubscriptionPlanRevenue, 2);
                result.TotalOneTimeSetupFeeRevenue += decimal.Round(data.OneTimeSetupFeeRevenue, 2);
                result.TotalMarkupRevenue += decimal.Round(data.MarkupRevenue, 2);
            }

            result.TotalRevenue += result.TotalSubscriptionPlanRevenue + result.TotalOneTimeSetupFeeRevenue +
                                   result.TotalMarkupRevenue;

            if (result.TotalRevenue > 0)
            {
                result.LastPaidSubscriptionPlanId = dailyRevenueAnalytics.LastOrDefault(x =>
                        x.MonthlyRecurringRevenue > 0 && x.CompanyRevenueBreakDowns.Exists(c =>
                            c.CompanyId == companyId &&
                            !ValidSubscriptionPlan.FreePlans.Contains(c.SubscriptionPlanId)))?
                    .CompanyRevenueBreakDowns.Find(x => x.CompanyId == companyId)?.SubscriptionPlanId;
            }

            return result;
        }

        /// <inheritdoc />
        public decimal CalculateInvoiceAmount(BillRecord billRecord, DateTime today)
        {
            var totalPayAmount = Convert.ToDecimal(billRecord.PayAmount);
            var actualPeriodEnd = BillRecordRevenueCalculatorHelper.GetActualPeriodEndFromMetadata(billRecord);

            switch (billRecord.PaymentIntervalType)
            {
                case PaymentIntervalType.Monthly:
                    // Monthly: PayAmount / MonthDifference(periodStart, periodEnd)
                    var monthsTotal = BillRecordRevenueCalculatorHelper.GetMonthDiff(
                        billRecord.PeriodStart.Date,
                        actualPeriodEnd.Date);
                    if (monthsTotal > 0)
                    {
                        return totalPayAmount / monthsTotal;
                    }

                    _logger?.LogError(
                        "Invalid month difference for BillRecord {BillRecordId}: {MonthsTotal}. Cannot calculate monthly invoice amount.",
                        billRecord.Id,
                        monthsTotal);
                    throw new InvalidOperationException(
                        $"Invalid subscription period for BillRecord {billRecord.Id}. Monthly billing requires a positive month difference.");

                case PaymentIntervalType.Yearly:
                    // Yearly: PayAmount / (MonthDifference(periodStart, periodEnd) / 12)
                    var totalMonths = BillRecordRevenueCalculatorHelper.GetMonthDiff(
                        billRecord.PeriodStart.Date,
                        actualPeriodEnd.Date);
                    if (totalMonths > 0 && totalMonths % 12 == 0)
                    {
                        return totalPayAmount / (totalMonths / 12.0m);
                    }

                    _logger?.LogError(
                        "Invalid yearly subscription period for BillRecord {BillRecordId}: {TotalMonths} months. Yearly billing requires positive months that are multiples of 12.",
                        billRecord.Id,
                        totalMonths);
                    throw new InvalidOperationException(
                        $"Invalid yearly subscription period for BillRecord {billRecord.Id}. Expected multiple of 12 months, got {totalMonths} months.");

                case PaymentIntervalType.Custom:
                    // Custom: PayAmount * splitPercentage / 100
                    var splitPercentage = GetSplitPercentageForToday(billRecord, today);
                    if (splitPercentage > 0)
                    {
                        return totalPayAmount * splitPercentage / 100;
                    }

                    _logger?.LogError(
                        "Invalid split percentage for BillRecord {BillRecordId}: {SplitPercentage}. Cannot calculate custom invoice amount.",
                        billRecord.Id,
                        splitPercentage);
                    throw new InvalidOperationException(
                        $"Invalid payment split configuration for BillRecord {billRecord.Id}. No valid split percentage found for today.");

                case PaymentIntervalType.OneOff:
                    // OneOff: Return the full PayAmount since it's a one-time payment
                    return totalPayAmount;

                default:
                    _logger?.LogError(
                        "Unsupported PaymentIntervalType {PaymentIntervalType} for BillRecord {BillRecordId}.",
                        billRecord.PaymentIntervalType,
                        billRecord.Id);
                    throw new InvalidOperationException(
                        $"Unsupported payment interval type '{billRecord.PaymentIntervalType}' for BillRecord {billRecord.Id}.");
            }
        }

        /// <inheritdoc />
        public PaymentSplit FindMatchingPaymentSplit(
            BillRecord billRecord,
            DateTime today)
        {
            if (billRecord.PaymentSplits == null || billRecord.PaymentSplits.Count == 0)
            {
                _logger?.LogError(
                    "BillRecord {BillRecordId} has Custom PaymentIntervalType but no PaymentSplits defined.",
                    billRecord.Id);

                return null;
            }

            if (billRecord.PaymentTerms == null)
            {
                _logger?.LogError(
                    "BillRecord {BillRecordId} has Custom PaymentIntervalType but PaymentTerms is null.",
                    billRecord.Id);

                return null;
            }

            var validSplits = BillRecordRevenueCalculatorHelper.GetValidPaymentSplits(billRecord);
            var todayDate = today.Date; // Cache to avoid repeated .Date calls

            // Iterate through all valid payment splits to find one that needs an invoice today
            return (from split in validSplits
                    let invoiceCreationDate = split.PaymentDate.Date.AddDays(-billRecord.PaymentTerms.Value)
                    where invoiceCreationDate == todayDate
                    select split).FirstOrDefault();
        }

        #region Private Helper Methods

        /// <summary>
        /// Enhanced version of GetDetailedMonthlyRecurringRevenue with optional timezone awareness.
        /// </summary>
        /// <param name="cmsBillingPeriodUsages">The billing period usages.</param>
        /// <param name="validDate">The valid date for calculation.</param>
        /// <param name="companyId">Optional company ID for timezone-aware calculations.</param>
        /// <param name="timezoneInfoId">Optional timezone info ID for timezone-aware calculations.</param>
        /// <returns>Detailed monthly recurring revenue with enhanced precision when company ID is provided.</returns>
        private DetailMonthlyRecurringRevenueDto GetDetailedMonthlyRecurringRevenue(
            List<CmsAnalyticBillRecordDto> cmsBillingPeriodUsages,
            DateTime validDate,
            string companyId,
            string timezoneInfoId)
        {
            var result = new DetailMonthlyRecurringRevenueDto();

            if (validDate == default)
            {
                validDate = DateTime.UtcNow;
            }

            var allCmsBillingPeriodUsages = cmsBillingPeriodUsages
                .Where(x => x.PeriodStart.Date <= validDate && x.PeriodEnd >= validDate.Date).ToList();

            var realValidDate = validDate.Date.AddDays(1);

            if (realValidDate > DateTime.UtcNow)
            {
                realValidDate = DateTime.UtcNow;
            }

            var realValidBillingPeriodUsages = cmsBillingPeriodUsages
                .Where(x => x.PeriodStart <= realValidDate && x.PeriodEnd >= realValidDate).ToList();

            // Fix the double count issue
            cmsBillingPeriodUsages = allCmsBillingPeriodUsages.Count > realValidBillingPeriodUsages.Count
                ? realValidBillingPeriodUsages
                : allCmsBillingPeriodUsages;

            if (cmsBillingPeriodUsages.Count == 0)
            {
                return null;
            }

            var currentSubscriptionBillRecord = cmsBillingPeriodUsages.Where(b =>
                    ValidSubscriptionPlan.SubscriptionPlan.Contains(b.SubscriptionPlanId))
                .OrderByDescending(br => br.Created)
                .ThenByDescending(br => br.PayAmount)
                .FirstOrDefault();

            // Sum Subscription Bill Record
            var subscriptionRevenue = CalculateMonthlyRecurringRevenue(
                currentSubscriptionBillRecord,
                companyId,
                timezoneInfoId);

            // Sum All Add-On MRR
            var addOnRevenues = cmsBillingPeriodUsages
                .Where(b => ValidSubscriptionPlan.CmsMrrAllAddOnWithoutRevenuePlan.Contains(b.SubscriptionPlanId))
                .Select(b => CalculateMonthlyRecurringRevenue(b, companyId, timezoneInfoId))
                .ToList();

            result.AddOnRevenues = addOnRevenues;
            result.SubscriptionPlanRevenue = subscriptionRevenue;

            result.MonthlyRecurringRevenue = (subscriptionRevenue?.MonthlyRecurringRevenue ?? 0) +
                                             addOnRevenues.Sum(x => x.MonthlyRecurringRevenue);

            return result;
        }

        /// <summary>
        /// Enhanced version of CalculateMonthlyRecurringRevenue with optional timezone awareness.
        /// </summary>
        /// <param name="cmsBillingPeriodUsage">The billing period usage data.</param>
        /// <param name="companyId">Optional company ID for timezone-aware calculations.</param>
        /// <param name="timezoneInfoId">Optional timezone info ID for timezone-aware calculations.</param>
        /// <returns>Plan and revenue data with enhanced precision when company ID is provided.</returns>
        private PlanAndRevenueDto CalculateMonthlyRecurringRevenue(
            CmsAnalyticBillRecordDto cmsBillingPeriodUsage,
            string companyId,
            string timezoneInfoId)
        {
            var result = new PlanAndRevenueDto();

            if (cmsBillingPeriodUsage == null)
            {
                return null;
            }

            // Calculate Stripe Pay Amount
            var totalRevenue = CurrencyConverter.ConvertToUsd(
                (decimal) cmsBillingPeriodUsage.PayAmount,
                cmsBillingPeriodUsage.Currency);

            // Calculate Cms Payment Input Amount
            if (!cmsBillingPeriodUsage.IsIrregularPlan)
            {
                totalRevenue += cmsBillingPeriodUsage.CmsSalesPaymentRecords.Sum(x =>
                    CurrencyConverter.ConvertToUsd(x.SubscriptionFee, x.Currency));
            }

            var truePeriodEnd = cmsBillingPeriodUsage.PeriodEnd;
            if (cmsBillingPeriodUsage.metadata?.TryGetValue("grace_period_start", out var gracePeriodStartStr) == true)
            {
                truePeriodEnd = DateTime.Parse(gracePeriodStartStr, CultureInfo.InvariantCulture);
            }

            // To Monthly
            var monthlyRecurringRevenue = CalculateFinalMonthlyRecurringRevenue(
                totalRevenue,
                cmsBillingPeriodUsage.PeriodStart,
                truePeriodEnd,
                companyId,
                timezoneInfoId);

            result.PlanId = cmsBillingPeriodUsage.SubscriptionPlanId;
            result.MonthlyRecurringRevenue = monthlyRecurringRevenue;

            return result;
        }

        private decimal CalculateMonthlyRecurringRevenue(
            BillRecord cmsBillingPeriodUsage,
            string timezoneInfoId = null)
        {
            if (cmsBillingPeriodUsage == null)
            {
                return 0M;
            }

            // Calculate Stripe Pay Amount
            var monthlyRecurringRevenue = CurrencyConverter.ConvertToUsd(
                (decimal) cmsBillingPeriodUsage.PayAmount,
                cmsBillingPeriodUsage.currency);

            // Calculate Cms Payment Input Amount
            if (!cmsBillingPeriodUsage.IsIrregularPlan)
            {
                monthlyRecurringRevenue += cmsBillingPeriodUsage.CmsSalesPaymentRecords.Sum(x =>
                    CurrencyConverter.ConvertToUsd(x.SubscriptionFee, x.Currency));
            }

            var truePeriodEnd = cmsBillingPeriodUsage.PeriodEnd;
            if (cmsBillingPeriodUsage.metadata?.TryGetValue("grace_period_start", out var gracePeriodStartStr) == true)
            {
                truePeriodEnd = DateTime.Parse(gracePeriodStartStr, CultureInfo.InvariantCulture);
            }

            // To Monthly
            monthlyRecurringRevenue = CalculateFinalMonthlyRecurringRevenue(
                monthlyRecurringRevenue,
                cmsBillingPeriodUsage.PeriodStart,
                truePeriodEnd,
                cmsBillingPeriodUsage.CompanyId,
                timezoneInfoId);

            return monthlyRecurringRevenue;
        }

        private RevenueBreakdown GetDailyRevenue(
            List<CmsAnalyticBillRecordDto> cmsBillingPeriodUsages,
            DateTime validDate = default)
        {
            var revenueBreakdown = new RevenueBreakdown();

            if (validDate == default)
            {
                validDate = DateTime.UtcNow;
            }

            cmsBillingPeriodUsages = cmsBillingPeriodUsages
                .Where(x => x.PeriodStart.Date <= validDate && x.PeriodEnd >= validDate.Date).ToList();

            if (cmsBillingPeriodUsages.Count == 0)
            {
                return revenueBreakdown;
            }

            var currentSubscriptionBillRecord = cmsBillingPeriodUsages.Where(b =>
                    ValidSubscriptionPlan.SubscriptionPlan.Contains(b.SubscriptionPlanId))
                .OrderByDescending(br => br.Created)
                .ThenByDescending(br => br.PayAmount)
                .FirstOrDefault();

            // Sum Subscription Bill Record
            var subscriptionRevenue = CalculateDailyRevenue(
                currentSubscriptionBillRecord,
                validDate);

            // Sum All Add-On MRR
            var addOnRevenues = cmsBillingPeriodUsages
                .Where(b => ValidSubscriptionPlan.CmsMrrAllAddOn.Contains(b.SubscriptionPlanId))
                .Select(x => CalculateDailyRevenue(x, validDate))
                .ToList();

            // Sum result
            if (subscriptionRevenue != null)
            {
                revenueBreakdown += subscriptionRevenue;
            }

            foreach (var addOnRevenue in addOnRevenues)
            {
                revenueBreakdown += addOnRevenue;
            }

            return revenueBreakdown;
        }

        private RevenueBreakdown GetDailyAccruedSubscriptionAndOneTimeSetupRevenue(
            List<CmsAnalyticBillRecordDto> cmsBillingPeriodUsages,
            DateTime validDate = default)
        {
            var revenueBreakdown = new RevenueBreakdown();

            if (validDate == default)
            {
                validDate = DateTime.UtcNow;
            }

            // Get required CMS billing period usages
            cmsBillingPeriodUsages = cmsBillingPeriodUsages
                .Where(x => x.PeriodStart.Date <= validDate.Date && x.PeriodEnd.Date >= validDate.Date).ToList();

            if (cmsBillingPeriodUsages.Count == 0)
            {
                return revenueBreakdown;
            }

            var currentSubscriptionBillRecord = cmsBillingPeriodUsages.Where(b =>
                    ValidSubscriptionPlan.SubscriptionPlan.Contains(b.SubscriptionPlanId))
                .OrderByDescending(br => br.Created)
                .ThenByDescending(br => br.PayAmount)
                .FirstOrDefault();

            // Sum Subscription Bill Record
            var subscriptionRevenue = CalculateDailyAccruedRevenue(
                currentSubscriptionBillRecord,
                validDate);

            // Sum All Add-On MRR
            var addOnRevenues = cmsBillingPeriodUsages
                .Where(b => ValidSubscriptionPlan.CmsMrrAllAddOnWithoutRevenuePlan.Contains(b.SubscriptionPlanId))
                .Select(x => CalculateDailyAccruedRevenue(x, validDate))
                .ToList();

            // Sum result
            if (subscriptionRevenue != null)
            {
                revenueBreakdown += subscriptionRevenue;
            }

            foreach (var addOnRevenue in addOnRevenues)
            {
                revenueBreakdown += addOnRevenue;
            }

            return revenueBreakdown;
        }

        private decimal GetDailyAccruedSleekPayRevenue(
            List<CmsSleekPayReportData> cmsSleekPayReportData,
            DateTime validDate = default)
        {
            if (validDate == default)
            {
                validDate = DateTime.UtcNow;
            }

            cmsSleekPayReportData = cmsSleekPayReportData
                .Where(x => x.StartActivityDate.Date == validDate.Date).ToList();

            if (cmsSleekPayReportData.Count == 0)
            {
                return decimal.MinValue;
            }

            // Sum Net Earnings of CmsSleekPayReportData
            return cmsSleekPayReportData.Sum(reportData => CurrencyConverter.ConvertToUsd(
                reportData.NetEarning,
                reportData.Currency));
        }

        private RevenueBreakdown CalculateDailyRevenue(
            CmsAnalyticBillRecordDto cmsBillingPeriodUsage,
            DateTime validDate)
        {
            if (cmsBillingPeriodUsage == null)
            {
                return null;
            }

            var revenueBreakdown = new RevenueBreakdown();

            if (cmsBillingPeriodUsage.PayAmount > 0 && cmsBillingPeriodUsage.PeriodStart.Date == validDate.Date)
            {
                var revenue = CurrencyConverter.ConvertToUsd(
                    (decimal) cmsBillingPeriodUsage.PayAmount,
                    cmsBillingPeriodUsage.Currency);

                // One Off
                if (cmsBillingPeriodUsage.SubscriptionPlanId.Contains("oneoff"))
                {
                    revenueBreakdown.OneTimeSetupFeeRevenue += revenue;
                }

                // mark up
                else if (cmsBillingPeriodUsage.SubscriptionPlanId.Contains("markup"))
                {
                    revenueBreakdown.MarkupRevenue += revenue;
                }

                // sub
                else
                {
                    revenueBreakdown.SubscriptionPlanRevenue += revenue;
                }
            }

            // Calculate Cms Payment Input Amount
            foreach (var paymentRecord in cmsBillingPeriodUsage.CmsSalesPaymentRecords)
            {
                var paidDate = cmsBillingPeriodUsage.PeriodStart.Date;

                if (paymentRecord.CreatedAt.Date == validDate.Date)
                {
                    revenueBreakdown.OneTimeSetupFeeRevenue += CurrencyConverter.ConvertToUsd(
                        paymentRecord.OneTimeSetupFee,
                        paymentRecord.Currency);
                }

                if (paymentRecord.PaidAt != null && paymentRecord.PaidAt.Value.Date == validDate.Date)
                {
                    revenueBreakdown.OneTimeSetupFeeRevenueBasedOnPaidAt += CurrencyConverter.ConvertToUsd(
                        paymentRecord.OneTimeSetupFee,
                        paymentRecord.Currency);
                }

                if (paymentRecord.PaidAt != null && paidDate < paymentRecord.PaidAt)
                {
                    paidDate = paymentRecord.PaidAt.Value.Date;
                }

                if (paidDate == validDate.Date)
                {
                    revenueBreakdown.SubscriptionPlanRevenue += CurrencyConverter.ConvertToUsd(
                        paymentRecord.SubscriptionFee,
                        paymentRecord.Currency);
                }
            }

            return revenueBreakdown;
        }

        private RevenueBreakdown CalculateDailyAccruedRevenue(
            CmsAnalyticBillRecordDto cmsBillingPeriodUsage,
            DateTime validDate)
        {
            if (cmsBillingPeriodUsage == null)
            {
                return null;
            }

            var revenueBreakdown = new RevenueBreakdown();

            if (cmsBillingPeriodUsage.PayAmount > 0)
            {
                var revenue = CurrencyConverter.ConvertToUsd(
                    (decimal) cmsBillingPeriodUsage.PayAmount,
                    cmsBillingPeriodUsage.Currency);

                // Subscription Fee
                var deltaDays = (cmsBillingPeriodUsage.PeriodEnd.Date - cmsBillingPeriodUsage.PeriodStart.Date).Days +
                                1;
                revenueBreakdown.SubscriptionPlanRevenue += revenue / deltaDays;
            }

            // Calculate Cms Payment Input Amount (One Time Setup Fee)
            foreach (var paymentRecord in cmsBillingPeriodUsage.CmsSalesPaymentRecords.Where(paymentRecord =>
                         paymentRecord.CreatedAt.Date == validDate.Date))
            {
                revenueBreakdown.OneTimeSetupFeeRevenue += CurrencyConverter.ConvertToUsd(
                    paymentRecord.OneTimeSetupFee,
                    paymentRecord.Currency);
            }

            return revenueBreakdown;
        }

        /// <summary>
        /// Enhanced version of CalculateFinalMonthlyRecurringRevenue with optional timezone awareness.
        /// </summary>
        /// <param name="monthlyRecurringRevenue">The monthly recurring revenue amount.</param>
        /// <param name="periodStart">The period start time (UTC).</param>
        /// <param name="periodEnd">The period end time (UTC).</param>
        /// <param name="companyId">Optional company ID for timezone-aware calculations.</param>
        /// <param name="timezoneInfoId">Optional timezone info ID for enhanced timezone calculations.</param>
        /// <returns>The final monthly recurring revenue with precise calculations when company ID is provided.</returns>
        private decimal CalculateFinalMonthlyRecurringRevenue(
            decimal monthlyRecurringRevenue,
            DateTime periodStart,
            DateTime periodEnd,
            string companyId,
            string timezoneInfoId = null)
        {
            if (monthlyRecurringRevenue == 0)
            {
                return 0;
            }

            // Calculate original MRR for comparison
            var originalMonthDiff = BillRecordRevenueCalculatorHelper.GetMonthDiff(periodStart, periodEnd);
            var originalMrr = monthlyRecurringRevenue / originalMonthDiff;

            // Use enhanced calculation if timezone service is available and company ID is provided
            if (_timezoneAwareMrrService != null && !string.IsNullOrWhiteSpace(companyId))
            {
                try
                {
                    var preciseMonthDiff = _timezoneAwareMrrService.GetPreciseMonthDiff(
                        periodStart,
                        periodEnd,
                        companyId,
                        timezoneInfoId);

                    var enhancedMrr = monthlyRecurringRevenue / preciseMonthDiff;

                    // Calculate variance for monitoring
                    var variance = Math.Abs(enhancedMrr - originalMrr);
                    var variancePercentage = originalMrr != 0 ? (variance / originalMrr) * 100 : 0;

                    // Log variance for monitoring
                    _logger?.LogDebug(
                        "Timezone-aware MRR calculation - Company: {CompanyId}, TimezoneInfoId: {TimezoneInfoId}, Original: {OriginalMrr:F4}, Enhanced: {EnhancedMrr:F4}, Variance: {Variance:F4} ({VariancePercentage:F2}%)",
                        companyId,
                        timezoneInfoId ?? "N/A",
                        originalMrr,
                        enhancedMrr,
                        variance,
                        variancePercentage);

                    // Alert if variance exceeds the 1% threshold
                    if (variancePercentage > 1.0m)
                    {
                        _logger?.LogDebug(
                            "High MRR calculation variance detected - Company: {CompanyId}, TimezoneInfoId: {TimezoneInfoId}, Original: {OriginalMrr:F4}, Enhanced: {EnhancedMrr:F4}, Variance: {VariancePercentage:F2}% (threshold: 1%)",
                            companyId,
                            timezoneInfoId ?? "N/A",
                            originalMrr,
                            enhancedMrr,
                            variancePercentage);
                    }

                    return enhancedMrr;
                }
                catch (Exception ex)
                {
                    _logger?.LogError(
                        ex,
                        "Enhanced MRR calculation failed for company {CompanyId}, timezoneInfoId: {TimezoneInfoId}, falling back to original calculation",
                        companyId,
                        timezoneInfoId ?? "N/A");

                    // Fallback to the original calculation if the enhanced version fails
                    // This ensures system reliability
                }
            }
            else if (!string.IsNullOrWhiteSpace(companyId))
            {
                // Log when timezone service is not available
                _logger?.LogDebug(
                    "Using original MRR calculation for company {CompanyId}, timezoneInfoId: {TimezoneInfoId} - Timezone service available: {ServiceAvailable}",
                    companyId,
                    timezoneInfoId ?? "N/A",
                    _timezoneAwareMrrService != null);
            }

            // Original calculation as fallback
            return originalMrr;
        }

        /// <summary>
        /// Gets the split percentage for the payment split that should be invoiced today.
        /// </summary>
        /// <param name="billRecord">The bill record containing payment splits.</param>
        /// <param name="today">The current date.</param>
        /// <returns>The split percentage for today, or 0 if no matching split is found.</returns>
        private decimal GetSplitPercentageForToday(BillRecord billRecord, DateTime today)
        {
            var matchingSplit = FindMatchingPaymentSplit(billRecord, today);
            if (matchingSplit != null)
            {
                return Convert.ToDecimal(matchingSplit.SplitPercentage);
            }

            _logger?.LogWarning(
                "No matching payment split found for today {Today} in BillRecord {BillRecordId}",
                today,
                billRecord.Id);
            return 0;
        }

        #endregion
    }
}