using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.ChannelDomain.Models;
using Travis_backend.Constants;
using Travis_backend.Database;
using Travis_backend.FlowHubs.Interfaces;

namespace Travis_backend.FlowHubs.Handlers.ChannelOptions;

public class LiveChatV2ChannelOptionsProvider : IChannelOptionsProvider
{
    public string ChannelType => ChannelTypes.LiveChatV2;

    public async Task<List<NeedConfigOption>> GetOptionsAsync(
        BaseDbContext dbContext,
        string companyId,
        string searchName,
        Dictionary<string, string> additionalParams = null,
        int? offset = null,
        int? limit = null)
    {
        var query = dbContext.Set<LiveChatV2Config>()
            .Where(x => x.CompanyId == companyId && !x.IsDeleted);

        if (!string.IsNullOrEmpty(searchName))
        {
            query = query.Where(config => config.ChannelDisplayName.Contains(searchName));
        }

        if (offset is >= 0)
        {
            query = query.Skip(offset.Value);
        }

        if (limit is > 0)
        {
            query = query.Take(limit.Value);
        }

        var results = await query
            .Select(x => new NeedConfigOption(
                x.ChannelIdentityId,
                x.ChannelDisplayName,
                x.ChannelIdentityId,
                null,
                x.ChannelType,
                null))
            .ToListAsync();

        return results;
    }

    public async Task<List<NeedConfigOption>> GetChannelNameOptionsAsync(
        BaseDbContext dbContext,
        string companyId,
        string searchName,
        Dictionary<string, string> additionalParams = null,
        int? offset = null,
        int? limit = null)
    {
        var query = dbContext.Set<LiveChatV2Config>()
            .Where(x => x.CompanyId == companyId);

        if (!string.IsNullOrEmpty(searchName))
        {
            query = query.Where(config => config.ChannelDisplayName.Contains(searchName));
        }

        if (offset is >= 0)
        {
            query = query.Skip(offset.Value);
        }

        if (limit is > 0)
        {
            query = query.Take(limit.Value);
        }

        var results = await query
            .Select(x => new NeedConfigOption(
                x.ChannelIdentityId,
                x.ChannelDisplayName,
                x.ChannelIdentityId,
                null,
                x.ChannelType,
                null))
            .ToListAsync();

        return results;
    }
}