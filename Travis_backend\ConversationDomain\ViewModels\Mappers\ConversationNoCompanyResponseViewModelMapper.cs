using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Travis_backend.ChannelDomain.Repositories;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.MessageDomain.Models;

namespace Travis_backend.ConversationDomain.ViewModels.Mappers;

public interface IConversationNoCompanyResponseViewModelMapper
{
    public Task<ConversationNoCompanyResponseViewModel> ToViewModelAsync(Conversation conversation);

    public Task<List<ConversationNoCompanyResponseViewModel>> ToViewModelsAsync(
        List<Conversation> conversations);
}

// Anti Pattern including Repository in the mapper but currently no centralized place for all the ConversationNoCompanyResponseViewModel related logic
// This is a short term that lead to long term solution please refactor it later if you have time
public class ConversationNoCompanyResponseViewModelMapper : IConversationNoCompanyResponseViewModelMapper
{
    private readonly IMapper _mapper;
    private readonly ILiveChatV2SenderReadOnlyRepository _liveChatV2SenderReadOnlyRepository;

    public ConversationNoCompanyResponseViewModelMapper(
        IMapper mapper,
        ILiveChatV2SenderReadOnlyRepository liveChatV2SenderReadOnlyRepository)
    {
        _mapper = mapper;
        _liveChatV2SenderReadOnlyRepository = liveChatV2SenderReadOnlyRepository;
    }

    public async Task<ConversationNoCompanyResponseViewModel> ToViewModelAsync(Conversation conversation)
    {
        await AddConversationChannelSenders(conversation);
        var viewModel = _mapper.Map<ConversationNoCompanyResponseViewModel>(conversation);

        if (viewModel.UserProfile != null)
        {
            MapChannelSpecificSenderData(conversation, viewModel.UserProfile);
        }

        return viewModel;
    }

    public async Task<List<ConversationNoCompanyResponseViewModel>> ToViewModelsAsync(List<Conversation> conversations)
    {
        if (conversations == null || conversations.Count == 0)
        {
            return[];
        }

        await AddConversationChannelSenders(conversations);

        var viewModels = new List<ConversationNoCompanyResponseViewModel>(conversations.Count);
        foreach (var conversation in conversations)
        {
            var viewModel = _mapper.Map<ConversationNoCompanyResponseViewModel>(conversation);
            if (viewModel.UserProfile != null)
            {
                MapChannelSpecificSenderData(conversation, viewModel.UserProfile);
            }
            viewModels.Add(viewModel);
        }

        return viewModels;
    }

    private async Task AddConversationChannelSenders(Conversation conversation)
    {
        if (conversation.LiveChatV2Users is not null && conversation.LiveChatV2Users.Count > 0)
        {
            return;
        }

        var liveChatV2Senders =
            await _liveChatV2SenderReadOnlyRepository.FindSendersByConversationIdAsync(conversation.Id, conversation.CompanyId);

        if (liveChatV2Senders.Count == 0)
        {
            return;
        }

        conversation.LiveChatV2Users ??=[];
        conversation.LiveChatV2Users.AddRange(liveChatV2Senders);
    }

    private async Task AddConversationChannelSenders(List<Conversation> conversations)
    {
        var companyId = conversations.Where(c => c.CompanyId is not null)
            .Select(c => c.CompanyId)
            .FirstOrDefault();

        var conversationIds = conversations.Select(c => c.Id).ToList();

        var allLiveChatV2Senders = await _liveChatV2SenderReadOnlyRepository.FindSendersByConversationIdsAsync(
            conversationIds,
            companyId);

        var liveChatV2SenderByConversationId = allLiveChatV2Senders.ToLookup(sender => sender.ConversationId);

        foreach (var conversation in conversations)
        {
            var matchingSenders = liveChatV2SenderByConversationId[conversation.Id];
            var liveChatV2Senders = matchingSenders.ToList();
            if (liveChatV2Senders.Count == 0)
            {
                continue;
            }

            conversation.LiveChatV2Users ??=[];
            conversation.LiveChatV2Users.AddRange(liveChatV2Senders);
        }
    }

    private void MapChannelSpecificSenderData(
        Conversation conversation,
        UserProfileNoCompanyResponse userProfileViewModel)
    {
        if (userProfileViewModel is null)
        {
            return;
        }

        if (conversation.WhatsappCloudApiUser != null)
        {
            userProfileViewModel.WhatsappCloudApiUser =
                WhatsappCloudApiSenderResponse.MapFrom(conversation.WhatsappCloudApiUser);
        }

        if (conversation.facebookUser != null)
        {
            userProfileViewModel.FacebookAccount = _mapper.Map<FacebookInfoResponse>(conversation.facebookUser);
        }

        if (conversation.InstagramUser != null)
        {
            userProfileViewModel.InstagramUser = _mapper.Map<InstagramSenderResponse>(conversation.InstagramUser);
        }

        if (conversation.WhatsappUser != null)
        {
            userProfileViewModel.WhatsAppAccount = _mapper.Map<WhatsAppSenderResponse>(conversation.WhatsappUser);
        }

        if (conversation.WhatsApp360DialogUser != null)
        {
            userProfileViewModel.WhatsApp360DialogUser =
                _mapper.Map<WhatsApp360DialogSenderResponse>(conversation.WhatsApp360DialogUser);
        }

        if (conversation.EmailAddress != null)
        {
            userProfileViewModel.EmailAddress = _mapper.Map<EmailSenderResponse>(conversation.EmailAddress);
        }

        if (conversation.WebClient != null)
        {
            userProfileViewModel.WebClient = _mapper.Map<WebClientResponse>(conversation.WebClient);
        }

        if (conversation.LineUser != null)
        {
            userProfileViewModel.LineUser = _mapper.Map<LineSenderResponse>(conversation.LineUser);
        }

        if (conversation.SMSUser != null)
        {
            userProfileViewModel.SMSUser = _mapper.Map<SMSSenderResponse>(conversation.SMSUser);
        }

        if (conversation.ViberUser != null)
        {
            userProfileViewModel.ViberUser = _mapper.Map<ViberSenderResponse>(conversation.ViberUser);
        }

        if (conversation.TelegramUser != null)
        {
            userProfileViewModel.TelegramUser = _mapper.Map<TelegramSenderResponse>(conversation.TelegramUser);
        }

        if (conversation.WeChatUser != null)
        {
            userProfileViewModel.WeChatUser = _mapper.Map<WeChatUserInfoResponse>(conversation.WeChatUser);
        }

        if (conversation.TikTokUser != null)
        {
            userProfileViewModel.TikTokUser =
                _mapper.Map<TikTokSenderResponse>(conversation.TikTokUser);
        }

        if (conversation.LiveChatV2Users.Count != 0)
        {
            userProfileViewModel.LiveChatV2Senders =
                _mapper.Map<List<LiveChatV2SenderResponse>>(conversation.LiveChatV2Users);
        }
    }
}
