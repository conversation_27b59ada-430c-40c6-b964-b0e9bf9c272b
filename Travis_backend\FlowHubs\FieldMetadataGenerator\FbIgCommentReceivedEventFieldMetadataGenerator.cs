﻿#nullable enable
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.Database.Services;
using Travis_backend.Extensions;
using Travis_backend.FlowHubs.Constants;
using Travis_backend.FlowHubs.Models;
using Travis_backend.FlowHubs.Services;
using Travis_backend.Utils;

namespace Travis_backend.FlowHubs.FieldMetadataGenerator;

public sealed class FbIgCommentReceivedEventFieldMetadataGenerator :
    FlowHubEventFieldMetadataGeneratorBase,
    IFlowHubEventFieldMetadataGenerator
{
    public string EventName => TriggerIds.FbIgPostCommentReceived;

    public Dictionary<string, string> FieldPathOptionRequestPathDict { get; } = new()
    {
        { "contact.labels[*].LabelValue", "FlowHub/SelectOptions/Labels" },
        { "page_id", "FlowHub/SelectOptions/FbIgChannels" },
        { "contactOwner.id", "FlowHub/SelectOptions/Staffs" },
        { "contact.LastChannel", "FlowHub/SelectOptions/ChannelTypes" }
    };

    public FbIgCommentReceivedEventFieldMetadataGenerator(
        IDbContextService dbContextService, IFieldMetadataVariableVisibilityService fieldMetadataVariableVisibilityService)
        : base(dbContextService, fieldMetadataVariableVisibilityService)
    {
    }

    public async Task<(HashSet<FieldMetadata> FieldMetadataSet, Type EventBodyType, string ContactObjectName)> GenerateAsync(
        string companyId,
        Dictionary<string, object?>? parameters)
    {
        var (userProfileDict, customFieldOptionsRequestPathDict) = await GenerateUserProfileDictAsync(companyId);

        var eventBody = JsonUtils.GenerateSampleObjectFromType<OnFbIgPostCommentReceivedEventBody>();

        eventBody.Contact = userProfileDict;
        eventBody.ConversationId = userProfileDict["conversation_id"].ToString()!;
        eventBody.PostComment.CommentId = null!;
        eventBody.PostComment.From.FacebookId = null!;
        eventBody.CommentId = null!;

        var eventBodyJsonString = eventBody.ToJson();
        var eventBodyJson = JObject.Parse(eventBodyJsonString);
        AddContactExpansionToEventBodyJson(eventBodyJson);

        var jsonData = JsonUtils.SimplifyJsonData(eventBodyJson.ToString());

        var fieldMetadataSet = GenerateFilteredFieldMetadata(jsonData, EventName);

        PopulateOptionRequestPath(
            fieldMetadataSet,
            customFieldOptionsRequestPathDict,
            FieldPathOptionRequestPathDict);

        return (fieldMetadataSet, typeof(OnFbIgPostCommentReceivedEventBody), nameof(eventBody.Contact));
    }
}