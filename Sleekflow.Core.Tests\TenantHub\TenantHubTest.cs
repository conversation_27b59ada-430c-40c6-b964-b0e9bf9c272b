using System.Globalization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using NetTools;
using Newtonsoft.Json;
using Sleekflow.Apis.TenantHub.Model;
using Sleekflow.Core.Tests.Tools;
using Travis_backend.Auth0.Controllers.Webhook;
using Travis_backend.Auth0.Services;
using Travis_backend.Cache;
using Travis_backend.Database;
using Travis_backend.TenantHubDomain.Middleware;
using Travis_backend.TenantHubDomain.Services;
using Microsoft.EntityFrameworkCore;

namespace Sleekflow.Core.Tests.TenantHub;

[TestFixture]
public class TenantHubTest : TenanthubSetup
{
    [Test]
    public async Task TenantHubConnectionTest()
    {
        var allFeatures = await _managementFeaturesApi.ManagementFeaturesGetAllFeaturesPostAsync(body: new object());
        await TestContext.Progress.WriteLineAsync($"{JsonConvert.SerializeObject(allFeatures)}");

        Assert.IsNotNull(allFeatures);
    }

    [Test]
    [TestCase("<EMAIL>","105066e3-19e1-4857-97a2-28eaea89108b", "**************")]
    [TestCase("<EMAIL>","105066e3-19e1-4857-97a2-28eaea89108b", "**************")]
    [TestCase("<EMAIL>","105066e3-19e1-4857-97a2-28eaea89108b", "**************", false)]
    [TestCase("<EMAIL>","105066e3-19e1-4857-97a2-28eaea89108b", "**************", false)]
    public async Task IPTokenEventTest(string userEmail, string companyId, string ip, bool isValidResult=true)
    {
        var secretKey = FakeDataGenerator.GetAuth0EventSecret(_conf);
        var eventToken = FakeDataGenerator.CreateAuth0EventToken(_conf, secretKey);
        var mockIpLists = FakeDataGenerator.validIpLists;
        var mockIpDetails = mockIpLists.Select(
            ip => new IpRangeDetails(ip, string.Empty))
            .ToList();
        var user = await _userManager.FindByEmailAsync(userEmail);
        var eventUsers = await _userManager.GetAuth0Users(user);

        await TestContext.Progress.WriteLineAsync(
            $"Please add following data first:\n{JsonConvert.SerializeObject(mockIpDetails, Formatting.Indented)}");

        var result =
            await _managementIpWhitelistsApi.ManagementIpWhitelistsIsAllowedIpPostAsync(
                managementIsAllowedIpInput: new (
                    companyId,
                    ip));

        await TestContext.Progress.WriteLineAsync($"Result:\n{JsonConvert.SerializeObject(result)}");
        Assert.That(result.Data.IsAllowedIp, Is.EqualTo(isValidResult));
    }

    [TestCase("**************, **************:21433")]
    public async Task AzureIpStringTest(string ipStr)
    {
        var ipInfo = ipStr
            .Replace("\"", string.Empty)
            .Split(',')
            .Where(v => !string.IsNullOrEmpty(v))
            .ToList()
            .First();

        await TestContext.Progress.WriteLineAsync($"Ip: {ipInfo}");

        try
        {
            IPAddressRange.Parse(ipInfo);
            Assert.True(true);
        }
        catch (Exception e)
        {
            Assert.Fail(e.Message);
        }
    }

    [TestCase("105066e3-19e1-4857-97a2-28eaea89108b")]
    public async Task IpWhitelistsCacheWebhookTest(string companyId)
    {
        var secretKey = FakeDataGenerator.GetAuth0EventSecret(_conf);
        var token = TenanthubSetup.CreateAuth0EventToken();
        // var token = FakeDataGenerator.CreateAuth0EventToken(_conf, secretKey);
        var context = new DefaultHttpContext();
        context.Request.Headers.Add("X-Tenant-Hub-Authorization", token);
        _internalTenantHubActionEventController.ControllerContext = new ControllerContext()
        {
            HttpContext = context
        };
        var res =
            (await _internalTenantHubActionEventController.RefreshIpWhitelistCache(
                new InternalTenantHubActionEventController.RefreshIpWhitelistCacheInput()
                {
                    CompanyId = companyId
                })).Result as OkObjectResult;
        var output = res.Value as InternalTenantHubActionEventController.RefreshIpWhitelistCacheOutput;

        await TestContext.Progress.WriteLineAsync($"Result: {JsonConvert.SerializeObject(res, Formatting.Indented)}");

        Assert.That(output.Success && output.IsEnabled, Is.True);
    }

    [TestCase("<EMAIL>", "P@ssw0rd!")]
    public async Task IPWhitelistMiddlewareTest(string userEmail, string password)
    {
        var invalidIp = "**************";
        var context = new DefaultHttpContext();
        var testUser = await LoginUser(userEmail, password);
        var principal = TokenToUserLoginPrincipal(testUser.AccessToken);
        var next = (HttpContext hc) => Task.CompletedTask;
        var _logger = new Mock<ILogger<IPWhitelistMiddleware>>();
        var middleware = new IPWhitelistMiddleware(
            next: (innerHttpContext) =>
            {
                innerHttpContext.Response.WriteAsync(CultureInfo.CurrentCulture.TwoLetterISOLanguageName);
                return Task.CompletedTask;
            },
            _logger.Object);
        var userId = _userManager.GetUserId(principal);
        var userCacheKey = $"IPWhitelist-{invalidIp}-{userId}-isAllowed";

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(userCacheKey, "false", TimeSpan.FromMinutes(5));

        context.Request.Headers.Add("Via", "Azure");
        context.Request.Headers.Add("X-Forwarded-For", invalidIp);
        context.User = principal;

        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName: "TestDb")
            .Options;
        var _appDbContext = new ApplicationDbContext(options);

        var enabledFeaturesServiceLogger = new Mock<ILogger<EnabledFeaturesService>>();
        var enabledFeaturesService = new EnabledFeaturesService(
            _managementEnabledFeaturesApi,
            _managementFeaturesApi,
            enabledFeaturesServiceLogger.Object,
            _cacheManagerService,
            _appDbContext);

        var providerLogger = new Mock<ILogger<IpWhitelistSettingsProvider>>();
        var ipWhitelistSettingsProvider = new IpWhitelistSettingsProvider(
            _cacheManagerService,
            enabledFeaturesService,
            _managementIpWhitelistsApi,
            providerLogger.Object);

        var mockMobileDetectionService = new Mock<IMobilePlatformDetectionService>();
        mockMobileDetectionService.Setup(s => s.IsMobileDevice(It.IsAny<string>())).Returns(false);

        var testCount = 10;
        var blockedCount = 0;
        for (int i = 0; i < testCount; i++)
        {
            try
            {
                middleware.InvokeAsync(
                        context,
                        _userManager,
                        _coreService,
                        _cacheManagerService,
                        ipWhitelistSettingsProvider,
                        mockMobileDetectionService.Object)
                    .Wait();
            }
            catch (Exception err)
            {
                blockedCount++;
                await TestContext.Progress.WriteLineAsync($"{err.Message} at attempts: {i}");
            }
        }

        Assert.That(blockedCount, Is.GreaterThan(0), "Blocked requests should be more than 0");
        await TestContext.Progress.WriteLineAsync($"Blocked requests: {blockedCount}/{testCount}");
    }
}