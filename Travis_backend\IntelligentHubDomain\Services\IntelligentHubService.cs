﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Sleekflow.Apis.IntelligentHub.Api;
using Sleekflow.Apis.IntelligentHub.Model;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.InternalDomain.ViewModels;
using Travis_backend.SubscriptionPlanDomain.Constants;
using Travis_backend.SubscriptionPlanDomain.Enums;
using Travis_backend.SubscriptionPlanDomain.Models;
using Travis_backend.SubscriptionPlanDomain.Services;
using IntelligentHubConfig = Travis_backend.IntelligentHubDomain.Models.IntelligentHubConfig;
using V2IntelligentHubConfig = Sleekflow.Apis.IntelligentHub.Model.IntelligentHubConfig;

namespace Travis_backend.IntelligentHubDomain.Services;

public interface IIntelligentHubService
{
    Task RefreshIntelligentHubConfigAsync(string companyId);

    Task<Dictionary<string, int>> InitializeIntelligentHubConfig(string companyId);

    Task<GetTopicsOutputOutput> GetTopics(string companyId);

    Task<CreateTopicOutputOutput> CreateTopic(
        string companyId,
        long staffId,
        string name,
        List<TopicAnalyticsTerm> terms);

    Task<UpdateTopicOutputOutput> UpdateTopic(
        string companyId,
        string id,
        long staffId,
        Dictionary<string, object> updatedProperties);

    Task<DeleteTopicsOutputOutput> DeleteTopics(
        string companyId,
        List<string> ids,
        long staffId);

    Task<List<CmsCompanyIntelligentHubData>> GetCompaniesIntelligentHubDataAsync();

    Task<InternalIntelligentHubDto> GetInternalIntelligentHubDataAsync(string companyId);

    Task UpdateIntelligentHubConfigUsageLimitOffsetsAsync(
        string companyId,
        Dictionary<string, int> usageLimitOffsets,
        string userId);

    Task<IntelligentHubUsageFilter> GenerateIntelligentHubUsageFilter(string companyId);

    Task RefreshIntelligentHubConfigsAsync(List<string> companyIds);

    Task<Dictionary<string, int>> CheckAndUpdateIntelligentHubConfig(
        string companyId,
        Dictionary<string, int> originalUsageLimits);
}

public class IntelligentHubService : IIntelligentHubService
{
    private readonly ILogger<IntelligentHubService> _logger;
    private readonly ITopicAnalyticsApi _topicAnalyticsApi;
    private readonly ApplicationDbContext _appDbContext;
    private readonly IIntelligentHubConfigsApi _intelligentHubConfigsApi;
    private readonly IPlanDefinitionService _planDefinitionService;
    private readonly ICompanySubscriptionService _companySubscriptionService;

    private readonly IInternalsApi _internalsApi;

    public IntelligentHubService(
        ApplicationDbContext appDbContext,
        IIntelligentHubConfigsApi intelligentHubConfigsApi,
        ILogger<IntelligentHubService> logger,
        ITopicAnalyticsApi topicAnalyticsApi,
        IPlanDefinitionService planDefinitionService,
        ICompanySubscriptionService companySubscriptionService,
        IInternalsApi internalsApi)
    {
        _appDbContext = appDbContext;
        _intelligentHubConfigsApi = intelligentHubConfigsApi;
        _logger = logger;
        _topicAnalyticsApi = topicAnalyticsApi;
        _planDefinitionService = planDefinitionService;
        _companySubscriptionService = companySubscriptionService;
        _internalsApi = internalsApi;
    }

    public async Task RefreshIntelligentHubConfigAsync(string companyId)
    {
        _logger.LogInformation("Update IntelligentHub config for Company {CompanyId}", companyId);

        var usageLimits = await GetUsageLimits(companyId);

        var getIntelligentHubConfigOutputOutput =
            await _intelligentHubConfigsApi.IntelligentHubConfigsGetIntelligentHubConfigPostAsync(
                getIntelligentHubConfigInput: new GetIntelligentHubConfigInput(companyId));

        // ReSharper disable once ConditionIsAlwaysTrueOrFalse - false positive
        if (getIntelligentHubConfigOutputOutput.Data.IntelligentHubConfig == null)
        {
            await InitializeIntelligentHubConfig(companyId);

            return;
        }

        var updateIntelligentHubConfigOutputOutput =
            await _intelligentHubConfigsApi.IntelligentHubConfigsUpdateIntelligentHubConfigPostAsync(
                updateIntelligentHubConfigInput: new UpdateIntelligentHubConfigInput(
                    companyId,
                    usageLimits));

        if (!updateIntelligentHubConfigOutputOutput.Success)
        {
            _logger.LogError(
                "Update IntelligentHub config error: company id {CompanyId}",
                companyId);
        }
    }

    public async Task<Dictionary<string, int>> InitializeIntelligentHubConfig(string companyId)
    {
        _logger.LogInformation("Initialize IntelligentHub config for Company {CompanyId}", companyId);

        try
        {
            var usageLimits = await GetUsageLimits(companyId);

            var intelligentHubConfigOutputOutput =
                await _intelligentHubConfigsApi.IntelligentHubConfigsInitializeIntelligentHubConfigPostAsync(
                    initializeIntelligentHubConfigInput: new InitializeIntelligentHubConfigInput(
                        companyId,
                        usageLimits));

            if (!intelligentHubConfigOutputOutput.Success)
            {
                throw new Exception(intelligentHubConfigOutputOutput.Message);
            }

            return intelligentHubConfigOutputOutput.Data.IntelligentHubConfig.UsageLimits;
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "Failed to initialize IntelligentHub Config. {CompanyId} {Message}",
                companyId,
                e.Message);
            throw new Exception("Failed to initialize IntelligentHub Config.", e);
        }
    }

    private async Task<Dictionary<string, int>> GetUsageLimits(string companyId)
    {
        var usageLimits = new Dictionary<string, int>();

        var billRecord = await _companySubscriptionService.GetCurrentBaseSubscriptionBillRecord(companyId);

        var planDefinition = await _planDefinitionService.GetPlanDefinitionAsync(billRecord?.SubscriptionPlanId);

        foreach (var priceableFeatureId in IntelligentHubConfig.PriceableFeatureIds)
        {
            var featureQuantity =
                planDefinition.FeatureQuantities.Find(fq => fq.FeatureId == priceableFeatureId);
            usageLimits.Add(priceableFeatureId, featureQuantity?.Quantity ?? 0);
        }

        var addOnBillRecords =
            await _companySubscriptionService.GetActiveBillRecordsAsync(
                companyId,
                addOnBillRecord =>
                    addOnBillRecord.SubscriptionPlanId == "sleekflow_v10_ai_poc_300" ||
                    addOnBillRecord.SubscriptionPlanId == "sleekflow_v10_ai_poc_500" ||
                    addOnBillRecord.SubscriptionPlanId == "sleekflow_v10_ai_poc_1000");

        // Handle add-on plans for AI POC plans
        foreach (var addOnBillRecord in addOnBillRecords)
        {
            var addOnPlanDefinition =
                await _planDefinitionService.GetPlanDefinitionAsync(addOnBillRecord.SubscriptionPlanId);

            // Update usage limits with add-on plan values
            foreach (var priceableFeatureId in IntelligentHubConfig.PriceableFeatureIds)
            {
                var addOnFeatureQuantity =
                    addOnPlanDefinition.FeatureQuantities.Find(fq => fq.FeatureId == priceableFeatureId);
                if (addOnFeatureQuantity == null)
                {
                    continue;
                }

                if (addOnFeatureQuantity.Quantity > usageLimits[priceableFeatureId])
                {
                    usageLimits[priceableFeatureId] = addOnFeatureQuantity.Quantity;
                }
            }
        }

        return usageLimits;
    }

    public async Task<List<CmsCompanyIntelligentHubData>> GetCompaniesIntelligentHubDataAsync()
    {
        string continuationToken = null;
        var intelligentHubConfigs = new List<V2IntelligentHubConfig>();

        do
        {
            var intelligentHubConfigsOutputOutput =
                await _intelligentHubConfigsApi.IntelligentHubConfigsGetIntelligentHubConfigsPostAsync(
                    getIntelligentHubConfigsInput: new GetIntelligentHubConfigsInput(
                        continuationToken,
                        1000));

            intelligentHubConfigs.AddRange(intelligentHubConfigsOutputOutput.Data.IntelligentHubConfigs);
            continuationToken = intelligentHubConfigsOutputOutput.Data.ContinuationToken;
        }
        while (!string.IsNullOrEmpty(continuationToken));

        var intelligentHubConfigsLookup =
            intelligentHubConfigs.ToDictionary(
                x => x.SleekflowCompanyId,
                x => x);

        var cmsCompanyIntelligentHubDataList = await _appDbContext.CompanyCompanies
            .AsNoTracking()
            .Where(x => x.IsDeleted == false)
            .Select(
                x => new CmsCompanyIntelligentHubData
                {
                    CompanyId = x.Id
                })
            .ToListAsync();

        ConcurrentBag<CmsCompanyIntelligentHubData> enrolledIntelligentHubCompanies = new();
        var totalAiFeaturesId = IntelligentHubConfig.PriceableFeatureIds[0];
        var totalAgentFeaturesId = IntelligentHubConfig.PriceableFeatureIds[1];

        await Parallel.ForEachAsync(
            cmsCompanyIntelligentHubDataList,
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 10
            },
            (company, cancellationToken) =>
            {
                if (!intelligentHubConfigsLookup.TryGetValue(company.CompanyId, out var intelligentHubConfig))
                {
                    return ValueTask.CompletedTask;
                }

                company.TotalAiFeaturesUsageLimit =
                    intelligentHubConfig.UsageLimits.TryGetValue(totalAiFeaturesId, out var aiFeaturesUsageLimit)
                        ? aiFeaturesUsageLimit
                        : 0;

                if (intelligentHubConfig.UsageLimitOffsets != null &&
                    intelligentHubConfig.UsageLimitOffsets.TryGetValue(totalAiFeaturesId, out var usageLimitOffset))
                {
                    company.TotalAiFeaturesUsageLimit += usageLimitOffset!.Value;
                }

                company.TotalAgentFeaturesUsageLimit =
                    intelligentHubConfig.UsageLimits.TryGetValue(totalAgentFeaturesId, out var agentFeaturesUsageLimit)
                        ? agentFeaturesUsageLimit
                        : 0;

                if (intelligentHubConfig.UsageLimitOffsets != null &&
                    intelligentHubConfig.UsageLimitOffsets.TryGetValue(
                        totalAgentFeaturesId,
                        out var agentUsageLimitOffset))
                {
                    company.TotalAgentFeaturesUsageLimit += agentUsageLimitOffset!.Value;
                }

                enrolledIntelligentHubCompanies.Add(company);

                return ValueTask.CompletedTask;
            });

        // Get current subscription for each enrolled company
        var enrolledIntelligentHubCompaniesIds = enrolledIntelligentHubCompanies.Select(x => x.CompanyId).ToList();

        var companiesCurrentSubscriptionDictionary = await _appDbContext.CompanyCompanies
            .AsNoTracking()
            .AsSplitQuery()
            .Include(x => x.BillRecords)
            .Where(c => enrolledIntelligentHubCompaniesIds.Contains(c.Id))
            .Select(x => new
            {
                CompanyId = x.Id,
                CurrentSubscription = x.BillRecords.Where(br =>
                        br.Status != BillStatus.Inactive
                        && br.Status != BillStatus.Terminated
                        && ValidSubscriptionPlan.SubscriptionPlan.Contains(br.SubscriptionPlanId)
                        && br.PeriodStart <= DateTime.UtcNow
                        && br.PeriodEnd > DateTime.UtcNow)
                    .OrderByDescending(br => br.created)
                    .ThenByDescending(br => br.PayAmount)
                    .FirstOrDefault(),
                CurrentAiPocAddOnPlans = x.BillRecords.Where(br =>
                        br.Status != BillStatus.Inactive
                        && br.Status != BillStatus.Terminated
                        && ValidSubscriptionPlan.AiAgentCreditPocAddOns.Contains(br.SubscriptionPlanId)
                        && br.PeriodStart <= DateTime.UtcNow
                        && br.PeriodEnd > DateTime.UtcNow)
                    .OrderByDescending(br => br.created)
                    .ThenByDescending(br => br.PayAmount)
                    .ToList()
            })
            .ToDictionaryAsync(
                x => x.CompanyId,
                x => new
                {
                    x.CurrentSubscription,
                    x.CurrentAiPocAddOnPlans
                });

        var subscriptionPlanIds = companiesCurrentSubscriptionDictionary
            .Where(x => x.Value?.CurrentSubscription != null)
            .Select(x => x.Value.CurrentSubscription.SubscriptionPlanId)
            .Distinct()
            .ToList();

        subscriptionPlanIds.AddRange(ValidSubscriptionPlan.AiAgentCreditPocAddOns);

        var planDefinitionsDictionary =
            await _planDefinitionService.GetPlanDefinitionsDictionaryAsync(subscriptionPlanIds);

        var companyIdsNeedToUpdateConfig = new List<string>();

        await Parallel.ForEachAsync(
            enrolledIntelligentHubCompanies,
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 10
            },
            async (company, cancellationToken) =>
            {
                try
                {
                    var currentSubscriptions =
                        companiesCurrentSubscriptionDictionary[company.CompanyId];

                    if (currentSubscriptions?.CurrentSubscription != null)
                    {
                        var intelligentHubConfig = intelligentHubConfigsLookup[company.CompanyId];

                        // Get base subscription plan definition
                        var planDefinitions = new List<PlanDefinition>();
                        if (planDefinitionsDictionary.TryGetValue(
                                currentSubscriptions.CurrentSubscription.SubscriptionPlanId,
                                out var basePlanDefinition))
                        {
                            planDefinitions.Add(basePlanDefinition);
                        }

                        // Get add-on plan definitions
                        foreach (var addOnBillRecord in currentSubscriptions.CurrentAiPocAddOnPlans)
                        {
                            if (planDefinitionsDictionary.TryGetValue(
                                    addOnBillRecord.SubscriptionPlanId,
                                    out var addOnPlanDefinition))
                            {
                                planDefinitions.Add(addOnPlanDefinition);
                            }
                        }

                        var (isUpdateNeeded, updatedUsageLimits) =
                            CheckIsValidUsageLimit(intelligentHubConfig.UsageLimits, planDefinitions);

                        if (isUpdateNeeded)
                        {
                            companyIdsNeedToUpdateConfig.Add(company.CompanyId);
                            company.TotalAiFeaturesUsageLimit =
                                updatedUsageLimits.TryGetValue(totalAiFeaturesId, out var updatedAiFeaturesUsageLimit)
                                    ? updatedAiFeaturesUsageLimit
                                    : 0;

                            if (intelligentHubConfig.UsageLimitOffsets != null &&
                                intelligentHubConfig.UsageLimitOffsets.TryGetValue(
                                    totalAiFeaturesId,
                                    out var usageLimitOffset))
                            {
                                company.TotalAiFeaturesUsageLimit += usageLimitOffset!.Value;
                            }

                            company.TotalAgentFeaturesUsageLimit = updatedUsageLimits.TryGetValue(
                                totalAgentFeaturesId,
                                out var updatedAgentFeaturesUsageLimit)
                                ? updatedAgentFeaturesUsageLimit
                                : 0;

                            if (intelligentHubConfig.UsageLimitOffsets != null &&
                                intelligentHubConfig.UsageLimitOffsets.TryGetValue(
                                    totalAgentFeaturesId,
                                    out var agentUsageLimitOffset))
                            {
                                company.TotalAgentFeaturesUsageLimit += agentUsageLimitOffset!.Value;
                            }
                        }
                    }

                    var intelligentHubUsageFilter =
                        GenerateIntelligentHubUsageFilter(currentSubscriptions?.CurrentSubscription);

                    var getFeatureUsagesOutputOutput =
                        await _intelligentHubConfigsApi.IntelligentHubConfigsGetFeatureUsagesPostAsync(
                            getFeatureUsagesInput: new GetFeatureUsagesInput(
                                company.CompanyId,
                                IntelligentHubConfig.PriceableFeatureIds.ToList(),
                                intelligentHubUsageFilter),
                            cancellationToken: cancellationToken);

                    if (getFeatureUsagesOutputOutput.Success)
                    {
                        company.TotalAiFeaturesUsage =
                            getFeatureUsagesOutputOutput.Data.FeatureUsages[totalAiFeaturesId];
                        company.TotalAiFeaturesUsagePercentage = company.TotalAiFeaturesUsageLimit == 0
                            ? 0.00M
                            : company.TotalAiFeaturesUsage / Convert.ToDecimal(company.TotalAiFeaturesUsageLimit);
                        company.TotalAgentFeaturesUsage =
                            getFeatureUsagesOutputOutput.Data.FeatureUsages[totalAgentFeaturesId];
                        company.TotalAgentFeaturesUsagePercentage = company.TotalAgentFeaturesUsageLimit == 0
                            ? 0.00M
                            : company.TotalAgentFeaturesUsage / Convert.ToDecimal(company.TotalAgentFeaturesUsageLimit);
                    }
                    else
                    {
                        company.TotalAiFeaturesUsage = 0;
                        company.TotalAiFeaturesUsagePercentage = 0.00M;
                        company.TotalAgentFeaturesUsage = 0;
                        company.TotalAgentFeaturesUsagePercentage = 0.00M;
                    }
                }
                catch (Exception e)
                {
                    _logger.LogError(
                        e,
                        "Intelligent Hub Get Feature Usage Error, Company ID: {CompanyID}, Error Message: {ErrorMessage}",
                        company.CompanyId,
                        e.Message);
                }
            });

        BackgroundJob.Enqueue<IIntelligentHubService>(
            x => x.RefreshIntelligentHubConfigsAsync(companyIdsNeedToUpdateConfig));

        return enrolledIntelligentHubCompanies.ToList();
    }

    public async Task<InternalIntelligentHubDto> GetInternalIntelligentHubDataAsync(string companyId)
    {
        var internalIntelligentHubDto = new InternalIntelligentHubDto();

        var intelligentHubConfigOutputOutput =
            await _intelligentHubConfigsApi.IntelligentHubConfigsGetIntelligentHubConfigPostAsync(
                getIntelligentHubConfigInput: new GetIntelligentHubConfigInput(companyId));

        if (intelligentHubConfigOutputOutput.Data.IntelligentHubConfig == null)
        {
            return null;
        }

        internalIntelligentHubDto.UsageLimits = await CheckAndUpdateIntelligentHubConfig(
            companyId,
            intelligentHubConfigOutputOutput.Data.IntelligentHubConfig.UsageLimits);

        if (intelligentHubConfigOutputOutput.Data.IntelligentHubConfig.UsageLimitOffsets == null)
        {
            internalIntelligentHubDto.UsageLimitOffsets =
                internalIntelligentHubDto.UsageLimits.ToDictionary(x => x.Key, x => 0);
        }
        else
        {
            internalIntelligentHubDto.UsageLimitOffsets =
                internalIntelligentHubDto.UsageLimits.ToDictionary(
                    x => x.Key,
                    x => intelligentHubConfigOutputOutput.Data.IntelligentHubConfig.UsageLimitOffsets.TryGetValue(
                        x.Key,
                        out var usageLimitOffset)
                        ? usageLimitOffset!.Value
                        : 0);
        }

        var featureNames = new List<string>(internalIntelligentHubDto.UsageLimits.Keys);

        var getFeatureUsagesOutputOutput =
            await _intelligentHubConfigsApi.IntelligentHubConfigsGetFeatureUsagesPostAsync(
                getFeatureUsagesInput: new GetFeatureUsagesInput(
                    companyId,
                    featureNames,
                    await GenerateIntelligentHubUsageFilter(companyId)));

        if (getFeatureUsagesOutputOutput.Success)
        {
            internalIntelligentHubDto.FeatureUsages = getFeatureUsagesOutputOutput.Data.FeatureUsages;
        }

        var getInternalCompanyAgentConfigsOutputOutput = await _internalsApi.InternalsGetInternalCompanyAgentConfigsPostAsync(
            getInternalCompanyAgentConfigsInput: new GetInternalCompanyAgentConfigsInput(companyId));

        if (getInternalCompanyAgentConfigsOutputOutput.Success)
        {
            internalIntelligentHubDto.CompanyAgentConfigs = getInternalCompanyAgentConfigsOutputOutput.Data;
        }

        return internalIntelligentHubDto;
    }

    public async Task UpdateIntelligentHubConfigUsageLimitOffsetsAsync(
        string companyId,
        Dictionary<string, int> usageLimitOffsets,
        string userId)
    {
        Dictionary<string, int> newUsageLimitOffsets;

        var intelligentHubConfigOutputOutput =
            await _intelligentHubConfigsApi.IntelligentHubConfigsGetIntelligentHubConfigPostAsync(
                getIntelligentHubConfigInput: new GetIntelligentHubConfigInput(companyId));

        var existingUsageLimitOffsets =
            intelligentHubConfigOutputOutput.Data.IntelligentHubConfig.UsageLimitOffsets;

        if (existingUsageLimitOffsets != null)
        {
            newUsageLimitOffsets = existingUsageLimitOffsets.ToDictionary(x => x.Key, x => x.Value!.Value);

            foreach (var usageLimitOffset in usageLimitOffsets)
            {
                newUsageLimitOffsets[usageLimitOffset.Key] = usageLimitOffset.Value;
            }
        }
        else
        {
            newUsageLimitOffsets = usageLimitOffsets;
        }

        var response = await _intelligentHubConfigsApi
            .IntelligentHubConfigsUpdateIntelligentHubConfigUsageLimitOffsetsPostAsync(
                updateIntelligentHubConfigUsageLimitOffsetsInput: new UpdateIntelligentHubConfigUsageLimitOffsetsInput(
                    companyId,
                    newUsageLimitOffsets,
                    userId));

        if (!response.Success)
        {
            _logger.LogError(
                "Failed to update IntelligentHubConfig UsageLimitOffsets. {CompanyId}, {Message}",
                companyId,
                response.Message);

            throw new Exception("Failed to update IntelligentHubConfig UsageLimitOffsets.");
        }
    }

    public async Task<GetTopicsOutputOutput> GetTopics(string companyId)
    {
        var result = await _topicAnalyticsApi.TopicAnalyticsGetTopicsPostAsync(
            getTopicsInput: new GetTopicsInput(companyId));
        return result;
    }

    public async Task<CreateTopicOutputOutput> CreateTopic(
        string companyId,
        long staffId,
        string name,
        List<TopicAnalyticsTerm> terms)
    {
        var staffTeamIds = new List<string>();

        staffTeamIds = await _appDbContext.CompanyTeamMembers
            .AsNoTracking()
            .Where(y => y.StaffId == staffId)
            .Select(y => y.CompanyTeamId.ToString())
            .ToListAsync();

        var result = await _topicAnalyticsApi.TopicAnalyticsCreateTopicPostAsync(
            createTopicInput: new CreateTopicInput(companyId, staffId.ToString(), staffTeamIds, name, terms));
        return result;
    }

    public async Task<UpdateTopicOutputOutput> UpdateTopic(
        string companyId,
        string id,
        long staffId,
        Dictionary<string, object> updatedProperties)
    {
        var staffTeamIds = new List<string>();

        staffTeamIds = await _appDbContext.CompanyTeamMembers
            .AsNoTracking()
            .Where(y => y.StaffId == staffId)
            .Select(y => y.CompanyTeamId.ToString())
            .ToListAsync();

        var result = await _topicAnalyticsApi.TopicAnalyticsUpdateTopicPostAsync(
            updateTopicInput: new UpdateTopicInput(companyId, id, staffId.ToString(), staffTeamIds, updatedProperties));
        return result;
    }

    public async Task<DeleteTopicsOutputOutput> DeleteTopics(string companyId, List<string> ids, long staffId)
    {
        var staffTeamIds = new List<string>();

        staffTeamIds = await _appDbContext.CompanyTeamMembers
            .AsNoTracking()
            .Where(y => y.StaffId == staffId)
            .Select(y => y.CompanyTeamId.ToString())
            .ToListAsync();
        var result = await _topicAnalyticsApi.TopicAnalyticsDeleteTopicsPostAsync(
            deleteTopicsInput: new DeleteTopicsInput(companyId, ids, staffId.ToString(), staffTeamIds));
        return result;
    }

    public async Task<IntelligentHubUsageFilter> GenerateIntelligentHubUsageFilter(string companyId)
    {
        // Calculate per month based on billing period
        var billRecord = await _companySubscriptionService.GetCurrentBaseSubscriptionBillRecord(companyId);

        var usageCycle = _companySubscriptionService.GetMonthlyUsageCycleAsync(billRecord);

        return new IntelligentHubUsageFilter(
            new DateTimeOffset(usageCycle.From, TimeSpan.Zero),
            new DateTimeOffset(usageCycle.To, TimeSpan.Zero));
    }

    public async Task RefreshIntelligentHubConfigsAsync(
        List<string> companyIds)
    {
        foreach (var companyId in companyIds)
        {
            try
            {
                await RefreshIntelligentHubConfigAsync(companyId);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Refresh IntelligentHub config error: company id {CompanyId}",
                    companyId);
            }
        }
    }

    public async Task<Dictionary<string, int>> CheckAndUpdateIntelligentHubConfig(
        string companyId,
        Dictionary<string, int> originalUsageLimits)
    {
        var isUpdateNeeded = false;

        var usageLimits = await GetUsageLimits(companyId);

        foreach (var priceableFeatureId in IntelligentHubConfig.PriceableFeatureIds)
        {
            if (originalUsageLimits.TryGetValue(priceableFeatureId, out var originalUsageLimit))
            {
                // No need to update if the usage limit is the same
                if (originalUsageLimit == usageLimits[priceableFeatureId])
                {
                    continue;
                }

                isUpdateNeeded = true;
                originalUsageLimits[priceableFeatureId] = usageLimits[priceableFeatureId];
            }
            else
            {
                isUpdateNeeded = true;
                originalUsageLimits.Add(priceableFeatureId, usageLimits[priceableFeatureId]);
            }
        }

        if (isUpdateNeeded)
        {
            BackgroundJob.Enqueue<IIntelligentHubService>(
                x => x.RefreshIntelligentHubConfigAsync(companyId));
        }

        return originalUsageLimits;
    }

    private IntelligentHubUsageFilter GenerateIntelligentHubUsageFilter(BillRecord billRecord)
    {
        var usageCycle = _companySubscriptionService.GetMonthlyUsageCycleAsync(billRecord);

        return new IntelligentHubUsageFilter(
            new DateTimeOffset(usageCycle.From, TimeSpan.Zero),
            new DateTimeOffset(usageCycle.To, TimeSpan.Zero));
    }

    private static (bool IsUpdateNeeded, Dictionary<string, int> UpdatedUsageLimit) CheckIsValidUsageLimit(
        Dictionary<string, int> originalUsageLimits,
        List<PlanDefinition> planDefinitions)
    {
        var isUpdateNeeded = false;
        var updatedUsageLimit = originalUsageLimits;

        // Check all priceable feature IDs
        foreach (var priceableFeatureId in IntelligentHubConfig.PriceableFeatureIds)
        {
            // Sum quantities from all plan definitions for this feature
            var totalFeatureUsageLimit = planDefinitions.Sum(planDefinition =>
                planDefinition.FeatureQuantities.Find(x => x.FeatureId == priceableFeatureId)?.Quantity ?? 0);

            if (originalUsageLimits.TryGetValue(priceableFeatureId, out var originalUsageLimit) &&
                originalUsageLimit == totalFeatureUsageLimit)
            {
                continue;
            }

            isUpdateNeeded = true;
            updatedUsageLimit[priceableFeatureId] = totalFeatureUsageLimit;
        }

        return (isUpdateNeeded, updatedUsageLimit);
    }
}