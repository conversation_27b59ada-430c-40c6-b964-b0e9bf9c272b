﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using CsvHelper;
using CsvHelper.Configuration;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Polly;
using Sleekflow.Apis.FlowHub.Api;
using Sleekflow.Apis.FlowHub.Client;
using Sleekflow.Apis.FlowHub.Model;
using Sleekflow.Apis.MessagingHub.Model;
using Travis_backend.BroadcastDomain.Constants;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.Helpers;
using Travis_backend.InternalDomain.Models;
using Travis_backend.InternalDomain.ViewModels;
using Travis_backend.MessageDomain.Models;
using Travis_backend.ResellerDomain.Models;

namespace Travis_backend.InternalDomain.Services
{
    public interface IInternalCompanyDataRepository
    {
        ValueTask<List<CmsCompanyDetail>> GetCmsCompanyDetails(
            List<string> companyIds = null,
            List<CompanySnapshotData> lastDay = null);

        Task<bool> SaveCompanySnapshotDataAsync(List<CompanySnapshotData> companySnapshotData, DateTime savedAt);

        Task<List<CompanySnapshotData>> GetCompanySnapshotDataAsync(DateTime savedAt);

        Task<List<CmsCompanyOwner>> GetCmsCompanyOwners(List<string> companyIds = null);

        Task<List<CmsCompanyBillRecordDetail>> GetCmsCompanyBillRecords(List<string> companyIds = null);

        Task<List<CmsCompanySubscriptionPlan>> GetCmsCompanySubscriptionPlan(List<string> companyIds = null);

        Task<List<CmsCompanyOnboardingStatus>> GetCompanyOnboardingStatuses(List<string> companyIds = null);

        Task<List<CmsCompanyOnboardingProgress>> GetCompanyOnboardingProgresses(
            List<string> companyIds = null,
            List<CmsCompanySubscriptionPlan> companyDetails = null);

        Task<List<CmsSalesPaymentRecordSnapshotData>> GetCmsSalesInputPaymentCsv(List<string> companyIds = null);

        Task<List<CmsCompanyStaffData>> GetCmsCompanyStaffList(List<string> companyIds = null);

        Task<List<CmsSleekPayReportDataWithCompany>> GetCmsSleekPayReportData(List<string> stripeAccountIds = null);

        Task<List<ManagementWhatsappCloudApiConversationUsageAnalytic>>
            GetAllTimeWhatsappCloudApiConversationUsageAnalytic(DateTime start, DateTime end);

        Task<List<WhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData>>
            GetDailyWhatsappCloudApiConversationUsageAnalytic(
                DateTime start,
                DateTime end,
                bool allowSnapshotData = true);

        Task<List<WhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData>>
            GetDailyWhatsappCloudApiConversationUsageAnalyticMonthlySnapshot(DateTime startOfMonth);

        Task<List<ByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData>>
            GetByBusinessDailyWhatsappCloudApiConversationUsageAnalyticMonthlySnapshot(DateTime startOfMonth);

        Task<List<ByWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData>>
            GetByWabaDailyWhatsappCloudApiConversationUsageAnalyticMonthlySnapshot(DateTime startOfMonth);

        Task<bool>
            CreateAllTimeDailyWhatsappCloudApiConversationUsageAnalyticSnapshot(
                DateTime startOfMonth,
                List<WhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData> data);

        Task<List<CompanyFlowBuilderUsageSnapshotData>> GetCmsCompanyFlowBuilderUsageAsync(
            DateTime start,
            DateTime end);

        Task<List<CompanyFlowBuilderUsageSnapshotData>> GetCmsCompanyFlowBuilderUsageSnapshotAsync(
            DateTime date);

        Task<(List<ByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData> ByBusinessData,
              List<ByWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData> ByWabaData)>
            GetInternalDailyWhatsappCloudApiConversationUsageSnapshotDataAsync(DateTime startOfMonth);

        Task<bool>
            CreateInternalDailyWhatsappCloudApiConversationUsageAnalyticSnapshot(
                DateTime startOfMonth,
                List<ByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData>
                    byBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshots,
                List<ByWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData>
                    byWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshots);
    }

    public class InternalCompanyDataRepository : IInternalCompanyDataRepository
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly ILogger<InternalCompanyDataRepository> _logger;
        private readonly ICompanyUsageService _companyUsageService;
        private readonly IInternalWhatsappCloudApiService _internalWhatsappCloudApiService;
        private readonly IAzureBlobStorageService _azureBlobStorageService;
        private readonly IInternalGoogleCloudStorage _internalGoogleCloudStorage;
        private readonly IMapper _mapper;
        private readonly IWorkflowsApi _workflowsApi;
        private readonly IExecutionsApi _executionsApi;

        public InternalCompanyDataRepository(
            ApplicationDbContext appDbContext,
            ILogger<InternalCompanyDataRepository> logger,
            ICompanyUsageService companyUsageService,
            IMapper mapper,
            IInternalWhatsappCloudApiService internalWhatsappCloudApiService,
            IAzureBlobStorageService azureBlobStorageService,
            IWorkflowsApi workflowsApi,
            IExecutionsApi executionsApi,
            IInternalGoogleCloudStorage internalGoogleCloudStorage)
        {
            _appDbContext = appDbContext;
            _logger = logger;
            _companyUsageService = companyUsageService;
            _mapper = mapper;
            _internalWhatsappCloudApiService = internalWhatsappCloudApiService;
            _azureBlobStorageService = azureBlobStorageService;
            _workflowsApi = workflowsApi;
            _executionsApi = executionsApi;
            _internalGoogleCloudStorage = internalGoogleCloudStorage;
        }

        public async ValueTask<List<CmsCompanyDetail>> GetCmsCompanyDetails(
            List<string> companyIds = null,
            List<CompanySnapshotData> lastDay = null)
        {
            var from = DateTime.UtcNow.Date.AddDays(-1).AddHours(8);
            var to = DateTime.UtcNow.Date.AddHours(8);
            var pastWeek = DateTime.UtcNow.Date.AddDays(-7);
            var pastTwoWeek = DateTime.UtcNow.Date.AddDays(-14);
            var pastMonth = DateTime.UtcNow.Date.AddDays(-30);
            var pastTwoMonth = DateTime.UtcNow.Date.AddDays(-60);
            var pastThreeMonth = DateTime.UtcNow.Date.AddDays(-90);

            _appDbContext.Database.SetCommandTimeout(180);

            // Get Reseller Staffs
            var resellerStaffs = await _appDbContext.ResellerStaffs
                .AsNoTracking()
                .Select(rs => new ResellerStaff
                {
                    IdentityId = rs.IdentityId,
                    ResellerCompanyProfileId = rs.ResellerCompanyProfileId
                })
                .ToListAsync();

            var resellerClientCompanyProfiles = await _appDbContext.ResellerClientCompanyProfiles
                .AsNoTracking()
                .WhereIf(companyIds != null, r => companyIds.Contains(r.ClientCompanyId))
                .Select(r => new ResellerClientCompanyProfile
                {
                    Id = r.Id,
                    ResellerCompanyProfileId = r.ResellerCompanyProfileId,
                    ClientCompanyId = r.ClientCompanyId
                })
                .ToListAsync();

            var companyDetails = await _appDbContext.CompanyCompanies
                .AsSplitQuery()
                .AsNoTracking()
                .Include(x => x.Staffs)
                .ThenInclude(x => x.Identity)
                .Include(x => x.TwilioUsageRecords)
                .Include(x => x.CmsHubSpotCompanyMap)
                .Include(x => x.BillRecords)
                .ThenInclude(x => x.CmsSalesPaymentRecords)
                .Include(x => x.BillRecords)
                .ThenInclude(x => x.SubscriptionPlan)
                .Include(x => x.CmsCompanyAdditionalInfo)
                .Include(x => x.WhatsappCloudApiConfigs)
                .OrderBy(x => x.CreatedAt)
                .WhereIf(companyIds != null, company => companyIds.Contains(company.Id))
                .Select(
                    x => new CmsCompanyDetail
                    {
                        // HubSpot Map
                        CmsHubSpotCompanyMap = x.CmsHubSpotCompanyMap,

                        // Basic
                        CompanyId = x.Id,
                        CompanyName = x.CompanyName,
                        CreateAt = x.CreatedAt,
                        CompanyCountry = x.CompanyCountry,
                        CmsLeadSource = x.CmsLeadSource,
                        CmsCompanyIndustry = x.CmsCompanyIndustry,
                        CmsCompanyAdditionalInfo = x.CmsCompanyAdditionalInfo,

                        // Channel
                        WhatsAppConfigCount = x.WhatsApp360DialogConfigs.Count,
                        WhatsApp360DialogConfigCount = x.WhatsApp360DialogConfigs.Count,
                        WhatsappCloudApiConfigCount = x.WhatsappCloudApiConfigs.Count,
                        WhatsappCloudApiConfigs = x.WhatsappCloudApiConfigs,
                        InstagramConfigCount = x.InstagramConfigs.Count,
                        FacebookConfigCount = x.FacebookConfigs.Count,
                        WhatsappChatAPIConfigCount = 0,
                        WebClientSenderCount = x.WebClientSenders.Any() ? 1 : 0,
                        LineConfigCount = x.LineConfigs.Count,
                        SMSConfigCount = x.SMSConfigs.Count,
                        TelegramConfigCount = x.TelegramConfigs.Count,
                        ViberConfigCount = x.ViberConfigs.Count,
                        WeChatConfigCount = x.WeChatConfig != null ? 1 : 0,
                        EmailConfigCount = x.EmailConfig != null ? 1 : 0,

                        // Integration
                        ApiKeyCount = x.CompanyAPIKeys.Count,
                        ZapierIntegrationCount = x.CompanyAPIKeys.Count(a => a.UsedInZapier),
                        ShoplineConfigCount = x.ShoplineConfigs.Count,
                        ShopifyConfigCount = x.ShopifyConfigs.Count,
                        StripePaymentCount = x.StripePaymentConfigs.Count,

                        // Contact Owner
                        ActivationOwnerId = x.CmsActivationOwnerId,
                        CmsCompanyOwnerOwnerId = x.CmsCompanyOwnerId,
                        CmsCsOwnerId = x.CmsCsOwnerId,
                        ActivationOwnerName = x.CmsActivationOwner.DisplayName,
                        CmsCompanyOwnerOwnerName = x.CmsCompanyOwner.DisplayName,
                        HubSpotCompanyOwnerId = _appDbContext.CmsHubSpotContactOwnerMaps
                            .Where(c => c.ContactOwnerId == x.CmsCompanyOwnerId)
                            .Select(c => c.HubSpotContactOwnerId)
                            .FirstOrDefault(),
                        HubSpotActivationOwnerId = _appDbContext.CmsHubSpotContactOwnerMaps
                            .Where(c => c.ContactOwnerId == x.CmsActivationOwnerId)
                            .Select(c => c.HubSpotContactOwnerId)
                            .FirstOrDefault(),
                        HubSpotCsOwnerId = _appDbContext.CmsHubSpotContactOwnerMaps
                            .Where(c => c.ContactOwnerId == x.CmsCsOwnerId)
                            .Select(c => c.HubSpotContactOwnerId)
                            .FirstOrDefault(),

                        // Staff
                        Owner = x.Staffs.OrderBy(s => s.Order)
                                .ThenBy(s => s.Id)
                                .FirstOrDefault(),
                        LastStaffLoginAt = x.Staffs.Max(s => s.Identity.LastLoginAt),

                        // Subscription, MRR
                        BillRecords = x.BillRecords
                            .Where(b => b.Status != BillStatus.Inactive)
                            .OrderByDescending(br => br.created)
                            .ToList(),

                        // Usages
                        TwilioUsageRecord =
                            x.TwilioUsageRecords.OrderByDescending(t => t.TotalCreditValue).FirstOrDefault(),
                        WhatsApp360DialogUsageRecord =
                            x.WhatsApp360DialogUsageRecords.OrderByDescending(t => t.Credit).FirstOrDefault(),
                        ContactCount = x.UserProfiles.Count(u => u.ActiveStatus == ActiveStatus.Active),
                        MaximumContacts = x.MaximumContacts,
                        ConversationCount = _appDbContext.Conversations.Count(c => c.CompanyId == x.Id),
                        BroadcastCount =
                            _appDbContext.CompanyMessageTemplates.Count(c => c.CompanyId == x.Id && c.Status == BroadcastStatus.Sent),
                        BroadcastCountInPastMonth =
                            _appDbContext.CompanyMessageTemplates.Count(
                                c => c.CompanyId == x.Id && c.Status == BroadcastStatus.Sent && c.CreatedAt >= pastMonth),
                        StaffCount = x.Staffs.Count(u => u.Id != 1),
                        MaximumAgents = x.MaximumAgents,
                        AutomationCount = x.AssignmentRules.Count(a => a.Status != AutomationStatus.Saved),
                        LiveAutomationCount = x.AssignmentRules.Count(a => a.Status == AutomationStatus.Live),
                        MaximumAutomations = x.MaximumAutomations,
                        SupportTicketCount = _appDbContext.SupportTickets.Count(s => x.Id == s.CompanyId),
                        SupportTicketCountInPastTwoWeek =
                            _appDbContext.SupportTickets.Count(s => x.Id == s.CompanyId && s.CreatedAt >= pastTwoWeek),
                        SupportTicketCountInPastMonth =
                            _appDbContext.SupportTickets.Count(s => x.Id == s.CompanyId && s.CreatedAt >= pastMonth),
                        PaymentFailedCount = x.CompanyPaymentFailedLogs.Count,
                        PaymentFailedCountInPastThreeMonth =
                            x.CompanyPaymentFailedLogs.Count(s => x.Id == s.CompanyId && s.CreatedAt >= pastThreeMonth),

                        // InboxMessagesInPast7Days = _appDbContext.ConversationMessages.Count(m => x.Id == m.CompanyId && m.CreatedAt >= pastWeek && m.SenderId != null),
                        IsDeleted = x.IsDeleted,
                        CompanyType = x.CompanyType,
                        CommunicationTools = x.CommunicationTools,
                        TimeZoneInfoId = x.TimeZoneInfoId
                    })
                .ToListAsync();

            foreach (var company in companyDetails)
            {
                if (string.IsNullOrWhiteSpace(company.CmsCsOwnerId))
                {
                    continue;
                }

                var csOwner = await _appDbContext.Users.FirstOrDefaultAsync(u => u.Id == company.CmsCsOwnerId);

                if (csOwner != null)
                {
                    company.CmsCsOwnerName = csOwner.DisplayName;
                }
            }

            // Reseller Client Staff handling
            foreach (var company in companyDetails.Where(x => x.CompanyType == CompanyType.ResellerClient))
            {
                var currentResellerCompanyProfiles =
                    resellerClientCompanyProfiles.Find(x => x.ClientCompanyId == company.CompanyId);

                if (currentResellerCompanyProfiles == null)
                {
                    continue;
                }

                var resellerStaffsInCompany = resellerStaffs
                    .Where(rs => rs.ResellerCompanyProfileId == currentResellerCompanyProfiles.ResellerCompanyProfileId)
                    .Select(rs => rs.IdentityId)
                    .ToList();

                var actualStaffs = await _appDbContext.UserRoleStaffs
                    .AsNoTracking()
                    .Include(x => x.Identity)
                    .Where(x => x.CompanyId == company.CompanyId && !resellerStaffsInCompany.Contains(x.IdentityId))
                    .OrderBy(s => s.Order)
                    .ThenBy(s => s.Id)
                    .ToListAsync();

                company.Owner = actualStaffs.Count > 0 ? actualStaffs[0] : null;
                company.LastStaffLoginAt = actualStaffs.Count > 0 ? actualStaffs.Max(s => s.Identity.LastLoginAt) : null;
                company.StaffCount = actualStaffs.Count(s => s.Id != 1);
            }

            // Count message Past 7 Days inbox message
            foreach (var companyIdsChunk in companyDetails.Select(x => x.CompanyId).Chunk(500))
            {
                var messageCountResult = await _appDbContext.ConversationMessages
                    .AsNoTracking()
                    .Where(x => companyIdsChunk.Contains(x.CompanyId) && x.CreatedAt >= pastWeek && x.SenderId != null)
                    .GroupBy(x => x.CompanyId)
                    .Select(
                        x => new
                        {
                            CompanyId = x.Key,
                            InboxMessagesInPast7Days = x.Count(),
                        })
                    .ToListAsync();

                messageCountResult.ForEach(
                    x =>
                    {
                        var detail = companyDetails.Find(d => d.CompanyId == x.CompanyId);

                        if (detail != null)
                        {
                            detail.InboxMessagesInPast7Days = x.InboxMessagesInPast7Days;
                        }
                    });

                var activeContactCurrentResult = await _appDbContext.ConversationMessages
                    .AsNoTracking()
                    .Where(cm => companyIdsChunk.Contains(cm.CompanyId)
                                    && cm.CreatedAt >= pastMonth
                                    && cm.DeliveryType == DeliveryType.Normal
                                    && new[] { MessageStatus.Sent, MessageStatus.Received, MessageStatus.Read }.Contains(cm.Status)
                                    && cm.IsSandbox == false)
                    .GroupBy(cm1 => cm1.CompanyId)
                    .Select(group => new
                    {
                        CompanyId = group.Key,
                        ActiveContactsInCurrentMonth = group.Select(g => g.ConversationId).Distinct().Count()
                    })
                    .ToListAsync();

                foreach (var x in activeContactCurrentResult)
                {
                    var detail = companyDetails.Find(d => d.CompanyId == x.CompanyId);
                    if (detail != null)
                    {
                        detail.ActiveContactsInCurrentMonth = x.ActiveContactsInCurrentMonth;
                    }
                }

                var activeContactPastMonthResult = await _appDbContext.ConversationMessages
                    .AsQueryable()
                    .Where(cm => companyIdsChunk.Contains(cm.CompanyId)
                                  && cm.CreatedAt >= pastTwoMonth && cm.CreatedAt <= pastMonth
                                  && cm.DeliveryType == DeliveryType.Normal
                                  && new[] { MessageStatus.Sent, MessageStatus.Received, MessageStatus.Read }.Contains(cm.Status)
                                  && cm.IsSandbox == false)
                    .GroupBy(cm1 => cm1.CompanyId)
                    .Select(group => new
                    {
                        CompanyId = group.Key,
                        ActiveContactsInPastMonth = group.Select(g => g.ConversationId).Distinct().Count()
                    })
                    .ToListAsync();

                foreach (var x in activeContactPastMonthResult)
                {
                    var detail = companyDetails.Find(d => d.CompanyId == x.CompanyId);
                    if (detail != null)
                    {
                        detail.ActiveContactsInPastMonth = x.ActiveContactsInPastMonth;
                    }
                }

                var customFieldResult = await _appDbContext.CompanyCompanies
                    .AsNoTracking()
                    .Where(x => companyIdsChunk.Contains(x.Id))
                    .Select(
                        x => new
                        {
                            CompanyId = x.Id,
                            Industry =
                                x.CompanyCustomFields
                                    .FirstOrDefault(f => f.CompanyId == x.Id && f.FieldName == "Industry").Value,
                            OnlineShopSystem =
                                x.CompanyCustomFields.FirstOrDefault(
                                    f => f.CompanyId == x.Id && f.FieldName == "OnlineShopSystem").Value,
                            CompanyWebsite =
                                x.CompanyCustomFields.FirstOrDefault(
                                    f => f.CompanyId == x.Id && f.FieldName == "CompanyWebsite").Value,
                            CompanySize = x.CompanyCustomFields
                                .FirstOrDefault(f => f.CompanyId == x.Id && f.FieldName == "CompanySize").Value,
                            PlatformUsageIntent = x.CompanyCustomFields
                                .FirstOrDefault(f => f.CompanyId == x.Id && f.FieldName == "PlatformUsageIntent").Value,
                        })
                    .ToListAsync();

                customFieldResult.ForEach(
                    x =>
                    {
                        var detail = companyDetails.Find(d => d.CompanyId == x.CompanyId);

                        if (detail != null)
                        {
                            detail.Industry = x.Industry;
                            detail.OnlineShopSystem = x.OnlineShopSystem;
                            detail.CompanyWebsite = x.CompanyWebsite;
                            detail.PlatformUsageIntent = x.PlatformUsageIntent;
                            detail.CompanySize = x.CompanySize?.Replace(" ", string.Empty);
                        }
                    });
            }

            var newUserProfileCounts = await _appDbContext.UserProfiles
                .AsNoTracking()
                .WhereIf(companyIds != null, userProfile => companyIds.Contains(userProfile.CompanyId))
                .Where(x => x.ActiveStatus == ActiveStatus.Active && x.CreatedAt >= from && x.CreatedAt < to)
                .GroupBy(x => x.CompanyId)
                .Select(
                    g => new
                    {
                        CompanyId = g.Key,
                        NewUserProfiles = g.Count()
                    })
                .ToListAsync();

            newUserProfileCounts.ForEach(
                x =>
                {
                    var companyDetail = companyDetails.First(detail => detail.CompanyId == x.CompanyId);
                    companyDetail.NewContactsInPastDay = x.NewUserProfiles;
                });

            // Conversation Analytic
            var newEnquiryConversations = await _appDbContext.Conversations
                .AsNoTracking()
                .WhereIf(companyIds != null, conversation => companyIds.Contains(conversation.CompanyId))
                .Where(
                    x => ActiveStatus.Active == x.ActiveStatus && x.CreatedAt >= from && x.CreatedAt < to &&
                         x.ChatHistory.Any())
                .GroupBy(x => x.CompanyId)
                .Select(
                    g => new
                    {
                        CompanyId = g.Key,
                        NewEnquiryConversationInPastDay = g.Count()
                    })
                .ToListAsync();

            newEnquiryConversations.ForEach(
                x =>
                {
                    var companyDetail = companyDetails.First(detail => detail.CompanyId == x.CompanyId);
                    companyDetail.NewEnquiriesInPastDay = x.NewEnquiryConversationInPastDay;
                });

            // Conversation Message
            var conversationMessagesAnalytic = await _appDbContext.ConversationMessages
                .Where(x => x.CreatedAt >= from && x.CreatedAt < to)
                .WhereIf(companyIds != null, conversationMessage => companyIds.Contains(conversationMessage.CompanyId))
                .GroupBy(x => x.CompanyId)
                .Select(
                    g => new
                    {
                        CompanyId = g.Key,
                        NumberOfActiveConversationInPastDay =
                            g.Where(x => !x.IsSentFromSleekflow).Select(x => x.ConversationId).Distinct().Count(),
                        ActiveUsersInPastDay = g.Select(x => x.SenderId ?? "0").Distinct().Count(x => x != "0"),
                    })
                .ToListAsync();

            conversationMessagesAnalytic.ForEach(
                x =>
                {
                    var companyDetail = companyDetails.First(detail => detail.CompanyId == x.CompanyId);
                    companyDetail.ActiveConversationsInPastDay = x.NumberOfActiveConversationInPastDay;
                    companyDetail.ActiveAgentsInPastDay = x.ActiveUsersInPastDay;
                });

            var conversationMessageLastAgentMessageSentAnalytic = await _appDbContext.ConversationMessages
                .Where(x => x.CreatedAt >= from && x.CreatedAt < to)
                .WhereIf(companyIds != null, conversationMessage => companyIds.Contains(conversationMessage.CompanyId))
                .GroupBy(x => x.CompanyId)
                .Select(
                    g => new ConversationMessageLastAgentMessageSentAnalytic
                    {
                        CompanyId = g.Key,
                        LastAgentMessageSentAt = g.Where(x => x.IsSentFromSleekflow).Select(m => m.CreatedAt).Max()
                    })
                .ToListAsync();

            conversationMessageLastAgentMessageSentAnalytic.ForEach(
                x =>
                {
                    var companyDetail = companyDetails.First(detail => detail.CompanyId == x.CompanyId);
                    companyDetail.LastAgentMessageSentAt = x.LastAgentMessageSentAt;
                });

            if (lastDay != null)
            {
                foreach (var snapshotData in lastDay)
                {
                    var companyDetail = companyDetails.FirstOrDefault(x => x.CompanyId == snapshotData.CompanyId);

                    if (companyDetail != null)
                    {
                        companyDetail.NewContactsDifferenceInPercentage = Diff(
                            companyDetail.NewContactsInPastDay,
                            snapshotData.NewContactsInPastDay);

                        companyDetail.ActiveConversationsDifferenceInPercentage = Diff(
                            companyDetail.ActiveConversationsInPastDay,
                            snapshotData.ActiveConversationsInPastDay);

                        companyDetail.NewEnquiriesDifferenceInPercentage = Diff(
                            companyDetail.NewEnquiriesInPastDay,
                            snapshotData.NewEnquiriesInPastDay);

                        if (companyDetail.LastAgentMessageSentAt == null)
                        {
                            companyDetail.LastAgentMessageSentAt = snapshotData.LastAgentMessageSentAt;
                        }
                    }
                }
            }

            var facebookConfigsCount = await _appDbContext.ConfigFacebookConfigs
                .Where(f => f.SubscribedFields.Contains("leadgen"))
                .GroupBy(f => f.CompanyId)
                .Select(
                    g => new
                    {
                        Id = g.Key,
                        Count = g.Count()
                    })
                .ToListAsync();

            var shopifyConfigsCount = await _appDbContext.ConfigShopifyConfigs
                .GroupBy(s => s.CompanyId)
                .Select(
                    g => new
                    {
                        Id = g.Key,
                        Count = g.Count()
                    })
                .ToListAsync();

            var stripePaymentConfigsCount = await _appDbContext.ConfigStripePaymentConfigs
                .GroupBy(s => s.CompanyId)
                .Select(
                    g => new
                    {
                        Id = g.Key,
                        Count = g.Count()
                    })
                .ToListAsync();

            var billRecordsHubspotCount = await _appDbContext.CompanyBillRecords
                .Where(b => b.SubscriptionPlanId.StartsWith("sleekflow_v9_hubspot_integration"))
                .GroupBy(b => b.CompanyId)
                .Select(
                    g => new
                    {
                        Id = g.Key,
                        Count = g.Count()
                    })
                .ToListAsync();

            var billRecordsSalesforceCount = await _appDbContext.CompanyBillRecords
                .Where(b => b.SubscriptionPlanId.StartsWith("sleekflow_v9_salesforce_integration"))
                .GroupBy(b => b.CompanyId)
                .Select(
                    g => new
                    {
                        Id = g.Key,
                        Count = g.Count()
                    })
                .ToListAsync();

            var apiKeysSendMessageCount = _appDbContext.CompanyAPIKeys
                .ToList()
                .Where(a => a.Permissions != null && a.Permissions.Contains("SendMessage") && a.Calls > 0)
                .GroupBy(a => a.CompanyId)
                .Select(
                    g => new
                    {
                        Id = g.Key,
                        Count = g.Count()
                    })
                .ToList();

            var billRecordsMarketingCloudCount = await _appDbContext.CompanyBillRecords
                .Where(b => b.SubscriptionPlanId.StartsWith("sleekflow_v9_salesforce_marketing_cloud"))
                .GroupBy(b => b.CompanyId)
                .Select(
                    g => new
                    {
                        Id = g.Key,
                        Count = g.Count()
                    })
                .ToListAsync();

            var apiKeysUsedInZapierCount = await _appDbContext.CompanyAPIKeys
                .Where(a => a.UsedInZapier)
                .GroupBy(a => a.CompanyId)
                .Select(
                    g => new
                    {
                        Id = g.Key,
                        Count = g.Count()
                    })
                .ToListAsync();

            var apiKeysMakeCount = _appDbContext.CompanyAPIKeys
                .ToList()
                .Where(a => a.Permissions != null && a.Permissions.Contains("Make") && a.Calls > 0)
                .GroupBy(a => a.CompanyId)
                .Select(
                    g => new
                    {
                        Id = g.Key,
                        Count = g.Count()
                    })
                .ToList();

            var totalIntegrations = facebookConfigsCount
                .Concat(shopifyConfigsCount)
                .Concat(stripePaymentConfigsCount)
                .Concat(billRecordsHubspotCount)
                .Concat(billRecordsSalesforceCount)
                .Concat(apiKeysSendMessageCount)
                .Concat(billRecordsMarketingCloudCount)
                .Concat(apiKeysUsedInZapierCount)
                .Concat(apiKeysMakeCount)
                .GroupBy(x => x.Id)
                .Select(
                    g => new
                    {
                        CompanyId = g.Key,
                        IntegrationCount = g.Sum(x => x.Count)
                    })
                .ToList();

            foreach (var x in totalIntegrations)
            {
                var detail = companyDetails.Find(d => d.CompanyId == x.CompanyId);

                if (detail != null)
                {
                    detail.IntegrationCount = x.IntegrationCount;
                }
            }

            return companyDetails;
        }

        public async Task<bool> SaveCompanySnapshotDataAsync(
            List<CompanySnapshotData> companySnapshotData,
            DateTime savedAt)
        {
            // Write CSV in memory stream
            var ms = new MemoryStream();

            await using (var writer = new StreamWriter(ms, leaveOpen: true))
            {
                await using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
                {
                    await csv.WriteRecordsAsync(companySnapshotData);
                }
            }

            ms.Position = 0;

            // Upload to Blob
            var snapshotDate = savedAt.ToString("yyyyMMdd");

            await _azureBlobStorageService.UploadFileAsBlob(
                ms,
                $"company/snapshot-{snapshotDate}.csv",
                "internalsnapshotdata",
                "text/csv");

            // Create or update Snapshot Record
            var snapshotRecord = await _appDbContext.CmsCompanyDataSnapshots.Where(x => x.SnapshotDate == snapshotDate)
                .FirstOrDefaultAsync();

            if (snapshotRecord != null)
            {
                snapshotRecord.UpdatedAt = DateTime.UtcNow;
            }
            else
            {
                _appDbContext.CmsCompanyDataSnapshots.Add(
                    new CmsCompanyDataSnapshot()
                    {
                        SnapshotDate = snapshotDate,
                        RecordCount = companySnapshotData.Count
                    });
            }

            await _appDbContext.SaveChangesAsync();

            return true;
        }

        public async Task<List<CompanySnapshotData>> GetCompanySnapshotDataAsync(DateTime savedAt)
        {
            var snapshotDate = savedAt.ToString("yyyyMMdd");

            if (!await _appDbContext.CmsCompanyDataSnapshots.AnyAsync(x => x.SnapshotDate == snapshotDate))
            {
                return null;
            }

            try
            {
                var snapshotFileStream = await _azureBlobStorageService.DownloadFromAzureBlob(
                    $"company/snapshot-{snapshotDate}.csv",
                    "internalsnapshotdata");
                var ms = new MemoryStream(snapshotFileStream.ToArray());

                var csvReaderConfiguration = new CsvConfiguration(cultureInfo: CultureInfo.InvariantCulture)
                {
                    HeaderValidated = _ => { },
                    MissingFieldFound = _ => { },
                };
                using var reader = new StreamReader(ms);

                using var csv = new CsvReader(reader, csvReaderConfiguration);

                var result = csv.GetRecords<CompanySnapshotData>();

                return result.ToList();
            }
            catch (Exception e)
            {
                return null;
            }
        }

        public async Task<List<CmsCompanyOwner>> GetCmsCompanyOwners(List<string> companyIds = null)
        {
            var companyOwners = await _appDbContext.CompanyCompanies
                .AsSplitQuery()
                .AsNoTracking()
                .WhereIf(companyIds != null, company => companyIds.Contains(company.Id))
                .Select(
                    x => new CmsCompanyOwner
                    {
                        CompanyId = x.Id,
                        CompanyName = x.CompanyName,
                        ActivationOwnerId = x.CmsActivationOwnerId,
                        ActivationOwnerName = x.CmsActivationOwner.DisplayName,
                        CompanyOwnerOwnerId = x.CmsCompanyOwnerId,
                        CompanyOwnerOwnerName = x.CmsCompanyOwner.DisplayName,
                        CsOwnerId = x.CmsCsOwnerId
                    })
                .ToListAsync();

            foreach (var owner in companyOwners)
            {
                if (string.IsNullOrWhiteSpace(owner.CsOwnerId))
                {
                    continue;
                }

                var csOwner = await _appDbContext.Users.FirstOrDefaultAsync(u => u.Id == owner.CsOwnerId);

                if (csOwner != null)
                {
                    owner.CsOwnerName = csOwner.DisplayName;
                }
            }

            return companyOwners;
        }

        public async Task<List<CmsCompanyBillRecordDetail>> GetCmsCompanyBillRecords(List<string> companyIds = null)
        {
            _appDbContext.Database.SetCommandTimeout(180);

            var companyBillRecords = await _appDbContext.CompanyBillRecords
                .AsNoTracking()
                .AsSplitQuery()
                .Include(x => x.CmsSalesPaymentRecords)
                .WhereIf(companyIds != null, x => companyIds.Contains(x.CompanyId))
                .Select(
                    x => new CmsCompanyBillRecordDetail
                    {
                        Id = x.Id,
                        CompanyId = x.CompanyId,

                        // CompanyName = _appDbContext.CompanyCompanies.FirstOrDefault(c=> c.Id == x.CompanyId).CompanyName,
                        SubscriptionPlanId = x.SubscriptionPlanId,
                        PeriodStart = x.PeriodStart,
                        PeriodEnd = x.PeriodEnd,
                        Status = x.Status,
                        PaymentStatus = x.PaymentStatus,
                        PayAmount = x.PayAmount,
                        PurchaseStaffId = x.PurchaseStaffId,
                        invoice_Id = x.invoice_Id,
                        stripe_subscriptionId = x.stripe_subscriptionId,
                        customerId = x.customerId,
                        customer_email = x.customer_email,
                        hosted_invoice_url = x.hosted_invoice_url,
                        invoice_pdf = x.invoice_pdf,
                        chargeId = x.chargeId,
                        amount_due = x.amount_due,
                        amount_paid = x.amount_paid,
                        amount_remaining = x.amount_remaining,
                        currency = x.currency,
                        created = x.created,
                        quantity = x.quantity,
                        UpgradeFromBillRecordId = x.UpgradeFromBillRecordId,
                        DowngradeFromBillRecordId = x.DowngradeFromBillRecordId,
                        CmsSalesPaymentRecords = x.CmsSalesPaymentRecords,
                        UpdatedAt = x.UpdatedAt,
                        SubscriptionTier = x.SubscriptionTier,
                        stripeId = x.stripeId,
                        ShopifyChargeId = x.ShopifyChargeId,
                        PaidByReseller = x.PaidByReseller,
                    })
                .ToListAsync();

            var companyOwners = await GetCmsCompanyOwners(companyIds);

            companyBillRecords.ForEach(
                x =>
                {
                    var companyOwner = companyOwners.FirstOrDefault(c => c.CompanyId == x.CompanyId);

                    if (companyOwner == null)
                    {
                        return;
                    }

                    x.CompanyName = companyOwner.CompanyName;
                    x.ActivationOwnerId = companyOwner.ActivationOwnerId;
                    x.ActivationOwnerName = companyOwner.ActivationOwnerName;
                    x.CompanyOwnerOwnerId = companyOwner.CompanyOwnerOwnerId;
                    x.CompanyOwnerOwnerName = companyOwner.CompanyOwnerOwnerName;
                });

            return companyBillRecords;
        }

        public async Task<List<CmsCompanySubscriptionPlan>> GetCmsCompanySubscriptionPlan(
            List<string> companyIds = null)
        {
            _appDbContext.Database.SetCommandTimeout(180);

            var companySubscriptionPlan = await _appDbContext.CompanyCompanies
                .AsNoTracking()
                .AsSplitQuery()
                .WhereIf(companyIds != null, x => companyIds.Contains(x.Id))
                .Select(
                    x => new CmsCompanySubscriptionPlan
                    {
                        CompanyId = x.Id,
                        CompanyName = x.CompanyName,
                        SubscriptionPlanId = x.BillRecords
                            .OrderByDescending(br => br.created)
                            .ThenByDescending(br => br.PayAmount)
                            .FirstOrDefault(b => ValidSubscriptionPlan.SubscriptionPlan.Contains(b.SubscriptionPlanId))
                            .SubscriptionPlanId,
                    })
                .ToListAsync();

            return companySubscriptionPlan;
        }

        public async Task<List<CmsCompanyOnboardingStatus>> GetCompanyOnboardingStatuses(List<string> companyIds = null)
        {
            var defaultQuickReplies = new[]
            {
                "Welcome",
                "Thanks",
                "Away",
                "Close"
            };

            var companyOnboardingStatuses = await _appDbContext.CompanyCompanies
                .WhereIf(companyIds != null, x => companyIds.Contains(x.Id))
                .Select(
                    x => new CmsCompanyOnboardingStatus
                    {
                        CompanyId = x.Id,
                        CompanyName = x.CompanyName,
                        IsMessagingChannelConnected =
                            x.FacebookConfigs.Any() || x.WhatsAppConfigs.Any() || x.WeChatConfig != null ||
                             x.LineConfigs.Any() ||
                             x.SMSConfigs.Any() || x.EmailConfig != null || x.ViberConfigs.Any() ||
                             x.ShoplineConfigs.Any() || x.ShopifyConfigs.Any() || x.InstagramConfigs.Any() ||
                             x.TelegramConfigs.Any() || x.WhatsApp360DialogConfigs.Any() ||
                             x.WhatsappCloudApiConfigs.Any(),
                        IsQuickReplyAdded = x.QuickReplies.Where(y => !defaultQuickReplies.Contains(y.Value)).Any(),
                        IsInvitedTeammate = x.Staffs.Count > 1,
                        IsWhatsappConsultationBooked =
                            _appDbContext.UserRoleStaffs.Any(
                                y => y.CompanyId == x.Id && y.IsWhatsappConsultationBooked),
                        IsInboxDemoCompleted =
                            _appDbContext.UserRoleStaffs.Any(y => y.CompanyId == x.Id && y.IsInboxDemoCompleted),
                        IsInboxInUse = _appDbContext.Conversations.Any(y => y.CompanyId == x.Id),
                        IsAutomationRuleAdded =
                            _appDbContext.CompanyAssignmentRules.Any(
                                y => y.CompanyId == x.Id && y.AssignmentRuleName != "Default"),
                        IsContactListCreated =
                            _appDbContext.CompanyImportContactHistories.Any(
                                y => y.CompanyId == x.Id &&
                                     (y.Status == ImportStatus.Imported || y.Status == ImportStatus.Saved)),
                        IsCampaignCreated = _appDbContext.CompanyMessageTemplates.Any(
                            y => y.CompanyId == x.Id && (y.SentAt != null || y.ScheduledAt != null)),
                    }).ToListAsync();

            return companyOnboardingStatuses;
        }

        public async Task<List<CmsCompanyOnboardingProgress>> GetCompanyOnboardingProgresses(
            List<string> companyIds = null,
            List<CmsCompanySubscriptionPlan> companyDetails = null)
        {
            var companyOnboardingStatuses = await GetCompanyOnboardingStatuses(companyIds);

            var companyOnboardingProgresses = new List<CmsCompanyOnboardingProgress>();

            companyDetails ??= await GetCmsCompanySubscriptionPlan(companyIds);

            foreach (var companyOnboardingStatus in companyOnboardingStatuses)
            {
                var companyOnboardingProgress = new CmsCompanyOnboardingProgress
                {
                    CompanyId = companyOnboardingStatus.CompanyId,
                    CompanyName = companyOnboardingStatus.CompanyName,
                    InboxProgress =
                        (companyOnboardingStatus.IsInboxDemoCompleted || companyOnboardingStatus.IsInboxInUse)
                            ? "completed"
                            : "outstanding",
                    WorkflowProgress =
                        (companyOnboardingStatus.IsAutomationRuleAdded && companyOnboardingStatus.IsQuickReplyAdded)
                            ? "completed"
                            : ((companyOnboardingStatus.IsAutomationRuleAdded ||
                                companyOnboardingStatus.IsQuickReplyAdded)
                                ? "in_progress"
                                : "outstanding"),
                    BroadcastProgress =
                        (companyOnboardingStatus.IsMessagingChannelConnected &&
                         companyOnboardingStatus.IsContactListCreated && companyOnboardingStatus.IsCampaignCreated)
                            ? "completed"
                            : ((companyOnboardingStatus.IsMessagingChannelConnected ||
                                companyOnboardingStatus.IsContactListCreated ||
                                companyOnboardingStatus.IsCampaignCreated)
                                ? "in_progress"
                                : "outstanding"),
                    ChatReadinessProgress =
                        ((companyOnboardingStatus.IsMessagingChannelConnected ||
                          companyOnboardingStatus.IsWhatsappConsultationBooked) &&
                         companyOnboardingStatus.IsInvitedTeammate)
                            ? "completed"
                            : ((companyOnboardingStatus.IsMessagingChannelConnected ||
                                companyOnboardingStatus.IsWhatsappConsultationBooked ||
                                companyOnboardingStatus.IsInvitedTeammate)
                                ? "in_progress"
                                : "outstanding")
                };

                // Company Plan
                var companyDetail =
                    companyDetails.FirstOrDefault(x => x.CompanyId == companyOnboardingProgress.CompanyId);

                companyOnboardingProgress.CurrentPlan = companyDetail?.SubscriptionPlanId;

                companyOnboardingProgresses.Add(companyOnboardingProgress);
            }

            return companyOnboardingProgresses;
        }

        public async Task<List<CmsSalesPaymentRecordSnapshotData>> GetCmsSalesInputPaymentCsv(
            List<string> companyIds = null)
        {
            var cmsSalesPaymentRecords = await _appDbContext
                .CmsSalesPaymentRecords
                .WhereIf(companyIds is { Count: > 0 }, x => companyIds.Contains(x.CompanyId))
                .ProjectTo<CmsSalesPaymentRecordSnapshotData>(_mapper.ConfigurationProvider)
                .ToListAsync();

            return cmsSalesPaymentRecords;
        }

        public async Task<List<CmsCompanyStaffData>> GetCmsCompanyStaffList(List<string> companyIds = null)
        {
            var companyData = await _appDbContext.CompanyCompanies
                .AsSplitQuery()
                .AsNoTracking()
                .Include(x => x.Staffs)
                .ThenInclude(x => x.Identity)
                .WhereIf(companyIds != null, x => companyIds.Contains(x.Id))
                .ToListAsync();

            var companyStaffData = new List<CmsCompanyStaffData>();

            foreach (var company in companyData)
            {
                var currentSubscriptionPlan = await _companyUsageService.GetCompanySubscriptionPlan(company.Id);

                var isOwner = true;

                foreach (var staff in company.Staffs.OrderBy(x => x.Order).ThenBy(x => x.Id))
                {
                    var user = new CmsCompanyStaffData
                    {
                        CompanyId = company.Id,
                        CompanyName = company.CompanyName,
                        SubscriptionPlanId = currentSubscriptionPlan.SubscriptionPlanId,
                        StaffId = staff.Id,
                        UserId = staff.IdentityId,
                        Email = staff.Identity.Email,
                        PhoneNumber = staff.Identity.PhoneNumber,
                        FirstName = staff.Identity.FirstName,
                        LastName = staff.Identity.LastName,
                        DisplayName = staff.Identity.DisplayName,
                        UserRole = staff.RoleType.ToString(),
                        IsOwner = isOwner ? "Yes" : "No", // first user is owner
                        CreatedAt = staff.Identity.CreatedAt,
                        LastLoginAt = staff.Identity.LastLoginAt
                    };

                    companyStaffData.Add(user);
                    isOwner = false;
                }
            }

            return companyStaffData;
        }

        public async Task<List<CmsSleekPayReportDataWithCompany>> GetCmsSleekPayReportData(
            List<string> stripeAccountIds = null)
        {
            var sleekPayReportData = await _appDbContext.CmsSleekPayReportDatas
                .WhereIf(
                    stripeAccountIds != null,
                    x => stripeAccountIds.Contains(x.StripeAccountId))
                .ToListAsync();

            var sleekPayReportDataStripeAccountIds =
                sleekPayReportData.Select(x => x.StripeAccountId).Distinct().ToList();

            var sleekPayConfigs =
                await _appDbContext.ConfigStripePaymentConfigs.Where(
                    x => sleekPayReportDataStripeAccountIds.Contains(x.AccountId)).ToListAsync();

            var sleekPayConfigsCompanyName =
                await _appDbContext.CompanyCompanies.Where(
                    x => sleekPayConfigs.Select(y => y.CompanyId).Contains(x.Id)).Select(
                    x => new
                    {
                        Id = x.Id,
                        CompanyName = x.CompanyName
                    }).ToListAsync();
            var sleekPayReportDatasWithCompany = new List<CmsSleekPayReportDataWithCompany>();

            foreach (var sleekPayConfig in sleekPayConfigs)
            {
                var sleekPayReportDataById =
                    sleekPayReportData.Where(x => x.StripeAccountId == sleekPayConfig.AccountId).ToList();

                var companyName = sleekPayConfigsCompanyName.FirstOrDefault(x => x.Id == sleekPayConfig.CompanyId)
                    ?.CompanyName ?? string.Empty;

                sleekPayReportDatasWithCompany.AddRange(
                    sleekPayReportDataById.Select(
                        x => new CmsSleekPayReportDataWithCompany
                        {
                            StripeAccountId = x.StripeAccountId,
                            StripeAccountName = x.StripeAccountId,
                            OfferedRate = x.OfferedRate,
                            CostRate = x.CostRate,
                            GrossEarning = x.GrossEarning,
                            Cost = x.Cost,
                            AverageOrderValue = x.AverageOrderValue,
                            NetEarning = x.NetEarning,
                            VariableCostRate = x.VariableCostRate,
                            Volume = x.Volume,
                            PerAuthFees = x.PerAuthFees,
                            Currency = x.Currency,
                            OrderNos = x.OrderNos,
                            FailedTransactionsNos = x.FailedTransactionsNos,
                            StartActivityDate = x.StartActivityDate,
                            EndActivityDate = x.EndActivityDate,
                            CompanyId = sleekPayConfig.CompanyId,
                            CompanyName = companyName
                        }));
            }

            return sleekPayReportDatasWithCompany;
        }

        public async Task<List<ManagementWhatsappCloudApiConversationUsageAnalytic>>
            GetAllTimeWhatsappCloudApiConversationUsageAnalytic(DateTime start, DateTime end)
        {
            start = start.Date;
            end = end.Date;

            var dateList = MyDateTimeUtil.GetMonthStartEndLists(start, end);

            var byMonthConversationUsageAnalytics =
                new Dictionary<DateTime, List<ManagementWhatsappCloudApiConversationUsageAnalytic>>();

            foreach (var pair in dateList)
            {
                var output =
                    await _internalWhatsappCloudApiService.GetAllWhatsappCloudApiConversationUsageAnalytics(
                        pair.StartOfMonth,
                        pair.EndOfMonth,
                        "DAILY");

                byMonthConversationUsageAnalytics.Add(
                    pair.StartOfMonth,
                    output.WhatsappCloudApiConversationUsageAnalytics);
            }

            var result = new List<ManagementWhatsappCloudApiConversationUsageAnalytic>();

            foreach (var byBusinessIdConversationUsageAnalytics in byMonthConversationUsageAnalytics
                         .Select(x => x.Value).SelectMany(x => x).GroupBy(x => x.FacebookBusinessId))
            {
                var aggregatedConversationUsageAnalytic = byBusinessIdConversationUsageAnalytics
                    .Select(x => x.SummarizedConversationUsageAnalytic)
                    .OrderBy(x => x.Start)
                    .Aggregate(WhatsappCloudApiConversationUsageHelper.SumWhatsappCloudApiConversationUsageAnalytic);

                result.Add(
                    new ManagementWhatsappCloudApiConversationUsageAnalytic(
                        aggregatedConversationUsageAnalytic,
                        byBusinessIdConversationUsageAnalytics.First().FacebookBusinessId,
                        byBusinessIdConversationUsageAnalytics.First().FacebookBusinessName,
                        null));
            }

            return result;
        }

        public async
            Task<List<WhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData>>
            GetDailyWhatsappCloudApiConversationUsageAnalytic(
                DateTime start,
                DateTime end,
                bool allowSnapshotData = true)
        {
            start = start.Date;
            end = end.Date;

            var dailyConversationUsageAnalytics =
                new List<WhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData>();

            var dateList = MyDateTimeUtil.GetMonthStartEndLists(start, end);

            foreach (var pair in dateList)
            {
                List<WhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData>
                    byMonthDailyConversationUsageAnalytics =
                        new List<WhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData>();

                // Find from blob storage
                if (allowSnapshotData)
                {
                    var byMonthDailyConversationUsageAnalyticsSnapshot =
                        await GetDailyWhatsappCloudApiConversationUsageAnalyticMonthlySnapshot(pair.StartOfMonth);

                    if (byMonthDailyConversationUsageAnalyticsSnapshot != null)
                    {
                        byMonthDailyConversationUsageAnalytics = byMonthDailyConversationUsageAnalyticsSnapshot;
                    }
                }

                // Fetch from Messaging Hub
                if (byMonthDailyConversationUsageAnalytics.Count == 0)
                {
                    try
                    {
                        for (var date = pair.StartOfMonth;
                             date <= pair.EndOfMonth && date <= DateTime.UtcNow.Date.AddDays(-1);
                             date = date.AddDays(1))
                        {
                            var output =
                                await _internalWhatsappCloudApiService.GetAllWhatsappCloudApiConversationUsageAnalytics(
                                    date,
                                    date,
                                    "DAILY");

                            byMonthDailyConversationUsageAnalytics.AddRange(
                                output.WhatsappCloudApiConversationUsageAnalytics.Select(
                                    analytic => CmsCompanyDetailConverter
                                        .ConvertToWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData(
                                            analytic,
                                            date)));
                        }
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(e, "Error on GetAllTimeDailyWhatsappCloudApiConversationUsageAnalytic");
                    }
                }

                dailyConversationUsageAnalytics.AddRange(byMonthDailyConversationUsageAnalytics);
            }

            return dailyConversationUsageAnalytics;
        }

        public async Task<List<WhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData>>
            GetDailyWhatsappCloudApiConversationUsageAnalyticMonthlySnapshot(DateTime startOfMonth)
        {
            var snapshotMonth = startOfMonth.ToString("yyyyMM");

            try
            {
                var snapshotFileStream = await _azureBlobStorageService.DownloadFromAzureBlob(
                    $"whatsappcloudapi/by-month-daily-conversation-usage-snapshot-{snapshotMonth}.csv",
                    "internalsnapshotdata");
                var ms = new MemoryStream(snapshotFileStream.ToArray());

                var csvReaderConfiguration = new CsvConfiguration(cultureInfo: CultureInfo.InvariantCulture)
                {
                    HeaderValidated = _ => { },
                    MissingFieldFound = _ => { },
                };
                using var reader = new StreamReader(ms);

                using var csv = new CsvReader(reader, csvReaderConfiguration);

                var result = csv.GetRecords<WhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData>();

                return result.ToList();
            }
            catch (Exception e)
            {
                return null;
            }
        }

        public async Task<List<ByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData>>
            GetByBusinessDailyWhatsappCloudApiConversationUsageAnalyticMonthlySnapshot(DateTime startOfMonth)
        {
            var snapshotMonth = startOfMonth.ToString("yyyyMM");

            try
            {
                var snapshotFileStream = await _azureBlobStorageService.DownloadFromAzureBlob(
                    $"whatsappcloudapi/by-business-month-daily-conversation-usage-snapshot-{snapshotMonth}.csv",
                    "internalsnapshotdata");
                var ms = new MemoryStream(snapshotFileStream.ToArray());

                var csvReaderConfiguration = new CsvConfiguration(cultureInfo: CultureInfo.InvariantCulture)
                {
                    HeaderValidated = _ => { }, MissingFieldFound = _ => { },
                };
                using var reader = new StreamReader(ms);

                using var csv = new CsvReader(reader, csvReaderConfiguration);

                var result = csv.GetRecords<ByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData>();

                return result.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error retrieving by-business WhatsApp Cloud API conversation usage analytics for month {Month}",
                    snapshotMonth);
                return null;
            }
        }

        public async Task<List<ByWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData>>
            GetByWabaDailyWhatsappCloudApiConversationUsageAnalyticMonthlySnapshot(DateTime startOfMonth)
        {
            var snapshotMonth = startOfMonth.ToString("yyyyMM");

            try
            {
                var snapshotFileStream = await _azureBlobStorageService.DownloadFromAzureBlob(
                    $"whatsappcloudapi/by-waba-month-daily-conversation-usage-snapshot-{snapshotMonth}.csv",
                    "internalsnapshotdata");
                var ms = new MemoryStream(snapshotFileStream.ToArray());

                var csvReaderConfiguration = new CsvConfiguration(cultureInfo: CultureInfo.InvariantCulture)
                {
                    HeaderValidated = _ => { }, MissingFieldFound = _ => { },
                };
                using var reader = new StreamReader(ms);

                using var csv = new CsvReader(reader, csvReaderConfiguration);

                var result = csv.GetRecords<ByWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData>();

                return result.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error retrieving by-waba WhatsApp Cloud API conversation usage analytics for month {Month}",
                    snapshotMonth);
                return null;
            }
        }

        public async Task<bool>
            CreateAllTimeDailyWhatsappCloudApiConversationUsageAnalyticSnapshot(
                DateTime startOfMonth,
                List<WhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData> data)
        {
            var snapshotMonth = startOfMonth.ToString("yyyyMM");

            var ms = new MemoryStream();

            await using (var writer = new StreamWriter(ms, leaveOpen: true))
            {
                await using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
                {
                    await csv.WriteRecordsAsync(data);
                }
            }

            ms.Position = 0;

            // Upload to Blob
            await _azureBlobStorageService.UploadFileAsBlob(
                ms,
                $"whatsappcloudapi/by-month-daily-conversation-usage-snapshot-{snapshotMonth}.csv",
                "internalsnapshotdata",
                "text/csv");

            return true;
        }

        public async Task<bool>
            CreateInternalDailyWhatsappCloudApiConversationUsageAnalyticSnapshot(
                DateTime startOfMonth,
                List<ByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData>
                    byBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshots,
                List<ByWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData>
                    byWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshots)
        {
            var snapshotMonth = startOfMonth.ToString("yyyyMM");

            // Upload by-business snapshot data
            var byBusinessMs = new MemoryStream();
            await using (var writer = new StreamWriter(byBusinessMs, leaveOpen: true))
            {
                await using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
                {
                    await csv.WriteRecordsAsync(byBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshots);
                }
            }

            byBusinessMs.Position = 0;

            await _azureBlobStorageService.UploadFileAsBlob(
                byBusinessMs,
                $"whatsappcloudapi/by-business-month-daily-conversation-usage-snapshot-{snapshotMonth}.csv",
                "internalsnapshotdata",
                "text/csv");

            // Upload by-waba snapshot data
            var byWabaMs = new MemoryStream();
            await using (var writer = new StreamWriter(byWabaMs, leaveOpen: true))
            {
                await using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
                {
                    await csv.WriteRecordsAsync(byWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshots);
                }
            }

            byWabaMs.Position = 0;

            await _azureBlobStorageService.UploadFileAsBlob(
                byWabaMs,
                $"whatsappcloudapi/by-waba-month-daily-conversation-usage-snapshot-{snapshotMonth}.csv",
                "internalsnapshotdata",
                "text/csv");

            return true;
        }

        public async Task<List<CompanyFlowBuilderUsageSnapshotData>> GetCmsCompanyFlowBuilderUsageAsync(
            DateTime start,
            DateTime end)
        {
            var companies = await _appDbContext.CompanyCompanies.Where(c => !c.IsDeleted).AsNoTracking().ToListAsync();

            var companyFlowBuilderUsageSnapshotData = new List<CompanyFlowBuilderUsageSnapshotData>
            {
                Capacity = companies.Count
            };

            var retryPolicy = Policy
                .Handle<ApiException>()
                .WaitAndRetryAsync(
                    retryCount: 10,
                    sleepDurationProvider: currentRetryCount => TimeSpan.FromSeconds(1 * currentRetryCount));

            foreach (var company in companies)
            {
                var companyFlowBuilderUsage = new CompanyFlowBuilderUsageSnapshotData();

                var companyId = company.Id;
                var companyName = company.CompanyName;

                companyFlowBuilderUsage.CompanyId = companyId;
                companyFlowBuilderUsage.CompanyName = companyName;
                companyFlowBuilderUsage.Date = start.ToString("yyyy-MM-dd");

                var countWorkflowsPolicyResult = await retryPolicy.ExecuteAndCaptureAsync(
                    async () => await _workflowsApi.WorkflowsCountWorkflowsPostAsync(
                        countWorkflowsInput: new CountWorkflowsInput(companyId)));
                if (countWorkflowsPolicyResult.FinalException is null)
                {
                    companyFlowBuilderUsage.WorkflowCount = countWorkflowsPolicyResult.Result.Success
                        ? countWorkflowsPolicyResult.Result.Data.NumOfWorkflows
                        : 0;
                    companyFlowBuilderUsage.ActiveWorkflowCount = countWorkflowsPolicyResult.Result.Success
                        ? countWorkflowsPolicyResult.Result.Data.NumOfActiveWorkflows
                        : 0;
                }
                else
                {
                    companyFlowBuilderUsage.WorkflowCount = 0;
                    companyFlowBuilderUsage.ActiveWorkflowCount = 0;
                }

                var normalWorkflowExecutionStatistics = await retryPolicy.ExecuteAndCaptureAsync(
                    async () => await _executionsApi.ExecutionsGetMonetizedWorkflowExecutionStatisticsPostAsync(
                        getMonetizedWorkflowExecutionStatisticsInput: new GetMonetizedWorkflowExecutionStatisticsInput(
                            companyId,
                            new GetWorkflowExecutionStatisticsInputFilters(
                                null,
                                start,
                                end,
                                "normal"))));

                var aiWorkflowExecutionStatistics = await retryPolicy.ExecuteAndCaptureAsync(
                    async () => await _executionsApi.ExecutionsGetMonetizedWorkflowExecutionStatisticsPostAsync(
                        getMonetizedWorkflowExecutionStatisticsInput: new GetMonetizedWorkflowExecutionStatisticsInput(
                            companyId,
                            new GetWorkflowExecutionStatisticsInputFilters(
                                null,
                                start,
                                end,
                                "ai_workflow"))));

                if (normalWorkflowExecutionStatistics.FinalException is null && aiWorkflowExecutionStatistics.FinalException is null)
                {
                    var completeWorkflowCount = normalWorkflowExecutionStatistics.Result.Data.NumOfCompleteWorkflows + aiWorkflowExecutionStatistics.Result.Data.NumOfCompleteWorkflows;
                    companyFlowBuilderUsage.DailyWorkflowExecutionCount =
                        normalWorkflowExecutionStatistics.Result.Success
                            ? completeWorkflowCount
                            : 0;
                }
                else
                {
                    companyFlowBuilderUsage.DailyWorkflowExecutionCount = 0;
                }

                companyFlowBuilderUsageSnapshotData.Add(companyFlowBuilderUsage);
            }

            return companyFlowBuilderUsageSnapshotData;
        }

        public Task<List<CompanyFlowBuilderUsageSnapshotData>> GetCmsCompanyFlowBuilderUsageSnapshotAsync(
            DateTime date)
        {
            return _internalGoogleCloudStorage.DownloadFlowBuilderUsageCsvAsync(date);
        }

        public async Task<(List<ByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData> ByBusinessData,
                List<ByWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData> ByWabaData)>
            GetInternalDailyWhatsappCloudApiConversationUsageSnapshotDataAsync(DateTime startOfMonth)
        {
            var start = startOfMonth.StartOfMonth();
            var end = startOfMonth.EndOfMonth();

            var byBusinessData =
                new ConcurrentBag<ByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData>();
            var byWabaData = new ConcurrentBag<ByWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData>();

            // Use a more efficient date range generation
            var dateRange = new List<DateTime>((end - start).Days + 1);
            for (var date = start; date <= end; date = date.AddDays(1))
            {
                dateRange.Add(date);
            }

            // Process API calls and conversion in a single parallel operation to reduce memory overhead
            await Parallel.ForEachAsync(
                dateRange,
                new ParallelOptions
                {
                    MaxDegreeOfParallelism = 10
                },
                async (date, cancellationToken) =>
                {
                    try
                    {
                        var output = await _internalWhatsappCloudApiService
                            .GetAllWhatsappCloudApiConversationUsageAnalytics(
                                date,
                                date,
                                "DAILY");

                        // Process each analytic immediately to avoid storing in intermediate collection
                        foreach (var analytic in output.WhatsappCloudApiConversationUsageAnalytics)
                        {
                            try
                            {
                                var convertedData = CmsCompanyDetailConverter
                                    .ConvertToInternalWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData(
                                        analytic,
                                        date);

                                byBusinessData.Add(
                                    convertedData.ByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshot);

                                // Use AddRange pattern for better performance
                                foreach (var byWabaItem in convertedData
                                             .ByWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshots)
                                {
                                    byWabaData.Add(byWabaItem);
                                }
                            }
                            catch (Exception e)
                            {
                                _logger.LogError(
                                    e,
                                    "Error converting analytic data for date {Date}, business {BusinessId}",
                                    date.ToString("yyyy-MM-dd"),
                                    analytic.FacebookBusinessId);
                            }
                        }
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(
                            e,
                            "Error on GetInternalDailyWhatsappCloudApiConversationUsageSnapshotsAsync for date {Date}",
                            date.ToString("yyyy-MM-dd"));
                    }
                });

            // Use more efficient sorting with pre-allocated lists
            var byBusinessList = byBusinessData.ToList();
            var byWabaList = byWabaData.ToList();

            // Sort in-place for better performance
            byBusinessList.Sort((a, b) =>
            {
                var dateComparison = string.Compare(a.Date, b.Date, StringComparison.Ordinal);
                return dateComparison != 0
                    ? dateComparison
                    : string.Compare(a.FacebookBusinessId, b.FacebookBusinessId, StringComparison.Ordinal);
            });

            byWabaList.Sort((a, b) =>
            {
                var dateComparison = string.Compare(a.Date, b.Date, StringComparison.Ordinal);
                return dateComparison != 0
                    ? dateComparison
                    : string.Compare(a.FacebookBusinessId, b.FacebookBusinessId, StringComparison.Ordinal);
            });

            return (byBusinessList, byWabaList);
        }

        private class ConversationMessageLastAgentMessageSentAnalytic
        {
            public string CompanyId { get; set; }

            public DateTime? LastAgentMessageSentAt { get; set; }
        }

        private float Diff(int newValue, int oldValue)
        {
            if (oldValue == 0)
            {
                return 0;
            }

            return ((float) newValue - oldValue) / oldValue;
        }
    }
}