using System.ComponentModel.DataAnnotations;
using System.IdentityModel.Tokens.Jwt;
using System.Text;
using AutoMapper;
using Hangfire;
using Hangfire.Storage.Monitoring;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Sleekflow.Apis.FlowHub.Api;
using Sleekflow.Apis.FlowHub.Model;
using Sleekflow.Apis.TenantHub.Api;
using Sleekflow.Apis.TenantHub.Model;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.Auth0.Exceptions;
using Travis_backend.Auth0.Models;
using Travis_backend.Auth0.Services;
using Travis_backend.Cache;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.CompanyDomain.Utils;
using Travis_backend.CompanyDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.CoreDomain.BackgroundJobs;
using Travis_backend.CoreDomain.Models;
using Travis_backend.CoreDomain.Services;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.FlowHubs;
using Travis_backend.Infrastructures.Options;
using Travis_backend.IntelligentHubDomain.Services;
using Travis_backend.InternalDomain.Services;
using Travis_backend.ShareInvitationDomain.Models;
using Travis_backend.ShareInvitationDomain.ViewModels;
using Travis_backend.SignalR;
using Travis_backend.TenantHubDomain.Services;
using Auth0AppMetadata = Sleekflow.Apis.TenantHub.Model.Auth0AppMetadata;
using Auth0User = Auth0.ManagementApi.Models.User;
using RegisterCompanyInput = Travis_backend.Auth0.Models.RegisterCompanyInput;
using ShareableInvitationViewModel = Travis_backend.Auth0.Models.ShareableInvitationViewModel;
using StaffDeletionJobEntry = Travis_backend.CoreDomain.Models.StaffDeletionJobEntry;
using ********************** = Travis_backend.CoreDomain.Models.**********************;
using StaffWithoutCompanyResponse = Travis_backend.ConversationDomain.ViewModels.StaffWithoutCompanyResponse;

namespace Travis_backend.Auth0.Controllers.Webhook;

[Route("auth0/tenant-hub")]
public class InternalTenantHubActionEventController : Controller
{
    private readonly ICoreService _coreService;
    private readonly IConfiguration _configuration;
    private readonly ICompanyTeamService _companyTeamService;
    private readonly SleekflowUserManager _sleekflowUserManager;
    private readonly ILogger<InternalTenantHubActionEventController> _logger;
    private readonly ApplicationDbContext _appDbContext;
    private readonly ICacheManagerService _cacheManagerService;
    private readonly IManagementIpWhitelistsApi _ipWhitelistsApi;
    private readonly IManagementFeaturesApi _managementFeaturesApi;
    private readonly IManagementEnabledFeaturesApi _managementEnabledFeaturesApi;
    private readonly ISignalRService _signalRService;
    private readonly IMapper _mapper;
    private readonly IAuth0CompanyService _auth0CompanyService;
    private readonly ICompanyUsageService _companyUsageService;
    private readonly IFlowHubConfigsApi _flowHubConfigsApi;
    private readonly IStaffHooks _staffHooks;
    private readonly ICompanyStaffControllerService _companyStaffControllerService;
    private readonly IRbacService _rbacService;
    private readonly IStaffRemovalService _staffRemovalService;
    private readonly ILockService _lockService;

    public InternalTenantHubActionEventController(
        ICoreService coreService,
        IConfiguration configuration,
        ICompanyTeamService companyTeamService,
        UserManager<ApplicationUser> userManager,
        ILogger<InternalTenantHubActionEventController> logger,
        ApplicationDbContext appDbContext,
        ICacheManagerService cacheManagerService,
        IManagementIpWhitelistsApi ipWhitelistsApi,
        IManagementFeaturesApi managementFeaturesApi,
        IManagementEnabledFeaturesApi managementEnabledFeaturesApi,
        ISignalRService signalRService,
        IMapper mapper,
        IAuth0CompanyService auth0CompanyService,
        ICompanyUsageService companyUsageService,
        IFlowHubConfigsApi flowHubConfigsApi,
        IStaffHooks staffHooks,
        ICompanyStaffControllerService companyStaffControllerService,
        IRbacService rbacService,
        IStaffRemovalService staffRemovalService,
        ILockService lockService)
    {
        _logger = logger;
        _appDbContext = appDbContext;
        _cacheManagerService = cacheManagerService;
        _ipWhitelistsApi = ipWhitelistsApi;
        _managementFeaturesApi = managementFeaturesApi;
        _managementEnabledFeaturesApi = managementEnabledFeaturesApi;
        _signalRService = signalRService;
        _mapper = mapper;
        _auth0CompanyService = auth0CompanyService;
        _companyUsageService = companyUsageService;
        _coreService = coreService;
        _configuration = configuration;
        _companyTeamService = companyTeamService;
        _sleekflowUserManager = (SleekflowUserManager) userManager;
        _flowHubConfigsApi = flowHubConfigsApi;
        _staffHooks = staffHooks;
        _companyStaffControllerService = companyStaffControllerService;
        _rbacService = rbacService;
        _staffRemovalService = staffRemovalService;
        _lockService = lockService;
    }

    // TODO Update Validation Logic
    private async Task<bool> ValidateToken(string jwt)
    {
        var jwtHandler = new JwtSecurityTokenHandler();
        // The secretKey
        // 1. No conflict with different environment. (e.g. uat only work in uat only and not work with production)
        // 2. Audience + random string can improve security.
        // 3. Issuer - TenantHub
        // 4. Audience - Sleekflow.TenantHub.io

        var secretKey = _configuration["Auth0:TenantHub:SecretKey"] ?? string.Empty;

        var tokenValidate = new TokenValidationParameters
        {
            ValidateLifetime = false,
            ValidateAudience = false,
            ValidateIssuer = false,
            RequireExpirationTime = true,
            RequireSignedTokens = true,
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(secretKey))
        };
        return (await jwtHandler.ValidateTokenAsync(jwt, tokenValidate)).IsValid;
    }

    private async Task<bool> ValidateTenantHubHeader()
    {
        Request.Headers.TryGetValue("X-Tenant-Hub-Authorization", out var token);

        return !string.IsNullOrEmpty(token) && await ValidateToken(token.ToString());
    }

    public class GetUserInput
    {
        [JsonProperty("sleekflow_user_id")]
        public string SleekflowUserId { get; set; }

        [JsonConstructor]
        public GetUserInput(string sleekflowUserId)
        {
            SleekflowUserId = sleekflowUserId;
        }
    }

    public class GetUserOutput
    {
        [JsonProperty("staff_id")]
        public long StaffId { get; set; }

        [JsonProperty("company_id")]
        public string? CompanyId { get; set; }

        [JsonProperty("user_id")]
        public string UserId { get; set; }

        [JsonProperty("first_name")]
        public string FirstName { get; set; }

        [JsonProperty("last_name")]
        public string LastName { get; set; }

        [JsonProperty("display_id")]
        public string DisplayName { get; set; }

        [JsonProperty("user_name")]
        public string? UserName { get; set; }

        [JsonProperty("email")]
        public string? Email { get; set; }

        [JsonProperty("phone_number")]
        public string? PhoneNumber { get; set; }

        [JsonProperty("position")]
        public string Position { get; set; }

        [JsonProperty("created_at")]
        public DateTime CreatedAt { get; set; }

        [JsonProperty("last_login_at")]
        public DateTime LastLoginAt { get; set; }

        [JsonProperty("email_confirmed")]
        public bool EmailConfirmed { get; set; }

        [JsonProperty("status")]
        [JsonConverter(typeof(StringEnumConverter))]
        public StaffStatus Status { get; set; }

        [JsonProperty("role")]
        public string Role { get; set; }

        [JsonProperty("role_type")]
        [JsonConverter(typeof(StringEnumConverter))]
        public StaffUserRole RoleType { get; set; }

        [JsonProperty("roles")]
        public List<string> Roles { get; set; }

        [JsonProperty("time_zone_info_id")]
        public string TimeZoneInfoId { get; set; }

        [JsonProperty("team_ids")]
        public List<string> TeamIds { get; set; }

        [JsonConstructor]
        public GetUserOutput(
            long staffId,
            string companyId,
            string userId,
            string firstName,
            string lastName,
            string displayName,
            string? userName,
            string? email,
            string? phoneNumber,
            string position,
            DateTime createdAt,
            DateTime lastLoginAt,
            bool emailConfirmed,
            StaffStatus status,
            string role,
            StaffUserRole roleType,
            List<string> roles,
            string timeZoneInfoId,
            List<string> teamIds)
        {
            StaffId = staffId;
            CompanyId = companyId;
            UserId = userId;
            FirstName = firstName;
            LastName = lastName;
            DisplayName = displayName;
            UserName = userName;
            Email = email;
            PhoneNumber = phoneNumber;
            Position = position;
            CreatedAt = createdAt;
            LastLoginAt = lastLoginAt;
            EmailConfirmed = emailConfirmed;
            Status = status;
            Role = role;
            RoleType = roleType;
            Roles = roles;
            TimeZoneInfoId = timeZoneInfoId;
            TeamIds = teamIds;
        }

        public GetUserOutput(Staff staff, List<string> teamIds, List<string> roles)
            : this(
                staff.Id,
                staff.CompanyId,
                staff.Identity.Id,
                staff.Identity.FirstName,
                staff.Identity.LastName,
                staff.Identity.DisplayName,
                staff.Identity.UserName,
                staff.Identity.Email,
                staff.Identity.PhoneNumber,
                staff.Position,
                staff.Identity.CreatedAt,
                staff.Identity.LastLoginAt,
                staff.Identity.EmailConfirmed,
                staff.Status,
                staff.Role,
                staff.RoleType,
                roles,
                staff.TimeZoneInfoId,
                teamIds)
        {
        }
    }

    [HttpPost("GetUser")]
    [ProducesResponseType(typeof(GetUserOutput), StatusCodes.Status200OK)]
    public async Task<ActionResult<GetUserOutput>> GetUser(
        [FromBody]
        GetUserInput getUserInput)
    {
        Request.Headers.TryGetValue("X-Tenant-Hub-Authorization", out var token);
        if (string.IsNullOrEmpty(token))
        {
            Request.Headers.TryGetValue("X-Tenant-Hub-Authorziation", out token);
        }

        _logger.LogInformation("GetUser {GetUserInput}", JsonConvert.SerializeObject(getUserInput));
        if (string.IsNullOrEmpty(token) || !await ValidateToken(token.ToString()))
        {
            _logger.LogError("[{GetUserName}] Token is invalid {Token}",nameof(GetUser), token);
            return NotFound();
        }

        var user = await _sleekflowUserManager.FindByIdAsync(getUserInput.SleekflowUserId);
        _logger.LogInformation("Obtained asp.net user information {User}", JsonConvert.SerializeObject(user));
        if (user is null)
        {
            _logger.LogError("[{GetUserName}] User ({Id}) not found", nameof(GetUser), getUserInput.SleekflowUserId);
            return NotFound();
        }

        var staff = await _coreService.GetCompanyStaff(user);
        if (staff is null)
        {
            _logger.LogError(
                "[{GetUserName}] User staff of identity id ({Id}) not found",
                nameof(GetUser),
                getUserInput.SleekflowUserId);
            return NotFound();
        }

        var roles = await _sleekflowUserManager.GetRolesAsync(user);
        var companyTeams = await _companyTeamService.GetCompanyteamFromStaffId(staff.CompanyId, staff.IdentityId);
        return Ok(
            new GetUserOutput(
                staff,
                companyTeams.Select(c => c.Id.ToString()).ToList(),
                new List<string>
                {

                    Enum.GetName(typeof(StaffUserRole), staff.RoleType) ?? ""
                }.Concat(roles).ToList()));
    }

    public class CleanIpWhitelistCacheInput
    {
        [JsonProperty("company_id")]
        public string CompanyId { get; set; }

        public CleanIpWhitelistCacheInput(string companyId)
        {
            CompanyId = companyId;
        }
    }

    public class CleanIpWhitelistCacheOutput
    {
        [JsonProperty("success")]
        public bool Success { get; set; }

        public CleanIpWhitelistCacheOutput(bool success)
        {
            Success = success;
        }
    }

    [HttpPost("CleanIpWhitelistCache")]
    [ProducesResponseType(typeof(CleanIpWhitelistCacheOutput), StatusCodes.Status200OK)]
    public async Task<ActionResult<CleanIpWhitelistCacheOutput>> CleanIpWhitelistCache(
        [FromBody]
        CleanIpWhitelistCacheInput cleanIpWhitelistCacheInput)
    {
        try
        {
            Request.Headers.TryGetValue("X-Tenant-Hub-Authorization", out var token);
            _logger.LogInformation(
                "CleanIPWhitelistCache {CompanyId}",
                cleanIpWhitelistCacheInput.CompanyId);
            if (string.IsNullOrEmpty(token) || !await ValidateToken(token.ToString()))
            {
                return NotFound();
            }

            var ipWhitelistCacheKeyPattern = new IpWhitelistCacheKeyPattern(cleanIpWhitelistCacheInput.CompanyId);

            await _cacheManagerService.DeleteCacheAsync(ipWhitelistCacheKeyPattern);

            return Ok(new CleanIpWhitelistCacheOutput(true));
        }
        catch (Exception e)
        {
            _logger.LogError(
                "[{CleanIpWhitelistCacheName}] Error when send the clean cache request: {EMessage}",
                nameof(CleanIpWhitelistCache), e.Message);
            return Ok(new CleanIpWhitelistCacheOutput(false));
        }
    }

    public class RefreshIpWhitelistCacheInput
    {
        [JsonProperty("company_id")]
        public string CompanyId { get; set; }

        public RefreshIpWhitelistCacheInput()
        {
        }

        public RefreshIpWhitelistCacheInput(string companyId)
        {
            CompanyId = companyId;
        }
    }

    public class RefreshIpWhitelistCacheOutput
    {
        [JsonProperty("success")]
        public bool Success { get; set; } = false;

        [JsonProperty("is_enabled")]
        public bool IsEnabled { get; set; } = false;

        [JsonProperty("data")]
        public IpWhitelistSettings? IpWhitelistSettings { get; set; }

        public RefreshIpWhitelistCacheOutput(bool success, bool isEnabled, IpWhitelistSettings? ipWhitelistSettings)
        {
            Success = success;
            IsEnabled = isEnabled;
            IpWhitelistSettings = ipWhitelistSettings;
        }
    }

    [HttpPost("RefreshIPWhitelistCache")]
    [ProducesResponseType(typeof(RefreshIpWhitelistCacheOutput), StatusCodes.Status200OK)]
    public async Task<ActionResult<RefreshIpWhitelistCacheOutput>> RefreshIpWhitelistCache(
        [FromBody] RefreshIpWhitelistCacheInput refreshIpWhitelistCacheInput)
    {
        try
        {
            Request.Headers.TryGetValue("X-Tenant-Hub-Authorization", out var token);
            _logger.LogInformation(
                "Refresh IPWhitelist Cache {RefreshIPWhitelistCache}",
                JsonConvert.SerializeObject(refreshIpWhitelistCacheInput));
            if (string.IsNullOrEmpty(token) || !await ValidateToken(token.ToString()))
            {
                return NotFound();
            }

            var company =
                await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(
                    c => c.Id == refreshIpWhitelistCacheInput.CompanyId);
            if (company is null)
            {
                throw new Exception(
                    $"{nameof(RefreshIpWhitelistCache)} Company id {refreshIpWhitelistCacheInput.CompanyId} not found.");
            }

            var managementGetAllFeaturesOutputOutput =
                await _managementFeaturesApi.ManagementFeaturesGetAllFeaturesPostAsync(body: new object());
            var ipWhitelistFeature =
                managementGetAllFeaturesOutputOutput.Data.Features.First(
                    f => f.Name == "IpWhitelist");
            var isIPWhitelistFeatureEnabled =
                (await _managementEnabledFeaturesApi.ManagementEnabledFeaturesIsFeatureEnabledForCompanyPostAsync(
                    managementIsFeatureEnabledForCompanyInput: new ManagementIsFeatureEnabledForCompanyInput(
                        refreshIpWhitelistCacheInput.CompanyId,
                        ipWhitelistFeature.Id))).Data?.IsFeatureEnabled ?? false;

            if (isIPWhitelistFeatureEnabled)
            {
                var ipWhitelists = _ipWhitelistsApi.ManagementIpWhitelistsGetIpWhitelistSettingsPost(
                    managementGetIpWhitelistSettingsInput: new ManagementGetIpWhitelistSettingsInput(
                        refreshIpWhitelistCacheInput.CompanyId)).Data?.IpWhitelistSettings;

                var ipWhitelistCacheKeyPatternRefresh = new IpWhitelistCacheKeyPattern(refreshIpWhitelistCacheInput.CompanyId);

                // If don't want it to expire, don't specify an expiration
                await _cacheManagerService.SaveCacheAsync(ipWhitelistCacheKeyPatternRefresh, ipWhitelists, TimeSpan.FromDays(1));

                return Ok(
                    new RefreshIpWhitelistCacheOutput(
                        success: true,
                        isEnabled: true,
                        ipWhitelistSettings: ipWhitelists));
            }

            var ipWhitelistCacheKeyPatternClean = new IpWhitelistCacheKeyPattern(refreshIpWhitelistCacheInput.CompanyId);
            await _cacheManagerService.DeleteCacheAsync(ipWhitelistCacheKeyPatternClean);

            return Ok(
                new RefreshIpWhitelistCacheOutput(
                    success: true,
                    isEnabled: false,
                    ipWhitelistSettings: null));
        }
        catch (Exception e)
        {
            _logger.LogError(
                "[{RefreshIpWhitelistCacheName}] Error when saving cache: {EMessage}",
                nameof(RefreshIpWhitelistCache),
                e.Message);

            return Ok(
                new RefreshIpWhitelistCacheOutput(
                    success: false,
                    isEnabled: false,
                    ipWhitelistSettings: null));
        }
    }

    public class RegisterCompanyResponse : RegisterCompanyResult
    {
        public string UserId { get; set; }

        public string Email { get; set; }

        public string UserName { get; set; }

        public string? FirstName { get; set; }

        public string? LastName { get; set; }

        public RegisterCompanyResponse(
            string staffId,
            string signalRGroupName,
            bool isShopifyAccount,
            List<string> associatedCompanyIds,
            string location,
            string userId,
            string email,
            string userName,
            string firstName,
            string lastName)
            : base(staffId, signalRGroupName, isShopifyAccount, associatedCompanyIds, location)
        {
            UserId = userId;
            Email = email;
            UserName = userName;
            FirstName = firstName;
            LastName = lastName;
        }
    }

    [HttpPost("RegisterCompany")]
    [ProducesResponseType(typeof(RegisterCompanyResponse), StatusCodes.Status200OK)]
    public async Task<ActionResult<RegisterCompanyResponse>> RegisterCompany(
        [FromBody]
        RegisterCompanyInput registerCompanyInput)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
        }

        if (registerCompanyInput.UserId is null)
        {
            return BadRequest("UserId is required");
        }

        var dbUser = await _sleekflowUserManager.FindByIdAsync(registerCompanyInput.UserId);
        try
        {
            if (dbUser is null)
            {
                // TODO: User will add to the current location if user is not found, should not be bad request .. ?
                // Implementation for migrated users
                /*
                _logger.LogError(
                    $"[{nameof(RegisterCompany)}] User ({registerCompanyInput.UserId}) not found in {registerCompanyInput.Location} server");

                return BadRequest(
                    new ResponseViewModel()
                    {
                        message =
                            $"[{nameof(RegisterCompany)}] User not found in {registerCompanyInput.Location} server"
                    });
                    */

                // Implementation for creating a new user
                _logger.LogCritical(
                    "[{RegisterCompanyName}] User ({UserId}) not found in {Location} server, creating new user",
                    nameof(RegisterCompany),
                    registerCompanyInput.UserId,
                    registerCompanyInput.Location);

                var auth0Users = await _sleekflowUserManager.GetAuth0UsersById(registerCompanyInput.UserId);
                var auth0User = auth0Users?.FirstOrDefault(
                    u => JsonConvert.DeserializeObject<Auth0AppMetadata>(u.AppMetadata.ToString()).SleekflowId ==
                         registerCompanyInput.UserId);
                if (auth0User is null)
                {
                    _logger.LogError(
                        "[{RegisterCompanyName}] Auth0 user not found for user id {UserId}",
                        nameof(RegisterCompany),
                        registerCompanyInput.UserId);
                    return BadRequest("User not found");
                }

                var (_, createdUser) = await _sleekflowUserManager.CreateNewDbUser(auth0User);
                dbUser = createdUser;
            }

            var isSocialLogin = registerCompanyInput.ConnectionStrategy is not "auth0";

            dbUser.EmailConfirmed = isSocialLogin;

            dbUser.FirstName = registerCompanyInput.FirstName;
            dbUser.LastName = registerCompanyInput.LastName;
            dbUser.DisplayName =
                $"{registerCompanyInput.FirstName} {registerCompanyInput.LastName}";

            dbUser.PhoneNumber = registerCompanyInput.PhoneNumber;
            dbUser.IsAgreeMarketingConsent = registerCompanyInput.IsAgreeMarketingConsent;

            var updateResult = await _sleekflowUserManager.UpdateAsync(dbUser);
            if (!updateResult.Succeeded)
            {
                foreach (var updateResultError in updateResult.Errors)
                {
                    _logger.LogError(
                        $"[{nameof(RegisterCompany)}] Unable to update the user, {{Description}} {{Code}}",
                        updateResultError.Description,
                        updateResultError.Code);
                }

                return BadRequest(ModelState);
            }

            var updatedUser = await _sleekflowUserManager.GetUserAsync(dbUser);
            var registerCompanyOutput =
                await _auth0CompanyService.RegisterCompanyAsync(
                    updatedUser ?? throw new InvalidOperationException(), registerCompanyInput);
            try
            {
                await _flowHubConfigsApi.FlowHubConfigsEnrollFlowHubPostAsync(
                    enrollFlowHubInput: new EnrollFlowHubInput(
                        registerCompanyInput.CompanyId,
                        dbUser.Id.ToString(),
                        new List<string>(),
                        _configuration.GetValue<string>("Values:DomainName")));
            }
            catch (Exception e)
            {
                _logger.LogError(
                    e,
                    "[{MethodName}] Unable to enrol flow limit for company {CompanyId}",
                    nameof(RegisterCompany),
                    registerCompanyInput.CompanyId);
            }

            BackgroundJob.Enqueue<IFlowHubService>(x => x.UpdateFlowHubConfigAsync(registerCompanyInput.CompanyId, null));
            BackgroundJob.Enqueue<IIntelligentHubService>(x => x.RefreshIntelligentHubConfigAsync(registerCompanyInput.CompanyId));

            return new RegisterCompanyResponse(
                registerCompanyOutput.StaffId,
                registerCompanyOutput.SignalRGroupName,
                registerCompanyOutput.IsShopifyAccount,
                registerCompanyOutput.AssociatedCompanyIds,
                registerCompanyInput.Location,
                updatedUser.Id,
                updatedUser.Email!,
                updatedUser.UserName!,
                updatedUser.FirstName,
                updatedUser.LastName);
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                $"[{nameof(RegisterCompany)}] Error occur when register company: {{ExceptionMessage}}",
                e.Message);
            throw;
        }
    }

    public class GetUserStaffInput
    {
        [Required]
        [JsonProperty("sleekflow_user_id")]
        public string SleekflowUserId { get; set; }

        [JsonConstructor]
        public GetUserStaffInput(string sleekflowUserId)
        {
            SleekflowUserId = sleekflowUserId;
        }
    }

    public class GetUserStaffOutput
    {
        [JsonProperty("user")]
        public ApplicationUser? User { get; set; }

        [JsonProperty("staff")]
        public StaffWithoutCompanyResponse? Staff { get; set; }

        [JsonConstructor]
        public GetUserStaffOutput(
            ApplicationUser? user,
            StaffWithoutCompanyResponse? staff)
        {
            User = user;
            Staff = staff;
        }
    }

    [HttpPost("GetUserStaff")]
    public async Task<ActionResult<GetUserStaffOutput>> GetUserStaff([FromBody] GetUserStaffInput input)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        var user = await _sleekflowUserManager.FindByIdAsync(input.SleekflowUserId);
        if (user is null)
        {
            return Ok(new GetUserStaffOutput(null, null));
        }

        StaffWithoutCompanyResponse? staffVm = null;
        var staff = await _coreService.GetCompanyStaff(user);

        if (staff is not null)
        {
            staffVm = _mapper.Map<StaffWithoutCompanyResponse>(staff);
        }

        return Ok(new GetUserStaffOutput(user, staffVm));
    }

    public class GetApplicationUserInput
    {
        [JsonProperty("sleekflow_user_id")]
        public string? SleekflowUserId { get; set; }

        [JsonProperty("email")]
        public string? Email { get; set; }

        [JsonProperty("username")]
        public string? UserName { get; set; }

        [JsonConstructor]
        public GetApplicationUserInput(string? sleekflowUserId=null, string? email=null, string? userName=null)
        {
            SleekflowUserId = sleekflowUserId;
            Email = email;
            UserName = userName;
        }
    }

    [HttpPost("GetApplicationUser")]
    [ProducesResponseType(typeof(ApplicationUser), StatusCodes.Status200OK)]
    public async Task<ActionResult<ApplicationUser>> GetApplicationUser(
        [FromBody]
        GetApplicationUserInput getUserInput)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        if (getUserInput?.SleekflowUserId is not null)
        {
            var user = await _sleekflowUserManager.FindByIdAsync(getUserInput.SleekflowUserId);
            return Ok(user);
        }

        if (getUserInput.Email is not null)
        {
            var user = await _sleekflowUserManager.FindByEmailAsync(getUserInput.Email);
            return Ok(user);
        }

        if (getUserInput.UserName is not null)
        {
            var user = await _sleekflowUserManager.FindByNameAsync(getUserInput.UserName);
            return Ok(user);
        }

        return BadRequest("Invalid input");
    }

    public class AssociateDbUserWithAuth0Input
    {
        [JsonProperty("auth0_user")]
        public Auth0User Auth0User { get; set; }

        [JsonProperty("application_user")]
        public ApplicationUser ApplicationUser { get; set; }

        [JsonProperty("roles")]
        public List<string?> Roles { get; set; }

        public AssociateDbUserWithAuth0Input(Auth0User auth0User, ApplicationUser applicationUser)
        {
            Auth0User = auth0User;
            ApplicationUser = applicationUser;
        }
    }

    public class AssociateDbUserWithAuth0Output
    {
        [JsonProperty("auth0_user")]
        public Auth0User UpdatedAuth0User { get; set; }

        [JsonProperty("application_user")]
        public ApplicationUser ApplicationUser { get; set; }

        [JsonProperty("roles")]
        public List<string>? Roles { get; set; }

        public AssociateDbUserWithAuth0Output(Auth0User auth0User, ApplicationUser applicationUser, List<string>? roles)
        {
            UpdatedAuth0User = auth0User;
            ApplicationUser = applicationUser;
            Roles = roles;
        }
    }


    [HttpPost("AssociateDbUserToAuth0")]
    public async Task<ActionResult<AssociateDbUserWithAuth0Output>> AssociateDbUserWithAuth0(
        [FromBody] AssociateDbUserWithAuth0Input input)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        var dbRoles = await _sleekflowUserManager.GetRolesAsync(input.ApplicationUser);
        var removeItems = input.Roles.Except(dbRoles);
        var addItems = dbRoles.Except(input.Roles);

        var enumerable = removeItems.ToList();
        if (enumerable.Any())
        {
            foreach (var role in enumerable)
            {
                await _sleekflowUserManager.RemoveFromRoleAsync(input.ApplicationUser, role!);
            }
        }

        var items = addItems.ToList();
        if (items.Any())
        {
            foreach (var role in items)
            {
                await _sleekflowUserManager.AddToRoleAsync(input.ApplicationUser, role!);
            }
        }

        return Ok(
            new AssociateDbUserWithAuth0Output(
                input.Auth0User,
                input.ApplicationUser,
                (await _sleekflowUserManager.GetRolesAsync(input.ApplicationUser))?.ToList()));
    }

    public class CreateNewDbUserInput
    {
        [JsonProperty("sleekflow_user_id")]
        public string SleekflowUserId { get; set; }

        [JsonProperty("auth0_user")]
        public Auth0User Auth0User { get; set; }

        public CreateNewDbUserInput(Auth0User auth0User, string sleekflowUserId)
        {
            Auth0User = auth0User;
            SleekflowUserId = sleekflowUserId;
        }
    }

    public class CreateNewDbUserOutput
    {
        [JsonProperty("application_user")]
        public ApplicationUser ApplicationUser { get; set; }

        public CreateNewDbUserOutput(ApplicationUser applicationUser)
        {
            ApplicationUser = applicationUser;
        }
    }

    [HttpPost("CreateNewDbUser")]
    public async Task<ActionResult<CreateNewDbUserOutput>> CreateNewDbUser([FromBody] CreateNewDbUserInput input)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        var createdUser = await _sleekflowUserManager.CreateNewDbUserByAuth0User(input.Auth0User);

        return Ok(new CreateNewDbUserOutput(createdUser));
    }

    public class UpdateDbuserInput
    {
        [JsonProperty("auth0_user")]
        public Auth0User Auth0User { get; set; }

        public UpdateDbuserInput(Auth0User auth0User)
        {
            Auth0User = auth0User;
        }
    }

    public class UpdateDbUserOutput
    {
        [JsonProperty("message")]
        public string Message { get; set; }

        [JsonProperty("application_user")]
        public ApplicationUser ApplicationUser { get; set; }

        public UpdateDbUserOutput(string message, ApplicationUser applicationUser)
        {
            Message = message;
            ApplicationUser = applicationUser;
        }
    }

    [HttpPost("UpdateDbUser")]
    public async Task<ActionResult<UpdateDbUserOutput>> UpdateDbUser([FromBody] UpdateDbuserInput input)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        var updatedUser = await _sleekflowUserManager.UpdateDbUserByAuth0User(input.Auth0User);

        return Ok(new UpdateDbUserOutput("User updated successfully", updatedUser));
    }

    public class DeleteDbUserInput
    {
        [JsonProperty("sleekflow_user_id")]
        public string SleekflowUserId { get; set; }

        [JsonProperty("is_also_delete_auth0_user")]
        public bool AlsoDeleteAuth0User { get; set; }

        public DeleteDbUserInput(string sleekflowUserId, bool? alsoDeleteAuth0User = true)
        {
            SleekflowUserId = sleekflowUserId;
            AlsoDeleteAuth0User = alsoDeleteAuth0User ?? true;
        }
    }

    [HttpPost("DeleteDbUser")]
    public async Task<IActionResult> DeleteDbUser([FromBody] DeleteDbUserInput input)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        var deleteUser = await _sleekflowUserManager.FindByIdAsync(input.SleekflowUserId);
        if (deleteUser is not null)
        {
            await _sleekflowUserManager.DeleteAsync(deleteUser, input.AlsoDeleteAuth0User);
        }

        return Ok("User deleted successfully");
    }

    public class OnUserChangePasswordInput
    {
        [JsonProperty("sleekflow_user_id")]
        public string SleekflowUserId { get; set; }

        public OnUserChangePasswordInput(string sleekflowUserId)
        {
            SleekflowUserId = sleekflowUserId;
        }
    }

    [HttpPost("OnUserChangePassword")]
    public async Task<IActionResult> OnUserChangePassword([FromBody] OnUserChangePasswordInput input)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        var dbUser = await _sleekflowUserManager.FindByIdAsync(input.SleekflowUserId);

        if (dbUser is not null)
        {
            // Reset password auto logout all session
            var staff = await _appDbContext.UserRoleStaffs
                .Where(x => x.IdentityId == dbUser.Id)
                .Include(x => x.RegisteredSessions)
                .FirstOrDefaultAsync();
            if (staff == null)
            {
                _logger.LogInformation(
                    $"[{nameof(OnUserChangePassword)}] Skip the disconnect Signal connection because Staff is null user id: {dbUser.Id}");

                return Ok();
            }

            staff
                .RegisteredSessions
                .ForEach(x => x.SessionStatus = SessionStatus.AutoLogout);
            foreach (var device in staff.RegisteredSessions)
            {
                await _signalRService.SignalRAutoLogout(device.UUID, _mapper.Map<ActiveSessionResponse>(device));
            }

            await _appDbContext.SaveChangesAsync();
            await _sleekflowUserManager.UpdateAsync(dbUser);
        }

        return Ok();
    }

    public class InviteUserByEmailsInput
    {
        [Required]
        [JsonProperty("sleekflow_user_id")]
        public string SleekflowUserId { get; set; }

        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string CompanyId { get; set; }

        [Required]
        [JsonProperty("invite_users")]
        public List<InviteUserInputObject> InviteUsers { get; set; }

        [Required]
        [JsonProperty("team_ids")]
        public List<long> TeamIds { get; set; }

        [JsonProperty("tenanthub_user_id")]
        public string? TenanthubUserId { get; set; }

        [JsonConstructor]
        public InviteUserByEmailsInput(string sleekflowUserId, List<InviteUserInputObject> inviteUsers, List<long> teamIds, string companyId, string? tenanthubUserId=null)
        {
            SleekflowUserId = sleekflowUserId;
            InviteUsers = inviteUsers;
            TeamIds = teamIds;
            CompanyId = companyId;
            TenanthubUserId = tenanthubUserId;
        }
    }

    public class InviteUserByEmailsResponse : List<InvitedUserByEmailObject>
    {
        public InviteUserByEmailsResponse(
            List<InvitedUserByEmailObject> invitedUsers)
        {
            var result = this;
            result.AddRange(invitedUsers);
        }
    }

    [HttpPost("InviteUsersByEmail")]
    [ProducesResponseType(typeof(InviteUserByEmailsResponse), StatusCodes.Status200OK)]
    public async Task<ActionResult<InviteUserByEmailsResponse>> InviteUsersByEmail(
        [FromBody] InviteUserByEmailsInput input)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        try
        {
            var user = await _sleekflowUserManager.FindByIdAsync(input.SleekflowUserId);
            var invitedUsers =
                await _auth0CompanyService.InviteUsersByEmailAsync(user, input.CompanyId, input.TeamIds, input.InviteUsers);
            return Ok(new InviteUserByEmailsResponse(invitedUsers));
        }
        catch (IdentityResultException ex)
        {
            _logger.LogError(
                ex,
                "[{InviteUsersByEmailName}] Identity result exception: {Message}",
                nameof(InviteUsersByEmail),
                ex.Message);

            return BadRequest(new ResponseViewModel
            {
                message = ex.Message,
                errorId = "IDENTITY_RESULT_EXCEPTION"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{InviteUsersByEmailName}] Error inviting users: {Message}",
                nameof(InviteUsersByEmail),
                ex.Message);

            return BadRequest(new ResponseViewModel
            {
                message = "Failed to invite users. Please try again.",
                errorId = "INVITATION_FAILED"
            });
        }
    }

    public class ShareableInvitationInput
    {
        [JsonProperty("sleekflow_user_id")]
        public string SleekflowUserId { get; set; }

        [JsonProperty("data")]
        public ShareableInvitationViewModel ShareableInvitation { get; set; }

        [JsonConstructor]
        public ShareableInvitationInput(string sleekflowUserId, ShareableInvitationViewModel shareableInvitation)
        {
            SleekflowUserId = sleekflowUserId;
            ShareableInvitation = shareableInvitation;
        }
    }

    [HttpPost("GenerateShareableLink")]
    [ProducesResponseType(typeof(ShareableInvitationResponse), StatusCodes.Status200OK)]
    public async Task<ActionResult<ShareableInvitationResponse>> GenerateShareableLink(
        [FromBody] ShareableInvitationInput shareableInvitationInput)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        var (result, location) = await _auth0CompanyService.GenerateShareableLinkAsync(
            shareableInvitationInput.SleekflowUserId,
            shareableInvitationInput.ShareableInvitation);

        var response = _mapper.Map<ShareableInvitationResponse>(result);
        response.Location = location;

        return Ok(response);
    }

    public class AcceptInviteLinkInput
    {
        [Required]
        [JsonProperty("shareable_id")]
        public string ShareableId { get; set; }

        [Required]
        [JsonProperty("invite_shared_user_object")]
        public InviteSharedUserObject InviteSharedUserObject { get; set; }

        [JsonProperty("location")]
        public string Location { get; set; }

        public AcceptInviteLinkInput(string shareableId, InviteSharedUserObject inviteSharedUserObject, string location)
        {
            ShareableId = shareableId;
            InviteSharedUserObject = inviteSharedUserObject;
            Location = location;
        }
    }

    public class AcceptInviteLinkOutput
    {
        [JsonProperty("user_id")]
        public string UserId { get; set; }

        [JsonProperty("company_id")]
        public string CompanyId { get; set; }

        [JsonProperty("staff_id")]
        public string StaffId { get; set; }

        [JsonProperty("role_type")]
        public string RoleType { get; set; }

        [JsonProperty("team_ids")]
        public List<string> TeamIds { get; set; }

        [JsonProperty("auth0_user")]
        public Auth0User? Auth0User { get; set; }
    }

    [HttpPost("AcceptShareableInviteLink")]
    public async Task<ActionResult> AcceptInviteLink(
        [FromBody] AcceptInviteLinkInput acceptInviteLinkInput)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(new ResponseViewModel
            {
                message = ModelState?.First().Value?.Errors.First().ErrorMessage,
                errorId = "INVALID_FORM_VALUE"
            });
        }

        _logger.LogDebug(
            "[{AcceptShareableInviteLinkName}] Accepting shareable invite link {ShareableId}: {SerializeObject}",
            nameof(AcceptInviteLink),
            acceptInviteLinkInput.ShareableId,
            JsonConvert.SerializeObject(acceptInviteLinkInput));

        var invitation = await _appDbContext.CompanyShareableInvitations
                    .Include(x => x.ShareableInvitationRecords)
                    .FirstOrDefaultAsync(x => x.InvitationId == acceptInviteLinkInput.ShareableId);

        if (invitation == null)
        {
            return BadRequest();
        }

        var usage = await _companyUsageService.GetCompanyUsage(invitation.CompanyId);

        if (usage.MaximumAgents <= usage.totalAgents)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Not enough agent quota, please purchase",
                    errorId = "SHARED_INVITE_EXCEEDED_COMPANY_QUOTA"
                });
        }

        if (invitation.Status == ShareableLinkStatus.Disabled)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invitation disabled", errorId = "SHARED_INVITE_DISABLED"
                });
        }

        if (invitation.Quota <= invitation.ShareableInvitationRecords.Count)
        {
            invitation.Status = ShareableLinkStatus.Disabled;

            await _appDbContext.SaveChangesAsync();

            return BadRequest(
                new ResponseViewModel
                {
                    message = "Exceeded quota", errorId = "SHARED_INVITE_EXCEEDED_INVITATION_QUOTA"
                });
        }

        if (DateTime.UtcNow > invitation.ExpirationDate)
        {
            invitation.Status = ShareableLinkStatus.Disabled;

            await _appDbContext.SaveChangesAsync();

            return BadRequest(
                new ResponseViewModel
                {
                    message = "Expired", errorId = "SHARED_INVITE_EXPIRED"
                });
        }

        var staffId = await _appDbContext.UserRoleStaffs
            .Where(x => x.CompanyId == invitation.CompanyId)
            .OrderBy(x => x.Order)
            .ThenBy(x => x.Id)
            .Select(x => x.IdentityId)
            .FirstOrDefaultAsync();

        var inviteSharedUserObject = acceptInviteLinkInput.InviteSharedUserObject;
        var user = await _sleekflowUserManager.FindByEmailAsync(inviteSharedUserObject.Email);
        var auth0User = new Auth0User();

        _logger.LogInformation(
            "[{AcceptShareableInviteLink}] Company {CompanyId} creating user {UserEmail} with TenantHubUserId {TenantHubUserId}, object: {JsonObject}",
            nameof(AcceptInviteLink),
            invitation.CompanyId,
            inviteSharedUserObject.Email,
            inviteSharedUserObject.TenantHubUserId,
            JsonConvert.SerializeObject(inviteSharedUserObject));

        if (user is null)
        {
            if (inviteSharedUserObject.UserId is null)
            {
                _logger.LogError(
                    $"[{nameof(AcceptInviteLink)}] Missing user id in inviteSharedUserObject");
                throw new Exception("User id is Required");
            }
            if (inviteSharedUserObject.TenantHubUserId is null)
            {
                _logger.LogError(
                    $"[{nameof(AcceptInviteLink)}] Missing TenantHub user id in inviteSharedUserObject");
                throw new Exception("TenantHub User id is Required");
            }

            var result = await _sleekflowUserManager.CreateWithTenantHubIdAsync(
                new ApplicationUser
                {
                    Id = inviteSharedUserObject.UserId,
                    UserName = inviteSharedUserObject.UserName ?? inviteSharedUserObject.Email,
                    Email = inviteSharedUserObject.Email,
                    FirstName = inviteSharedUserObject.Firstname,
                    LastName = inviteSharedUserObject.Lastname,
                    DisplayName = $"{inviteSharedUserObject.Firstname} {inviteSharedUserObject.Lastname}",
                    PhoneNumber = inviteSharedUserObject.PhoneNumber
                },
                inviteSharedUserObject.Password,
                inviteSharedUserObject.TenantHubUserId);
            var identityResult = result.IdentityResult;
            auth0User = result.User;

            if (!identityResult.Succeeded)
            {
                _logger.LogError(
                    "[CompanyInvite] Company {CompanyId} create user {UserEmail} error: {Errors}",
                    invitation.CompanyId,
                    inviteSharedUserObject.Email,
                    JsonConvert.SerializeObject(identityResult.Errors));

                return BadRequest(
                    new ResponseViewModel
                    {
                        message = identityResult.Errors.First().Description, errorId = "CREATE_USER_FAILED"
                    });
            }

            user = await _sleekflowUserManager.FindByEmailAsync(inviteSharedUserObject.Email);
        }

        var userHaveCompanyRegistered = await _appDbContext.UserRoleStaffs
            .AnyAsync(u => u.IdentityId == user.Id);

        if (userHaveCompanyRegistered)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = $"User {user.Email} have registered a company",
                    errorId = "USER_HAVE_COMPANY_REGISTERED"
                });
        }

        var staff = new Staff
        {
            CompanyId = invitation.CompanyId,
            IdentityId = user.Id,
            Identity = user,
            Locale = "en",
            RoleType = invitation.Role,
            Position = inviteSharedUserObject.Position,
            TimeZoneInfoId = inviteSharedUserObject.TimeZoneInfoId,
            NotificationSettingId = 1,
            Order = 1
        };

        await _appDbContext.UserRoleStaffs.AddAsync(staff);

        invitation.ShareableInvitationRecords.Add(
            new ShareableInvitationRecord
            {
                InvitedStaff = staff
            });

        invitation.Redeemed = invitation.ShareableInvitationRecords.Count;

        if (invitation.Redeemed == invitation.Quota)
        {
            invitation.Status = ShareableLinkStatus.Disabled;
        }

        await _appDbContext.SaveChangesAsync();

        await _companyTeamService.AddOrRemoveTeam(
            invitation.CompanyId,
            staff.Id,
            invitation.TeamIds);

        var company = await _appDbContext.CompanyCompanies
            .FirstOrDefaultAsync(x => x.Id == invitation.CompanyId);

        await _coreService.UpdatePlanFreeToFreemium(staff);

        /* // TODO: Delete before release, this is for reference purpose
        var response = _mapper.Map<AuthenticationResponse>(user);

        response.SignalRGroupName = staff.Company.SignalRGroupName;
        response.IsShopifyAccount = staff.Company.IsShopifyAccount;
        response.AssociatedCompanyIds =
            await _appDbContext.UserRoleStaffs
                .Where(x => x.IdentityId == user.Id)
                .Select(x => x.CompanyId)
                .ToListAsync();
        _logger.LogInformation(
            $"[InviteByShareableURL] {response.Id}, {response.Email}, {response.SignalRGroupName}");
            */
        var response = new AcceptInviteLinkOutput()
        {
            UserId = user.Id,
            CompanyId = invitation.CompanyId,
            StaffId = staff.Id.ToString(),
            RoleType = staff.RoleType.ToString(),
            TeamIds = invitation.TeamIds is null
                ? []
                : invitation.TeamIds
                    .ConvertAll(x => x.ToString())
                    .ToList(),
            Auth0User = auth0User
        };

        BackgroundJob.Enqueue<ICoreService>(
            x => x.AddToSleekFlowCRM(
                staff.CompanyId,
                user,
                "User",
                null,
                null,
                null,
                null));

        BackgroundJob.Enqueue<IInternalHubSpotService>(
            x => x.SyncCompanyStaffs(staff.CompanyId));

        return Ok(response);
    }

    public class ResendInvitationEmailInput
    {
        [JsonProperty("admin_user_id")]
        public string AdminUserId { get; set; }

        [JsonProperty("company_id")]
        public string CompanyId { get; set; }

        [JsonProperty("sleekflow_user_id")]
        public string SleekflowUserId { get; set; }

        [JsonProperty("tenanthub_user_id")]
        public string? TenanthubUserId { get; set; }

        [JsonConstructor]
        public ResendInvitationEmailInput(
            string adminUserId,
            string companyId,
            string sleekflowUserId,
            string tenanthubUserId)
        {
            AdminUserId = adminUserId;
            CompanyId = companyId;
            SleekflowUserId = sleekflowUserId;
            TenanthubUserId = tenanthubUserId;
        }
    }

    [HttpPost("ResendInvitationEmail")]
    public async Task<ActionResult<bool>> ResendInvitationEmail([FromBody] ResendInvitationEmailInput emailInput)
    {
        if (ModelState.IsValid)
        {
            var adminStaff = await _coreService.GetCompanyStaff(
                await _sleekflowUserManager.FindByIdAsync(emailInput.AdminUserId));

            if (!_rbacService.IsRbacEnabled())
            {
                if (adminStaff == null || adminStaff.RbacRole == StaffUserRole.Staff)
                {
                    return Unauthorized();
                }
            }

            return await _auth0CompanyService.ResendInvitationEmailAsync(
                emailInput.SleekflowUserId,
                emailInput.TenanthubUserId!,
                adminStaff);
        }

        return BadRequest(ModelState);
    }

    public class IsCompanyRegisteredInput
    {
        [JsonProperty("sleekflow_user_id")]
        public string SleekflowUserId { get; set; }

        [JsonConstructor]
        public IsCompanyRegisteredInput(string sleekflowUserId)
        {
            SleekflowUserId = sleekflowUserId;
        }
    }

    public class IsCompanyRegisterOutput
    {
        [JsonProperty("is_company_registered")]
        public bool IsCompanyRegistered { get; set; }

        [JsonConstructor]
        public IsCompanyRegisterOutput(bool isCompanyRegistered)
        {
            IsCompanyRegistered = isCompanyRegistered;
        }
    }

    [HttpPost("IsCompanyRegistered")]
    public async Task<ActionResult<bool>> IsCompanyRegistered([FromBody] IsCompanyRegisteredInput input)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        var targetStaff = await _appDbContext.UserRoleStaffs.FirstOrDefaultAsync(u => u.IdentityId == input.SleekflowUserId);
        return Ok(new IsCompanyRegisterOutput(targetStaff != null));
    }

    public class DeleteCompanyStaffInput
    {
        [JsonProperty("company_id")]
        public string CompanyId { get; set; }

        [JsonProperty("staff_id")]
        public long StaffId { get; set; }

        [JsonConstructor]
        public DeleteCompanyStaffInput(long staffId, string companyId)
        {
            StaffId = staffId;
            CompanyId = companyId;
        }
    }

    public class DeleteCompanyStaffOutput
    {
        [JsonProperty("message")]
        public string Message { get; set; }

        [JsonConstructor]
        public DeleteCompanyStaffOutput(string message)
        {
            Message = message;
        }
    }

    [HttpPost("DeleteCompanyStaff")]
    public async Task<ActionResult<DeleteCompanyStaffOutput>> DeleteCompanyStaff([FromBody] DeleteCompanyStaffInput input)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        var isRemoved = await _coreService.RemoveStaffData(input.CompanyId, input.StaffId);
        if (!isRemoved)
        {
            return BadRequest(
                new DeleteCompanyStaffOutput($"Company Staff ({input.StaffId}) cannot be deleted!"));
        }

        var getCompanyUsageCacheKeyPattern = new GetCompanyUsageCacheKeyPattern(input.CompanyId);
        await _staffHooks.OnStaffDeletedAsync(input.CompanyId, input.StaffId);
        await _cacheManagerService.DeleteCacheAsync(getCompanyUsageCacheKeyPattern);

        return Ok(
            new DeleteCompanyStaffOutput($"Company Staff ({input.StaffId}) has been deleted!"));
    }
    public class DeleteCompanyStaffInBackgroundInput
    {
        [JsonProperty("company_id")]
        public string CompanyId { get; set; }

        [JsonProperty("staff_id")]
        public long StaffId { get; set; }

        [JsonProperty("user_id")]
        public string UserId { get; set; }

        [JsonConstructor]
        public DeleteCompanyStaffInBackgroundInput(long staffId, string companyId, string userId)
        {
            StaffId = staffId;
            CompanyId = companyId;
            UserId = userId;
        }
    }

    public class DeleteCompanyStaffInBackgroundOutput
    {
        [JsonProperty("job_id")]
        public string JobId { get; set; }

        [JsonProperty("message")]
        public string Message { get; set; }

        [JsonProperty("is_success")]
        public bool IsSuccess { get; set; }

        [JsonProperty("job_status")]
        public ********************** JobStatus { get; set; }

        [JsonConstructor]
        public DeleteCompanyStaffInBackgroundOutput(string jobId, string message, bool isSuccess, ********************** jobStatus)
        {
            JobId = jobId;
            Message = message;
            IsSuccess = isSuccess;
            JobStatus = jobStatus;
        }
    }

    [HttpPost("DeleteCompanyStaffInBackground")]
    public async Task<ActionResult<DeleteCompanyStaffInBackgroundOutput>> DeleteCompanyStaffInBackground(
        [FromBody] DeleteCompanyStaffInBackgroundInput input)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        // Use company-level lock to prevent race conditions
        var lockId = $"staff_deletion_company_lock:{input.CompanyId}";
        ILockService.Lock companyLock = null;

        try
        {
            // Acquire company-level lock to prevent concurrent modifications
            companyLock = await _lockService.AcquireLockAsync(lockId, TimeSpan.FromSeconds(30));

            if (companyLock == null)
            {
                _logger.LogWarning("Unable to acquire company lock for staff deletion in company {CompanyId}", input.CompanyId);
                return StatusCode(StatusCodes.Status503ServiceUnavailable, new DeleteCompanyStaffInBackgroundOutput(
                    string.Empty,
                    "Service temporarily unavailable. Please try again.",
                    false,
                    new **********************
                    {
                        CurrentStatus = **********************.JobStatus.Failed,
                        Message = "Unable to acquire company lock for staff deletion",
                        LastUpdated = DateTime.UtcNow
                    }));
            }

            // Enqueue the deletion job using Hangfire first
            var jobId = BackgroundJob.Enqueue<DeleteStaffBackgroundJob>(
                x => x.Execute(input.CompanyId, input.StaffId, input.UserId, null));

            // Add job entry to CompanyStaffDeletionJobs cache with proper locking
            var jobsKey = new StaffDeletionJobsByCompanyKeyPattern(input.CompanyId);
            var jobsJson = await _cacheManagerService.GetCacheAsync(jobsKey);
            var allJobs = string.IsNullOrEmpty(jobsJson)
                ? new CompanyStaffDeletionJobs()
                : (JsonConvert.DeserializeObject<CompanyStaffDeletionJobs>(jobsJson) ?? new CompanyStaffDeletionJobs());

            var jobStatus = new **********************
            {
                CurrentStatus = **********************.JobStatus.Pending,
                Message = $"Job has been queued for processing. job ID: {jobId}",
                LastUpdated = DateTime.UtcNow
            };

            allJobs.Jobs.Add(new StaffDeletionJobEntry
            {
                JobId = jobId,
                StaffId = input.StaffId,
                CompanyId = input.CompanyId,
                UserId = input.UserId,
                Status = jobStatus
            });
            await _cacheManagerService.SaveCacheAsync(jobsKey, allJobs, TimeSpan.FromDays(14));

            // Note: AgentsLimitOffset will be incremented in the background job only after successful execution
            // This prevents the race condition where the offset is incremented but the job fails

            return Ok(new DeleteCompanyStaffInBackgroundOutput(
                jobId,
                $"Staff deletion process started. Track progress with job ID: {jobId}",
                true,
                jobStatus));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to enqueue staff deletion job for company {CompanyId}, staff {StaffId}: {Message}",
                input.CompanyId, input.StaffId, ex.Message);

            return StatusCode(StatusCodes.Status500InternalServerError, new DeleteCompanyStaffInBackgroundOutput(
                string.Empty,
                $"Failed to start staff deletion process: {ex.Message}",
                false,
                new **********************
                {
                    CurrentStatus = **********************.JobStatus.Failed,
                    Message = $"Job enqueuing failed: {ex.Message}",
                    LastUpdated = DateTime.UtcNow
                }));
        }
        finally
        {
            // Always release the company lock
            if (companyLock != null)
            {
                await _lockService.ReleaseLockAsync(companyLock);
            }
        }
    }

    public class GetAllCompanyDeleteStaffJobsInput
    {
        [JsonProperty("company_id")]
        public string CompanyId { get; set; }

        [JsonConstructor]
        public GetAllCompanyDeleteStaffJobsInput(string companyId)
        {
            CompanyId = companyId;
        }
    }

    [HttpPost("GetAllCompanyDeleteStaffJobs")]
    public async Task<ActionResult<List<StaffDeletionJobEntry>>> GetAllCompanyDeleteStaffJobs(
        [FromBody] GetAllCompanyDeleteStaffJobsInput input)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        var jobsKey = new StaffDeletionJobsByCompanyKeyPattern(input.CompanyId);
        var jobsJson = await _cacheManagerService.GetCacheAsync(jobsKey);
        var allJobs = string.IsNullOrEmpty(jobsJson)
            ? new CompanyStaffDeletionJobs()
            : (Newtonsoft.Json.JsonConvert.DeserializeObject<CompanyStaffDeletionJobs>(jobsJson) ?? new CompanyStaffDeletionJobs());

        return Ok(allJobs.Jobs);
    }

    public class RemoveCompanyDeleteStaffJobsInput
    {
        [JsonProperty("company_id")]
        public string CompanyId { get; set; }

        [JsonProperty("delete_all")]
        public bool DeleteAll { get; set; }

        [JsonProperty("job_ids")]
        public List<string> JobIds { get; set; }

        [JsonConstructor]
        public RemoveCompanyDeleteStaffJobsInput(string companyId, bool deleteAll = false, List<string>? jobIds = null)
        {
            CompanyId = companyId;
            DeleteAll = deleteAll;
            JobIds = jobIds ?? new List<string>();
        }
    }

    public class RemoveCompanyDeleteStaffJobsOutput
    {
        [JsonProperty("success")]
        public bool Success { get; set; }

        [JsonProperty("message")]
        public string Message { get; set; }

        [JsonProperty("removed_count")]
        public int RemovedCount { get; set; }

        [JsonConstructor]
        public RemoveCompanyDeleteStaffJobsOutput(bool success, string message, int removedCount)
        {
            Success = success;
            Message = message;
            RemovedCount = removedCount;
        }
    }

    [HttpPost("RemoveCompanyDeleteStaffJobs")]
    public async Task<ActionResult<RemoveCompanyDeleteStaffJobsOutput>> RemoveCompanyDeleteStaffJobs(
        [FromBody] RemoveCompanyDeleteStaffJobsInput input)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        if (input is null)
        {
            return BadRequest("Not a valid input");
        }

        if (string.IsNullOrWhiteSpace(input.CompanyId))
        {
            return BadRequest("CompanyId is required");
        }

        if (input.DeleteAll && input.JobIds.Count > 0)
        {
            return BadRequest("DeleteAll cannot be used with JobIds");
        }

        try
        {
            var jobsKey = new StaffDeletionJobsByCompanyKeyPattern(input.CompanyId);
            var jobsJson = await _cacheManagerService.GetCacheAsync(jobsKey);
            var allJobs = string.IsNullOrEmpty(jobsJson)
                ? new CompanyStaffDeletionJobs()
                : (JsonConvert.DeserializeObject<CompanyStaffDeletionJobs>(jobsJson) ?? new CompanyStaffDeletionJobs());

            var removedCount = 0;

            if (input.DeleteAll)
            {
                // Remove all jobs for the company
                removedCount = allJobs.Jobs.Count;

                // Remove individual job status caches for all jobs
                foreach (var job in allJobs.Jobs)
                {
                    var jobStatusKey = **********************KeyPattern.Create(input.CompanyId, job.StaffId);
                    await _cacheManagerService.DeleteCacheAsync(jobStatusKey);
                }

                allJobs.Jobs.Clear();
            }
            else
            {
                // Remove specific jobs by job IDs
                var jobsToRemove = allJobs.Jobs.Where(j => input.JobIds.Contains(j.JobId)).ToList();
                removedCount = jobsToRemove.Count;

                if (removedCount == 0)
                {
                    _logger.LogWarning("No jobs found to remove for company {CompanyId}", input.CompanyId);
                    return Ok(new RemoveCompanyDeleteStaffJobsOutput(false, "No jobs found to remove", 0));
                }

                foreach (var job in jobsToRemove)
                {
                    // Remove from the jobs list
                    allJobs.Jobs.Remove(job);

                    // Remove individual job status cache
                    var jobStatusKey = **********************KeyPattern.Create(input.CompanyId, job.StaffId);
                    await _cacheManagerService.DeleteCacheAsync(jobStatusKey);
                }
            }

            // Save the updated jobs list back to cache
            if (allJobs.Jobs.Count > 0)
            {
                await _cacheManagerService.SaveCacheAsync(jobsKey, allJobs, TimeSpan.FromDays(14));
            }
            else
            {
                // If no jobs left, remove the entire cache entry
                await _cacheManagerService.DeleteCacheAsync(jobsKey);
            }

            var message = input.DeleteAll
                ? $"Successfully removed all {removedCount} staff deletion jobs for company {input.CompanyId}"
                : $"Successfully removed {removedCount} staff deletion jobs for company {input.CompanyId}";

            return Ok(new RemoveCompanyDeleteStaffJobsOutput(true, message, removedCount));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing staff deletion jobs for company {CompanyId}: {Message}",
                input.CompanyId, ex.Message);

            return Ok(new RemoveCompanyDeleteStaffJobsOutput(false,
                $"Error removing staff deletion jobs: {ex.Message}", 0));
        }
    }

    [HttpGet("DeleteCompanyStaff/Status/{jobId}")]
    public async Task<ActionResult<JobDetailsDto>> GetDeleteCompanyStaffStatusByJobId(string jobId)
    {
        var monitoringApi = JobStorage.Current.GetMonitoringApi();
        var jobDetails = monitoringApi.JobDetails(jobId);

        if (jobDetails == null)
        {
            return NotFound();
        }

        return Ok(jobDetails);
    }

    public class DeleteCompanyStaffStatusOutput
    {
        public string CompanyId { get; set; }

        public long StaffId { get; set; }

        public string Status { get; set; }

        public string Message { get; set; }

        public DateTime LastUpdated { get; set; }
    }

    [HttpGet("DeleteCompanyStaff/Status/{companyId}/{staffId:long}")]
    public async Task<ActionResult<DeleteCompanyStaffStatusOutput>> GetDeleteCompanyStaffStatus(string companyId, long staffId)
    {
        var keyPattern = **********************KeyPattern.Create(companyId, staffId);
        var statusData = await _cacheManagerService.GetCacheAsync(keyPattern);

        var status = (statusData is null) ? null : JsonConvert.DeserializeObject<**********************>(statusData);

        if (status == null)
        {
            return NotFound(new DeleteCompanyStaffStatusOutput
            {
                CompanyId = companyId,
                StaffId = staffId,
                Status = "NotFound",
                Message = "Job status not found"
            });
        }

        return Ok(new DeleteCompanyStaffStatusOutput
        {
            CompanyId = companyId,
            StaffId = staffId,
            Status = status.CurrentStatus.ToString(),
            Message = status.Message,
            LastUpdated = status.LastUpdated
        });
    }

    private string GetJobStatusFromHangfire(string hangfireState)
    {
        return hangfireState switch
        {
            "Processing" => "InProgress",
            "Succeeded" => "Completed",
            "Failed" => "Failed",
            "Deleted" => "Cancelled",
            _ => hangfireState
        };
    }

    public class IsRbacEnabledFromServerEnvironmentOutput
    {
        [JsonProperty("is_enabled")]
        public bool IsEnabled { get; set; }

        [JsonConstructor]
        public IsRbacEnabledFromServerEnvironmentOutput(bool isEnabled)
        {
            IsEnabled = isEnabled;
        }
    }

    [HttpPost("IsRbacEnabledFromServerEnvironment")]
    public async Task<ActionResult<bool>> IsRbacEnabledFromServerEnvironment()
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        var featureFlagsConfig = new FeatureFlagsOptions();
        _configuration.GetSection("FeatureFlags").Bind(featureFlagsConfig);

        return Ok(featureFlagsConfig.Rbac.IsEnabled);
    }

    public class StaffRoleUpdateOutput
    {
         [JsonProperty("success")]
         public bool Success { get; set; }

         [JsonProperty("message")]
         public string Message { get; set; }

         [JsonProperty("updated_role")]
         public string? UpdatedRole { get; set; }

         [JsonConstructor]
         public StaffRoleUpdateOutput(bool success, string message, string? updatedRole=null)
         {
             Success = success;
             Message = message;
             UpdatedRole = updatedRole;
         }
    }

    [HttpPost("UpdateStaffRole")]
    public async Task<ActionResult<StaffRoleUpdateOutput>> UpdateStaffRole([FromBody] StaffRoleUpdateRequest input)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        var result = await _companyStaffControllerService.UpdateStaffRoleAsync(
            input.CompanyId,
            input.StaffId.ToString(),
            input.NewRole);

        if (result.IsFailure)
        {
            return Ok(new StaffRoleUpdateOutput(false, result.Error));
        }

        return Ok(new StaffRoleUpdateOutput(
            true,
            "Role updated successfully",
            RoleTypeConverter.ConvertToString(result.Value)));
    }

    public class ImportUserFromCsvInput
    {
        [Required]
        [JsonProperty("company_id")]
        public string CompanyId { get; set; }

        [Required]
        [JsonProperty("import_users")]
        public List<ImportUserInputObject> ImportUsers { get; set; }

        [JsonConstructor]
        public ImportUserFromCsvInput(
            string companyId,
            List<ImportUserInputObject> importUsers)
        {
            CompanyId = companyId;
            ImportUsers = importUsers;
        }
    }

    public class ImportUserFromCsvResponse
    {
        [JsonProperty("success")]
        public bool Success { get; set; }

        [JsonProperty("message")]
        public string Message { get; set; }

        [JsonProperty("imported_users")]
        public List<InvitedUserByEmailObject> ImportedUsers { get; set; }

        public ImportUserFromCsvResponse(
            bool success,
            string message,
            List<InvitedUserByEmailObject> importedUsers)
        {
            Success = success;
            Message = message;
            ImportedUsers = importedUsers;
        }
    }

    [HttpPost("ImportUserFromCsv")]
    [ProducesResponseType(typeof(ImportUserFromCsvResponse), StatusCodes.Status200OK)]
    public async Task<ActionResult<ImportUserFromCsvResponse>> ImportUserFromCSV(
        [FromBody] ImportUserFromCsvInput input)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        try
        {
            var result = await _auth0CompanyService.ImportUsersFromCSVAsync(
                input.CompanyId,
                input.ImportUsers);

            BackgroundJob.Enqueue<IInternalHubSpotService>(
                x => x.SyncCompanyStaffs(input.CompanyId));

            return Ok(
                new ImportUserFromCsvResponse(
                    true,
                    "Users imported successfully",
                    result));
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "[{MethodName}] Error occur when import users from CSV: {ExceptionMessage}",
                nameof(ImportUserFromCSV),
                e.Message);

            return Ok(new ImportUserFromCsvResponse(
                false,
                e.Message,
                []));
        }
    }

    #region Get Company Team Ids by team names

    public class GetCompanyTeamIdsByNamesInput
    {
        [Required]
        [JsonProperty("company_id")]
        public string CompanyId { get; set; }

        [Required]
        [JsonProperty("team_names")]
        public List<string> TeamNames { get; set; }

        [JsonConstructor]
        public GetCompanyTeamIdsByNamesInput(string companyId, List<string> teamNames)
        {
            CompanyId = companyId;
            TeamNames = teamNames;
        }
    }

    public class GetCompanyTeamIdsByNamesOutput
    {
        [JsonProperty("team_ids")]
        public List<string> TeamIds { get; set; }

        [JsonConstructor]
        public GetCompanyTeamIdsByNamesOutput(List<string> teamIds)
        {
            TeamIds = teamIds;
        }
    }

    [HttpPost("GetCompanyTeamIdsByNames")]
    [ProducesResponseType(typeof(GetCompanyTeamIdsByNamesOutput), StatusCodes.Status200OK)]
    public async Task<ActionResult<GetCompanyTeamIdsByNamesOutput>> GetCompanyTeamIdsByNames(
        [FromBody]
        GetCompanyTeamIdsByNamesInput input)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        var teamIds = await _appDbContext.CompanyStaffTeams.Where(
                x => x.CompanyId == input.CompanyId && input.TeamNames.Contains(x.TeamName))
            .Select(x => x.Id.ToString())
            .ToListAsync();

        return Ok(new GetCompanyTeamIdsByNamesOutput(teamIds));
    }

    #endregion

    public class GetCompanyUsageInput
    {
        [JsonProperty("company_id")]
        public string CompanyId { get; set; }

        [JsonConstructor]
        public GetCompanyUsageInput(string companyId)
        {
            CompanyId = companyId;
        }
    }

    public class GetCompanyUsageOutput
    {
        [JsonProperty("company_id")]
        public string CompanyId { get; set; }

        [JsonProperty("usage")]
        public CompanyUsageResponse Usage { get; set; }

        public GetCompanyUsageOutput(string companyId, CompanyDomain.ViewModels.CompanyUsage usage)
        {
            CompanyId = companyId;
            Usage = new CompanyUsageResponse(companyId, usage);
        }
    }

    [HttpPost("GetCompanyUsage")]
    [ProducesResponseType(typeof(GetCompanyUsageOutput), StatusCodes.Status200OK)]
    public async Task<ActionResult<GetCompanyUsageOutput>> GetCompanyUsage(
        [FromBody] GetCompanyUsageInput input)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        _logger.LogInformation("GetCompanyUsage for companyId: {CompanyId}", input.CompanyId);

        var usage = await _companyUsageService.GetCompanyUsage(input.CompanyId);
        return Ok(new GetCompanyUsageOutput(input.CompanyId, usage));
    }

    public class GetUserLossInfoInput
{
    [JsonProperty("userId")]
    public string UserId { get; set; }

    [JsonProperty("email")]
    public string Email { get; set; }

    [JsonConstructor]
    public GetUserLossInfoInput (string userId,  string email)
    {
        UserId = userId;
        Email = email;
    }
}

public class GetUserLossInfoOutput
{
    [JsonProperty("sleekflow_user_id")]
    public string SleekflowUserId { get; set; }

    [JsonProperty("sleekflow_staff_id")]
    public string SleekflowStaffId { get; set; }

    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("role_ids")]
    public List<string> RoleIds { get; set; }

    public GetUserLossInfoOutput(
        string sleekflowUserId,
        string sleekflowStaffId,
        string sleekflowCompanyId,
        List<string> roleIds)
    {
        SleekflowUserId = sleekflowUserId;
        SleekflowStaffId = sleekflowStaffId;
        SleekflowCompanyId = sleekflowCompanyId;
        RoleIds = roleIds;
    }
}

[HttpPost("GetUserLossInfo")]
[ProducesResponseType(typeof(GetUserLossInfoOutput), StatusCodes.Status200OK)]
[ProducesResponseType(StatusCodes.Status404NotFound)]
[ProducesResponseType(StatusCodes.Status500InternalServerError)]
public async Task<ActionResult<GetUserLossInfoOutput>> GetUserLossInfo(
    [FromBody]
    GetUserLossInfoInput input)
{
    try
    {
        if (string.IsNullOrEmpty(input.UserId) && string.IsNullOrEmpty(input.Email))
        {
            return BadRequest("Either userId or email must be provided");
        }

        // Query user information using UserManager
        ApplicationUser user;
        if (!string.IsNullOrEmpty(input.Email) && !string.IsNullOrEmpty(input.UserId))
        {
            user = await _sleekflowUserManager.FindByEmailAsync(input.Email);
        }
        else
        {
            return BadRequest("Either userId or email must be provided");
        }

        if (user == null)
        {
            return NotFound("User not found");
        }

        // Get staff information
        var staff = await _appDbContext.UserRoleStaffs
            .Include(s => s.Company)
            .FirstOrDefaultAsync(s => s.IdentityId == user.Id);

        if (staff == null)
        {
            return NotFound("Staff not found");
        }

        // add logger
        _logger.LogInformation("Found staff: {StaffId} for company: {CompanyId}", staff.Id, staff.CompanyId);

        // Get user roles
        var rbacUserRoles = await _rbacService.GetRbacUserRoles(staff.CompanyId, staff.Id.ToString());

        // add check null
        if (rbacUserRoles == null || rbacUserRoles.Roles == null)
        {
            _logger.LogWarning("No roles found for staff {StaffId}", staff.Id);

            var result = new GetUserLossInfoOutput(user.Id, staff.Id.ToString(),
                staff.CompanyId, new List<string>());

            return Ok(result);
        }

        var roleIds = rbacUserRoles.Roles.Select(r => r.RoleId).ToList();
        _logger.LogInformation("Found roles for staff {StaffId}: {RoleIds}", staff.Id, string.Join(", ", roleIds));

        // Create response
        var response = new GetUserLossInfoOutput(
            sleekflowUserId: user.Id,
            sleekflowStaffId: staff.Id.ToString(),
            sleekflowCompanyId: staff.CompanyId,
            roleIds: roleIds
        );

        return Ok(response);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Error occurred while getting user loss info for userId: {UserId}, email: {Email}",
            input.UserId, input.Email);
        return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while processing your request");
    }
}

    public class GetCompanyOwnerInput
    {
        [JsonProperty("company_id")]
        public string CompanyId { get; set; }

        [JsonConstructor]
        public GetCompanyOwnerInput(string companyId)
        {
            CompanyId = companyId;
        }
    }

    public class GetCompanyOwnerOutput
    {
        [JsonProperty("sleekflow_staff_id")]
        public long SleekflowStaffId { get; set; }

        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("sleekflow_user_id")]
        public string SleekflowUserId { get; set; }

        public GetCompanyOwnerOutput(Staff staff)
        {
            SleekflowStaffId = staff.Id;
            SleekflowCompanyId = staff.CompanyId;
            SleekflowUserId = staff.IdentityId;
        }
    }

    [HttpPost("GetCompanyOwner")]
    [ProducesResponseType(typeof(GetCompanyOwnerOutput), StatusCodes.Status200OK)]
    public async Task<ActionResult<GetCompanyOwnerOutput>> GetCompanyOwner(
        [FromBody] GetCompanyOwnerInput input)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        _logger.LogInformation("GetCompanyOwner for companyId: {CompanyId}", input.CompanyId);

        var owner = await _rbacService.GetCompanyOwnerAsync(input.CompanyId);
        return Ok(new GetCompanyOwnerOutput(owner));
    }

    public class OnRbacFeatureEnabledRequest
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("is_first_time_enabled")]
        public bool IsFirstTimeEnabled { get; set; }
    }

    [HttpPost("OnRbacFeatureEnabled")]
    public async Task<IActionResult> OnRbacFeatureEnabled([FromBody] OnRbacFeatureEnabledRequest request)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        try
        {
            _logger.LogInformation(
                "Received notification that RBAC feature was enabled for company {CompanyId}, IsFirstTime: {IsFirstTime}",
                request.SleekflowCompanyId,
                request.IsFirstTimeEnabled);

            await _rbacService.OnRbacFeatureEnabledAsync(
                request.SleekflowCompanyId,
                request.IsFirstTimeEnabled);

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error when handling RBAC feature enablement notification for company {CompanyId}: {Message}",
                request.SleekflowCompanyId,
                ex.Message);

            return StatusCode(500, new { error = "An error occurred while processing the request" });
        }
    }

    public class OnRbacFeatureDisabledRequest
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }
    }

    [HttpPost("OnRbacFeatureDisabled")]
    public async Task<IActionResult> OnRbacFeatureDisabled([FromBody] OnRbacFeatureDisabledRequest request)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        try
        {
            _logger.LogInformation(
                "Received notification that RBAC feature was disabled for company {CompanyId}",
                request.SleekflowCompanyId);

            await _rbacService.OnRbacFeatureDisabledAsync(
                request.SleekflowCompanyId);

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error when handling RBAC feature disablement notification for company {CompanyId}: {Message}",
                request.SleekflowCompanyId,
                ex.Message);

            return StatusCode(500, new { error = "An error occurred while processing the request" });
        }
    }

    public class OnUserRoleAssignedRequest
    {
        [JsonProperty("sleekflow_company_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("user_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string UserId { get; set; }

        [JsonProperty("staff_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string StaffId { get; set; }

        [JsonProperty("role_id_to_name_map")]
        [System.ComponentModel.DataAnnotations.Required]
        public Dictionary<string, string> RoleIdToNameMap { get; set; }

        [JsonProperty("assigned_at")]
        [System.ComponentModel.DataAnnotations.Required]
        public DateTimeOffset AssignedAt { get; set; }

        [JsonConstructor]
        public OnUserRoleAssignedRequest(
            string sleekflowCompanyId,
            string userId,
            string staffId,
            Dictionary<string, string> roleIdToNameMap,
            DateTimeOffset assignedAt)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            UserId = userId;
            StaffId = staffId;
            RoleIdToNameMap = roleIdToNameMap;
            AssignedAt = assignedAt;
        }
    }

    [HttpPost("OnUserRoleAssigned")]
    public async Task<IActionResult> OnUserRoleAssigned([FromBody] OnUserRoleAssignedRequest request)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        try
        {
            _logger.LogInformation(
                "Received user role assignment notification for company {CompanyId}, user {UserId}, staff {StaffId} with {RoleCount} roles",
                request.SleekflowCompanyId,
                request.UserId,
                request.StaffId,
                request.RoleIdToNameMap.Count);

            await _rbacService.OnUserRoleAssignedAsync(
                request.SleekflowCompanyId,
                request.UserId,
                request.StaffId,
                request.RoleIdToNameMap,
                request.AssignedAt);

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing user role assignment for company {CompanyId}, user {UserId}: {Message}",
                request.SleekflowCompanyId,
                request.UserId,
                ex.Message);

            return StatusCode(StatusCodes.Status500InternalServerError, ex.Message);
        }
    }

    public class OnCompanyPoliciesSavedRequest
    {
        [JsonProperty("sleekflow_company_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("role_permissions")]
        [System.ComponentModel.DataAnnotations.Required]
        public Dictionary<string, Dictionary<string, List<string>>> RolePermissions { get; set; }

        [JsonProperty("saved_at")]
        [System.ComponentModel.DataAnnotations.Required]
        public DateTimeOffset SavedAt { get; set; }

        [JsonConstructor]
        public OnCompanyPoliciesSavedRequest(
            string sleekflowCompanyId,
            Dictionary<string, Dictionary<string, List<string>>> rolePermissions,
            DateTimeOffset savedAt)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            RolePermissions = rolePermissions;
            SavedAt = savedAt;
        }
    }

    [HttpPost("OnCompanyPoliciesSaved")]
    public async Task<IActionResult> OnCompanyPoliciesSaved([FromBody] OnCompanyPoliciesSavedRequest request)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        try
        {
            _logger.LogInformation(
                "Received company policies saved notification for company {CompanyId} with {RoleCount} roles",
                request.SleekflowCompanyId,
                request.RolePermissions.Count);

            await _rbacService.OnCompanyPoliciesSavedAsync(
                request.SleekflowCompanyId,
                request.RolePermissions,
                request.SavedAt);

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing company policies saved for company {CompanyId}: {Message}",
                request.SleekflowCompanyId,
                ex.Message);

            return StatusCode(StatusCodes.Status500InternalServerError, ex.Message);
        }
    }
}