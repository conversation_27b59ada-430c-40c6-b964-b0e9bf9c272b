using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.ConversationServices;
using Travis_backend.CoreDomain.Models;
using Travis_backend.Data;
using Travis_backend.Database;
using Travis_backend.Enums;
using Z.EntityFramework.Extensions;
using static Travis_backend.CoreDomain.Models.**********************;

namespace Travis_backend.CoreDomain.Services
{
    /// <summary>
    /// Service for handling staff removal operations in batches
    /// </summary>
    public interface IStaffRemovalService
    {
        /// <summary>
        /// Removes staff data in batches to handle large datasets efficiently
        /// </summary>
        /// <param name="companyId">The company ID the staff belongs to</param>
        /// <param name="staffId">The ID of the staff to remove</param>
        /// <param name="reportProgress">Optional callback to report progress messages</param>
        /// <returns>Result of the staff removal operation</returns>
        Task<StaffRemovalResult> RemoveStaffDataInBatchesAsync(string companyId, long staffId, Func<string, Task> reportProgress = null);
    }

    /// <summary>
    /// Service implementation for removing staff data in batches
    /// </summary>
    public sealed class StaffRemovalService : IStaffRemovalService
    {
        private const int BatchSize = 10;
        private readonly ApplicationDbContext _appDbContext;
        private readonly ILogger<StaffRemovalService> _logger;
        private readonly ICoreService _coreService;

        /// <summary>
        /// Initializes a new instance of the StaffRemovalService
        /// </summary>
        public StaffRemovalService(ApplicationDbContext appDbContext, ILogger<StaffRemovalService> logger, ICoreService coreService)
        {
            _appDbContext = appDbContext ?? throw new ArgumentNullException(nameof(appDbContext));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _coreService = coreService;
        }

        /// <inheritdoc/>
        public async Task<StaffRemovalResult> RemoveStaffDataInBatchesAsync(string companyId, long staffId, Func<string, Task> reportProgress = null)
        {
            ArgumentNullException.ThrowIfNull(companyId);

            var result = new StaffRemovalResult();

            try
            {
                _appDbContext.Database.SetCommandTimeout(3600); // 1 hour timeout for batch operations

                var targetedStaff = await _appDbContext.UserRoleStaffs
                    .Include(s => s.Identity)
                    .FirstOrDefaultAsync(s => s.CompanyId == companyId && s.Id == staffId);

                if (targetedStaff == null)
                {
                    var message = $"Staff {staffId} not found in company {companyId}";
                    result.ProcessedOperations.Add(message);
                    result.Success = false;
                    result.Message = message;
                    result.Status = **********************.JobStatus.StaffNotFound;
                    return result;
                }

                // Process data in batches
                await ProcessRelatedDataInBatches(targetedStaff, result, reportProgress).ConfigureAwait(false);

                result.Success = true;
                result.Status = **********************.JobStatus.Completed;
                result.Message = $"Successfully removed staff {staffId} from company {companyId} in batches";
                result.ProcessedOperations.Add(result.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Critical error during batch staff removal for staff {StaffId} in company {CompanyId}",
                    staffId,
                    companyId);
                result.Success = false;
                result.Message = $"Critical error during batch staff removal: {ex.Message}";
                result.ProcessedOperations.Add(result.Message);
                throw; // Rethrow to maintain the stack trace
            }

            return result;
        }

        private async Task ProcessRelatedDataInBatches(Staff targetedStaff, StaffRemovalResult result, Func<string, Task> reportProgress = null)
        {
            ArgumentNullException.ThrowIfNull(targetedStaff);
            ArgumentNullException.ThrowIfNull(result);

            try
            {
                // Phase 1: Delete leaf nodes (no dependencies)
                if (reportProgress != null) await reportProgress("Phase 1: Removing leaf nodes");
                _logger.LogInformation("Phase 1: Removing leaf nodes for staff {StaffId}", targetedStaff.Id);

                // Message attachments and bookmarks
                await ProcessBatchUpdate(
                        _appDbContext.ConversationMessageUploadedFiles.Where(
                            x => x.SenderId == targetedStaff.IdentityId),
                        x => x.SenderId,
                        (string?) null,
                        "UploadedFiles",
                        result)
                    .ConfigureAwait(false);

                await ProcessBatchDelete(
                        _appDbContext.ConversationBookmarks.Where(x => x.StaffId == targetedStaff.Id),
                        "ConversationBookmarks",
                        result)
                    .ConfigureAwait(false);

                // Phase 2: Delete conversation-related data
                if (reportProgress != null) await reportProgress("Phase 2: Removing conversation-related data");
                _logger.LogInformation(
                    "Phase 2: Removing conversation-related data for staff {StaffId}",
                    targetedStaff.Id);

                await ProcessBatchDelete(
                        _appDbContext.ConversationAdditionalAssignees.Where(x => x.AssigneeId == targetedStaff.Id),
                        "ConversationAssignees",
                        result)
                    .ConfigureAwait(false);

                // Clean up conversation messages
                await _appDbContext.ConversationMessages
                    .Where(x => x.SenderId == targetedStaff.IdentityId || x.ReceiverId == targetedStaff.IdentityId)
                    .ExecuteUpdateAsync(x => x
                        .SetProperty(f => f.SenderId, (string)null)
                        .SetProperty(f => f.ReceiverId, (string)null));

                // Clean up conversations
                while (await _appDbContext.Conversations
                           .AnyAsync(x => x.AssigneeId == targetedStaff.Id))
                {
                    await _appDbContext.Conversations
                        .Where(x => x.AssigneeId == targetedStaff.Id)
                        .Take(500)
                        .ExecuteUpdateAsync(
                            conversation =>
                                conversation.SetProperty(c => c.AssigneeId, (long?) null));
                }

                // Phase 3: Delete automation records (child) before automation actions (parent)
                if (reportProgress != null) await reportProgress("Phase 3: Removing automation data");
                _logger.LogInformation("Phase 3: Removing automation data for staff {StaffId}", targetedStaff.Id);

                await ProcessBatchUpdate(
                        _appDbContext.CompanyAutomationActionRecords.Where(x => x.AssignedStaffId == targetedStaff.Id),
                        x => x.AssignedStaffId,
                        (long?) null,
                        "AutomationActionRecords",
                        result)
                    .ConfigureAwait(false);

                await ProcessBatchUpdate(
                        _appDbContext.CompanyAutomationActions.Where(x => x.AssignedStaff.Id == targetedStaff.Id),
                        x => x.AssignedStaffId,
                        (long?) null,
                        "AutomationActions",
                        result)
                    .ConfigureAwait(false);

                await ProcessBatchUpdate(
                        _appDbContext.CampaignAutomationActions.Where(x => x.AssignedStaff.Id == targetedStaff.Id),
                        x => x.AssignedStaffId,
                        (long?) null,
                        "CampaignAutomationActions",
                        result)
                    .ConfigureAwait(false);

                // Phase 4: Delete team memberships and assignments
                if (reportProgress != null) await reportProgress("Phase 4: Removing team memberships and assignments");
                _logger.LogInformation(
                    "Phase 4: Removing team memberships and assignments for staff {StaffId}",
                    targetedStaff.Id);

                await ProcessBatchDelete(
                        _appDbContext.CompanyTeamMembers.Where(x => x.StaffId == targetedStaff.Id),
                        "TeamMembers",
                        result)
                    .ConfigureAwait(false);

                await ProcessBatchUpdate(
                        _appDbContext.CompanyAssignmentRules.Where(x => x.AssignedStaff.Id == targetedStaff.Id),
                        x => x.AssignedStaffId,
                        (long?) null,
                        "AssignmentRules",
                        result)
                    .ConfigureAwait(false);

                // Phase 5: Delete content and templates
                if (reportProgress != null) await reportProgress("Phase 5: Removing content and templates");
                _logger.LogInformation("Phase 5: Removing content and templates for staff {StaffId}", targetedStaff.Id);

                await ProcessBatchUpdate(
                        _appDbContext.CompanyMessageTemplates.Where(x => x.LastSentById == targetedStaff.Id),
                        x => x.LastSentById,
                        (long?) null,
                        "MessageTemplatesLastSent",
                        result)
                    .ConfigureAwait(false);

                await ProcessBatchUpdate(
                        _appDbContext.CompanyMessageTemplates.Where(x => x.SavedById == targetedStaff.Id),
                        x => x.SavedById,
                        (long?) null,
                        "MessageTemplatesSaved",
                        result)
                    .ConfigureAwait(false);

                await ProcessBatchUpdate(
                        _appDbContext.CompanyQuickReplies.Where(x => x.SavedById == targetedStaff.Id),
                        x => x.SavedById,
                        (long?) null,
                        "QuickReplies",
                        result)
                    .ConfigureAwait(false);

                // Clean up blast message templates
                await _appDbContext.BlastMessageTemplates
                    .Where(x =>
                        x.CompanyId == targetedStaff.CompanyId &&
                        x.SavedById == targetedStaff.Id)
                    .ExecuteUpdateAsync(blastMessageTemplate =>
                        blastMessageTemplate.SetProperty(t => t.SavedById, (long?) null));

                // Phase 6: Delete analytics and reporting data
                if (reportProgress != null) await reportProgress("Phase 6: Removing analytics and reporting data");
                _logger.LogInformation(
                    "Phase 6: Removing analytics and reporting data for staff {StaffId}",
                    targetedStaff.Id);

                await ProcessBatchUpdate(
                        _appDbContext.CompanyAnalyticSegment.Where(x => x.SavedById == targetedStaff.Id),
                        x => x.SavedById,
                        (long?) null,
                        "AnalyticSegments",
                        result)
                    .ConfigureAwait(false);

                await ProcessBatchUpdate(
                        _appDbContext.BroadcastCompaignHistories.Where(x => x.BroadcastSentById == targetedStaff.Id),
                        x => x.BroadcastSentById,
                        (long?) null,
                        "BroadcastCampaigns",
                        result)
                    .ConfigureAwait(false);

                await ProcessBatchUpdate(
                        _appDbContext.CompanyBillRecords.Where(x => x.PurchaseStaffId == targetedStaff.Id),
                        x => x.PurchaseStaffId,
                        (long?) null,
                        "BillRecords",
                        result)
                    .ConfigureAwait(false);

                // Phase 7: Finally, handle invitation records
                if (reportProgress != null) await reportProgress("Phase 7: Removing invitation records");
                _logger.LogInformation("Phase 7: Removing invitation records for staff {StaffId}", targetedStaff.Id);

                // Delete invitation records (child) before invitations (parent)
                await ProcessBatchDelete(
                        _appDbContext.CompanyShareableInvitationRecords.Where(
                            x => x.InvitedStaffId == targetedStaff.Id),
                        "ShareableInvitationRecords",
                        result)
                    .ConfigureAwait(false);

                // Update shareable invitations and disable them
                var invitations = await _appDbContext.CompanyShareableInvitations
                    .Where(x => x.GeneratedById == targetedStaff.Id)
                    .ToListAsync();

                foreach (var invitation in invitations)
                {
                    invitation.GeneratedById = null;
                    invitation.Status = ShareableLinkStatus.Disabled;
                }
                await _appDbContext.SaveChangesAsync();
                result.ProcessedOperations.Add($"Updated and disabled {invitations.Count} ShareableInvitations");

                // Phase 8: Clean up staff sessions and profile
                if (reportProgress != null) await reportProgress("Phase 8: Cleaning up staff sessions and profile");
                _logger.LogInformation(
                    "Phase 8: Cleaning up staff sessions and profile for staff {StaffId}",
                    targetedStaff.Id);

                // Clean up import contact histories
                await _appDbContext.CompanyImportContactHistories
                    .Where(x => x.ImportedFromId == targetedStaff.Id)
                    .ExecuteDeleteAsync();

                var companyContactOwnerCustomFieldId = await _appDbContext.CompanyCustomUserProfileFields
                    .AsNoTracking()
                    .Where(
                        field =>
                            field.CompanyId == targetedStaff.CompanyId
                            && field.FieldName.ToLower() == "contactowner")
                    .Select(field => field.Id)
                    .FirstOrDefaultAsync();

                // Clean up user profile custom fields
                await _appDbContext.UserProfileCustomFields
                    .Where(
                        x => x.CompanyDefinedFieldId == companyContactOwnerCustomFieldId &&
                             x.Value == targetedStaff.IdentityId)
                    .ExecuteUpdateAsync(x => x
                        .SetProperty(f => f.Value, string.Empty));

                // Clean up user profiles
                await _appDbContext.UserProfiles
                    .Where(
                        profile =>
                            profile.CompanyId == targetedStaff.CompanyId
                            && profile.ContactOwnerId == targetedStaff.IdentityId)
                    .ExecuteUpdateAsync(
                        field =>
                            field.SetProperty(f => f.ContactOwnerId, (string)null));

                // Clean up notification records
                await _appDbContext.CompanyNotificationRecords
                    .Where(x => x.StaffId == targetedStaff.Id)
                    .ExecuteDeleteAsync();

                // Clean up user role guests
                await _appDbContext.UserRoleGuests
                    .Where(
                        x =>
                            x.CompanyId == targetedStaff.CompanyId &&
                            x.IdentityId == targetedStaff.IdentityId)
                    .ExecuteDeleteAsync();

                // Clean up whatsapp applications
                await _appDbContext.CmsWhatsappApplications
                    .Where(
                        x =>
                            x.CreatedByUserId == targetedStaff.IdentityId ||
                            x.UpdatedByUserId == targetedStaff.IdentityId)
                    .ExecuteUpdateAsync(x => x
                        .SetProperty(f => f.CreatedByUserId, (string)null)
                        .SetProperty(f => f.UpdatedByUserId, (string)null));

                // Clean up whatsapp template bookmarks
                await _appDbContext.WhatsApp360DialogTemplateBookmarks
                    .Where(x => x.CreatedByStaffId == targetedStaff.Id)
                    .ExecuteDeleteAsync();

                // Clean up AspNetUser related records
                await _appDbContext.UserClaims
                    .Where(x => x.UserId == targetedStaff.IdentityId)
                    .ExecuteDeleteAsync();

                await _appDbContext.UserLogins
                    .Where(x => x.UserId == targetedStaff.IdentityId)
                    .ExecuteDeleteAsync();

                await _appDbContext.UserRoles
                    .Where(x => x.UserId == targetedStaff.IdentityId)
                    .ExecuteDeleteAsync();

                await _appDbContext.UserTokens
                    .Where(x => x.UserId == targetedStaff.IdentityId)
                    .ExecuteDeleteAsync();

                // Clean up contact owner assign logs
                await _appDbContext.CmsContactOwnerAssignLogs
                    .Where(x =>
                        x.AssignedByUserId == targetedStaff.IdentityId ||
                        x.FromContactOwnerId == targetedStaff.IdentityId ||
                        x.ToContactOwnerId == targetedStaff.IdentityId)
                    .ExecuteDeleteAsync();

                // Clean up hubspot contact owner maps
                await _appDbContext.CmsHubSpotContactOwnerMaps
                    .Where(x => x.ContactOwnerId == targetedStaff.IdentityId)
                    .ExecuteDeleteAsync();

                // Clean up sales payment records
                await _appDbContext.CmsSalesPaymentRecords
                    .Where(x =>
                        x.CreateUserId == targetedStaff.IdentityId ||
                        x.LastModifiedByUserId == targetedStaff.IdentityId)
                    .ExecuteDeleteAsync();

                // Clean up whatsapp applications contact owner
                await _appDbContext.CmsWhatsappApplications
                    .Where(x => x.ContactOwnerId == targetedStaff.IdentityId)
                    .ExecuteUpdateAsync(x => x
                        .SetProperty(f => f.ContactOwnerId, (string)null));

                // Clean up company companies
                await _appDbContext.CompanyCompanies
                    .Where(x =>
                        x.CmsActivationOwnerId == targetedStaff.IdentityId ||
                        x.CmsCompanyOwnerId == targetedStaff.IdentityId)
                    .ExecuteUpdateAsync(x => x
                        .SetProperty(f => f.CmsActivationOwnerId, (string)null)
                        .SetProperty(f => f.CmsCompanyOwnerId, (string)null));

                // Clean up custom subscription plan translation maps
                await _appDbContext.CustomSubscriptionPlanTranslationMaps
                    .Where(x =>
                        x.CreatedByUserId == targetedStaff.IdentityId ||
                        x.UpdatedByUserId == targetedStaff.IdentityId)
                    .ExecuteDeleteAsync();

                // Clean up reseller activity logs
                await _appDbContext.ResellerActivityLogs
                    .Where(x => x.CreatedByUserId == targetedStaff.IdentityId)
                    .ExecuteDeleteAsync();

                // Clean up user role admins and operators
                await _appDbContext.UserRoleAdmins
                    .Where(x => x.IdentityId == targetedStaff.IdentityId)
                    .ExecuteDeleteAsync();

                await _appDbContext.UserRoleOperators
                    .Where(x => x.IdentityId == targetedStaff.IdentityId)
                    .ExecuteDeleteAsync();

                // Clean up user devices
                await ProcessBatchDelete(
                        _appDbContext.UserUserDevices.Where(x => x.UserId == targetedStaff.IdentityId),
                        "UserDevices",
                        result)
                    .ConfigureAwait(false);

                // Clean up company assignment rules saved by
                await _appDbContext.CompanyAssignmentRules
                    .Where(x => x.SavedById == targetedStaff.Id)
                    .ExecuteUpdateAsync(x => x
                        .SetProperty(f => f.SavedById, (long?)null));

                // Finally remove the staff record
                _logger.LogInformation(
                    "[{MethodName}] Removing staff record {StaffId} in company {CompanyId}",
                    nameof(RemoveStaffDataInBatchesAsync),
                    targetedStaff.Id,
                    targetedStaff.CompanyId);

                _appDbContext.UserRoleStaffs.Remove(targetedStaff);
                await _appDbContext.SaveChangesAsync();

                // Delete the identity user
                await _coreService.DeleteIdentityUser(targetedStaff.IdentityId);

                result.ProcessedOperations.Add("Removed staff record and identity user");
                result.Success = true;

                if (reportProgress != null)
                {
                    await reportProgress("Completed all phases of staff data removal");
                }

                _logger.LogInformation(
                    "Completed all phases of staff data removal for staff {StaffId}",
                    targetedStaff.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error during batch processing for staff {StaffId}\n{Message}\n{StackTrace}",
                    targetedStaff.Id, ex.Message, ex.StackTrace);
                result.FailedOperations.Add($"Failed to process staff {targetedStaff.Id}: {ex.Message}");
                throw; // Rethrow to maintain the stack trace
            }
        }

        private async Task ProcessBatchDelete<T>(IQueryable<T> query, string operationName, StaffRemovalResult result)
            where T : class
        {
            ArgumentNullException.ThrowIfNull(query);
            ArgumentNullException.ThrowIfNull(operationName);
            ArgumentNullException.ThrowIfNull(result);

            try
            {
                var totalCount = await query.CountAsync().ConfigureAwait(false);
                var processedCount = 0;

                while (processedCount < totalCount)
                {
                    var batch = await query.Take(BatchSize).ToListAsync().ConfigureAwait(false);
                    if (batch.Count == 0) break;

                    _appDbContext.RemoveRange(batch);
                    await _appDbContext.SaveChangesAsync().ConfigureAwait(false);

                    processedCount += batch.Count;
                    result.ProcessedOperations.Add($"Deleted {processedCount}/{totalCount} {operationName}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing batch delete for {OperationName}", operationName);
                result.FailedOperations.Add($"Failed to process {operationName}: {ex.Message}");
                throw;
            }
        }

        private async Task ProcessBatchUpdate<T, TProperty>(
            IQueryable<T> query,
            Expression<Func<T, TProperty>> propertyExpression,
            TProperty? newValue,
            string operationName,
            StaffRemovalResult result) where T : class
        {
            ArgumentNullException.ThrowIfNull(query);
            ArgumentNullException.ThrowIfNull(propertyExpression);
            ArgumentNullException.ThrowIfNull(operationName);

            try
            {
                var totalCount = await query.CountAsync().ConfigureAwait(false);
                var processedCount = 0;

                while (processedCount < totalCount)
                {
                    var batchQuery = query.Skip(processedCount).Take(BatchSize);
                    var batchCount = await batchQuery.CountAsync().ConfigureAwait(false);

                    if (batchCount == 0) break;

                    // Load entities in small batches and update them using reflection
                    var batch = await batchQuery.ToListAsync().ConfigureAwait(false);
                    if (batch.Count == 0) break;

                    foreach (var entity in batch)
                    {
                        // Extract property name from expression and set value using reflection
                        if (propertyExpression.Body is MemberExpression memberExpression)
                        {
                            var property = typeof(T).GetProperty(memberExpression.Member.Name);
                            property?.SetValue(entity, newValue);
                        }
                    }

                    await _appDbContext.SaveChangesAsync().ConfigureAwait(false);

                    processedCount += batchCount;
                    result.ProcessedOperations.Add(
                        $"Updated {processedCount}/{totalCount} {operationName}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating {OperationName} in batch", operationName);
                result.FailedOperations.Add($"Failed to update {operationName}: {ex.Message}");
                throw;
            }
        }
    }
}