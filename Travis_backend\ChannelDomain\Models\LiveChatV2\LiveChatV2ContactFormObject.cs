using System.Collections.Generic;
using Newtonsoft.Json;

namespace Travis_backend.ChannelDomain.Models.LiveChatV2
{
    /// <summary>
    /// Represents the structure of a contact form payload for LiveChatV2.
    /// </summary>
    public class LiveChatV2ContactFormObject
    {
        [JsonProperty("form_title")]
        public string FormTitle { get; set; }

        [JsonProperty("fields")]
        public List<LiveChatV2ContactFormField> Fields { get; set; }

        [JsonProperty("submit_button")]
        public LiveChatV2ContactFormSubmitButton SubmitButton { get; set; }
    }

    /// <summary>
    /// Represents a single field within the contact form.
    /// </summary>
    public class LiveChatV2ContactFormField
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("label")]
        public string Label { get; set; }

        [JsonProperty("type")]
        public string Type { get; set; }

        [JsonProperty("placeholder")]
        public string Placeholder { get; set; }

        [JsonProperty("required")]
        public bool Required { get; set; }

        [JsonProperty("value")]
        public string Value { get; set; }

        [JsonProperty("default_value")]
        public string DefaultValue { get; set; }

        [JsonProperty("description")]
        public string Description { get; set; }
    }

    /// <summary>
    /// Represents the submit button of the form.
    /// </summary>
    public class LiveChatV2ContactFormSubmitButton
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("title")]
        public string Title { get; set; }

        [JsonProperty("action")]
        public LiveChatV2ContactFormSubmitButtonAction Action { get; set; }
    }

    /// <summary>
    /// Represents the action associated with the submit button.
    /// </summary>
    public class LiveChatV2ContactFormSubmitButtonAction
    {
        [JsonProperty("type")]
        public string Type { get; set; }

        [JsonProperty("payload")]
        public LiveChatV2ContactFormSubmitButtonActionPayload Payload { get; set; }
    }

    /// <summary>
    /// Represents the payload for the form submission action.
    /// </summary>
    public class LiveChatV2ContactFormSubmitButtonActionPayload
    {
        [JsonProperty("form_id")]
        public string FormId { get; set; }
    }
}