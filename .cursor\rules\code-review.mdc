---
description:
globs:
alwaysApply: false

# {
#   "mcpServers": {
#     "context7": {
#       "command": "npx",
#       "args": [
#         "-y",
#         "@upstash/context7-mcp@latest"
#       ]
#     },
#     "github": {
#       "url": "https://api.githubcopilot.com/mcp/",
#       "headers": {
#         "Authorization": "Bearer {YOUR_PAT}"
#       }
#     }
#   }
# }
---

Adopt a structured, top-down review methodology: Begin with an assessment of high-level architecture, design decisions, and overall impact (e.g., scalability, modularity, and alignment with project goals). Then, proceed to mid-level concerns like module interactions, data flow, and error handling. Finally, examine low-level details such as code implementation, syntax, and optimizations. Restrict your analysis strictly to the modified or added code; ignore unchanged sections.

If the PR provides insufficient context (e.g., unclear dependencies, missing documentation, or unfamiliar patterns), proactively gather additional information by browsing the repository, reviewing related files, commit history, or associated issues to ensure a well-informed review.

Leverage available GitHub tools (via MCP integration if applicable) to simulate or directly create a PR review. Structure your output as follows:

1. **High-level PR comment**: Post this as a general comment on the PR. Include a concise summary of the changes, positive aspects, potential improvements, and any critical issues (e.g., regarding architecture, performance, security, maintainability, or functionality). Rate the PR's readiness (e.g., approve, request changes, or comment) and justify it.

2. **Line-specific comments**: For each notable change, add inline comments using GitHub's review tools. For every comment:
   - Reference the exact file path and line range (e.g., src/main.py:42-45).
   - Quote the relevant code snippet for clarity.
   - Provide constructive feedback, categorized appropriately (e.g., [Bug] for potential errors, [Suggestion] for improvements, [Question] for clarifications, [Nit] for minor style issues, [Praise] for strong elements).
   - Explain the rationale, suggest alternatives if applicable, and link to best practices or documentation where relevant.

In your review, comprehensively address:
- **Correctness**: Verify logic, edge cases, and potential bugs.
- **Performance**: Evaluate efficiency, resource usage, and scalability.
- **Security**: Check for vulnerabilities like injection risks or improper access controls.
- **Readability and Style**: Ensure consistency with the project's coding standards (e.g., PEP8 for Python), clear naming, and comments.
- **Testing**: Assess included tests for coverage, including unit, integration, and edge cases; suggest additions if needed.
- **Maintainability**: Consider extensibility, modularity, and adherence to SOLID principles.
- **Regressions**: Identify possible impacts on existing features.

Maintain an objective, professional, and encouraging tone. Prioritize actionable feedback, and if the code is exemplary, highlight what works well to reinforce good practices. If using tools to post comments directly, confirm successful submission in your response.