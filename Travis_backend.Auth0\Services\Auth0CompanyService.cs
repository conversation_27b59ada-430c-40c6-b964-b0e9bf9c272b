using System.Security;
using AutoMapper;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.AccountAuthenticationDomain.Services;
using Travis_backend.AccountAuthenticationDomain.ViewModels;
using Travis_backend.Auth0.Controllers;
using Travis_backend.Auth0.Controllers.Webhook;
using Travis_backend.Auth0.Exceptions;
using Travis_backend.Auth0.Models;
using Travis_backend.Cache;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.CompanyDomain.Utils;
using Travis_backend.CompanyDomain.ViewModels;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.ShareInvitationDomain.Models;
using Travis_backend.SubscriptionPlanDomain.Services;
using Auth0User = Auth0.ManagementApi.Models.User;
using ShareableInvitationViewModel = Travis_backend.Auth0.Models.ShareableInvitationViewModel;

namespace Travis_backend.Auth0.Services;

public interface IAuth0CompanyService
{
    Task<string> GenerateResetPasswordLinkForStaff(string companyId, string staffIdentityId);

    Task<RegisterCompanyResult> RegisterCompanyAsync(
        ApplicationUser adminUser,
        RegisterCompanyInput registerCompanyInput);

    Task<List<InvitedUserByEmailObject>> InviteUsersByEmailAsync(
        ApplicationUser? adminUser,
        string companyId,
        List<long> teamIds,
        List<InviteUserInputObject> inviteUsers);

    Task<(ShareableInvitation, string)> GenerateShareableLinkAsync(
        string adminUserId,
        ShareableInvitationViewModel shareableInvitationViewModel);

    Task<bool> ResendInvitationEmailAsync(string staffUserId, string? tenantHubUserId, Staff adminUser);

    Task<List<InvitedUserByEmailObject>> ImportUsersFromCSVAsync(
        string companyId,
        List<ImportUserInputObject> importUsers);
}

public class Auth0CompanyService
    : IAuth0CompanyService
{
    private readonly SleekflowUserManager _userManager;
    private readonly ICoreService _coreService;
    private readonly ILogger<Auth0CompanyService> _logger;
    private readonly ApplicationDbContext _appDbContext;
    private readonly ICompanyTeamService _companyTeamService;
    private readonly ICompanyInfoCacheService _companyInfoCacheService;
    private readonly IMapper _mapper;
    private readonly IEmailNotificationService _emailNotificationService;
    private readonly IConfiguration _configuration;
    private readonly IDefaultSubscriptionPlanIdGetter _defaultSubscriptionPlanIdGetter;
    private readonly ICompanyUsageService _companyUsageService;

    public Auth0CompanyService(
        SleekflowUserManager userManager,
        ILogger<Auth0CompanyService> logger,
        ICoreService coreService,
        ApplicationDbContext appDbContext,
        ICompanyInfoCacheService companyInfoCacheService,
        IMapper mapper,
        IEmailNotificationService emailNotificationService,
        ICompanyTeamService companyTeamService,
        IConfiguration configuration,
        IDefaultSubscriptionPlanIdGetter defaultSubscriptionPlanIdGetter,
        ICompanyUsageService companyUsageService)
    {
        _userManager = userManager;
        _logger = logger;
        _coreService = coreService;
        _appDbContext = appDbContext;
        _companyInfoCacheService = companyInfoCacheService;
        _mapper = mapper;
        _emailNotificationService = emailNotificationService;
        _companyTeamService = companyTeamService;
        _configuration = configuration;
        _defaultSubscriptionPlanIdGetter = defaultSubscriptionPlanIdGetter;
        _companyUsageService = companyUsageService;
    }

    public async Task<string> GenerateResetPasswordLinkForStaff(string companyId, string staffIdentityId)
    {
        var userIdentity = await _userManager.FindByIdAsync(staffIdentityId);
        if (userIdentity is null)
        {
            throw new Exception($"The user staff of identity id {staffIdentityId} not found");
        }

        var userStaff = await _coreService.GetCompanyStaff(userIdentity);
        if (userStaff is null || userStaff.CompanyId != companyId)
        {
            throw new Exception($"User is not in your company");
        }

        var url = await _userManager.GeneratePasswordResetUrlAsync(userIdentity);
        return url;
    }

    public async Task<RegisterCompanyResult> RegisterCompanyAsync(
        ApplicationUser adminUser,
        RegisterCompanyInput registerCompanyInput)
    {
        try
        {
            await _coreService.CreateCompany(
                adminUser,
                new RegisterCompanyViewModel
                {
                    Id = registerCompanyInput.CompanyId,
                    CompanyName = registerCompanyInput.CompanyName,
                    CompanySize = registerCompanyInput.CompanySize,
                    PromotionCode = registerCompanyInput.PromotionCode,
                    PhoneNumber = registerCompanyInput.PhoneNumber,
                    SubscriptionPlanId = registerCompanyInput.SubscriptionPlanId,
                    HeardFrom = registerCompanyInput.HeardFrom,
                    lmref = registerCompanyInput.Lmref,
                    TimeZoneInfoId = registerCompanyInput.TimeZoneInfoId,
                    WebClientUUID = registerCompanyInput.WebClientUUID,
                    Referral = registerCompanyInput.Referral,
                    CompanyType = registerCompanyInput.CompanyType,
                    Industry = registerCompanyInput.Industry,
                    OnlineShopSystem = registerCompanyInput.OnlineShopSystem,
                    CommunicationTools = registerCompanyInput.CommunicationTools,
                    CompanyWebsite = registerCompanyInput.CompanyWebsite,
                    PlatformUsageIntent = registerCompanyInput.PlatformUsageIntent,
                });
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                $"[{nameof(RegisterCompanyAsync)}]Create company error: {{ExceptionMessage}}",
                e.Message);
            throw;
        }

        var staff = await _coreService.GetCompanyStaff(adminUser);
        var freeBills = _appDbContext.CompanyBillRecords.Where(
            x =>
                x.CompanyId == staff.CompanyId && x.SubscriptionPlanId == "sleekflow_free").ToList();
        if (freeBills.Count > 0)
        {
            // change all free to freemium
            foreach (var free in freeBills)
            {
                // Default plan update: use "sleekflow_v10_startup" instead of "sleekflow_freemium" after Global Pricing release
                free.SubscriptionPlanId = _defaultSubscriptionPlanIdGetter.GetDefaultStartupPlanId();
                free.Status = BillStatus.Active;
            }

            await _appDbContext.SaveChangesAsync();
        }

        var associatedCompanyIds = await _appDbContext.UserRoleStaffs
            .Where(x => x.IdentityId == adminUser.Id)
            .Select(x => x.CompanyId)
            .ToListAsync();

        _logger.LogInformation(
                "Company created: {0}, \nStaff created: {1}, {2}, {3}",
                registerCompanyInput.CompanyName,
                staff.Identity.Id,
                staff.Identity.Email,
                staff.Company.SignalRGroupName);

        return new RegisterCompanyResult(
            staffId: staff.Id.ToString(),
            signalRGroupName: staff.Company.SignalRGroupName,
            isShopifyAccount: staff.Company.IsShopifyAccount,
            associatedCompanyIds: associatedCompanyIds,
            location: registerCompanyInput.Location);
    }



    public async Task<List<InvitedUserByEmailObject>> InviteUsersByEmailAsync(
        ApplicationUser? adminUser,
        string companyId,
        List<long> teamIds,
        List<InviteUserInputObject> inviteUsers)
    {
        try
        {
            if (adminUser is null)
            {
                throw new Exception("[InviteUsersByEmailAsync] User not found");
            }

            var companyAdminUser = await _coreService.GetCompanyStaff(adminUser);

            if (companyAdminUser == null)
            {
                throw new Exception($"[InviteUsersByEmailAsync] User not found in company.");
            }

            var addedUsers = new List<InvitedUserByEmailObject>();
            foreach (var inviteUser in inviteUsers)
            {
                Auth0User? auth0User = null;
                var user = await _userManager.FindByEmailAsync(inviteUser.Email);
                if (user is null)
                {
                    if (inviteUser.TenantHubUserId is null)
                    {
                        throw new Exception("[InviteUsersByEmailAsync] TenantHubUserId is required");
                    }

                    // var identityResult = await _userManager.CreateAsync(
                    var result = await _userManager.CreateWithTenantHubIdAsync(
                        new ApplicationUser
                        {
                            Id = inviteUser.SleekflowUserId,
                            UserName = inviteUser.UserName ?? inviteUser.Email,
                            Email = inviteUser.Email,
                            FirstName = inviteUser.Firstname ?? "-",
                            LastName = inviteUser.Lastname ?? "-",
                            DisplayName = $"{inviteUser.Firstname} {inviteUser.Lastname}",
                            EmailConfirmed = true,
                        },
                        inviteUser.TenantHubUserId);
                    var identityResult = result.IdentityResult;

                    if (!identityResult.Succeeded)
                    {
                        _logger.LogError(
                            "[InviteUsersByEmailAsync] Company {CompanyId} create user {UserEmail} error: {Errors}",
                            companyAdminUser.CompanyId,
                            inviteUser.Email,
                            JsonConvert.SerializeObject(identityResult.Errors));

                        throw new IdentityResultException(identityResult);
                    }

                    auth0User = result.Auth0User;
                    user = await _userManager.FindByEmailAsync(inviteUser.Email);
                }

                var userIsRegisteredCompany =
                    await _appDbContext.UserRoleStaffs.AnyAsync(u => u.IdentityId == user!.Id);
                if (userIsRegisteredCompany)
                {
                    _logger.LogError("[InviteUsersByEmailAsync] User ${Email} have registered a company", user!.Email);
                    throw new Exception($"User ${user!.Email} have registered a company");
                }

                // If user is no company associated, set it to invite. user for later checking
                if (!user.UserName.StartsWith("invite."))
                {
                    var changeUserNameResult = await _userManager.SetUserNameAsync(
                        user,
                        $"invite.{SleekflowUserManager.GenerateRandomString(22, false)}");

                    if (!changeUserNameResult.Succeeded)
                    {
                        _logger.LogError(
                            "[InviteUsersByEmailAsync] Company {CompanyId} set username failure for email {UserEmail} error: {Errors}",
                            companyAdminUser.CompanyId,
                            user.Email,
                            JsonConvert.SerializeObject(changeUserNameResult.Errors));

                        throw new IdentityResultException(changeUserNameResult);
                    }
                }

                var staff = new Staff
                {
                    CompanyId = companyAdminUser.CompanyId,
                    IdentityId = user.Id,
                    Identity = user,
                    Locale = "en",
                    RoleType = inviteUser.UserRole,
                    Position = inviteUser.Position,
                    TimeZoneInfoId = string.IsNullOrEmpty(inviteUser.TimeZoneInfoId)
                        ? companyAdminUser.Company.TimeZoneInfoId
                        : inviteUser.TimeZoneInfoId,
                    NotificationSettingId = 1,
                    Order = 1
                };
                await _appDbContext.UserRoleStaffs.AddAsync(staff);
                await _appDbContext.SaveChangesAsync();

                await _companyTeamService.AddOrRemoveTeam(
                    companyAdminUser.CompanyId,
                    staff.Id,
                    teamIds);

                addedUsers.Add(
                    new InvitedUserByEmailObject(
                            user.Email!,
                            user.UserName,
                            user.FirstName,
                            user.LastName,
                            staff.RoleType.ToString(),
                            staff.Position,
                            staff.TimeZoneInfoId,
                            user.Id,
                            inviteUser.TenantHubUserId!,
                            staff.Id,
                            auth0User,
                            staff.IsCustomRole()
                                ? inviteUser.UserRbacRoles
                                : null
                        ));

                var code = await _userManager.GenerateUserTokenAsync(
                    user,
                    SleekflowTokenProviderOptions.InviteTokenProviderName,
                    "InviteUsersByEmail");
                await _emailNotificationService.SendInvitationEmail(user, code, companyAdminUser, inviteUser.TenantHubUserId);
                var staffId = await _appDbContext.UserRoleStaffs
                    .Where(x => x.CompanyId == companyAdminUser.CompanyId)
                    .OrderBy(x => x.Order)
                    .ThenBy(x => x.Id)
                    .Select(x => x.IdentityId)
                    .FirstOrDefaultAsync();
            }

            await _companyInfoCacheService.RemoveCompanyInfoCache(companyAdminUser.CompanyId);
            return addedUsers;
        }
        catch (Exception e)
        {
            // Remove all added users when invite failed.
            foreach (var inviteUser in inviteUsers)
            {
                var user = await _userManager.FindByEmailAsync(inviteUser.Email);
                if (user is not null)
                {
                    var staff = await _coreService.GetCompanyStaff(user);
                    if (staff is not null && staff.CompanyId == companyId)
                    {
                        await _coreService.RemoveStaffData(staff.CompanyId, staff.Id);
                    }
                    else
                    {
                        await _userManager.DeleteAsync(user, true);
                    }
                }
            }

            if (e is IdentityResultException)
            {
                _logger.LogError(
                    "[{InviteUsersByEmailAsyncName}] {Message}, InnerException: {InnerExceptionMessage}",
                    nameof(InviteUsersByEmailAsync),
                    e.Message,
                    e.InnerException?.Message);
                throw;
            }

            _logger.LogError(
                e,
                "[{InviteUsersByEmailAsyncName}] error: {EMessage}",
                nameof(InviteUsersByEmailAsync),
                e.Message);
            throw new Exception($"Invite user failed: {e.Message}", e);
        }
    }

    public async Task<(ShareableInvitation, string)> GenerateShareableLinkAsync(
        string adminUserId, ShareableInvitationViewModel shareableInvitationViewModel)
    {
       var companyUser = await _coreService.GetCompanyStaff(
                await _userManager.FindByIdAsync(adminUserId));
       var roleType = RoleTypeConverter.ConvertToEnum(shareableInvitationViewModel.Role);

       if (roleType.IsFailure)
       {
           _logger.LogError("{GenerateShareableLinkAsyncName} {RoleTypeError}", nameof(GenerateShareableLinkAsync), roleType.Error);
           throw new Exception(roleType.Error);
       }

       if (companyUser == null)
       {
           _logger.LogError($"{nameof(GenerateShareableLinkAsync)} UnauthorizedAccessException: Access denied");
           throw new UnauthorizedAccessException("User Unauthorized.");
       }

       if (companyUser.RbacRole == StaffUserRole.Staff)
       {
           _logger.LogError($"{nameof(GenerateShareableLinkAsync)} Access denied");
           throw new SecurityException("Access denied for staff role");
       }

       var invitation = await _appDbContext.CompanyShareableInvitations
           .Include(x => x.ShareableInvitationRecords)
           .ThenInclude(x => x.InvitedStaff.Identity)
           .FirstOrDefaultAsync(
               x =>
                   x.TeamIds == shareableInvitationViewModel.TeamIds
                   && x.Role == roleType.Value
                   && x.GeneratedById == companyUser.Id
                   && x.Status == ShareableLinkStatus.Enabled);

       if (invitation == null)
       {
           invitation = new ShareableInvitation
           {
               CompanyId = companyUser.CompanyId,
               Role = roleType.Value,
               TeamIds = shareableInvitationViewModel.TeamIds,
               Quota = shareableInvitationViewModel.Quota,
               ExpirationDate = shareableInvitationViewModel.ExpirationDate.ToUniversalTime(),
               GeneratedById = companyUser.Id,
               Status = ShareableLinkStatus.Enabled,
           };

           _appDbContext.CompanyShareableInvitations.Add(invitation);
       }

       invitation.Role = roleType.Value;
       invitation.TeamIds = shareableInvitationViewModel.TeamIds;
       invitation.Quota = shareableInvitationViewModel.Quota;
       invitation.ExpirationDate = shareableInvitationViewModel.ExpirationDate;
       invitation.UpdatedAt = DateTime.UtcNow;

       await _appDbContext.SaveChangesAsync();

       var location = _configuration["SF_REGION"] ?? string.Empty;

       return (invitation, location);
    }

    public async Task<bool> ResendInvitationEmailAsync(string staffUserId, string? tenantHubUserId, Staff adminUser)
    {
        if (tenantHubUserId is null)
        {
            _logger.LogError($"[{nameof(ResendInvitationEmailAsync)}] TenantHubUserId is required.");
            return false;
        }

        var user = await _userManager.FindByIdAsync(staffUserId);
        if (user is null)
        {
            _logger.LogError($"[{nameof(ResendInvitationEmailAsync)}] User not found when try to resend invitation email.");
            return false;
        }

        var code = await _userManager.GenerateUserTokenAsync(
            user,
            SleekflowTokenProviderOptions.InviteTokenProviderName,
            "ResendInvitationEmail");
        await _emailNotificationService.SendInvitationEmail(user, code, adminUser, tenantHubUserId);

        return true;
    }

    public async Task<List<InvitedUserByEmailObject>> ImportUsersFromCSVAsync(
        string companyId,
        List<ImportUserInputObject> importUsers)
    {
        try
        {
            var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == companyId);
            if (company is null)
            {
                throw new Exception($"Company {companyId} not found");
            }

            var addedUsers = new List<InvitedUserByEmailObject>();
            foreach (var importUser in importUsers)
            {
                Auth0User? auth0User = null;
                var user = await _userManager.FindByEmailAsync(importUser.Email);
                if (user is null)
                {
                    if (importUser.TenantHubUserId is null)
                    {
                        throw new Exception("[ImportUsersFromCSVAsync] TenantHubUserId is required");
                    }

                    var result = await _userManager.CreateWithTenantHubIdAsync(
                        new ApplicationUser
                        {
                            Id = importUser.SleekflowUserId,
                            UserName = importUser.UserName ?? importUser.Email,
                            Email = importUser.Email,
                            FirstName = importUser.Firstname,
                            LastName = importUser.Lastname,
                            DisplayName = $"{importUser.Firstname} {importUser.Lastname}",
                            EmailConfirmed = true,
                        },
                        importUser.Password ?? string.Empty,
                        importUser.TenantHubUserId);

                    var identityResult = result.IdentityResult;

                    if (!identityResult.Succeeded)
                    {
                        _logger.LogError(
                            "[InviteUsersByEmailAsync] Company {CompanyId} create user {UserEmail} error: {Errors}",
                            companyId,
                            importUser.Email,
                            JsonConvert.SerializeObject(identityResult.Errors));

                        throw new IdentityResultException(identityResult);
                    }

                    auth0User = result.User;
                    user = await _userManager.FindByEmailAsync(importUser.Email);
                }

                if (user is null)
                {
                    throw new Exception($"User {importUser.Email} not found");
                }

                var userIsRegisteredCompany =
                    await _appDbContext.UserRoleStaffs.AnyAsync(u => u.IdentityId == user!.Id);
                if (userIsRegisteredCompany)
                {
                    _logger.LogError("[InviteUsersByEmailAsync] User ${Email} have registered a company", user!.Email);
                    throw new Exception($"User ${user!.Email} have registered a company");
                }

                var staff = new Staff
                {
                    CompanyId = companyId,
                    IdentityId = user.Id,
                    Identity = user,
                    Locale = "en",
                    RoleType = importUser.UserRole,
                    Position = importUser.Position,
                    TimeZoneInfoId = string.IsNullOrEmpty(importUser.TimeZoneInfoId)
                        ? company.TimeZoneInfoId
                        : importUser.TimeZoneInfoId,
                    NotificationSettingId = 1,
                    Order = 1
                };
                await _appDbContext.UserRoleStaffs.AddAsync(staff);
                await _appDbContext.SaveChangesAsync();

                await _companyTeamService.AddOrRemoveTeam(
                    companyId,
                    staff.Id,
                    importUser.TeamIds);

                addedUsers.Add(
                    new InvitedUserByEmailObject(
                        user.Email!,
                        user.UserName ?? user.Email!,
                        user.FirstName,
                        user.LastName,
                        staff.RoleType.ToString(),
                        staff.Position,
                        staff.TimeZoneInfoId,
                        user.Id,
                        importUser.TenantHubUserId!,
                        staff.Id,
                        auth0User
                    ));
            }

            await _companyInfoCacheService.RemoveCompanyInfoCache(companyId);
            return addedUsers;
        }
        catch (Exception e)
        {
            // Remove all added users when import failed.
            foreach (var importUser in importUsers)
            {
                var user = await _userManager.FindByIdAsync(importUser.SleekflowUserId);
                if (user is not null)
                {
                    var staff = await _coreService.GetCompanyStaff(user);
                    if (staff is not null)
                    {
                        await _coreService.RemoveStaffData(staff.CompanyId, staff.Id);
                    }
                    else
                    {
                        await _userManager.DeleteAsync(user);
                    }
                }
            }

            _logger.LogError(
                e,
                "[{ImportUsersFromCSVAsyncName}] Error: {EMessage}",
                nameof(ImportUsersFromCSVAsync),
                e.Message);
            throw new Exception($"Invite user failed: {e.Message}", e);
        }
    }
}

