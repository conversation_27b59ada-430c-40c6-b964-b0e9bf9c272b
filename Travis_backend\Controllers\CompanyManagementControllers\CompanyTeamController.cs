﻿using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Npoi.Mapper;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.AutomationDomain.Models;
using Travis_backend.Cache;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.CompanyDomain.ViewModels;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.Services;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.FileDomain.Services;
using Travis_backend.FlowHubs;
using Travis_backend.SignalR;
using Travis_backend.TenantHubDomain.Services;


namespace Travis_backend.Controllers.CompanyManagementControllers
{
    [Authorize]
    public class CompanyTeamController : Controller
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IMapper _mapper;
        private readonly IUploadService _uploadService;
        private readonly IAzureBlobStorageService _azureBlobStorageService;
        private readonly ICompanyService _companyService;
        private readonly ICompanyTeamService _companyTeamService;
        private readonly ICompanyInfoCacheService _companyInfoCacheService;
        private readonly ICoreService _coreService;
        private readonly ICompanyTeamAssignmentQueueService _companyTeamAssignmentQueueService;
        private readonly IStaffHooks _staffHooks;
        private readonly ILogger<CompanyTeamController> _logger;
        private readonly IRbacService _rbacService;

        public CompanyTeamController(
            ApplicationDbContext appDbContext,
            UserManager<ApplicationUser> userManager,
            IMapper mapper,
            IUploadService uploadService,
            IAzureBlobStorageService azureBlobStorageService,
            ICompanyService companyService,
            ICompanyTeamService companyTeamService,
            ICompanyInfoCacheService companyInfoCacheService,
            ICoreService coreService,
            ICompanyTeamAssignmentQueueService companyTeamAssignmentQueueService,
            IStaffHooks staffHooks,
            ILogger<CompanyTeamController> logger,
            IRbacService rbacService)
        {
            _userManager = userManager;
            _appDbContext = appDbContext;
            _mapper = mapper;
            _uploadService = uploadService;
            _azureBlobStorageService = azureBlobStorageService;
            _companyService = companyService;
            _companyTeamService = companyTeamService;
            _companyInfoCacheService = companyInfoCacheService;
            _coreService = coreService;
            _companyTeamAssignmentQueueService = companyTeamAssignmentQueueService;
            _staffHooks = staffHooks;
            _logger = logger;
            _rbacService = rbacService;
        }

        [HttpGet]
        [Route("Company/Team")]
        public async Task<IActionResult> GetTeams(
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 300)
        {
            if (User.Identity.IsAuthenticated)
            {
                var companyUser = await _coreService.GetCompanyStaff(
                    await _userManager.GetUserAsync(
                        User));

                if (companyUser == null)
                {
                    return Unauthorized();
                }

                try
                {
                    var teams = await _appDbContext.CompanyStaffTeams
                        .Where(x => x.CompanyId == companyUser.CompanyId)
                        .Include(x => x.Members)
                        .ThenInclude(x => x.Staff.Identity)
                        .Include(x => x.Members)
                        .ThenInclude(x => x.Staff.ProfilePicture)
                        .OrderBy(x => x.TeamName)
                        .Skip(offset)
                        .Take(limit)
                        .ToListAsync(HttpContext.RequestAborted);

                    // Adhoc for SCMP
                    if (companyUser.CompanyId is "c39b40f9-38c3-4e2a-bc53-af46389363a9"
                        or "5edfbcc3-2179-405f-b860-0afd7d2356e3"
                        or "c3c8fa8c-c64f-4f94-873d-250845bf71cf")
                    {
                        switch (companyUser.RoleType)
                        {
                            case StaffUserRole.Staff:
                            case StaffUserRole.TeamAdmin:
                                var currentUserTeamIds = await _appDbContext.CompanyTeamMembers
                                    .Where(y => y.StaffId == companyUser.Id)
                                    .Select(y => y.CompanyTeamId)
                                    .ToListAsync(HttpContext.RequestAborted);

                                teams = teams
                                    .Where(x => currentUserTeamIds.Contains(x.Id))
                                    .ToList();

                                if (companyUser.RoleType == StaffUserRole.Staff)
                                {
                                    teams.ForEach(
                                        x => x.Members = x.Members
                                            .Where(y => y.StaffId == companyUser.Id)
                                            .ToList());
                                }

                                break;
                        }
                    }

                    foreach (var team in teams)
                    {
                        team.QRCodeChannel = await _companyService.GetTargetedChannelTeam(team);
                    }

                    var response = _mapper.Map<List<CompanyTeamResponse>>(teams);

                    var teamsMemberCountDict = await _appDbContext.CompanyTeamMembers
                        .Where(
                            x => response
                                .Select(y => y.Id)
                                .Contains(x.CompanyTeamId))
                        .GroupBy(x => x.CompanyTeamId)
                        .ToDictionaryAsync(
                            g => g.Key,
                            g => g.Count());

                    // add membercount and teamadmin
                    foreach (var data in response)
                    {
                        data.MemberCount = teamsMemberCountDict.TryGetValue(data.Id, out var memberCount) ?
                            memberCount :
                            0;

                        var teamAdmins = await _appDbContext.CompanyTeamMembers
                            .Where(x => x.CompanyTeamId == data.Id)
                            .Include(x => x.Staff.Identity)
                            .ToListAsync(HttpContext.RequestAborted);

                        data.TeamAdmins = _mapper.Map<List<StaffWithoutCompanyResponse>>(
                            teamAdmins
                                .Select(x => x.Staff));
                    }

                    return Ok(response);
                }
                catch
                {
                    return NotFound();
                }
            }

            return BadRequest();
        }

        [HttpGet]
        [Route("v2/Company/Team")]
        public async Task<IActionResult> GetV2Teams(
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 10)
        {
            var companyUser = await _coreService.GetCompanyStaff(
                await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var isRbacEnabled = _rbacService.IsRbacEnabled();

            if (isRbacEnabled)
            {
                return await RbacGetV2Teams(offset, limit, companyUser);
            }

            return await DefaultGetV2Teams(offset, limit);
        }

        private async Task<IActionResult> RbacGetV2Teams(int offset, int limit, Staff companyUser)
        {
            var teams = await _companyTeamService.GetTeamsAsync(companyUser);
            teams = teams.Skip(offset).Take(limit).ToList();

            var teamsMemberCountDict = await _appDbContext.CompanyTeamMembers
                .Where(x => teams.Select(y => y.Id).Contains(x.CompanyTeamId))
                .GroupBy(x => x.CompanyTeamId)
                .ToDictionaryAsync(g => g.Key, g => g.Count());

            var response = new List<CompanyTeamResponse>();

            foreach (var team in teams)
            {
                team.QRCodeChannel = await _companyService.GetTargetedChannelTeam(team);
                var members = await _appDbContext.CompanyTeamMembers
                    .Where(x => x.CompanyTeamId == team.Id)
                    .Include(x => x.Staff.Identity)
                    .ToListAsync(HttpContext.RequestAborted);

                var data = _mapper.Map<CompanyTeamResponse>(team);
                data.Members = _mapper.Map<List<StaffWithoutCompanyResponse>>(members.Select(x => x.Staff));
                data.MemberCount = teamsMemberCountDict.TryGetValue(team.Id, out var memberCount) ? memberCount : 0;
                data.TeamAdmins = [];
                response.Add(data);
            }

            return Ok(response);
        }

        private async Task<IActionResult> DefaultGetV2Teams(int offset, int limit)
        {
            if (User.Identity.IsAuthenticated)
            {
                // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).FirstOrDefaultAsync();
                var companyUser = await _coreService.GetCompanyStaff(
                    await _userManager.GetUserAsync(User));

                if (companyUser == null)
                {
                    return Unauthorized();
                }

                try
                {
                    var teams = await _appDbContext.CompanyStaffTeams
                        .Where(x => x.CompanyId == companyUser.CompanyId)
                        .OrderBy(x => x.TeamName)
                        .Skip(offset)
                        .Take(limit)
                        .ToListAsync(HttpContext.RequestAborted);

                    // Adhoc for SCMP
                    if (companyUser.CompanyId is "c39b40f9-38c3-4e2a-bc53-af46389363a9"
                        or "5edfbcc3-2179-405f-b860-0afd7d2356e3"
                        or "c3c8fa8c-c64f-4f94-873d-250845bf71cf")
                    {
                        switch (companyUser.RoleType)
                        {
                            case StaffUserRole.Staff:
                            case StaffUserRole.TeamAdmin:
                                var currentUserTeamIds = await _appDbContext.CompanyTeamMembers
                                    .Where(y => y.StaffId == companyUser.Id)
                                    .Select(y => y.CompanyTeamId)
                                    .ToListAsync(HttpContext.RequestAborted);

                                teams = teams
                                    .Where(x => currentUserTeamIds.Contains(x.Id))
                                    .ToList();

                                if (companyUser.RoleType == StaffUserRole.Staff)
                                {
                                    teams.ForEach(
                                        x => x.Members = x.Members
                                            .Where(y => y.StaffId == companyUser.Id)
                                            .ToList());
                                }

                                break;
                        }
                    }

                    foreach (var team in teams)
                    {
                        team.QRCodeChannel = await _companyService.GetTargetedChannelTeam(team);
                    }

                    var response = _mapper.Map<List<CompanyTeamResponse>>(teams);

                    // add membercount and teamadmin
                    var teamsMemberCountDict = await _appDbContext.CompanyTeamMembers
                        .Where(
                            x => response
                                .Select(y => y.Id)
                                .Contains(x.CompanyTeamId))
                        .GroupBy(x => x.CompanyTeamId)
                        .ToDictionaryAsync(
                            g => g.Key,
                            g => g.Count());

                    foreach (var data in response)
                    {
                        data.MemberCount = teamsMemberCountDict.TryGetValue(data.Id, out var memberCount) ?
                            memberCount :
                            0;

                        var teamAdmins = await _appDbContext.CompanyTeamMembers
                            .Where(x => x.CompanyTeamId == data.Id)
                            .Include(x => x.Staff.Identity)
                            .ToListAsync(HttpContext.RequestAborted);

                        data.TeamAdmins = _mapper.Map<List<StaffWithoutCompanyResponse>>(
                            teamAdmins
                                .Select(x => x.Staff));
                    }

                    return Ok(response);
                }
                catch
                {
                    return NotFound();
                }
            }

            return BadRequest();
        }

        [HttpGet]
        [Route("v2/Company/Team/{teamId}")]
        public async Task<IActionResult> GetV2Teams(long? teamId = null)
        {
            if (User.Identity.IsAuthenticated)
            {
                var companyUser = await _coreService.GetCompanyStaff(
                    await _userManager.GetUserAsync(User));

                if (companyUser == null)
                {
                    return Unauthorized();
                }

                try
                {
                    var team = await _appDbContext.CompanyStaffTeams
                        .Include(x => x.Members)
                        .ThenInclude(x => x.Staff.Identity)
                        .Include(x => x.Members)
                        .ThenInclude(x => x.Staff.ProfilePicture)
                        .OrderBy(x => x.TeamName)
                        .FirstOrDefaultAsync(
                            x =>
                                x.CompanyId == companyUser.CompanyId
                                && x.Id == teamId);

                    var response = _mapper.Map<CompanyTeamResponse>(team);

                    return Ok(response);
                }
                catch
                {
                    return NotFound();
                }
            }

            return BadRequest();
        }

        [HttpGet]
        [Route("company/teams/staffs")]
        public async Task<IActionResult> GetTeamsStaffs()
        {
            var companyUser = await _coreService.GetCompanyStaff(
                await _userManager.GetUserAsync(User));

            if (companyUser is null
                or not { RoleType: StaffUserRole.TeamAdmin })
            {
                return Unauthorized();
            }

            var teamIdList = await _appDbContext.CompanyTeamMembers
                .Where(y => y.StaffId == companyUser.Id)
                .Select(y => y.CompanyTeamId)
                .ToListAsync(HttpContext.RequestAborted);

            var staffList = await _appDbContext.CompanyTeamMembers
                .Where(y => teamIdList.Contains(y.CompanyTeamId))
                .Include(y => y.Staff.Identity)
                .Select(y => y.Staff)
                .Distinct()
                .ToListAsync(HttpContext.RequestAborted);

            var response = _mapper.Map<List<StaffWithoutCompanyResponse>>(staffList);

            return Ok(response);
        }

        [HttpPost]
        [Route("Company/Team/Create")]
        public async Task<IActionResult> CreateNewTeam([FromBody] CreateNewTeamViewModel createNewTeamViewModel)
        {
            if (User.Identity.IsAuthenticated)
            {
                var companyUser = await _coreService.GetCompanyStaff(
                    await _userManager.GetUserAsync(User));

                if (companyUser == null
                    || companyUser.RbacRole != StaffUserRole.Admin)
                {
                    return Unauthorized();
                }

                // Frond-end always pass twilio_whatsapp as channel, which server does not support
                createNewTeamViewModel.DefaultChannels
                    .Where(x => x.channel == "twilio_whatsapp")
                    .ForEach(x => x.channel = ChannelTypes.WhatsappTwilio);

                var newTeam = new CompanyTeam
                {
                    CompanyId = companyUser.CompanyId,
                    TeamName = createNewTeamViewModel.TeamName,
                    DefaultChannels = createNewTeamViewModel.DefaultChannels
                };

                if (createNewTeamViewModel.StaffIds != null)
                {
                    foreach (var staffId in createNewTeamViewModel.StaffIds)
                    {
                        var staff = await _appDbContext.UserRoleStaffs
                            .Include(x => x.Identity)
                            .FirstOrDefaultAsync(
                                x =>
                                    x.CompanyId == companyUser.CompanyId
                                    && x.IdentityId == staffId);

                        if (staff == null)
                        {
                            return BadRequest(
                                new ResponseViewModel
                                {
                                    message = "No member found"
                                });
                        }

                        newTeam.Members.Add(
                            new TeamMember
                            {
                                Staff = staff
                            });

                        await _staffHooks.OnStaffAddedToTeamsAsync(
                            newTeam.CompanyId,
                            staff.Id,
                            new List<CompanyTeam>()
                            {
                                newTeam
                            });
                    }
                }

                // Add QRCodeContent
                newTeam.QRCodeIdentity = await _companyService.GetTeamQRCodeContent(newTeam);

                _appDbContext.CompanyStaffTeams.Add(newTeam);
                await _appDbContext.SaveChangesAsync();

                var assignedTeamField = await _appDbContext.CompanyCustomUserProfileFields
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == companyUser.CompanyId
                            && x.FieldName.ToLower() == "assignedteam");

                if (assignedTeamField == null)
                {
                    var newTeamField = new CompanyCustomUserProfileField
                    {
                        CompanyId = companyUser.CompanyId,
                        FieldName = "AssignedTeam",
                        Type = FieldDataType.Options,
                        Order = 99,
                        CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                        {
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "Assigned Team",
                                Language = "en"
                            },
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "團隊",
                                Language = "zh-hk"
                            }
                        },
                        IsDeletable = false,
                        IsEditable = false,
                    };

                    _appDbContext.CompanyCustomUserProfileFields.Add(newTeamField);

                    await _appDbContext.SaveChangesAsync();
                }

                await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

                var response = _mapper.Map<CompanyTeamResponse>(newTeam);

                return Ok(response);
            }

            return BadRequest();
        }

        [HttpPost]
        [Route("company/team/{teamId}/update")]
        public async Task<IActionResult> UpdateTeam(long teamId, [FromBody] UpdateTeamViewModel updateTeamViewModel)
        {
            if (User.Identity.IsAuthenticated)
            {
                var companyUser = await _coreService.GetCompanyStaff(
                    await _userManager.GetUserAsync(User));

                if (companyUser == null
                    || companyUser.RbacRole != StaffUserRole.Admin)
                {
                    return Unauthorized();
                }

                _logger.LogInformation(
                    "[{Method}]: In staff Id: {StaffId}, company id: {CompanyId}, Team: {Team}",
                    nameof(UpdateTeam),
                    companyUser.Id,
                    companyUser.CompanyId,
                    JsonConvert.SerializeObject(updateTeamViewModel));

                var team = await _appDbContext.CompanyStaffTeams
                    .Include(x => x.Members)
                    .ThenInclude(x => x.Staff)
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == companyUser.CompanyId
                            && x.Id == teamId);

                if (team == null)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            message = "Not found"
                        });
                }

                if (companyUser.RbacRole != StaffUserRole.Admin
                    && (companyUser.RbacRole == StaffUserRole.TeamAdmin
                        && team.Members.All(x => x.StaffId != companyUser.Id)))
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            message = "You didn't have premission to update team",
                            errorId = "NO_PREMISSION"
                        });
                }

                if (updateTeamViewModel.TeamName != null)
                {
                    team.TeamName = updateTeamViewModel.TeamName;
                }

                // Frond-end always pass twilio_whatsapp as channel, which server does not support
                updateTeamViewModel.DefaultChannels
                    .Where(x => x.channel == "twilio_whatsapp")
                    .ForEach(x => x.channel = ChannelTypes.WhatsappTwilio);

                team.DefaultChannels = updateTeamViewModel.DefaultChannels;
                team.LastUpdated = DateTime.UtcNow;

                foreach (var adminId in updateTeamViewModel.AdminIds)
                {
                    await _companyTeamService.AddToTeam(
                        companyUser.CompanyId,
                        teamId,
                        new CreateNewTeamViewModel
                        {
                            TeamName = updateTeamViewModel.TeamName,
                            StaffIds = new List<string>
                            {
                                adminId
                            }
                        });

                    var teamAdmin = await _appDbContext.UserRoleStaffs
                        .FirstOrDefaultAsync(x => x.IdentityId == adminId);

                    if (teamAdmin.RoleType == StaffUserRole.Staff)
                    {
                        teamAdmin.RoleType = StaffUserRole.TeamAdmin;

                        await _staffHooks.OnStaffRoleUpdatedAsync(
                            teamAdmin.CompanyId,
                            teamAdmin.Id,
                            teamAdmin.RoleType);
                    }
                }

                await _appDbContext.SaveChangesAsync();

                foreach (var staffId in updateTeamViewModel.StaffIds)
                {
                    await _companyTeamService.AddToTeam(
                        companyUser.CompanyId,
                        teamId,
                        new CreateNewTeamViewModel
                        {
                            TeamName = updateTeamViewModel.TeamName,
                            StaffIds = new List<string>
                            {
                                staffId
                            }
                        });

                    var teamAdmin = await _appDbContext.UserRoleStaffs
                        .FirstOrDefaultAsync(
                            x =>
                                x.CompanyId == companyUser.CompanyId
                                && x.IdentityId == staffId);

                    if (teamAdmin.RoleType != StaffUserRole.Staff)
                    {
                        teamAdmin.RoleType = StaffUserRole.Staff;
                    }
                }

                await _appDbContext.SaveChangesAsync();

                await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

                var response = _mapper.Map<CompanyTeamResponse>(team);

                _logger.LogInformation(
                    "[{Method}] (done): In staff Id: {StaffId}, company id: {CompanyId}, Team: {Team}",
                    nameof(UpdateTeam),
                    companyUser.Id,
                    companyUser.CompanyId,
                    JsonConvert.SerializeObject(updateTeamViewModel));

                return Ok(response);
            }

            return BadRequest();
        }

        [HttpPost]
        [Route("company/team/{teamId}/qrcode")]
        public async Task<IActionResult> UpdateTeamQRCodeSetting(
            long teamId,
            [FromBody]
            UpdateTeamViewModel updateTeamViewModel)
        {
            var companyUser = await _coreService.GetCompanyStaff(
                await _userManager.GetUserAsync(User));

            if (companyUser == null
                || companyUser.RbacRole != StaffUserRole.Admin)
            {
                return Unauthorized();
            }

            var team = await _appDbContext.CompanyStaffTeams
                .Include(x => x.Members)
                .ThenInclude(x => x.Staff)
                .FirstOrDefaultAsync(x => x.CompanyId == companyUser.CompanyId && x.Id == teamId);

            if (team == null)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Not found"
                    });
            }

            if (companyUser.RbacRole != StaffUserRole.Admin
                && (companyUser.RbacRole == StaffUserRole.TeamAdmin
                    && team.Members.All(x => x.StaffId != companyUser.Id)))
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "You didn't have premission to update team",
                        errorId = "NO_PREMISSION"
                    });
            }

            if (updateTeamViewModel.QRCodeChannel != null)
            {
                team.QRCodeChannel = updateTeamViewModel.QRCodeChannel;
            }

            if (updateTeamViewModel.QRCodeIdentity != null)
            {
                team.QRCodeIdentity = updateTeamViewModel.QRCodeIdentity;
            }

            team.QRCodeAssignmentType = updateTeamViewModel.QRCodeAssignmentType;
            team.QRCodeAssignmentStaffId = updateTeamViewModel.QRCodeAssignmentStaffId;

            await _appDbContext.SaveChangesAsync();

            var response = _mapper.Map<CompanyTeamResponse>(team);

            return Ok(response);
        }

        [HttpDelete]
        [Route("Company/Team/Delete")]
        public async Task<IActionResult> DeleteNewTeam([FromBody] DeleteTeamViewModel deleteTeamViewModel)
        {
            var companyUser = await _coreService.GetCompanyStaff(
                await _userManager.GetUserAsync(User));

            if (companyUser == null
                || companyUser.RoleType == StaffUserRole.TeamAdmin
                || companyUser.RoleType == StaffUserRole.Staff)
            {
                return Unauthorized();
            }

            var teamsToBeDeleted = await _appDbContext.CompanyStaffTeams
                .Where(
                    x =>
                        deleteTeamViewModel.TeamIds.Contains(x.Id)
                        && x.CompanyId == companyUser.CompanyId)
                .ToListAsync();

            foreach (var team in teamsToBeDeleted)
            {
                var teamId = team.Id;

                await _appDbContext.Conversations
                    .Where(x => teamId == x.AssignedTeamId)
                    .ExecuteUpdateAsync(
                        conversation =>
                            conversation.SetProperty(c => c.AssignedTeamId, (long?) null));

                await _appDbContext.CompanyAutomationActions
                    .Where(
                        x =>
                            x.CompanyId == companyUser.CompanyId
                            && x.AssignedTeamId == teamId)
                    .ExecuteUpdateAsync(
                        action =>
                            action.SetProperty(a => a.AssignedTeamId, (long?) null));

                await _appDbContext.CompanyAutomationActionRecords
                    .Where(x => x.AssignedTeamId == teamId)
                    .ExecuteUpdateAsync(
                        actionRecord =>
                            actionRecord.SetProperty(r => r.AssignedTeamId, (long?) null));

                await _appDbContext.CompanyTeamMembers
                    .Where(x => x.CompanyTeamId == teamId)
                    .ExecuteDeleteAsync();

                await _appDbContext.CompanyStaffTeams
                    .Where(x => x.Id == teamId)
                    .ExecuteDeleteAsync();

                await _staffHooks.OnTeamDeletedAsync(
                    companyUser.CompanyId,
                    team);
            }

            await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

            return Ok(
                new ResponseViewModel
                {
                    message = "success"
                });
        }

        [HttpPost]
        [Route("Company/Team/{teamId}/add")]
        public async Task<IActionResult> AddNewMemberTeam(
            long teamId,
            [FromBody]
            CreateNewTeamViewModel createNewTeamViewModel)
        {
            if (User.Identity.IsAuthenticated)
            {
                var companyUser = await _coreService.GetCompanyStaff(
                    await _userManager.GetUserAsync(User));

                if (companyUser == null
                    || companyUser.RoleType == StaffUserRole.Staff)
                {
                    return Unauthorized();
                }

                var team = await _companyTeamService.AddToTeam(
                    companyUser.CompanyId,
                    teamId,
                    createNewTeamViewModel);

                var response = _mapper.Map<CompanyTeamResponse>(team);

                return Ok(response);
            }

            return BadRequest();
        }

        [HttpPost]
        [Route("Company/Team/{teamId}/remove")]
        public async Task<IActionResult> RemoveMemberFromTeam(
            long teamId,
            [FromBody]
            CreateNewTeamViewModel createNewTeamViewModel)
        {
            if (User.Identity.IsAuthenticated)
            {
                // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).FirstOrDefaultAsync();
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (companyUser == null
                    || companyUser.RoleType == StaffUserRole.Staff)
                {
                    return Unauthorized();
                }

                var team = await _companyTeamService.RemoveFromTeam(
                    companyUser.CompanyId,
                    teamId,
                    createNewTeamViewModel);

                var companyTeamAssignmentQueue =
                    await _companyTeamAssignmentQueueService.GetCompanyTeamAssignmentQueueByTeamIdAsync(teamId);

                foreach (var staffIdToRemove in createNewTeamViewModel.StaffIds)
                {
                    await _companyTeamAssignmentQueueService.RemoveStaffFromCompanyTeamAssignmentQueueAsync(
                        staffIdToRemove,
                        companyTeamAssignmentQueue);
                }

                var response = _mapper.Map<CompanyTeamResponse>(team);

                return Ok(response);
            }

            return BadRequest();
        }

        [HttpGet]
        [Route("Company/Team/Qrcode/{teamId}")]
        public async Task<IActionResult> GetTeamQrcode(long? teamId)
        {
            if (User.Identity.IsAuthenticated)
            {
                var companyUser = await _coreService.GetCompanyStaff(
                    await _userManager.GetUserAsync(User));

                if (companyUser == null)
                {
                    return Ok();
                }

                if (!teamId.HasValue)
                {
                    return BadRequest();
                }

                var team = await _appDbContext.CompanyStaffTeams
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == companyUser.CompanyId
                            && x.Id == teamId);

                var (qrCodeImage, url) = await _companyService.GetTeamQrcode(team);

                var stream = new MemoryStream(qrCodeImage.ToArray());
                var qrCodeFilename = $"qrcode/team/{team.QRCodeIdentity}.jpg";

                await _uploadService.UploadFileBySteam(
                    companyUser.CompanyId,
                    qrCodeFilename,
                    stream);

                dynamic response = new JObject();

                response.url = url;
                response.qrcodeBase64 = Convert.ToBase64String(qrCodeImage.ToArray());
                response.qrcodeUrl = _azureBlobStorageService.GetAzureBlobSasUri(
                    qrCodeFilename,
                    companyUser.CompanyId,
                    24 * 7);

                return Ok(response);
            }

            return BadRequest();
        }
    }
}