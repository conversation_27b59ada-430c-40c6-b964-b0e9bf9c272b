using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Repositories;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Enums;
using Travis_backend.InternalDomain.ViewModels;

namespace Sleekflow.Core.Tests.PowerflowApi;

/// <summary>
/// Integration tests for MRR calculations with timezone-aware functionality,
/// testing the complete business logic workflow without web application overhead.
/// </summary>
[TestFixture]
public class MrrCalculationIntegrationTests
{
    private Mock<ICompanyRepository> _mockCompanyRepository = null!;
    private Mock<ITimezoneAwareMrrCalculationService> _mockTimezoneService = null!;
    private Mock<ILogger<BillRecordRevenueCalculatorService>> _mockBillRecordRevenueCalculatorServiceLogger = null!;
    private BillRecordRevenueCalculatorService _billRecordRevenueCalculatorService = null!;

    [SetUp]
    public void SetUp()
    {
        // Reset mocks for each test
        _mockCompanyRepository = new Mock<ICompanyRepository>();
        _mockTimezoneService = new Mock<ITimezoneAwareMrrCalculationService>();
        _mockBillRecordRevenueCalculatorServiceLogger = new Mock<ILogger<BillRecordRevenueCalculatorService>>();
        _billRecordRevenueCalculatorService = new BillRecordRevenueCalculatorService(
            _mockBillRecordRevenueCalculatorServiceLogger.Object,
            _mockTimezoneService.Object);
    }

    #region Basic MRR Calculation Tests

    [Test]
    public void CompanyManagement_GetCompanyAnalytics_ShouldUseEnhancedMrrCalculation()
    {
        // Arrange
        var companyId = "test-company-analytics";
        var company = new Company
        {
            Id = companyId, TimeZoneInfoId = "Asia/Singapore"
        };

        _mockCompanyRepository.Setup(x => x.GetCompany(companyId))
            .ReturnsAsync(company);

        _mockTimezoneService.Setup(x => x.GetPreciseMonthDiff(
                It.IsAny<DateTime>(),
                It.IsAny<DateTime>(),
                companyId,
                It.IsAny<string?>()))
            .Returns(1.05m); // 5% variance for testing

        // Create test bill records
        var billRecords = CreateTestBillRecords(companyId, 100.00m);

        // Act
        var enhancedMrr =
            _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                billRecords,
                company.TimeZoneInfoId);

        // Assert
        enhancedMrr.Should().BeGreaterThan(0);
    }

    [Test]
    public void CompanyManagement_GetCompanyUsage_ShouldHandleTimezoneAwareCalculations()
    {
        // Arrange
        var companyId = "usage-calculation-company";
        var company = new Company
        {
            Id = companyId, TimeZoneInfoId = "America/New_York"
        };

        _mockCompanyRepository.Setup(x => x.GetCompany(companyId))
            .ReturnsAsync(company);

        _mockTimezoneService.Setup(x => x.GetPreciseMonthDiff(
                It.IsAny<DateTime>(),
                It.IsAny<DateTime>(),
                companyId,
                It.IsAny<string?>()))
            .Returns(0.98m); // Slightly less than 1 month

        var billRecords = CreateTestBillRecords(companyId, 150.00m);

        // Act
        var enhancedMrr =
            _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                billRecords,
                company.TimeZoneInfoId);

        // Assert
        enhancedMrr.Should().BeGreaterThan(0);
    }

    #endregion

    #region New Method Tests

    [Test]
    public void SumMonthlyRecurringRevenueWithTimezone_WithValidTimezone_ShouldCalculateCorrectly()
    {
        // Arrange
        var timezoneInfoId = "Asia/Singapore";
        var testDate = DateTime.UtcNow;
        var billRecords = CreateTestBillRecords("test-company", 1000.00m);

        // Act
        var result = _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
            billRecords,
            timezoneInfoId,
            testDate);

        // Assert
        result.Should().BeGreaterThan(0);
        // The result should be adjusted based on the timezone calculation
        // For a 30-day period, the monthly recurring revenue should be approximately 1000
        result.Should().BeApproximately(1000.00m, 100.00m); // Allow for some variance due to timezone calculations
    }

    [Test]
    public void SumMonthlyRecurringRevenueWithTimezone_WithMultipleBillRecords_ShouldSumAllRecords()
    {
        // Arrange
        var timezoneInfoId = "America/New_York";
        var testDate = DateTime.UtcNow;
        var billRecords = CreateTestBillRecords("test-company-1", 200.00m, 4);

        // Act
        var result = _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
            billRecords,
            timezoneInfoId,
            testDate);

        // Assert
        result.Should().BeGreaterThan(0);
        // Should include both subscription and add-on records
    }

    [Test]
    public void SumMonthlyRecurringRevenueWithTimezone_WithNoValidDate_ShouldUseCurrentDate()
    {
        // Arrange
        var timezoneInfoId = "Europe/London";
        var billRecords = CreateTestBillRecords("test-company", 100.00m);

        // Act
        var result = _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
            billRecords,
            timezoneInfoId);

        // Assert
        result.Should().BeGreaterThan(0);
        // Should use the current date as default
    }

    #endregion

    #region Payment Breakdown Tests

    [Test]
    public void GetPaymentBreakDowns_WithValidBillingPeriod_ShouldReturnCorrectBreakdown()
    {
        // Arrange
        var billingPeriodUsage = new CmsBillRecordDto
        {
            PayAmount = 1000,
            Currency = "usd",
            CmsSalesPaymentRecords =
            [
                new CmsSalesPaymentRecordDto
                {
                    SubscriptionFee = 500, Currency = "usd"
                }
            ]
        };

        var allValidBillingPeriodUsages = new List<BillRecord>();
        var timeZoneInfoId = "Asia/Singapore";

        // Act
        var result = _billRecordRevenueCalculatorService.GetPaymentBreakDowns(
            billingPeriodUsage,
            allValidBillingPeriodUsages,
            timeZoneInfoId);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCountGreaterThan(0);
        result.Should().Contain(x => x.Contains("Total:"));
        result.Should().Contain(x => x.Contains("Input Payment Subscription Fee:"));
    }

    [Test]
    public void GetPaymentBreakDowns_WithUpgradeScenario_ShouldIncludePreviousBillRemaining()
    {
        // Arrange
        var billingPeriodUsage = new CmsBillRecordDto
        {
            PayAmount = 2000,
            Currency = "usd",
            UpgradeFromBillRecordId = 1,
            CmsSalesPaymentRecords = new List<CmsSalesPaymentRecordDto>()
        };

        var allValidBillingPeriodUsages = new List<BillRecord>
        {
            new BillRecord
            {
                Id = 1,
                PayAmount = 1000,
                currency = "usd",
                PeriodStart = DateTime.UtcNow.AddDays(-60),
                PeriodEnd = DateTime.UtcNow.AddDays(-30),
                SubscriptionPlanId = "sleekflow_v10_startup"
            }
        };

        var timeZoneInfoId = "UTC";

        // Act
        var result = _billRecordRevenueCalculatorService.GetPaymentBreakDowns(
            billingPeriodUsage,
            allValidBillingPeriodUsages,
            timeZoneInfoId);

        // Assert
        result.Should().NotBeNull();
        result.Should().Contain(x => x.Contains("Previous Bill Remaining Payment MRR:"));
    }

    [Test]
    public void GetPaymentBreakDowns_WithDowngradeScenario_ShouldIncludePreviousBillRemaining()
    {
        // Arrange
        var billingPeriodUsage = new CmsBillRecordDto
        {
            PayAmount = 800,
            Currency = "usd",
            DowngradeFromBillRecordId = 1,
            CmsSalesPaymentRecords = new List<CmsSalesPaymentRecordDto>()
        };

        var allValidBillingPeriodUsages = new List<BillRecord>
        {
            new BillRecord
            {
                Id = 1,
                PayAmount = 1200,
                currency = "usd",
                PeriodStart = DateTime.UtcNow.AddDays(-60),
                PeriodEnd = DateTime.UtcNow.AddDays(-30),
                SubscriptionPlanId = "sleekflow_v10_startup"
            }
        };

        var timeZoneInfoId = "Europe/London";

        // Act
        var result = _billRecordRevenueCalculatorService.GetPaymentBreakDowns(
            billingPeriodUsage,
            allValidBillingPeriodUsages,
            timeZoneInfoId);

        // Assert
        result.Should().NotBeNull();
        result.Should().Contain(x => x.Contains("Previous Bill Remaining Payment MRR:"));
    }

    [Test]
    public void GetPaymentBreakDowns_WithNullBillingPeriod_ShouldReturnEmptyList()
    {
        // Arrange
        CmsBillRecordDto? billingPeriodUsage = null;
        var allValidBillingPeriodUsages = new List<BillRecord>();
        var timeZoneInfoId = "UTC";

        // Act
        var result = _billRecordRevenueCalculatorService.GetPaymentBreakDowns(
            billingPeriodUsage,
            allValidBillingPeriodUsages,
            timeZoneInfoId);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();
    }

    [Test]
    public void GetPaymentBreakDowns_WithIrregularPlan_ShouldHandleCorrectly()
    {
        // Arrange
        var billingPeriodUsage = new CmsBillRecordDto
        {
            PayAmount = 1000,
            Currency = "usd",
            IsIrregularPlan = true,
            CmsSalesPaymentRecords = new List<CmsSalesPaymentRecordDto>()
        };

        var allValidBillingPeriodUsages = new List<BillRecord>();
        var timeZoneInfoId = "Asia/Tokyo";

        // Act
        var result = _billRecordRevenueCalculatorService.GetPaymentBreakDowns(
            billingPeriodUsage,
            allValidBillingPeriodUsages,
            timeZoneInfoId);

        // Assert
        result.Should().NotBeNull();
        result.Should().Contain(x => x.Contains("Total:"));
        // Irregular plans should not include CMS payments in total
    }

    #endregion

    #region Advanced MRR Calculation Tests

    [Test]
    public void CompanyAnalytics_WithMultipleAddOns_ShouldUseConsistentTimezoneLogic()
    {
        // Arrange
        var companyId = "multi-addon-company";
        var company = new Company
        {
            Id = companyId, TimeZoneInfoId = "Europe/London"
        };

        _mockCompanyRepository.Setup(x => x.GetCompany(companyId))
            .ReturnsAsync(company);

        // Set up different month calculations for different periods (simulating add-ons)
        _mockTimezoneService.SetupSequence(x => x.GetPreciseMonthDiff(
                It.IsAny<DateTime>(),
                It.IsAny<DateTime>(),
                companyId,
                It.IsAny<string?>()))
            .Returns(1.0m) // Base subscription: exact month
            .Returns(0.75m) // Add-on 1: 3/4 month
            .Returns(1.25m) // Add-on 2: 1.25 months
            .Returns(2.0m); // Add-on 3: 2 months

        var billRecords = CreateTestBillRecords(companyId, 200.00m, 4);

        // Act
        var enhancedMrr =
            _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                billRecords,
                company.TimeZoneInfoId);

        // Assert
        enhancedMrr.Should().BeGreaterThan(0);
    }

    [Test]
    public void CrossTimezoneScenario_MultipleCompaniesInDifferentTimezones_ShouldCalculateCorrectly()
    {
        // Arrange
        var companies = new[]
        {
            new
            {
                Id = "company-asia", Timezone = "Asia/Tokyo", ExpectedVariance = 1.02m
            },
            new
            {
                Id = "company-europe", Timezone = "Europe/Berlin", ExpectedVariance = 0.98m
            },
            new
            {
                Id = "company-america", Timezone = "America/Los_Angeles", ExpectedVariance = 1.01m
            }
        };

        var allBillRecords = new List<BillRecord>();

        foreach (var companyData in companies)
        {
            var company = new Company
            {
                Id = companyData.Id, TimeZoneInfoId = companyData.Timezone
            };
            _mockCompanyRepository.Setup(x => x.GetCompany(companyData.Id))
                .ReturnsAsync(company);

            _mockTimezoneService.Setup(x => x.GetPreciseMonthDiff(
                    It.IsAny<DateTime>(),
                    It.IsAny<DateTime>(),
                    companyData.Id,
                    It.IsAny<string?>()))
                .Returns(companyData.ExpectedVariance);

            var companyBillRecords = CreateTestBillRecords(companyData.Id, 100.00m);
            allBillRecords.AddRange(companyBillRecords);
        }

        // Act
        var enhancedMrr =
            _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(allBillRecords, "UTC");

        // Assert
        enhancedMrr.Should().BeGreaterThan(0);
    }

    #endregion

    #region Error Handling and Fallback Tests

    [Test]
    public void CompanyAnalytics_WithTimezoneServiceFailure_ShouldFallbackGracefully()
    {
        // Arrange
        var companyId = "error-fallback-company";

        _mockCompanyRepository.Setup(x => x.GetCompany(companyId))
            .ThrowsAsync(new TimeoutException("Database timeout"));

        var billRecords = CreateTestBillRecords(companyId, 100.00m);

        // Act & Assert
        // The service should handle the exception gracefully by using UTC fallback
        var result = _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(billRecords, "UTC");

        // Should return a valid result despite the repository exception
        result.Should().BeGreaterThanOrEqualTo(0);
    }

    [Test]
    public void CompanyUsage_WithInvalidTimezone_ShouldUseUtcFallback()
    {
        // Arrange
        var companyId = "invalid-timezone-company";
        var company = new Company
        {
            Id = companyId, TimeZoneInfoId = "Invalid/Timezone"
        };

        _mockCompanyRepository.Setup(x => x.GetCompany(companyId))
            .ReturnsAsync(company);

        // Timezone service should handle invalid timezone gracefully
        _mockTimezoneService.Setup(x => x.GetPreciseMonthDiff(
                It.IsAny<DateTime>(),
                It.IsAny<DateTime>(),
                companyId,
                It.IsAny<string?>()))
            .Returns(1.0m); // UTC fallback calculation

        var billRecords = CreateTestBillRecords(companyId, 100.00m);

        // Act
        var enhancedMrr =
            _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                billRecords,
                company.TimeZoneInfoId);

        // Assert
        enhancedMrr.Should().BeGreaterThan(0);
    }

    #endregion

    #region Performance and Scalability Tests

    [Test]
    public void PerformanceImpact_HighVolumeCompanyAnalytics_ShouldCompleteWithinReasonableTime()
    {
        // Arrange
        var companyIds = Enumerable.Range(1, 50)
            .Select(i => $"performance-company-{i}")
            .ToArray();

        var allBillRecords = new List<BillRecord>();

        foreach (var companyId in companyIds)
        {
            var company = new Company
            {
                Id = companyId, TimeZoneInfoId = "UTC"
            };
            _mockCompanyRepository.Setup(x => x.GetCompany(companyId))
                .ReturnsAsync(company);

            _mockTimezoneService.Setup(x => x.GetPreciseMonthDiff(
                    It.IsAny<DateTime>(),
                    It.IsAny<DateTime>(),
                    companyId,
                    It.IsAny<string?>()))
                .Returns(1.0m);

            var companyBillRecords = CreateTestBillRecords(companyId, 100.00m);
            allBillRecords.AddRange(companyBillRecords);
        }

        // Act
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var enhancedMrr =
            _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(allBillRecords, "UTC");
        stopwatch.Stop();

        // Assert
        enhancedMrr.Should().BeGreaterThan(0);
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(5000); // Should complete within 5 seconds

        Console.WriteLine($"Processed {companyIds.Length} companies in {stopwatch.ElapsedMilliseconds}ms");
    }

    #endregion

    #region Data Consistency and Validation Tests

    [Test]
    public void DataConsistency_RepeatedCalculations_ShouldProduceSameResults()
    {
        // Arrange
        var companyId = "consistency-test-company";
        var company = new Company
        {
            Id = companyId, TimeZoneInfoId = "Asia/Hong_Kong"
        };

        _mockCompanyRepository.Setup(x => x.GetCompany(companyId))
            .ReturnsAsync(company);

        _mockTimezoneService.Setup(x => x.GetPreciseMonthDiff(
                It.IsAny<DateTime>(),
                It.IsAny<DateTime>(),
                companyId,
                It.IsAny<string?>()))
            .Returns(1.0156m); // Consistent precise value

        var billRecords = CreateTestBillRecords(companyId, 100.00m);

        // Act - Run the same calculation multiple times
        var results = new List<decimal>();
        for (var i = 0; i < 5; i++)
        {
            var result =
                _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                    billRecords,
                    "Asia/Hong_Kong");
            results.Add(result);
        }

        // Assert - All results should be identical
        for (var i = 1; i < results.Count; i++)
        {
            results[i].Should().Be(
                results[0],
                $"Result {i} should be identical to the first result for data consistency");
        }
    }

    #endregion

    #region Integration Test Helpers

    private static List<BillRecord> CreateTestBillRecords(string companyId, decimal amount, int count = 1)
    {
        var billRecords = new List<BillRecord>();
        var currentDate = DateTime.UtcNow;
        var baseDate = currentDate.AddDays(-15); // Started 15 days ago

        for (var i = 0; i < count; i++)
        {
            billRecords.Add(
                new BillRecord
                {
                    Id = i + 1,
                    CompanyId = companyId,
                    PayAmount = (double) amount,
                    PeriodStart = baseDate.AddDays(i * 30), // Each record spans 30 days
                    PeriodEnd = baseDate.AddDays((i + 1) * 30), // The end date is 30 days later
                    Status = BillStatus.Active,
                    SubscriptionPlanId = "sleekflow_pro", // Valid subscription plan ID
                    created = DateTime.UtcNow.AddDays(-1), // Add created date
                    currency = "usd",
                    CmsSalesPaymentRecords = []
                });
        }

        return billRecords;
    }

    #endregion
}