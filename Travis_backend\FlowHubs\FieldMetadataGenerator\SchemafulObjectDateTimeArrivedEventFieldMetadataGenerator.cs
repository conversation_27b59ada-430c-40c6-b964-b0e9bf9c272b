﻿#nullable enable
using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Sleekflow.Apis.CrmHub.Model;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.CustomObjectDomain.Services;
using Travis_backend.Database.Services;
using Travis_backend.Extensions;
using Travis_backend.FlowHubs.Constants;
using Travis_backend.FlowHubs.FieldMetadataGenerator.VariableTypeHelper;
using Travis_backend.FlowHubs.Models;
using Travis_backend.FlowHubs.Services;
using Travis_backend.Utils;

namespace Travis_backend.FlowHubs.FieldMetadataGenerator;

public sealed class SchemafulObjectDateTimeArrivedEventFieldMetadataGenerator
    : FlowHubEventFieldMetadataGeneratorBase,
        IFlowHubEventFieldMetadataGenerator
{
    private readonly ICustomObjectService _customObjectService;
    public string EventName => TriggerIds.CustomObjectPropertyDateAndTimeArrived;

    private readonly ILogger<SchemafulObjectDateTimeArrivedEventFieldMetadataGenerator> _logger;

    public Dictionary<string, string> FieldPathOptionRequestPathDict { get; } = new ()
    {
        {
            "contact.labels[*].LabelValue", "FlowHub/SelectOptions/Labels"
        },
        {
            "channel_id", "FlowHub/SelectOptions/Channels"
        }
    };

    public SchemafulObjectDateTimeArrivedEventFieldMetadataGenerator(
        IDbContextService dbContextService,
        IFieldMetadataVariableVisibilityService fieldMetadataVariableVisibilityService,
        ICustomObjectService customObjectService,
        ILogger<SchemafulObjectDateTimeArrivedEventFieldMetadataGenerator> logger)
        : base(dbContextService, fieldMetadataVariableVisibilityService)
    {
        _customObjectService = customObjectService;
        _logger = logger;
    }

    public async Task<(HashSet<FieldMetadata> FieldMetadataSet, Type EventBodyType, string ContactObjectName)>
        GenerateAsync(
            string companyId,
            Dictionary<string, object?>? parameters)
    {
        _logger.LogInformation("Generating metadata for schemaful object date/time arrived event, parameters: {Parameters}", JsonConvert.SerializeObject(parameters));
        var (userProfileDict, customFieldOptionsRequestPathDict) = await GenerateUserProfileDictAsync(companyId);

        var eventBody = JsonUtils.GenerateSampleObjectFromType<OnDateAndTimeArrivedCommonEventBody>();
        eventBody.Contact = userProfileDict;
        eventBody.WorkflowId = null!;
        eventBody.WorkflowVersionedId = null!;
        SchemaDto targetSchema = null;
        if (parameters != null && parameters.TryGetValue("schema_id", out var rawSchemaId)
                               && rawSchemaId is string schemaId
                               && !string.IsNullOrWhiteSpace(schemaId))
        {
            _logger.LogInformation("begin to get schema with schemaId {SchemaId}", schemaId);
            targetSchema = await _customObjectService.GetSchemaByIdAsync(
                companyId,
                schemaId);
            if (targetSchema != null)
            {
                _logger.LogInformation("schema with schemaId {SchemaId} is: {SchemaName}", schemaId, targetSchema.UniqueName);
                var eventBodyPropertyValues = targetSchema.Properties
                    .Select(x => new KeyValuePair<string, object>(x.UniqueName, x.DisplayName))
                    .ToDictionary();
                _logger.LogInformation("property value with schemaId {SchemaId} are: {PropertyValues}", schemaId, JsonConvert.SerializeObject(eventBodyPropertyValues));
                eventBody.PropertyValues = eventBodyPropertyValues;
            }
        }

        var fieldMetadataSet = JsonUtils
            .SimplifyJsonData(eventBody.ToJson())
            .GetFieldMetadata();
        if (targetSchema != null)
        {
            foreach (var property in targetSchema.Properties)
            {
                var metadataSet = fieldMetadataSet.Where(
                        x =>
                            (x.FieldPath.StartsWith("property_values.") ||
                             x.FieldPath.StartsWith("pre_updated_property_values.") ||
                             x.FieldPath.StartsWith("post_updated_property_values."))
                            && x.FieldPath.EndsWith(property.UniqueName)
                            && x.FieldPath.Split(".").LastOrDefault() == property.UniqueName)
                    .ToHashSet();

                if (metadataSet.Count == 0)
                {
                    continue;
                }

                foreach (var metadata in metadataSet)
                {
                    metadata.FieldType = SchemafulObjectDataTypeMapper.MapToFieldType(property.DataType.Name);

                    if (property.DataType.Name is "multiple_choice")
                    {
                        metadata.FieldPath += "[*]";
                        metadata.DisplayPath += "/[*]";
                    }

                    metadata.Options = property.Options?
                        .OrderBy(x => x.DisplayOrder)
                        .Select(
                            x => new NeedConfigOption(
                                x.Id,
                                x.Value,
                                x.Id))
                        .ToList();
                }
            }

            fieldMetadataSet.RemoveWhere(x => x.FieldType == "unknown");
        }

        PopulateOptionRequestPath(
            fieldMetadataSet,
            customFieldOptionsRequestPathDict,
            FieldPathOptionRequestPathDict);

        return (fieldMetadataSet, typeof(OnDateAndTimeArrivedCommonEventBody), nameof(eventBody.Contact));
    }
}