﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Hangfire;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Polly;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.ChannelDomain.ViewModels;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.ConversationServices;
using Travis_backend.CoreDomain.Models;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Helpers;
using Travis_backend.InternalDomain.Models;
using Travis_backend.InternalDomain.ViewModels;
using Travis_backend.InternalIntegrationHubDomain.Services;
using Travis_backend.ResellerDomain.Models;
using Travis_backend.TenantHubDomain.Services;

namespace Travis_backend.InternalDomain.Services
{
    public interface IInternalHubSpotService
    {
        ValueTask<bool> SyncCompany(string companyId, DateTime? skipAfterLastSyncAt = null);

        ValueTask<bool> SyncCompany(CmsCompanyDetail companyDetail, DateTime? skipAfterLastSyncAt = null);

        ValueTask<bool> MarkCompanyAsDeleted(string companyId);

        ValueTask<bool> SyncCompanyStaffs(string companyId);

        ValueTask<bool> SyncCompanyStaff(string companyId, long staffId);

        ValueTask<bool> UpdateAndSyncAllCompanies();

        ValueTask<bool> SyncAllCompaniesAndStaffs();

        ValueTask<bool> UpdateContactOwnerFromWebhook(SleekflowWebhookPayload payload, string hubSpotContactOwnerId);

        ValueTask<bool> SetCompanyOwnerUpgradeToProPlanFlag(string companyId);

        ValueTask<bool> SetCompanyOwnerUpgradeToPremiumPlanFlag(string companyId);

        ValueTask<bool> SyncCompanyOwnerFromHubspot(string companyId);

        #region Internal Contact Owner

        ValueTask<List<CmsHubSpotContactOwnerMap>> SyncInternalContactOwnerMapping();

        Task<List<string>> GetHubSpotTeamNames();

        #endregion

        #region New Signup

        ValueTask<bool> CreateNewSignupCompanyAndStaff(string companyId, string userEmail);

        ValueTask<bool> CreateNewResellerSignupCompanyAndStaff(string companyId);

        ValueTask<bool> UpdateContactAsNewSignupUser(string userEmail);

        #endregion

        #region Website

        ValueTask<bool> SubmitLeadFormToHubSpot(CreateLeadFormTicketViewModel request);

        ValueTask<bool> SubmitPartnershipFormToHubSpot(PartnershipTicketViewModel request);

        ValueTask<bool> SubmitPartnershipOfferFormToHubSpot(PartnershipOfferFormViewModel request);

        ValueTask<bool> SubmitSubscribeNewsletterFromToHubSpot(SubscribeNewsletterViewModel request);

        #endregion

        #region WhatsApp Application

        ValueTask<bool> CreateWhatsAppApplicationTicket(
            WhatsappRegistrationWithoutFileViewModel applyWhatsappViewModel,
            long cmsWhatsappApplicationId,
            long? staffId = null);

        ValueTask<bool> UpdateWhatsAppApplicationTicket(
            string ticketId,
            CmsWhatsappApplicationStep? step = null,
            bool? isVerified = null,
            bool? hasToppedUp = null);

        #endregion

        #region Cancel Ticket

        ValueTask<bool> CreateSubscriptionPlanCancelTicket(string companyId, long staffId, CannelReason reason);

        #endregion

        #region Analytic Data

        ValueTask<bool> UpdateLastMonthCompanyBreakdownStatuses(List<string> companyIds = null);

        #endregion

        #region WhatsApp Cloud API Usage

        ValueTask<bool> UpdateAndSyncAllCloudApiCredits();

        #endregion

        #region Subscription Cancellation

        ValueTask<bool> UpdateCompanyChurnInfomation(string companyId, string churnReason, string churnNote);

        #endregion

    }

    public class InternalHubSpotService : IInternalHubSpotService
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly ILogger<InternalHubSpotService> _logger;
        private readonly IInternalHubspotRepository _internalHubspotRepository;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IConfiguration _configuration;
        private readonly IInternalCompanyDataRepository _internalCompanyDataRepository;
        private readonly IInternalAnalyticService _internalAnalyticService;
        private readonly IInternalWhatsappCloudApiService _internalWhatsappCloudApiService;
        private readonly ICompaniesService _companiesService;

        private readonly IBillRecordRevenueCalculatorService _billRecordRevenueCalculatorService;

        public InternalHubSpotService(
            ApplicationDbContext appDbContext,
            ILogger<InternalHubSpotService> logger,
            IInternalHubspotRepository internalHubspotRepository,
            UserManager<ApplicationUser> userManager,
            IConfiguration configuration,
            IInternalCompanyDataRepository internalCompanyDataRepository,
            IInternalAnalyticService internalAnalyticService,
            IInternalWhatsappCloudApiService internalWhatsappCloudApiService,
            ICompaniesService companiesService,
            IBillRecordRevenueCalculatorService billRecordRevenueCalculatorService)
        {
            _appDbContext = appDbContext;
            _logger = logger;
            _internalHubspotRepository = internalHubspotRepository;
            _userManager = userManager;
            _configuration = configuration;
            _internalCompanyDataRepository = internalCompanyDataRepository;
            _internalAnalyticService = internalAnalyticService;
            _internalWhatsappCloudApiService = internalWhatsappCloudApiService;
            _companiesService = companiesService;
            _billRecordRevenueCalculatorService = billRecordRevenueCalculatorService;
        }

        private bool CheckIsHubSpotEnable()
        {
            return !string.IsNullOrWhiteSpace(_configuration["HubSpot:InternalHubSpotApiKey"]);
        }

        public async ValueTask<bool> SyncCompany(string companyId, DateTime? skipAfterLastSyncAt = null)
        {
            if (!CheckIsHubSpotEnable() || companyId == null)
            {
                return false;
            }

            var companyDetail = (await _internalCompanyDataRepository.GetCmsCompanyDetails(
                new List<string>
                {
                    companyId
                })).FirstOrDefault();

            if (companyDetail == null)
            {
                return false;
            }

            return await SyncCompany(companyDetail, skipAfterLastSyncAt);
        }

        public async ValueTask<bool> SyncCompany(CmsCompanyDetail companyDetail, DateTime? skipAfterLastSyncAt = null)
        {
            if (!CheckIsHubSpotEnable() || companyDetail == null)
            {
                return false;
            }

            if (IsSkipSyncCompany(companyDetail.CmsHubSpotCompanyMap, skipAfterLastSyncAt))
            {
                return false;
            }

            var hubspotCompany = CmsCompanyDetailConverter.ConvertToHubSpotCompany(companyDetail, _billRecordRevenueCalculatorService);

            // Find Existing company by user email domain
            var domainParseResult = EmailDomainNameHelper.Parse(companyDetail.Owner?.Identity?.Email);

            InternalHubSpotCompany parentCompanyObject = null;
            InternalHubSpotContact hubspotContact = null;
            bool isNewlyCreatedHubspotContact = false;

            // Check if company is not mapped for Sql Server & HubSpot
            if (companyDetail.CmsHubSpotCompanyMap == null &&
                !string.IsNullOrEmpty(companyDetail.Owner?.Identity?.Email))
            {
                hubspotContact =
                    await _internalHubspotRepository.GetContactObjectIdByEmailAsync(companyDetail.Owner.Identity.Email);

                // Insert / Update Owner Contact data
                if (hubspotContact == null)
                {
                    var ownerContactObject = await _internalHubspotRepository.CreateContactObjectAsync(
                        new InternalHubSpotContact()
                        {
                            Email = companyDetail.Owner.Identity.Email,
                            FirstName = companyDetail.Owner.Identity.FirstName,
                            LastName = companyDetail.Owner.Identity.LastName,
                            Website = companyDetail.CompanyWebsite,
                            NumberOfEmployees = companyDetail.CompanySize,
                            EComPlatform = companyDetail?.PlatformUsageIntent,
                            IsAgreeMarketingConsent = companyDetail.Owner.Identity.IsAgreeMarketingConsent,
                        });
                    isNewlyCreatedHubspotContact = true;
                }
                else
                {
                    hubspotContact = await _internalHubspotRepository.UpdateContactObjectAsync(
                        new InternalHubSpotContact()
                        {
                            Id = hubspotContact.Id,
                            Website = companyDetail.CompanyWebsite,
                            NumberOfEmployees = companyDetail.CompanySize?.Replace(" ", string.Empty),
                            EComPlatform = companyDetail?.PlatformUsageIntent,
                            IsAgreeMarketingConsent = companyDetail.Owner.Identity.IsAgreeMarketingConsent,
                        });
                }

                #region Get Or Create Parent Company

                // Use Parent Company Base Partner Stack Lead Key
                if (!isNewlyCreatedHubspotContact && hubspotContact != null)
                {
                    if (companyDetail.CompanyType == CompanyType.Reseller && hubspotContact.PartnerStackPartnerKey != null)
                    {
                        parentCompanyObject =
                            await _internalHubspotRepository.GetCompanyObjectByPartnerStackPartnerKeyAsync(
                                hubspotContact.PartnerStackPartnerKey);
                    }
                    else if (companyDetail.CompanyType == CompanyType.ResellerClient && hubspotContact.PartnerStackDealKey != null)
                    {
                        parentCompanyObject =
                            await _internalHubspotRepository.GetCompanyObjectByPartnerStackDealKeyAsync(
                                hubspotContact.PartnerStackDealKey);
                    }
                    else if (hubspotContact.PartnerStackLeadKey != null)
                    {
                        parentCompanyObject =
                            await _internalHubspotRepository.GetCompanyObjectByPartnerStackLeadKeyAsync(
                                hubspotContact.PartnerStackLeadKey);
                    }
                }

                // Use Hubspot Company create from HubSpot Company Insight via contact email domain
                if (domainParseResult is { IsCommonEmailProviderDomain: false } && parentCompanyObject == null)
                {
                    parentCompanyObject =
                        await _internalHubspotRepository.GetCompanyObjectIdByDomainAsync(domainParseResult.Domain);

                    // try create staff then the company object again
                    if (parentCompanyObject == null)
                    {
                        // Let HubSpot Company Insight Create company by domain
                        await Policy
                            .Handle<Exception>()
                            .WaitAndRetryAsync(
                                5,
                                sleepDurationProvider: i => TimeSpan.FromSeconds(10),
                                onRetry: (exception, retryCount) =>
                                {
                                    _logger.LogError(
                                        exception,
                                        "[HubSpot {MethodName}] Fetch company object id by email domain {Domain} error, Retry: {RetryCount}, Message:{ExceptionMessage}",
                                        nameof(SyncCompany),
                                        domainParseResult.Domain,
                                        retryCount,
                                        exception.Message);
                                })
                            .ExecuteAndCaptureAsync(
                                async () =>
                                {
                                    parentCompanyObject =
                                        await _internalHubspotRepository.GetCompanyObjectIdByDomainAsync(
                                            domainParseResult.Domain);

                                    if (parentCompanyObject == null)
                                    {
                                        throw new Exception(
                                            "Cannot get HubSpot generated Company Object Id from Owner Email Domain!");
                                    }
                                });
                    }
                }
                #endregion
            }

            CmsHubSpotCompanyMap hubSpotParentCompanyMap = null;

            if (parentCompanyObject != null)
            {
                hubSpotParentCompanyMap =
                    await _appDbContext.CmsHubSpotCompanyMaps.FirstOrDefaultAsync(
                        x => x.HubSpotCompanyObjectId == parentCompanyObject.Id);
            }

            var createAsParentCompany = hubSpotParentCompanyMap == null && parentCompanyObject != null &&
                                        string.IsNullOrWhiteSpace(parentCompanyObject.SleekflowCompanyId);
            var createAsChildCompany = parentCompanyObject != null && (hubSpotParentCompanyMap != null ||
                                                                       !string.IsNullOrWhiteSpace(
                                                                           parentCompanyObject.SleekflowCompanyId));

            if (createAsParentCompany)
            {
                hubspotCompany.Id = parentCompanyObject.Id;
                hubspotCompany.Name = null;

                var syncedHubSpotCompanyObject = await SyncHubSpotCompanyObjectToHubSpot(companyDetail, hubspotCompany);
            }
            else if (createAsChildCompany)
            {
                hubspotCompany.HubSpotOwnerId = parentCompanyObject.HubSpotOwnerId;

                var syncedHubSpotCompanyObject = await SyncHubSpotCompanyObjectToHubSpot(companyDetail, hubspotCompany);

                await _internalHubspotRepository.AssociateChildCompanyToParentCompanyAsync(
                    syncedHubSpotCompanyObject,
                    parentCompanyObject);
            }
            else
            {
                if (domainParseResult is { IsCommonEmailProviderDomain: false })
                {
                    hubspotCompany.Domain = domainParseResult.Domain;
                }

                var syncedHubSpotCompanyObject = await SyncHubSpotCompanyObjectToHubSpot(companyDetail, hubspotCompany);
            }

            return true;
        }

        public async ValueTask<bool> MarkCompanyAsDeleted(string companyId)
        {
            if (!CheckIsHubSpotEnable() || companyId == null)
            {
                return false;
            }

            var hubSpotCompany = await _internalHubspotRepository.GetCompanyObjectIdByCompanyIdAsync(companyId);

            if (hubSpotCompany != null)
            {
                await _internalHubspotRepository.UpdateCompanyObjectAsync(
                    new InternalHubSpotCompany()
                    {
                        Id = hubSpotCompany.Id,
                        IsDeletedFromSleekflow = true,
                    });
            }

            return true;
        }

        private bool IsSkipSyncCompany(CmsHubSpotCompanyMap cmsHubSpotCompanyMap, DateTime? skipAfterLastSyncAt)
        {
            if (!skipAfterLastSyncAt.HasValue || cmsHubSpotCompanyMap == null)
            {
                return false;
            }

            return cmsHubSpotCompanyMap.LastSyncAt > skipAfterLastSyncAt;
        }

        private async Task<InternalHubSpotCompany> SyncHubSpotCompanyObjectToHubSpot(
            CmsCompanyDetail company,
            InternalHubSpotCompany hubspotCompany)
        {
            var updatedHubSpotCompany =
                await _internalHubspotRepository.CreateOrUpdateCompanyObjectAsync(hubspotCompany);

            if (company.CmsHubSpotCompanyMap != null)
            {
                // Update Mapping
                var cmsHubSpotCompanyMap =
                    await _appDbContext.CmsHubSpotCompanyMaps.FirstAsync(x => x.Id == company.CmsHubSpotCompanyMap.Id);
                cmsHubSpotCompanyMap.LastSyncAt = DateTime.UtcNow;

                if (updatedHubSpotCompany.Id != cmsHubSpotCompanyMap.HubSpotCompanyObjectId)
                {
                    cmsHubSpotCompanyMap.HubSpotCompanyObjectId = updatedHubSpotCompany.Id;
                }

                await _appDbContext.SaveChangesAsync();
            }

            return updatedHubSpotCompany;
        }

        public async ValueTask<bool> SyncCompanyStaffs(string companyId)
        {
            if (!CheckIsHubSpotEnable() || companyId == null)
            {
                return false;
            }

            var hubSpotCompanyMap = await _appDbContext.CmsHubSpotCompanyMaps
                .Include(x => x.Company)
                .Where(x => x.CompanyId == companyId)
                .FirstOrDefaultAsync();

            if (hubSpotCompanyMap == null)
            {
                return false;
            }

            var company = await _appDbContext.CompanyCompanies
                .AsSplitQuery()
                .AsNoTracking()
                .Include(x => x.ResellerClientCompanyProfile)
                .Where(x => x.Id == companyId)
                .FirstOrDefaultAsync();

            List<Staff> staffs;

            if (company.CompanyType == CompanyType.ResellerClient)
            {
                var resellerStaffs = await _appDbContext.ResellerStaffs
                    .AsNoTracking()
                    .Where(rs => rs.ResellerCompanyProfileId == company.ResellerClientCompanyProfile.ResellerCompanyProfileId)
                    .Select(rs => new ResellerStaff
                    {
                        IdentityId = rs.IdentityId,
                        ResellerCompanyProfileId = rs.ResellerCompanyProfileId
                    })
                    .ToListAsync();

                staffs = await _appDbContext.UserRoleStaffs
                    .Include(x => x.Identity)
                    .Where(
                        x => x.CompanyId == companyId &&
                             !resellerStaffs
                                 .Select(rs => rs.IdentityId)
                                 .ToList()
                                 .Contains(x.IdentityId))
                    .ToListAsync();
            }
            else
            {
                staffs = await _appDbContext.UserRoleStaffs
                    .Include(x => x.Identity)
                    .Where(x => x.CompanyId == companyId)
                    .ToListAsync();
            }

            var owner = staffs
                .OrderBy(x => x.Order)
                .ThenBy(x => x.Id)
                .First();

            var validEmails = staffs.Select(s => s.Identity.Email).Where(e => !string.IsNullOrWhiteSpace(e)).ToList();

            var hubSpotUserContactMaps = await _appDbContext.CmsHubSpotUserContactMaps
                .Where(x => validEmails.Contains(x.Email))
                .ToListAsync();

            foreach (var staff in staffs)
            {
                if (string.IsNullOrWhiteSpace(staff.Identity.Email))
                {
                    continue;
                }

                var hubSpotContactPayload = new InternalHubSpotContact()
                {
                    Email = staff.Identity.Email,
                    FirstName = staff.Identity.FirstName,
                    LastName = staff.Identity.LastName,
                    Phone = staff.Identity.PhoneNumber,
                    IsSleekflowEmailConfirmed = staff.Identity.EmailConfirmed,
                    IsSleekflowUser = true,
                    IsNewSignupUser = false,
                    SleekflowUserRole = InternalHubSpotContact.Property.SleekflowUserRoleValue.Parse(staff.RoleType),
                    LeadType = owner.Id == staff.Id
                        ? InternalHubSpotContact.Property.LeadTypeValue.Owner
                        : InternalHubSpotContact.Property.LeadTypeValue.InvitedUser,
                    IsAgreeMarketingConsent = staff.Identity.IsAgreeMarketingConsent,
                };

                var hubSpotUserContactMap = hubSpotUserContactMaps.FirstOrDefault(x => x.Email == staff.Identity.Email);

                // Not in Mapping
                if (hubSpotUserContactMap == null)
                {
                    var hubspotContactId =
                        await _internalHubspotRepository.GetContactObjectIdByEmailAsync(staff.Identity.Email);

                    if (hubspotContactId != null)
                    {
                        // Existing Contact
                        hubSpotContactPayload.Id = hubspotContactId.Id;

                        await _internalHubspotRepository.UpdateContactObjectAsync(
                            hubSpotContactPayload,
                            new InternalHubSpotCompany()
                            {
                                Id = hubSpotCompanyMap.HubSpotCompanyObjectId
                            });
                    }
                    else
                    {
                        // New Contact
                        hubSpotContactPayload.Country = hubSpotCompanyMap.Company.CompanyCountry;
                        hubSpotContactPayload.JobTitle = staff.Position;

                        var createdHubSpotContact = await _internalHubspotRepository.CreateContactObjectAsync(
                            hubSpotContactPayload,
                            new InternalHubSpotCompany()
                            {
                                Id = hubSpotCompanyMap.HubSpotCompanyObjectId
                            });
                    }
                }

                // In Mapping
                else
                {
                    hubSpotContactPayload.Id = hubSpotUserContactMap.HubSpotContactObjectId;

                    var updatedHubSpotContact = await _internalHubspotRepository.UpdateContactObjectAsync(
                        hubSpotContactPayload,
                        new InternalHubSpotCompany()
                        {
                            Id = hubSpotCompanyMap.HubSpotCompanyObjectId
                        });
                }
            }

            return true;
        }

        public async ValueTask<bool> SyncCompanyStaff(string companyId, long staffId)
        {
            if (!CheckIsHubSpotEnable() || companyId == null)
            {
                return false;
            }

            var hubSpotCompanyMap = await _appDbContext.CmsHubSpotCompanyMaps
                .Include(x => x.Company)
                .Where(x => x.CompanyId == companyId)
                .FirstOrDefaultAsync();

            if (hubSpotCompanyMap == null)
            {
                return false;
            }

            var staff = await _appDbContext.UserRoleStaffs
                .Include(x => x.Identity)
                .Where(x => x.CompanyId == companyId && x.Id == staffId)
                .FirstAsync();

            if (string.IsNullOrWhiteSpace(staff.Identity.Email))
            {
                return false;
            }

            var ownerId = await _appDbContext.UserRoleStaffs
                .Include(x => x.Identity)
                .Where(x => x.CompanyId == companyId)
                .OrderBy(x => x.Order)
                .ThenBy(x => x.Id)
                .Select(x => x.Id)
                .FirstAsync();

            var hubSpotUserContactMaps = await _appDbContext.CmsHubSpotUserContactMaps
                .Where(x => staff.Identity.Email == x.Email)
                .ToListAsync();

            var hubSpotContactPayload = new InternalHubSpotContact()
            {
                Email = staff.Identity.Email,
                FirstName = staff.Identity.FirstName,
                LastName = staff.Identity.LastName,
                Phone = staff.Identity.PhoneNumber,
                IsSleekflowEmailConfirmed = staff.Identity.EmailConfirmed,
                IsSleekflowUser = true,
                IsNewSignupUser = false,
                SleekflowUserRole = InternalHubSpotContact.Property.SleekflowUserRoleValue.Parse(staff.RoleType),
                LeadType = ownerId == staff.Id
                    ? InternalHubSpotContact.Property.LeadTypeValue.Owner
                    : InternalHubSpotContact.Property.LeadTypeValue.InvitedUser,
                IsAgreeMarketingConsent = staff.Identity.IsAgreeMarketingConsent
            };

            var hubSpotUserContactMap = hubSpotUserContactMaps.FirstOrDefault(x => x.Email == staff.Identity.Email);

            // Not in Mapping
            if (hubSpotUserContactMap == null)
            {
                var hubspotContactId =
                    await _internalHubspotRepository.GetContactObjectIdByEmailAsync(staff.Identity.Email);

                if (hubspotContactId != null)
                {
                    // Existing Contact
                    hubSpotContactPayload.Id = hubspotContactId.Id;

                    await _internalHubspotRepository.UpdateContactObjectAsync(
                        hubSpotContactPayload,
                        new InternalHubSpotCompany()
                        {
                            Id = hubSpotCompanyMap.HubSpotCompanyObjectId
                        });
                }
                else
                {
                    // New Contact
                    hubSpotContactPayload.Country = hubSpotCompanyMap.Company.CompanyCountry;
                    hubSpotContactPayload.JobTitle = staff.Position;

                    var createdHubSpotContact = await _internalHubspotRepository.CreateContactObjectAsync(
                        hubSpotContactPayload,
                        new InternalHubSpotCompany()
                        {
                            Id = hubSpotCompanyMap.HubSpotCompanyObjectId
                        });
                }
            }

            // In Mapping
            else
            {
                hubSpotContactPayload.Id = hubSpotUserContactMap.HubSpotContactObjectId;

                var updatedHubSpotContact = await _internalHubspotRepository.UpdateContactObjectAsync(
                    hubSpotContactPayload,
                    new InternalHubSpotCompany()
                    {
                        Id = hubSpotCompanyMap.HubSpotCompanyObjectId
                    });
            }

            return true;
        }

        public async ValueTask<bool> SyncAllCompaniesAndStaffs()
        {
            if (!CheckIsHubSpotEnable())
            {
                return false;
            }

            var startAt = DateTime.UtcNow;

            var succeedCount = 0;
            var errorCount = 0;
            var errorCompanyIds = new List<string>();

            var companyDetails = await _internalCompanyDataRepository.GetCmsCompanyDetails();

            foreach (var company in companyDetails)
            {
                if (IsSkipSyncCompany(company.CmsHubSpotCompanyMap, DateTime.UtcNow.AddHours(24)))
                {
                    continue;
                }

                var executeResult = await Policy
                    .Handle<Exception>()
                    .WaitAndRetryAsync(
                        2,
                        sleepDurationProvider: i => TimeSpan.FromSeconds(10),
                        onRetry: (exception, retryCount) =>
                        {
                            _logger.LogError(
                                exception,
                                "[{MethodName}] Error: Company Id: {CompanyId}, Message: {ExceptionMessage}",
                                nameof(SyncAllCompaniesAndStaffs),
                                company.CompanyId,
                                exception.Message);
                        })
                    .ExecuteAndCaptureAsync(
                        async () =>
                        {
                            await SyncCompany(company);
                            await SyncCompanyStaffs(company.CompanyId);
                            succeedCount++;
                        });

                // Mark error item to logger
                if (executeResult.FinalException != null)
                {
                    errorCount++;
                    errorCompanyIds.Add(company.CompanyId);
                }
            }

            // Insert Log
            _logger.LogInformation(
                $"SyncAllCompaniesAndStaffs Completed:\n" +
                $"--------------------------------------------\n" +
                $"Total Company: {companyDetails.Count}\n" +
                $"Sync Succeed: {succeedCount}\n" +
                $"Sync Error: {errorCount}\n\n");

            _logger.LogInformation($"HubSpot Sync Error Company:\n {string.Join(", ", errorCompanyIds)}");

            _appDbContext.CmsHubSpotCompanySyncHistories.Add(
                new CmsHubSpotCompanySyncHistory()
                {
                    StartAt = startAt,
                    TotalCount = companyDetails.Count,
                    ErrorCount = errorCount,
                    SucceedCount = succeedCount,
                    Remark = errorCompanyIds.Count > 0 ? string.Join(", ", errorCompanyIds) : null,
                });

            await _appDbContext.SaveChangesAsync();

            return true;
        }

        public async ValueTask<bool> UpdateAndSyncAllCompanies()
        {
            if (!CheckIsHubSpotEnable())
            {
                return false;
            }

            var startAt = DateTime.UtcNow;

            // Total
            var succeedCount = 0;
            var errorCount = 0;
            var errorCompanyIds = new List<string>();

            // Batch Update
            var batchUpdateSucceedCount = 0;
            var batchUpdateErrorCount = 0;
            var batchUpdateErrorCompanyIds = new List<string>();

            // Company Without Mapping
            var companyWithoutMappingSucceedCount = 0;
            var companyWithoutMappingErrorCount = 0;
            var companyWithoutMappingErrorCompanyIds = new List<string>();

            var companySnapshotInPastDay =
                await _internalCompanyDataRepository.GetCompanySnapshotDataAsync(
                    DateTime.UtcNow.Date.AddDays(-1).AddHours(8));

            var allCompanyIds = await _appDbContext.CompanyCompanies
                .AsNoTracking()
                .Where(c => c.IsDeleted == false)
                .Select(c => c.Id)
                .ToListAsync();

            // Remove migrated companies
            var serverLocation = _configuration["SF_REGION"] ?? LocationNames.EastAsia;

            var deduplicationCompanyIdServerLocationLookup =
                await _companiesService.GetDeduplicationCompanyIdServerLocationLookup(serverLocation);

            allCompanyIds = serverLocation == LocationNames.EastAsia
                ? allCompanyIds.Where(x => !deduplicationCompanyIdServerLocationLookup.ContainsKey(x)).ToList()
                : allCompanyIds.Where(x => deduplicationCompanyIdServerLocationLookup.ContainsKey(x)).ToList();

            var companyDetails = new List<CmsCompanyDetail>();

            foreach (var chunk in allCompanyIds.Chunk(100))
            {
                var chunkDetails = await _internalCompanyDataRepository.GetCmsCompanyDetails(chunk.ToList(), companySnapshotInPastDay);
                companyDetails.AddRange(chunkDetails);
            }

            // Bulk Update
            var companyWithMapping = companyDetails.Where(x => x.CmsHubSpotCompanyMap != null);

            var batchUpdateCompanies = companyWithMapping
                .Select(x => CmsCompanyDetailConverter.ConvertToHubSpotCompany(x, _billRecordRevenueCalculatorService))
                .ToList();

            // Flow builder Usage
            try
            {
                var flowBuilderUsageListSnapshot =
                    await _internalCompanyDataRepository.GetCmsCompanyFlowBuilderUsageSnapshotAsync(
                        DateTime.Now.Date.AddDays(-1));

                foreach (var flowBuilderUsage in flowBuilderUsageListSnapshot)
                {
                    var companyDetail = batchUpdateCompanies.Find(x => x.SleekflowCompanyId == flowBuilderUsage.CompanyId);

                    if (companyDetail == null)
                    {
                        continue;
                    }

                    companyDetail.WorkflowCount = flowBuilderUsage.WorkflowCount;
                    companyDetail.ActiveWorkflowCount = flowBuilderUsage.ActiveWorkflowCount;
                    companyDetail.DailyWorkflowExecutionCount = flowBuilderUsage.DailyWorkflowExecutionCount;
                }
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error fetching and populating flow builder usage data: {Message}", e.Message);
            }

            var batchUpdateCompaniesChucks = batchUpdateCompanies.Chunk(80);

            foreach (var chuck in batchUpdateCompaniesChucks)
            {
                try
                {
                    var response = await _internalHubspotRepository.BulkUpdateCompanyObjectAsync(chuck);

                    succeedCount += response.SucceedCount;
                    batchUpdateSucceedCount += response.SucceedCount;

                    if (response.ErrorCompanyIds.Count == 0)
                    {
                        continue;
                    }

                    errorCount += response.ErrorCompanyIds.Count;
                    batchUpdateErrorCount += response.ErrorCompanyIds.Count;

                    errorCompanyIds.AddRange(response.ErrorCompanyIds);
                    batchUpdateErrorCompanyIds.AddRange(response.ErrorCompanyIds);
                }
                catch (Exception e)
                {
                    _logger.LogError(e, "Error bulk update company object to HubSpot: {Message}", e.Message);
                    errorCount += chuck.Length;
                    batchUpdateErrorCount += chuck.Length;

                    var errorIds = chuck.Select(c => c.SleekflowCompanyId).ToList();
                    errorCompanyIds.AddRange(errorIds);
                    batchUpdateErrorCompanyIds.AddRange(errorIds);
                }
            }

            // Create new Company Individually
            var companyWithoutMapping = companyDetails
                .Where(x => x.CmsHubSpotCompanyMap == null)
                .ToList();

            foreach (var company in companyWithoutMapping)
            {
                if (IsSkipSyncCompany(company.CmsHubSpotCompanyMap, DateTime.UtcNow.AddHours(20)))
                {
                    continue;
                }

                var executeResult = await Policy
                    .Handle<Exception>()
                    .WaitAndRetryAsync(
                        2,
                        sleepDurationProvider: i => TimeSpan.FromSeconds(10),
                        onRetry: (exception, retryCount) =>
                        {
                            _logger.LogError(
                                exception,
                                "[{MethodName}] Error: Company Id: {CompanyId}, Message: {ExceptionMessage}",
                                nameof(UpdateAndSyncAllCompanies),
                                company.CompanyId,
                                exception.Message);
                        })
                    .ExecuteAndCaptureAsync(
                        async () =>
                        {
                            await SyncCompany(company);
                            await SyncCompanyStaffs(company.CompanyId);
                            succeedCount++;
                            companyWithoutMappingSucceedCount++;
                        });

                // Mark error item to logger
                if (executeResult.FinalException == null)
                {
                    continue;
                }

                errorCount++;
                errorCompanyIds.Add(company.CompanyId);

                companyWithoutMappingErrorCount++;
                companyWithoutMappingErrorCompanyIds.Add(company.CompanyId);
            }

            // Insert Log
            // Total
            _logger.LogInformation(
                "SyncAllCompaniesAndStaffs Completed:\n" +
                "--------------------------------------------\n" +
                "Total Company: {TotalCompany}\n" +
                "Sync Succeed: {SucceedCount}\n" +
                "Sync Error: {ErrorCount}\n\n",
                companyDetails.Count,
                succeedCount,
                errorCount);

            _logger.LogInformation("HubSpot Sync Error Company:\n {ErrorCompanyIds}", string.Join(", ", errorCompanyIds));

            // Batch Update
            _logger.LogInformation(
                "Batch Update:\n" +
                "Total: {TotalCount}\n" +
                "Succeed: {SucceedCount}\n" +
                "Error: {ErrorCount}\n\n",
                batchUpdateCompanies.Count,
                batchUpdateSucceedCount,
                batchUpdateErrorCount);

            _logger.LogInformation("Batch Update HubSpot Sync Error Company:\n {ErrorCompanyIds}", string.Join(", ", batchUpdateErrorCompanyIds));

            // Company Without Mapping
            _logger.LogInformation(
                "Company Without Mapping:\n" +
                "Total: {TotalCount}\n" +
                "Succeed: {SucceedCount}\n" +
                "Error: {ErrorCount}\n\n",
                companyWithoutMapping.Count,
                companyWithoutMappingSucceedCount,
                companyWithoutMappingErrorCount);

            _logger.LogInformation("Company Without Mapping HubSpot Sync Error Company:\n {ErrorCompanyIds}", string.Join(", ", companyWithoutMappingErrorCompanyIds));

            _appDbContext.CmsHubSpotCompanySyncHistories.Add(
                new CmsHubSpotCompanySyncHistory
                {
                    StartAt = startAt,
                    TotalCount = companyDetails.Count,
                    ErrorCount = errorCount,
                    SucceedCount = succeedCount,
                    Remark = errorCompanyIds.Count > 0 ? string.Join(", ", errorCompanyIds) : null,
                });

            await _appDbContext.SaveChangesAsync();

            return true;
        }

        public async ValueTask<bool> UpdateContactOwnerFromWebhook(
            SleekflowWebhookPayload payload,
            string hubSpotContactOwnerId)
        {
            if (!CheckIsHubSpotEnable())
            {
                return false;
            }

            var contactObject = await _internalHubspotRepository.GetContactObjectByEmailAsync(payload.Email);

            if (contactObject == null)
            {
                await _internalHubspotRepository.CreateContactObjectAsync(
                    new InternalHubSpotContact
                    {
                        FirstName = payload.FirstName,
                        LastName = payload.LastName,
                        Email = payload.Email,
                        IsSleekflowContact = true,
                    });
            }
            else
            {
                contactObject.ContactOwner = hubSpotContactOwnerId;

                if (contactObject.IsSleekflowContact == null || !contactObject.IsSleekflowContact.Value)
                {
                    contactObject.IsSleekflowContact = true;
                }

                await _internalHubspotRepository.UpdateContactObjectAsync(contactObject);
            }

            return true;
        }

        public async ValueTask<bool> SetCompanyOwnerUpgradeToProPlanFlag(string companyId)
        {
            if (!CheckIsHubSpotEnable())
            {
                return false;
            }

            var owner = await _appDbContext.UserRoleStaffs
                .Include(x => x.Identity)
                .Where(x => x.CompanyId == companyId && x.Id != 1)
                .OrderBy(x => x.Order)
                .ThenBy(x => x.Id)
                .FirstOrDefaultAsync();

            if (owner == null || string.IsNullOrWhiteSpace(owner.Identity.Email))
            {
                return false;
            }

            var hubspotContactObject =
                await _internalHubspotRepository.GetContactObjectIdByEmailAsync(owner.Identity.Email);

            await _internalHubspotRepository.UpdateContactObjectAsync(
                new InternalHubSpotContact()
                {
                    Id = hubspotContactObject.Id,
                    IsCompanyPlanUpgradeToPro = InternalHubSpotContact.Property.CompanyPlanUpgradeStatus.Yes,
                });

            return true;
        }

        public async ValueTask<bool> SetCompanyOwnerUpgradeToPremiumPlanFlag(string companyId)
        {
            if (!CheckIsHubSpotEnable())
            {
                return false;
            }

            var owner = await _appDbContext.UserRoleStaffs
                .Include(x => x.Identity)
                .Where(x => x.CompanyId == companyId && x.Id != 1)
                .OrderBy(x => x.Order)
                .ThenBy(x => x.Id)
                .FirstOrDefaultAsync();

            if (owner == null || string.IsNullOrWhiteSpace(owner.Identity.Email))
            {
                return false;
            }

            var hubspotContactObject =
                await _internalHubspotRepository.GetContactObjectIdByEmailAsync(owner.Identity.Email);

            await _internalHubspotRepository.UpdateContactObjectAsync(
                new InternalHubSpotContact()
                {
                    Id = hubspotContactObject.Id,
                    IsCompanyPlanUpgradeToPremium = InternalHubSpotContact.Property.CompanyPlanUpgradeStatus.Yes,
                });

            return true;
        }

        #region Contact Owner

        public async ValueTask<bool> SyncCompanyOwnerFromHubspot(string companyId)
        {
            if (!CheckIsHubSpotEnable())
            {
                return false;
            }

            var all = await _appDbContext.CmsHubSpotContactOwnerMaps.AsNoTracking()
                .Include(cmsHubSpotContactOwnerMap => cmsHubSpotContactOwnerMap.ContactOwner).ToListAsync();

            var hubSpotCompany = await _internalHubspotRepository.GetCompanyObjectByCompanyIdAsync(companyId);
            var company = await _appDbContext.CompanyCompanies.FirstAsync(x => x.Id == companyId);

            if (hubSpotCompany.HubSpotOwnerId != null)
            {
                var newAssignedCompanyOwner =
                    all.FirstOrDefault(x => x.HubSpotContactOwnerId == hubSpotCompany.HubSpotOwnerId);

                if (newAssignedCompanyOwner != null &&
                    company.CmsCompanyOwnerId != newAssignedCompanyOwner.ContactOwnerId)
                {
                    _appDbContext.CmsContactOwnerAssignLogs.Add(
                        new CmsContactOwnerAssignLog()
                        {
                            CompanyId = company.Id,
                            ContactOwnerType = CmsContactOwnerType.CompanyOwner,
                            FromContactOwnerId = company.CmsCompanyOwnerId,
                            ToContactOwnerId = newAssignedCompanyOwner.ContactOwnerId,
                        });

                    company.CmsCompanyOwnerId = newAssignedCompanyOwner.ContactOwnerId;
                }
            }

            if (hubSpotCompany.HubSpotActivationOwnerId != null)
            {
                var newAssignedActivationOwnerId = all.FirstOrDefault(
                    x => x.HubSpotContactOwnerId == hubSpotCompany.HubSpotActivationOwnerId);

                if (newAssignedActivationOwnerId != null &&
                    company.CmsActivationOwnerId != newAssignedActivationOwnerId.ContactOwnerId)
                {
                    _appDbContext.CmsContactOwnerAssignLogs.Add(
                        new CmsContactOwnerAssignLog()
                        {
                            CompanyId = company.Id,
                            ContactOwnerType = CmsContactOwnerType.ActivationOwner,
                            FromContactOwnerId = company.CmsActivationOwnerId,
                            ToContactOwnerId = newAssignedActivationOwnerId.ContactOwnerId,
                        });

                    company.CmsActivationOwnerId = newAssignedActivationOwnerId.ContactOwnerId;

                    BackgroundJob.Enqueue<IInternalIntegrationService>(
                        x => x.UpdateCustomerAsync(
                            company.Id,
                            company.CompanyName,
                            company.CompanyCountry,
                            null,
                            null,
                            newAssignedActivationOwnerId.ContactOwner.Email));
                }
            }

            if (hubSpotCompany.HubSpotCsOwnerId != null)
            {
                var newAssignedCsOwnerId = all.FirstOrDefault(
                    x => x.HubSpotContactOwnerId == hubSpotCompany.HubSpotCsOwnerId);

                if (newAssignedCsOwnerId != null &&
                    company.CmsCsOwnerId != newAssignedCsOwnerId.ContactOwnerId)
                {
                    _appDbContext.CmsContactOwnerAssignLogs.Add(
                        new CmsContactOwnerAssignLog()
                        {
                            CompanyId = company.Id,
                            ContactOwnerType = CmsContactOwnerType.CsOwner,
                            FromContactOwnerId = company.CmsCsOwnerId,
                            ToContactOwnerId = newAssignedCsOwnerId.ContactOwnerId,
                        });

                    company.CmsCsOwnerId = newAssignedCsOwnerId.ContactOwnerId;
                }
            }

            await _appDbContext.SaveChangesAsync();

            return true;
        }

        public async ValueTask<List<CmsHubSpotContactOwnerMap>> SyncInternalContactOwnerMapping()
        {
            if (!CheckIsHubSpotEnable())
            {
                return null;
            }

            var hubSpotContactOwners = await _internalHubspotRepository.GetHubSpotContactOwnersAsync();

            var mappedContactOwners = await _appDbContext.CmsHubSpotContactOwnerMaps
                .ToListAsync();

            // Update Hubspot Team flow
            foreach (var mappedContactOwner in mappedContactOwners)
            {
                var hubSpotOwner =
                    hubSpotContactOwners.Results.Find(x => x.Id == mappedContactOwner.HubSpotContactOwnerId);

                if (hubSpotOwner == null)
                {
                    continue;
                }

                // Update Contact Owner Id
                var userId = await _userManager.Users
                    .Where(x => x.Email == hubSpotOwner.Email)
                    .Select(x => x.Id)
                    .FirstOrDefaultAsync();

                if (mappedContactOwner.ContactOwnerId != userId)
                {
                    mappedContactOwner.ContactOwnerId = userId;
                    _appDbContext.Entry(mappedContactOwner).Property(x => x.ContactOwnerId).IsModified = true;
                }

                // Update HubSpot Owner Teams
                if (hubSpotOwner.Teams == null)
                {
                    continue;
                }

                if (mappedContactOwner.HubspotTeams == null || mappedContactOwner.HubspotTeams.Count != hubSpotOwner.Teams.Count)
                {
                    mappedContactOwner.HubspotTeams = hubSpotOwner.Teams;
                    _appDbContext.Entry(mappedContactOwner).Property(x => x.HubspotTeams).IsModified = true;
                }
                else
                {
                    if (!mappedContactOwner.HubspotTeams.Exists(
                            team => !hubSpotOwner.Teams.Exists(x => x.Equals(team))))
                    {
                        continue;
                    }

                    mappedContactOwner.HubspotTeams = hubSpotOwner.Teams;
                    _appDbContext.Entry(mappedContactOwner).Property(x => x.HubspotTeams).IsModified = true;
                }
            }

            await _appDbContext.SaveChangesAsync();

            // Insert flow
            var unmappedContactOwners = hubSpotContactOwners.Results
                .Where(x => !mappedContactOwners.Select(x => x.HubSpotContactOwnerId).Contains(x.Id))
                .ToList();
            var newHubSpotContactOwnerMap = new List<CmsHubSpotContactOwnerMap>();

            foreach (var unmappedContactOwner in unmappedContactOwners)
            {
                var userId = await _userManager.Users
                    .Where(x => x.Email == unmappedContactOwner.Email)
                    .Select(x => x.Id)
                    .FirstOrDefaultAsync();

                if (userId != null)
                {
                    newHubSpotContactOwnerMap.Add(
                        new CmsHubSpotContactOwnerMap
                        {
                            HubSpotContactOwnerId = unmappedContactOwner.Id,
                            HubSpotUserId = unmappedContactOwner.UserId,
                            ContactOwnerId = userId,
                            HubspotTeams = unmappedContactOwner.Teams
                        });
                }
            }

            if (newHubSpotContactOwnerMap.Count > 0)
            {
                await _appDbContext.CmsHubSpotContactOwnerMaps.AddRangeAsync(newHubSpotContactOwnerMap);
                await _appDbContext.SaveChangesAsync();
            }

            return newHubSpotContactOwnerMap;
        }

        public async Task<List<string>> GetHubSpotTeamNames()
        {
            var result = new List<string>();

            var hubSpotTeams = await _appDbContext.CmsHubSpotContactOwnerMaps
                .Select(x => x.HubspotTeams)
                .ToListAsync();

            hubSpotTeams.ForEach(
                x =>
                {
                    if (x is { Count: > 0 })
                    {
                        result.AddRange(x.Select(y => y.Name));
                    }
                });

            result = result.Distinct().ToList();
            result.Sort();

            return result;
        }

        #endregion

        #region New Signup

        public async ValueTask<bool> CreateNewSignupCompanyAndStaff(string companyId, string userEmail)
        {
            if (!CheckIsHubSpotEnable())
            {
                return false;
            }

            await SyncCompany(companyId);
            await SyncCompanyStaffs(companyId);
            await UpdateContactAsNewSignupUser(userEmail);

            BackgroundJob.Schedule<IEmailNotificationService>(
                x => x.SendNewCompanyEmailToSlackChannel(companyId),
                TimeSpan.FromMinutes(8));

            return true;
        }

        public async ValueTask<bool> CreateNewResellerSignupCompanyAndStaff(string companyId)
        {
            if (!CheckIsHubSpotEnable())
            {
                return false;
            }

            await SyncCompany(companyId);
            await SyncCompanyStaffs(companyId);

            BackgroundJob.Schedule<IEmailNotificationService>(
                x => x.SendNewCompanyEmailToSlackChannel(companyId),
                TimeSpan.FromMinutes(8));

            return true;
        }

        public async ValueTask<bool> UpdateContactAsNewSignupUser(string userEmail)
        {
            if (!CheckIsHubSpotEnable())
            {
                return false;
            }

            var contact = await _internalHubspotRepository.GetContactObjectByEmailAsync(userEmail);

            if (contact == null)
            {
                return false;
            }

            await _internalHubspotRepository.UpdateContactObjectAsync(
                new InternalHubSpotContact()
                {
                    Id = contact.Id,
                    IsNewSignupUser = true,
                    IsSleekflowContact = true,
                });

            return true;
        }

        #endregion

        #region Website

        public async ValueTask<bool> SubmitLeadFormToHubSpot(CreateLeadFormTicketViewModel request)
        {
            if (!CheckIsHubSpotEnable())
            {
                return false;
            }

            Country country;
            InternalHubSpotContact contact;

            if (string.IsNullOrEmpty(request.Country))
            {
                country = null;
            }
            else
            {
                country = await _appDbContext.CoreCountries.AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == request.Country);
            }

            if (string.IsNullOrEmpty(request.Email))
            {
                contact = null;
            }
            else
            {
                contact = await _internalHubspotRepository.GetContactObjectByEmailAsync(request.Email.Trim().ToLower());
            }

            if (contact == null)
            {
                contact = await _internalHubspotRepository.CreateContactObjectAsync(
                    new InternalHubSpotContact()
                    {
                        Email = request.Email.Trim().ToLower(),
                        FirstName = request.FirstName,
                        LastName = request.LastName,
                        LeadSource = InternalHubSpotContact.Property.LeadSourceValue.Inbound,
                        JobTitle = request.JobTitle,
                        Country = country?.DisplayName,
                        Phone = request.Phone,
                        LeadFormCompanyName = request.LeadFormCompanyNameLeadFormCompanyName,
                        Website = request.WebsiteLink,
                        NumberOfEmployees = request.CompanySize,
                        IsAgreeMarketingConsent = request.IsAgreeMarketingConsent,
                        LatestLeadEntryPoint = request.LatestLeadEntryPoint,
                        Module = request.Module,
                    });
            }
            else
            {
                contact = await _internalHubspotRepository.UpdateContactObjectAsync(
                    new InternalHubSpotContact()
                    {
                        Id = contact.Id,
                        LeadFormCompanyName =
                            string.IsNullOrEmpty(request.LeadFormCompanyNameLeadFormCompanyName) ? string.Empty : request.LeadFormCompanyNameLeadFormCompanyName,
                        FirstName = contact.FirstName == null ? request.FirstName : null,
                        LastName = contact.LastName == null ? request.LastName : null,
                        LeadSource =
                            contact.LeadSource == null ? InternalHubSpotContact.Property.LeadSourceValue.Inbound : null,
                        JobTitle = contact.JobTitle == null ? request.JobTitle : null,
                        Country = contact.Country == null ? country?.DisplayName : null,
                        Website = contact.Website == null ? request.WebsiteLink : null,
                        NumberOfEmployees = contact.NumberOfEmployees == null ? request.CompanySize : null,
                        IsAgreeMarketingConsent = request.IsAgreeMarketingConsent,
                        LatestLeadEntryPoint = request.LatestLeadEntryPoint,
                        Module = request.Module,
                    });
            }

            return true;
        }

        public async ValueTask<bool> SubmitPartnershipFormToHubSpot(PartnershipTicketViewModel request)
        {
            if (!CheckIsHubSpotEnable())
            {
                return false;
            }

            var contact =
                await _internalHubspotRepository.GetContactObjectIdByEmailAsync(request.Email.Trim().ToLower());

            if (contact == null)
            {
                contact = await _internalHubspotRepository.CreateContactObjectAsync(
                    new InternalHubSpotContact()
                    {
                        Email = request.Email.Trim().ToLower(),
                        FirstName = request.FirstName,
                        LastName = request.LastName,
                        LeadSource = InternalHubSpotContact.Property.LeadSourceValue.Inbound,
                        Phone = request.PhoneNumber,
                        PartnerCompanyName = request.CompanyName,
                        PartnerName = $"{request.FirstName} {request.LastName}",
                        PartnerProgram = request.PartnerProgram,
                        PartnershipStatus = InternalHubSpotContact.Property.PartnershipStatusValue.PendingApproval,
                        Website = request.CompanyWebsite,
                        IsAgreeMarketingConsent = request.IsAgreeMarketingConsent,
                        LatestLeadEntryPoint = request.LatestLeadEntryPoint,
                    });
            }
            else
            {
                contact = await _internalHubspotRepository.GetContactObjectAsync(contact.Id);

                contact = await _internalHubspotRepository.UpdateContactObjectAsync(
                    new InternalHubSpotContact()
                    {
                        Id = contact.Id,
                        PartnerCompanyName = request.CompanyName,
                        PartnerName = $"{request.FirstName} {request.LastName}",
                        PartnerProgram = request.PartnerProgram,
                        PartnershipStatus =
                            contact.PartnershipStatus == null
                                ? InternalHubSpotContact.Property.PartnershipStatusValue.PendingApproval
                                : null,
                        Website = contact.Website == null ? request.CompanyWebsite : null,
                        IsAgreeMarketingConsent = request.IsAgreeMarketingConsent,
                        LatestLeadEntryPoint = request.LatestLeadEntryPoint,
                    });
            }

            var ticketContent =
                $"{request.Message}\n" +
                $"------------------\n" +
                $"Partnership Form Info:\n" +
                $"Partner Program: {request.PartnerProgram}\n" +
                $"By: {request.FirstName} {request.LastName}\n" +
                $"Email: {request.Email}\n" +
                $"Company: {request.CompanyName}\n" +
                $"Website: {request.CompanyWebsite}\n" +
                $"Phone: {request.PhoneNumber ?? "N/A"}\n" +
                $"Agree Marketing Consent: {(request.IsAgreeMarketingConsent is true ? "Yes" : "No")}\n";

            try
            {
                var ticket = await _internalHubspotRepository.CreateTicketAsync(
                    new HubSpotPartnershipTicket()
                    {
                        TicketName = $"{request.PartnerProgram} - {request.CompanyName}",
                        Content = ticketContent,
                        TicketStatus = HubSpotPartnershipTicket.PipelineStatus.New,
                        CreateDate = DateTime.UtcNow,
                        PartnerProgram = request.PartnerProgram,
                    },
                    null,
                    contact);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] error, company name {CompanyName}, partner program: {PartnerProgram}: {ExceptionMessage}",
                    nameof(SubmitPartnershipFormToHubSpot),
                    request.CompanyName,
                    request.PartnerProgram,
                    ex.Message);
            }

            return true;
        }

        public async ValueTask<bool> SubmitPartnershipOfferFormToHubSpot(PartnershipOfferFormViewModel request)
        {
            if (!CheckIsHubSpotEnable())
            {
                return false;
            }

            Country country = null;

            if (string.IsNullOrEmpty(request.Country))
            {
                country = null;
            }
            else
            {
                country = await _appDbContext.CoreCountries.AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == request.Country);
            }

            var contact =
                await _internalHubspotRepository.GetContactObjectIdByEmailAsync(request.Email.Trim().ToLower());

            if (contact == null)
            {
                contact = await _internalHubspotRepository.CreateContactObjectAsync(
                    new InternalHubSpotContact()
                    {
                        Email = request.Email.Trim().ToLower(),
                        PartnershipsOffer = request.PartnershipsOffer,
                        FirstName = request.FirstName,
                        LastName = request.LastName,
                        Company = request.CompanyName,
                        Country = country?.DisplayName,
                        Website = request.CompanyWebsite,
                        Phone = request.PhoneNumber,
                    });

                await _appDbContext.SaveChangesAsync();
            }
            else
            {
                contact = await _internalHubspotRepository.UpdateContactObjectAsync(
                    new InternalHubSpotContact()
                    {
                        Id = contact.Id,
                        Email = request.Email.Trim().ToLower(),
                        PartnershipsOffer = request.PartnershipsOffer,
                        FirstName = request.FirstName,
                        LastName = request.LastName,
                        Company = request.CompanyName,
                        Website = request.CompanyWebsite,
                        Country = contact.Country == null ? country?.DisplayName : null,
                        Phone = request.PhoneNumber,
                    });
            }

            return true;
        }

        public async ValueTask<bool> SubmitSubscribeNewsletterFromToHubSpot(SubscribeNewsletterViewModel request)
        {
            if (!CheckIsHubSpotEnable())
            {
                return false;
            }

            var contact =
                await _internalHubspotRepository.GetContactObjectIdByEmailAsync(request.Email.Trim().ToLower());

            if (contact == null)
            {
                contact = await _internalHubspotRepository.CreateContactObjectAsync(
                    new InternalHubSpotContact()
                    {
                        Email = request.Email.Trim().ToLower(),
                        LeadSource = InternalHubSpotContact.Property.LeadSourceValue.Inbound,
                        IsWebsiteENewsSubscriber = true,
                        WebsiteENewsSubscriptionSource = request.SubscriptionSource,
                        IsAgreeMarketingConsent = request.IsAgreeMarketingConsent,
                        LatestLeadEntryPoint = request.LatestLeadEntryPoint,
                    });

                await _appDbContext.SaveChangesAsync();
            }
            else
            {
                contact = await _internalHubspotRepository.UpdateContactObjectAsync(
                    new InternalHubSpotContact()
                    {
                        Id = contact.Id,
                        LeadSource =
                            !string.IsNullOrEmpty(contact.LeadSource)
                                ? InternalHubSpotContact.Property.LeadSourceValue.Inbound
                                : null,
                        IsWebsiteENewsSubscriber = true,
                        WebsiteENewsSubscriptionSource = request.SubscriptionSource,
                        IsAgreeMarketingConsent = request.IsAgreeMarketingConsent,
                        LatestLeadEntryPoint = request.LatestLeadEntryPoint,
                    });
            }

            return true;
        }

        #endregion

        #region WhatsApp Application

        public async ValueTask<bool> CreateWhatsAppApplicationTicket(
            WhatsappRegistrationWithoutFileViewModel applyWhatsappViewModel,
            long cmsWhatsappApplicationId,
            long? staffId = null)
        {
            if (!CheckIsHubSpotEnable())
            {
                return false;
            }

            var cmsWhatsappApplication =
                await _appDbContext.CmsWhatsappApplications.FirstAsync(x => x.Id == cmsWhatsappApplicationId);

            Staff companyUser = null;

            if (staffId != null)
            {
                companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.Id == staffId).FirstOrDefaultAsync();
            }

            var ticket = new HubSpotWhatsAppApplicationTicket()
            {
                TicketName =
                    $"{applyWhatsappViewModel.CompanyName} WhatsApp Application ({DateTime.UtcNow.AddHours(8):yyyy/MM/dd})",
                ReferenceId = cmsWhatsappApplication.ReferenceId,
                ContactPersonName = cmsWhatsappApplication.ContactPersonName,
                ApplicationCompanyName = cmsWhatsappApplication.CompanyName,
                ContactPersonEmail = cmsWhatsappApplication.ContactPersonEmail,
                ContactPersonPhoneNumber = cmsWhatsappApplication.ContactPersonPhoneNumber,
                FacebookBusinessManagerId = cmsWhatsappApplication.FacebookBusinessManagerId,
                HasCompletedFacebookVerifications = cmsWhatsappApplication.CompletedFacebookVerifications,
                HasToppedUp = false,
                CompanyWebsite = cmsWhatsappApplication.CompanyWebsite,
                PreferNumberStartWith = cmsWhatsappApplication.PreferStartWith,
                TwilioAccountSid = cmsWhatsappApplication.TwilioAccountSid,
                CmsWhatsAppApplicationId = cmsWhatsappApplication.Id.ToString(),
                TicketStatus = HubSpotWhatsAppApplicationTicket.PipelineStatus.ClientSubmitted,
                SleekflowCompanyId = companyUser?.CompanyId,
                CreateDate = DateTime.UtcNow
            };

            ticket.Content = $"Apply At: {DateTime.UtcNow.AddHours(8).ToString("yyyy/MM/dd HH:mm:ss")}\n" +
                             $"Company Id: {ticket.SleekflowCompanyId}\n" +
                             $"Application Company Name: {ticket.ApplicationCompanyName}\n" +
                             $"Contact Person Name: {ticket.ContactPersonName}\n" +
                             $"Contact Person Email: {ticket.ContactPersonEmail}\n" +
                             $"Phone Number: {ticket.ContactPersonPhoneNumber}\n" +
                             $"Facebook Business Manager Id: {ticket.FacebookBusinessManagerId}\n" +
                             $"Has Completed Facebook Verifications: {ticket.HasCompletedFacebookVerifications}\n" +
                             $"Company Website: {ticket.CompanyWebsite}\n" +
                             $"Prefer Start With: {ticket.PreferNumberStartWith}\n" +
                             $"Twilio Account SID: {ticket.TwilioAccountSid}";

            InternalHubSpotCompany companyObject = null;

            if (companyUser != null)
            {
                companyObject = await _appDbContext.CmsHubSpotCompanyMaps
                    .Where(x => x.CompanyId == companyUser.CompanyId)
                    .Select(
                        x => new InternalHubSpotCompany
                        {
                            Id = x.HubSpotCompanyObjectId
                        })
                    .FirstOrDefaultAsync();
            }

            var contact = await _internalHubspotRepository.GetContactObjectIdByEmailAsync(ticket.ContactPersonEmail);

            if (contact == null)
            {
                contact = await _internalHubspotRepository.CreateContactObjectAsync(
                    new InternalHubSpotContact()
                    {
                        Email = ticket.ContactPersonEmail,
                        FirstName = applyWhatsappViewModel.FirstName,
                        LastName = applyWhatsappViewModel.LastName,
                        LeadSource = InternalHubSpotContact.Property.LeadSourceValue.Inbound,
                        IsSleekflowUser = companyUser != null ? true : false,
                        Phone = applyWhatsappViewModel.PhoneNumber,
                    });
            }

            var createdTicket = await _internalHubspotRepository.CreateTicketAsync(ticket, companyObject, contact);

            cmsWhatsappApplication.HubSpotTicketId = createdTicket.Id;

            await _appDbContext.SaveChangesAsync();

            return true;
        }

        public async ValueTask<bool> UpdateWhatsAppApplicationTicket(
            string ticketId,
            CmsWhatsappApplicationStep? step = null,
            bool? isVerified = null,
            bool? hasToppedUp = null)
        {
            if (!CheckIsHubSpotEnable())
            {
                return false;
            }

            try
            {
                var request = new HubSpotUpdateObjectRequest<UpdateWhatsAppApplicationTicketRequest>
                {
                    Properties = new UpdateWhatsAppApplicationTicketRequest()
                    {
                        TicketStatus =
                            step != null ? HubSpotWhatsAppApplicationTicket.PipelineStatus.Parse(step.Value) : null,
                        HasCompletedFacebookVerifications = isVerified,
                        HasToppedUp = hasToppedUp,
                        LastModifiedDate = DateTime.UtcNow,
                    }
                };

                await _internalHubspotRepository.UpdateTicketAsync(ticketId, request);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Ticket {TicketId} error: {ExceptionMessage}",
                    nameof(UpdateWhatsAppApplicationTicket),
                    ticketId,
                    ex.Message);

                return false;
            }

            return true;
        }

        #endregion

        #region Cancel Ticket

        public async ValueTask<bool> CreateSubscriptionPlanCancelTicket(
            string companyId,
            long staffId,
            CannelReason reason)
        {
            if (!CheckIsHubSpotEnable())
            {
                return false;
            }

            var staff = await _appDbContext.UserRoleStaffs.Include(x => x.Identity).Where(x => x.Id == staffId)
                .FirstOrDefaultAsync();

            var company = await _appDbContext.CompanyCompanies
                .Where(x => x.Id == companyId)
                .Select(
                    x => new
                    {
                        CompanyId = x.Id,
                        CompanyName = x.CompanyName,
                        BillRecord = x.BillRecords
                            .Where(t => t.PeriodStart <= DateTime.UtcNow && t.PeriodEnd >= DateTime.UtcNow).ToList()
                    })
                .FirstOrDefaultAsync();

            if (staff == null || company == null || reason == null)
            {
                return false;
            }

            var hubSpotCompanyId = await _internalHubspotRepository.GetCompanyObjectIdByCompanyIdAsync(companyId);

            var hubSpotUserContactId =
                await _internalHubspotRepository.GetContactObjectIdByEmailAsync(staff.Identity.Email);

            var contact =
                $"Reason: {reason.Resaon}\n" +
                $"Message: {reason.Value}" +
                $"\n---------------------------------------\n" +
                $"Cancelled By: {staff.Identity.DisplayName}\n" +
                $"Email: {staff.Identity.Email}\n" +
                $"Company Name: {company.CompanyName}\n" +
                $"Company Id: {company.CompanyId}\n" +
                $"Plan: {company.BillRecord.FirstOrDefault(x => ValidSubscriptionPlan.SubscriptionPlan.Contains(x.SubscriptionPlanId))?.SubscriptionPlanId}\n" +
                $"Add-On: {string.Join(", ", company.BillRecord.Where(x => !ValidSubscriptionPlan.SubscriptionPlan.Contains(x.SubscriptionPlanId)).ToList())}\n";

            await _internalHubspotRepository.CreateTicketAsync(
                new HubSpotCancelPlanTicket()
                {
                    TicketName = $"Cancellation: {company.CompanyName} ({DateTime.UtcNow.AddHours(8):yyyy/MM/dd})",
                    Content = contact,
                    SleekflowCompanyId = companyId,
                    SleekflowStaffId = staffId.ToString(),
                    TicketStatus = HubSpotCancelPlanTicket.PipelineStatus.New,
                    Priority = HubSpotCancelPlanTicket.Property.PriorityValue.High,
                    CreateDate = DateTime.UtcNow
                },
                hubSpotCompanyId,
                hubSpotUserContactId);

            return true;
        }

        public async ValueTask<bool> UpdateLastMonthCompanyBreakdownStatuses(List<string> companyIds = null)
        {
            if (!CheckIsHubSpotEnable())
            {
                return false;
            }

            var now = DateTime.UtcNow;
            var from = new DateTime(now.Year, now.Month - 1, 1);
            var to = from.AddMonths(1).AddDays(-1);

            var breakdowns = await _internalAnalyticService.GetCompanyRevenueStatusBreakdowns(to, from, companyIds);

            var hubSpotAnalyticCompanies = new List<InternalHubSpotAnalyticCompany>();

            var hubspotCompanyIdPairs = await _appDbContext.CmsHubSpotCompanyMaps.Select(
                x => new
                {
                    x.CompanyId,
                    x.HubSpotCompanyObjectId
                }).ToListAsync();

            hubspotCompanyIdPairs.ForEach(
                hubspotCompanyPair =>
                {
                    var breakdown = breakdowns.FirstOrDefault(x => x.CompanyId == hubspotCompanyPair.CompanyId);

                    if (breakdown != null)
                    {
                        hubSpotAnalyticCompanies.Add(
                            new InternalHubSpotAnalyticCompany()
                            {
                                Id = hubspotCompanyPair.HubSpotCompanyObjectId,
                                MonthlyRecurringRevenueDifference = breakdown.MrrDiff,
                                RevenueStatus = breakdown.Status switch
                                {
                                    CompanyRevenueStatus.New => "New",
                                    CompanyRevenueStatus.Return => "Return",
                                    CompanyRevenueStatus.Increase => "Increase",
                                    CompanyRevenueStatus.Sustain => "Sustain",
                                    CompanyRevenueStatus.Decrease => "Decrease",
                                    CompanyRevenueStatus.Churn => "Churn",
                                    CompanyRevenueStatus.ChurnedInMiddle => "Churn"
                                }
                            });
                    }
                });

            foreach (var internalHubSpotAnalyticCompanies in hubSpotAnalyticCompanies.Chunk(80))
            {
                await _internalHubspotRepository.BulkUpdateAnalyticCompanyObjectAsync(internalHubSpotAnalyticCompanies);
            }

            return true;
        }

        public async ValueTask<bool> UpdateAndSyncAllCloudApiCredits()
        {
            if (!CheckIsHubSpotEnable())
            {
                return false;
            }

            var succeedCount = 0;
            var errorCount = 0;
            var errorCompanyIds = new List<string>();

            var getAllManagementWhatsappCloudApiBusinessBalancesOutput =
                await _internalWhatsappCloudApiService.GetAllWhatsappCloudApiBusinessBalances();

            var businessBalancesList = getAllManagementWhatsappCloudApiBusinessBalancesOutput.BusinessBalances
                .Where(x => x.SleekflowCompanyIds.Count > 0)
                .ToList();

            var businessBalancesByCompanyDictionary = new Dictionary<string, decimal>();

            businessBalancesList.ForEach(
                dto =>
                {
                    dto.SleekflowCompanyIds.ForEach(
                        companyId =>
                        {
                            if (businessBalancesByCompanyDictionary.TryGetValue(companyId, out var balance))
                            {
                                if (balance < dto.Balance.Amount)
                                {
                                    businessBalancesByCompanyDictionary[companyId] = dto.Balance.Amount;
                                }
                            }
                            else
                            {
                                businessBalancesByCompanyDictionary.Add(companyId, dto.Balance.Amount);
                            }
                        });
                });

            var internalHubSpotCloudApiUsageCompanies = new List<InternalHubSpotCloudApiUsageCompany>();
            var companyIds = businessBalancesByCompanyDictionary.Keys.ToList();

            var hubspotCompanyIdPairs = await _appDbContext.CmsHubSpotCompanyMaps
                .Where(x => companyIds.Contains(x.CompanyId))
                .Select(
                    x => new
                    {
                        x.CompanyId,
                        x.HubSpotCompanyObjectId
                    }).ToListAsync();

            hubspotCompanyIdPairs.ForEach(
                hubspotCompanyPair =>
                {
                    if (businessBalancesByCompanyDictionary.TryGetValue(hubspotCompanyPair.CompanyId, out var balance))
                    {
                        internalHubSpotCloudApiUsageCompanies.Add(
                            new InternalHubSpotCloudApiUsageCompany
                            {
                                Id = hubspotCompanyPair.HubSpotCompanyObjectId,
                                CloudApiCredits = balance
                            });
                    }
                });

            foreach (var companies in internalHubSpotCloudApiUsageCompanies.Chunk(80))
            {
                try
                {
                    succeedCount += await _internalHubspotRepository.BulkUpdateCloudApiUsageCompanyObjectAsync(companies);
                }
                catch (Exception e)
                {
                    _logger.LogError(e, "Error bulk update company object to HubSpot: {Message}", e.Message);
                    errorCount += companies.Length;
                    errorCompanyIds.AddRange(companies.Select(x => x.Id));
                }
            }

            // Insert Log
            _logger.LogInformation(
                "UpdateAndSyncAllCloudApiCredits Completed:\n" +
                "--------------------------------------------\n" +
                "Total Company: {TotalCompany}\n" +
                "Sync Succeed: {SucceedCount}\n" +
                "Sync Error: {ErrorCount}\n\n",
                internalHubSpotCloudApiUsageCompanies.Count,
                succeedCount,
                errorCount);

            _logger.LogInformation(
                "HubSpot Sync Error Company:\n {ErrorCompanyIds}",
                string.Join(", ", errorCompanyIds));

            return true;
        }

        public async ValueTask<bool> UpdateCompanyChurnInfomation(string companyId, string churnReason, string churnNote)
        {
            if (!CheckIsHubSpotEnable())
            {
                return false;
            }

            var companyObject = await _internalHubspotRepository.GetCompanyObjectIdByCompanyIdAsync(companyId);
            companyObject.ChurnReasons = churnReason;
            companyObject.CustomerChurnNote = churnNote;

            await _internalHubspotRepository.UpdateCompanyObjectAsync(companyObject);

            return true;
        }

        #endregion

    }
}