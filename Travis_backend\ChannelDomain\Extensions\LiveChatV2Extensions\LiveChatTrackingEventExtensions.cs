using Sleekflow.Apis.UserEventHub.Model;
using Travis_backend.ChannelDomain.ViewModels.LiveChatV2.DTO;

namespace Travis_backend.ChannelDomain.Extensions.LiveChatV2Extensions;

public static class LiveChatSenderTrackingEventExtensions
{
    public static LiveChatTrackingEventDto ToDto(this LiveChatTrackingEvent @event)
    {
        var liveChatTrackingEventDto = new LiveChatTrackingEventDto
        {
            Id = @event.Id,
            StartedAt = @event.StartedAt.ToString("yyyy-MM-dd'T'HH:mm:ss.fff'Z'"),
            EndedAt = @event.EndedAt.ToString("yyyy-MM-dd'T'HH:mm:ss.fff'Z'"),
            Name = @event.Name,
            Metadata = @event.Metadata
        };

        return liveChatTrackingEventDto;
    }
}