using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Sleekflow.Apis.UserEventHub.Api;
using Sleekflow.Apis.UserEventHub.Model;
using Travis_backend.Cache;
using Travis_backend.ChannelDomain.Extensions.LiveChatV2Extensions;
using Travis_backend.ChannelDomain.ViewModels.LiveChatV2;
using Travis_backend.Constants;
using Travis_backend.Database.Services;
using Travis_backend.**********************;
using Travis_backend.FlowHubs;

namespace Travis_backend.ChannelDomain.Services.LiveChatV2;

public interface ILiveChatV2TrackingService
{
    public Task<CreateLiveChatV2TrackingEventOutput> CreateTrackingEventAsync(CreateLiveChatV2TrackingEventInput input);
}

public class LiveChatV2TrackingService : ILiveChatV2TrackingService
{
    private readonly ILogger<LiveChatV2TrackingService> _logger;
    private readonly ILiveChatTrackingEventsApi _liveChatTrackingEventsApi;
    private readonly IDbContextService _dbContextService;
    private readonly IUserProfileHooks _userProfileHooks;
    private readonly ILockService _lockService;

    public LiveChatV2TrackingService(
        ILogger<LiveChatV2TrackingService> logger,
        ILiveChatTrackingEventsApi liveChatTrackingEventsApi,
        IDbContextService dbContextService,
        IUserProfileHooks userProfileHooks,
        ILockService lockService)
    {
        _logger = logger;
        _liveChatTrackingEventsApi = liveChatTrackingEventsApi;
        _dbContextService = dbContextService;
        _userProfileHooks = userProfileHooks;
        _lockService = lockService;
    }

    public async Task<CreateLiveChatV2TrackingEventOutput> CreateTrackingEventAsync(CreateLiveChatV2TrackingEventInput input)
    {
        var dbContext = _dbContextService.GetDbContext();

        var sender = await dbContext.SenderWebClientSenders.Where(
                sender => sender.WebClientUUID == input.SenderId && sender.ChannelIdentityId == input.ChannelIdentityId)
            .FirstOrDefaultAsync();

        if (sender is null)
        {
            return new CreateLiveChatV2TrackingEventOutput
            {
                ExitCode = ExitCode.Failure, Message = $"Sender {input.SenderId} not found"
            };
        }

        var createLiveChatTrackingEventInput = new CreateLiveChatTrackingEventInput(
            sleekflowCompanyId: sender.CompanyId,
            senderId: sender.WebClientUUID,
            channelIdentityId: sender.ChannelIdentityId,
            startedAt: input.StartedAt,
            endedAt: input.EndedAt,
            name: input.Name,
            metadata: input.Metadata);

        await _liveChatTrackingEventsApi.LiveChatTrackingEventsCreateLiveChatTrackingEventPostAsync(
            createLiveChatTrackingEventInput: createLiveChatTrackingEventInput);

        if (input.Name != LiveChatTrackingEventTypes.PageVisited)
        {
            return new CreateLiveChatV2TrackingEventOutput();
        }

        var visitedPage = input.Metadata.GetVisitedPage();
        if (visitedPage is not null && !string.IsNullOrEmpty(visitedPage.Url))
        {
            var key = GetLockKey(sender.WebClientUUID, visitedPage.Url);
            var @lock = await _lockService.AcquireLockAsync(key, TimeSpan.FromSeconds(15));

            // Anti-spam protection: Process URL detection only once per sender+URL combination
            // within 15 seconds to prevent overwhelming the workflow enrollment system
            if (@lock is not null)
            {
                await _userProfileHooks.OnLiveChatWebsiteUrlDetectedAsync(
                    sender.CompanyId,
                    sender.ChannelIdentityId,
                    sender.UserProfileId,
                    visitedPage.Url);
            }
        }

        return new CreateLiveChatV2TrackingEventOutput();
    }

    private string GetLockKey(string webClientUuid, string url)
    {
        const int maxSafeUrlLength = 150; // Keep total key under 250 chars

        if (url.Length <= maxSafeUrlLength)
        {
            return $"OnLiveChatWebsiteUrlDetected_{webClientUuid}_{url}";
        }

        // For long URLs, use prefix + hash
        var urlHash = url.GetHashCode().ToString("X8");
        var urlPrefix = url.Length > 50 ? url[..50] : url;
        return $"OnLiveChatWebsiteUrlDetected_{webClientUuid}_{urlPrefix}_{urlHash}";
    }
}