using Travis_backend.Constants;
using <PERSON>_backend.MessageDomain.Models;

namespace Travis_backend.MessageDomain.Extensions;

public static class LiveChatV2MessageExtensions
{
    public static bool IsLiveChatV2ContactFormMessage(this ConversationMessage message)
    {
        return message.Channel is ChannelTypes.LiveChatV2 && message.MessageType is MessageTypes.Interactive &&
               message.ExtendedMessagePayload?.ExtendedMessageType is ExtendedMessageType.LiveChatV2ContactFormMessage;
    }
}