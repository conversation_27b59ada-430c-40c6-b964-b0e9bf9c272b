using System.Collections.Generic;
using AutoMapper;
using Newtonsoft.Json;
using Travis_backend.ChannelDomain.Models;
using Travis_backend.ChannelDomain.ViewModels;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.MessageDomain.Models;

namespace Travis_backend.Mapping
{
    public class LiveChatV2MappingProfile : Profile
    {
        public LiveChatV2MappingProfile()
        {
            CreateMap<LiveChatV2Config, LiveChatV2ConfigDto>()
                .ForMember(dest => dest.Settings, opt => opt.MapFrom(src =>
                    string.IsNullOrEmpty(src.Settings)
                        ? new Dictionary<string, object>()
                        : JsonConvert.DeserializeObject<Dictionary<string, object>>(src.Settings)));

            CreateMap<WebClientSender, LiveChatV2SenderResponse>()
                .ForMember(dest => dest.WebClientUuid, opt => opt.MapFrom(src => src.WebClientUUID))
                .ForMember(dest => dest.Metadata, opt => opt.MapFrom(src => src.Metadata));
        }
    }
}