﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using CsvHelper.Configuration.Attributes;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.Enums;
using Travis_backend.InternalDomain.Models;
using Travis_backend.MessageDomain.Models;
using Travis_backend.ResellerDomain.Models;
using Travis_backend.ResellerDomain.ViewModels;
using Travis_backend.StripeIntegrationDomain.Models;
using Travis_backend.SubscriptionPlanDomain.Models;

namespace Travis_backend.InternalDomain.ViewModels
{
    public class CmsCompanyResponse : CompanyResponse
    {
        public UserInfoResponse CmsCompanyOwner { get; set; }

        public List<CmsContactOwnerAssignLogDto> CmsCompanyOwnerChangeLogs { get; set; }

        public UserInfoResponse CmsActivationOwner { get; set; }

        public List<CmsContactOwnerAssignLogDto> CmsActivationOwnerChangeLogs { get; set; }

        public List<StripePaymentConfig> StripePaymentConfigs { get; set; }

        public CmsLiveChatConfig LiveChatConfig { get; set; }

        public string CmsRemark { get; set; }

        public string CmsLeadSource { get; set; }

        public string CmsCompanyIndustry { get; set; }

        public CmsHubSpotCompanyMapDto CmsHubSpotCompanyMap { get; set; }

        public List<CmsWhatsApp360DialogUsageRecordViewModel> CmsWhatsApp360DialogUsageRecords { get; set; }

        public CompanyWhatsapp360DialogTopUpConfigViewModel CompanyWhatsapp360DialogTopUpConfig { get; set; }

        public CmsResellerResponse ResellerInfo { get; set; }

        public CmsResellerClientResponse ResellerClientInfo { get; set; }

        public int? SubscriptionTrialDays { get; set; }

        public bool IsDeleted { get; set; }

        public DateTime? UpdatedAt { get; set; }

        public CmsCompanyAdditionalInfoViewModel CmsCompanyAdditionalInfo { get; set; }
    }

    public class CmsResellerResponse
    {
        public ResellerBasicInformation ResellerInformation { get; set; }

        public TransactionLogsResponse ResellerTransactionLogs { get; set; }

        public List<string> ResellerCompanyIds { get; set; }
    }

    public class CmsResellerClientResponse
    {
        public ResellerBasicInformation ResellerInformation { get; set; }
    }

    public class CmsLiveChatConfig
    {
        public string Host { get; set; }

        public int RecentUserCount { get; set; }

        public DateTime CreateAt { get; set; }
    }

    public class CmsContactOwnerAssignLogDto
    {
        public long Id { get; set; }

        public string FromContactOwnerId { get; set; }

        public string FromContactOwnerName { get; set; }

        public string ToContactOwnerId { get; set; }

        public string ToContactOwnerName { get; set; }

        public string AssignedByUserId { get; set; }

        public string AssignedByUserName { get; set; }

        public DateTime CreatedAt { get; set; }
    }

    public class CmsWebClientSenderDto
    {
        public long Id { get; set; }

        public string WebClientUUID { get; set; }

        public string IPAddress { get; set; }

        public string Name { get; set; }

        public string locale { get; set; }

        public string SignalRConnectionId { get; set; }

        public string Device { get; set; }

        public string BrowserLocale { get; set; }

        [JsonConverter(typeof(StringEnumConverter))]
        public DeviceModel DeviceModel { get; set; }

        [JsonConverter(typeof(StringEnumConverter))]
        public OnlineStatus OnlineStatus { get; set; }

        public DateTime? CreatedAt { get; set; }

        public DateTime? UpdatedAt { get; set; }
    }

    public class WhatsappInstanceDto
    {
        public string CompanyId { get; set; }

        public string CompanyName { get; set; }

        public string SubscriptionPlanId { get; set; }

        public string WhatsappChatApiInstanceId { get; set; }

        public string WhatsappChatApiInstanceName { get; set; }

        public string WhatsappChatApiInstanceSender { get; set; }

        public string LastStatus { get; set; }

        public string Type { get; set; }

        public DateTime CreatedAt { get; set; }
    }

    public class FacebookConnectedDto
    {
        public string CompanyId { get; set; }

        public string CompanyName { get; set; }

        public string SubscriptionPlanId { get; set; }

        public string FacebookPageId { get; set; }

        public string FacebookPageName { get; set; }

        public string LastStatus { get; set; }

        public DateTime CreatedAt { get; set; }

        public string Type { get; set; }
    }

    public class GetCmsCompaniesResponse
    {
        public List<CmsCompanyListItemView> Companies { get; set; }
    }

    public class GetCmsCompanyAdditionalInfoResponse
    {
        public List<CmsCompanyAdditionalInfoViewModel> CmsCompanyAdditionalInfos { get; set; }
    }

    public class CmsCompanyListItemView
    {
        public string Id { get; set; }

        public string CompanyName { get; set; }

        public int? UserProfileCount { get; set; }

        public int StaffCount { get; set; }

        public string CompanyOwnerName { get; set; }

        public string ActivationOwnerName { get; set; }

        public CmsCompanyListConnectedChannelView ConnectedChannel { get; set; }

        public DateTime CreateAt { get; set; }

        public string SubscriptionPlanId { get; set; }

        public List<string> AddOnPlanIds { get; set; }

        public DateTime? SubscriptionPlanEndDay { get; set; }

        public decimal MonthlyRecurringRevenue { get; set; }

        public DateTime? LastLoginAt { get; set; }

        public string CompanyCountry { get; set; }

        public string CmsLeadSource { get; set; }

        public string CmsCompanyIndustry { get; set; }

        public string HubSpotCompanyObjectId { get; set; }

        [JsonIgnore]
        public List<BillRecord> BillRecords { get; set; }

        [JsonIgnore]
        public bool IsBankTransfer { get; set; }

        [JsonIgnore]
        public bool IsStripePlanCancelled { get; set; }

        public bool IsDeleted { get; set; }

        public CompanyType CompanyType { get; set; }
    }

    public class CmsTwilioUsageDto
    {
        public string CompanyId { get; set; }

        public string CompanyName { get; set; }

        // Twilio Response
        public string TwilioAccountId { get; set; }

        public string FriendlyName { get; set; }

        public DateTime? DateCreated { get; set; }

        public DateTime? DateUpdated { get; set; }

        public string SubAccountStatus { get; set; }

        // Twilio Usage Record
        public long TwilioUsageRecordId { get; set; }

        public decimal? TotalCreditValue { get; set; }

        public decimal? TotalPrice { get; set; }

        public string Currency { get; set; }

        public decimal? Balance => TotalCreditValue - TotalPrice;

        // Whatsapp Config
        public long? WhatsAppConfigId { get; set; }

        public string WhatsAppSender { get; set; }

        public string ChannelName { get; set; }

        public DateTime? ConnectedDateTime { get; set; }
    }

    public class CmsCompanyListConnectedChannelView
    {
        public int WhatsAppConfigCount { get; set; }

        public int InstagramConfigCount { get; set; }

        public int FacebookConfigCount { get; set; }

        public int WhatsappChatAPIConfigCount { get; set; }

        public int Whatsapp360DialogConfigCount { get; set; }

        public int WebClientSenderCount { get; set; }

        public int LineConfigCount { get; set; }

        public int SMSConfigCount { get; set; }

        public int ShoplineConfigCount { get; set; }

        public int ShopifyConfigCount { get; set; }

        public int TelegramConfigCount { get; set; }

        public int ViberConfigCount { get; set; }

        public int WeChatConfigCount { get; set; }

        public int EmailConfigCount { get; set; }

        public int StripePaymentConfigCount { get; set; }

        public int WhatsappCloudApiConfigCount { get; set; }
    }

    public class CompanyPublicApiKeyDto
    {
        public string APIKey { get; set; }

        public int? CallLimit { get; set; }

        public int Calls { get; set; }

        public DateTime CreatedAt { get; set; }
    }

    public class CmsCompanyStaffDto
    {
        public long StaffId { get; set; }

        public string UserId { get; set; }

        public string FirstName { get; set; }

        public string LastName { get; set; }

        public string DisplayName { get; set; }

        public string UserName { get; set; }

        public string Email { get; set; }

        public string PhoneNumber { get; set; }

        public string Position { get; set; }

        public DateTime CreatedAt { get; set; }

        public DateTime LastLoginAt { get; set; }

        public bool EmailConfirmed { get; set; }

        [JsonConverter(typeof(StringEnumConverter))]
        public StaffStatus Status { get; set; }

        [JsonConverter(typeof(StringEnumConverter))]
        public StaffUserRole RoleType { get; set; }

        public string TimeZoneInfoId { get; set; }

        public MyTimeZoneInfo TimeZoneInfo { get; set; }

        public int Order { get; set; }

        public List<CmsCompanyTeamDto> AssociatedTeams { get; set; } = new ();
    }

    public class CmsCompanyTeamDto
    {
        public long Id { get; set; }

        public string TeamName { get; set; }

        public List<long> TeamMemberStaffId { get; set; }
    }

    public class DivingUserInfoResponse : UserInfoResponse
    {
        public DateTime DiveAt { get; set; }
    }

    public class CmsBillRecordDto
    {
        public long Id { get; set; }

        public string CompanyId { get; set; }

        public string SubscriptionPlanId { get; set; }

        public string SubscriptionPlanName { get; set; }

        public SubscriptionPlan SubscriptionPlan { get; set; }

        public DateTime PeriodStart { get; set; }

        public DateTime PeriodEnd { get; set; }

        public double PayAmount { get; set; }

        public string Currency { get; set; }

        public long Quantity { get; set; }

        public List<string> PaymentBreakDowns { get; set; }

        public string invoice_Id { get; set; }

        public string stripe_subscriptionId { get; set; }

        public string customerId { get; set; }

        public string customer_email { get; set; }

        public string hosted_invoice_url { get; set; }

        public string invoice_pdf { get; set; }

        public string chargeId { get; set; }

        public long amount_due { get; set; }

        public long amount_paid { get; set; }

        public long amount_remaining { get; set; }

        public bool IsFreeTrial { get; set; }

        [JsonConverter(typeof(StringEnumConverter))]
        public BillStatus Status { get; set; }

        [JsonConverter(typeof(StringEnumConverter))]
        public PaymentStatus PaymentStatus { get; set; }

        public string PurchaseStaffId { get; set; }

        public string PurchaseStaffName { get; set; }

        public DateTime Created { get; set; }

        public bool IsDeletable { get; set; }

        public long? UpgradeFromBillRecordId { get; set; }

        public long? DowngradeFromBillRecordId { get; set; }

        public DateTime? UpdatedAt { get; set; }

        public bool IsIrregularPlan { get; set; }

        public string PaymentIntervalType { get; set; }

        public List<PaymentSplit> PaymentSplits { get; set; }

        public int? PaymentTerms { get; set; }

        public int SubscriptionInterval { get; set; }

        public int? NoticePeriod { get; set; }

        public List<CmsSalesPaymentRecordDto> CmsSalesPaymentRecords { get; set; } = new ();

        public Dictionary<string, string> Metadata { get; set; }

        public bool IsAutoRenewalEnabled { get; set; } = true;
    }

    public class CmsBillRecordLiteDto
    {
        public long Id { get; set; }

        public string SubscriptionPlanId { get; set; }

        public string SubscriptionPlanName { get; set; }

        public SubscriptionPlan SubscriptionPlan { get; set; }

        public DateTime PeriodStart { get; set; }

        public DateTime PeriodEnd { get; set; }

        public double PayAmount { get; set; }

        public string Currency { get; set; }

        public List<string> PaymentBreakDowns { get; set; }

        public string invoice_Id { get; set; }

        public string stripe_subscriptionId { get; set; }

        public string customerId { get; set; }

        public string customer_email { get; set; }

        public string hosted_invoice_url { get; set; }

        public string invoice_pdf { get; set; }

        public string chargeId { get; set; }

        public long amount_due { get; set; }

        public long amount_paid { get; set; }

        public long amount_remaining { get; set; }

        public bool IsFreeTrial { get; set; }

        [JsonConverter(typeof(StringEnumConverter))]
        public BillStatus Status { get; set; }

        [JsonConverter(typeof(StringEnumConverter))]
        public PaymentStatus PaymentStatus { get; set; }

        public string PurchaseStaffId { get; set; }

        public string PurchaseStaffName { get; set; }

        public DateTime Created { get; set; }

        public bool IsDeletable { get; set; }

        public long? UpgradeFromBillRecordId { get; set; }

        public long? DowngradeFromBillRecordId { get; set; }

        public DateTime? UpdatedAt { get; set; }

        public bool IsIrregularPlan { get; set; }

        public string PaymentIntervalType { get; set; }

        public List<PaymentSplit> PaymentSplits { get; set; }

        public int? PaymentTerms { get; set; }

        public int SubscriptionInterval { get; set; }

        public int? NoticePeriod { get; set; }
    }

    public class TwilioTopUpLogDto
    {
        public long Id { get; set; }

        public long TwilioUsageRecordId { get; set; }

        public string TwilioAccountSid { get; set; }

        public decimal TopUpAmount { get; set; }

        public string InternalUserName { get; set; }

        public long? AmountTotal { get; set; }

        public string InvoiceId { get; set; }

        public string CustomerId { get; set; }

        public string CustomerEmail { get; set; }

        public string Currency { get; set; }

        [JsonConverter(typeof(StringEnumConverter))]
        public TwilioTopUpMethod Method { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    }

    public class CompanyWhatsapp360DialogTopUpConfigViewModel
    {
        public long Id { get; set; }

        public string ClientId { get; set; }

        public string PartnerId { get; set; }

        public TopUpMode TopUpMode { get; set; }
    }

    public class CmsCompanyStaffData
    {
        public string CompanyId { get; set; }

        public string CompanyName { get; set; }

        public string SubscriptionPlanId { get; set; }

        public long StaffId { get; set; }

        public string UserId { get; set; }

        public string Email { get; set; }

        public string PhoneNumber { get; set; }

        public string FirstName { get; set; }

        public string LastName { get; set; }

        public string DisplayName { get; set; }

        public string UserRole { get; set; }

        public string IsOwner { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime LastLoginAt { get; set; } = DateTime.UtcNow;
    }

    public class CmsSleekPayReportDataWithCompany
    {
        public string StripeAccountId { get; set; }

        public string StripeAccountName { get; set; }

        [Column(TypeName = "decimal(18,5)")]
        public decimal OfferedRate { get; set; }

        [Column(TypeName = "decimal(18,5)")]
        public decimal CostRate { get; set; }

        [Column(TypeName = "decimal(18,5)")]
        public decimal GrossEarning { get; set; }

        [Column(TypeName = "decimal(18,5)")]
        public decimal Cost { get; set; }

        [Column(TypeName = "decimal(18,5)")]
        public decimal AverageOrderValue { get; set; }

        [Column(TypeName = "decimal(18,5)")]
        public decimal NetEarning { get; set; }

        [Column(TypeName = "decimal(18,5)")]
        public decimal VariableCostRate { get; set; }

        [Column(TypeName = "decimal(18,5)")]
        public decimal Volume { get; set; }

        [Column(TypeName = "decimal(18,5)")]
        public decimal PerAuthFees { get; set; }

        public string Currency { get; set; }

        public long OrderNos { get; set; }

        public long FailedTransactionsNos { get; set; }

        [Format("yyyy-MM-dd'T'HH:mm:ss'Z'")]
        public DateTime StartActivityDate { get; set; }

        [Format("yyyy-MM-dd'T'HH:mm:ss'Z'")]
        public DateTime EndActivityDate { get; set; }

        public string CompanyId { get; set; }

        public string CompanyName { get; set; }
    }

    public class CompanyIdNamePair
    {
        public string CompanyId { get; set; }

        public string CompanyName { get; set; }
    }

    public class Migrated360DialogAssignmentRulesResponse
    {
        public int MigratedActionsCount { get; set; }

        public List<string> MigratedAssignmentRuleNames { get; set; } = new List<string>();

        public int MigratedAssignmentRuleConditionsCount { get; set; }

        public List<string> MigratedAssignmentConditionNames { get; set; } = new List<string>();
    }

    public class MigratedWhatsapp360DialogDefaultChannelResponse
    {
        public List<string> MigratedWhatsAppNumber { get; set; } = new List<string>();

        public List<string> MigratedDefaultChannelTeams { get; set; } = new List<string>();

        public int MigratedDefaultChannelsCount { get; set; }
    }

    public class MigratedWhatsapp360DialogLastChannelResponse
    {
        public List<string> MigratedWhatsAppNumber { get; set; } = new List<string>();

        public List<string> MigratedLastChannelContactsNames { get; set; } = new List<string>();

        public int MigratedLastChannelContactsCount { get; set; }
    }

    public class ExpiringCompanySubscriptionPlan
    {
        public string CompanyId { get; set; }

        public string CompanyName { get; set; }

        public string PlanId { get; set; }

        public string PlanName { get; set; }

        public DateTime PeriodEnd { get; set; }

        public double PayAmount { get; set; }

        public string StripeId { get; set; }

        public string CompanyOwnerName { get; set; }

        public string ActivationOwnerName { get; set; }

        public string AccountOwnerName { get; set; }

        public DateTime? LastLoginAt { get; set; }

        public string HubSpotCompanyObjectId { get; set; }
    }
}