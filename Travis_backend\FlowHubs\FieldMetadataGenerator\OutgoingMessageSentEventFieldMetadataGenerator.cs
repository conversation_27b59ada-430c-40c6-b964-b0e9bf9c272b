﻿#nullable enable
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.Database.Services;
using Travis_backend.Extensions;
using Travis_backend.FlowHubs.Constants;
using Travis_backend.FlowHubs.Models;
using Travis_backend.FlowHubs.Services;
using Travis_backend.Utils;
using Newtonsoft.Json.Linq;

namespace Travis_backend.FlowHubs.FieldMetadataGenerator;

public sealed class OutgoingMessageSentEventFieldMetadataGenerator :
    FlowHubEventFieldMetadataGeneratorBase,
    IFlowHubEventFieldMetadataGenerator
{
    public string EventName => TriggerIds.OutgoingMessageSent;

    public Dictionary<string, string> FieldPathOptionRequestPathDict { get; } = new()
    {
        { "contact.labels[*].LabelValue", "FlowHub/SelectOptions/Labels" },
        { "channel_id", "FlowHub/SelectOptions/Channels" },
        { "lists[*].id", "FlowHub/SelectOptions/ContactLists" },
        { "sleekflow_staff_id", "FlowHub/SelectOptions/Staffs" },
        { "message.message_type", "FlowHub/SelectOptions/MessageTypes" },
        { "message.message_status", "FlowHub/SelectOptions/MessageStatuses" },
        { "message.message_delivery_type", "FlowHub/SelectOptions/MessageDeliveryTypes" },
        { "contact.ContactOwner", "FlowHub/SelectOptions/Staffs" },
        { "contactOwner.id", "FlowHub/SelectOptions/Staffs" },
        { "contact.LastChannel", "FlowHub/SelectOptions/ChannelTypes" }
    };

    public OutgoingMessageSentEventFieldMetadataGenerator(
        IDbContextService dbContextService,
        IFieldMetadataVariableVisibilityService fieldMetadataVariableVisibilityService)
        : base(dbContextService, fieldMetadataVariableVisibilityService)
    {
    }

    public async Task<(HashSet<FieldMetadata> FieldMetadataSet, Type EventBodyType, string ContactObjectName)> GenerateAsync(
        string companyId,
        Dictionary<string, object?>? parameters)
    {
        var (userProfileDict, customFieldOptionsRequestPathDict) = await GenerateUserProfileDictAsync(companyId);
        var eventBody = JsonUtils.GenerateSampleObjectFromType<OnMessageSentEventBody>();

        eventBody.Contact = userProfileDict;
        eventBody.ConversationId = userProfileDict["conversation_id"].ToString()!;

        eventBody.Message.MessageBody = new MessageBody
        {
            AudioMessage = new AudioMessageObject { Id = null!, Provider = null!, Caption = null! },
            VideoMessage = new VideoMessageObject { Id = null!, Provider = null!, Caption = null! },
            ImageMessage = new ImageMessageObject { Id = null!, Provider = null!, Caption = null! },
            DocumentMessage = new DocumentMessageObject { Id = null!, Provider = null!, Caption = null! }
        };

        var eventBodyJsonString = eventBody.ToJson();
        var eventBodyJson = JObject.Parse(eventBodyJsonString);
        AddContactExpansionToEventBodyJson(eventBodyJson);

        var jsonData = JsonUtils.SimplifyJsonData(eventBodyJson.ToString());

        var fieldMetadataSet = GenerateFilteredFieldMetadata(jsonData, EventName);

        PopulateOptionRequestPath(
            fieldMetadataSet,
            customFieldOptionsRequestPathDict,
            FieldPathOptionRequestPathDict);

        return (fieldMetadataSet, typeof(OnMessageSentEventBody), nameof(eventBody.Contact));
    }
}