using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Newtonsoft.Json;

namespace Travis_backend.Extensions;

public static class ObjectExtension
{
    public static T ToObject<T>(this IDictionary<string, object> source)
        where T : class, new()
    {
        if (source is null)
        {
            return null;
        }

        // 1. Serialize the dictionary to a JSON string.
        var jsonString = JsonConvert.SerializeObject(source);

        // 2. Deserialize the JSON string to the target object.
        // This handles casing, type conversion, nested objects, and everything else.
        return JsonConvert.DeserializeObject<T>(jsonString);
    }

    public static IDictionary<string, object> AsDictionary(
        this object source,
        BindingFlags bindingAttr = BindingFlags.DeclaredOnly | BindingFlags.Public | BindingFlags.Instance)
    {
        return source.GetType().GetProperties(bindingAttr).ToDictionary(
            propInfo => propInfo.Name,
            propInfo => propInfo.GetValue(source, null));
    }
}