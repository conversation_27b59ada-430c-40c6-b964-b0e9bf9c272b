﻿using System;
using System.Collections.Generic;
using Travis_backend.Enums;
using Travis_backend.InternalDomain.Models;

namespace Travis_backend.InternalDomain.ViewModels;

public class CmsCompanyMrrAnalyticDto
{
    public DateOnly Date { get; set; }

    public string CompanyId { get; set; }

    public string SubscriptionPlanId { get; set; }

    public decimal MonthlyRecurringRevenueUsd { get; set; }
}

public class CmsCompanyAnalyticDto
{
    public string Id { get; set; }

    public string CompanyName { get; set; }

    public string CompanyCountry { get; set; }

    public DateTime CreatedAt { get; set; }

    public string CmsLeadSource { get; set; }

    public string CmsCompanyIndustry { get; set; }

    public List<CmsAnalyticBillRecordDto> BillRecords { get; set; }

    public string TimeZoneInfoId { get; set; }
}

public class CmsAnalyticBillRecordDto
{
    public long Id { get; set; }

    public string SubscriptionPlanId { get; set; }

    public DateTime PeriodStart { get; set; }

    public DateTime PeriodEnd { get; set; }

    public double PayAmount { get; set; }

    public string Currency { get; set; }

    public BillStatus Status { get; set; }

    public List<CmsSalesPaymentRecordAnalyticDto> CmsSalesPaymentRecords { get; set; } = new();

    public DateTime Created { get; set; }

    public Dictionary<string, string> metadata { get; set; } = new();

    public bool IsIrregularPlan { get; set; }
}

public class CmsAnalyticSalesPaymentRecordDto
{
    public long Id { get; set; }

    public decimal SubscriptionFee { get; set; }

    public decimal OneTimeSetupFee { get; set; }

    public decimal WhatsappCreditAmount { get; set; }

    public decimal? Discount { get; set; }

    public int? PeriodInMonths { get; set; }

    public string Currency { get; set; }
}

public class DetailMonthlyRecurringRevenueDto
{
    public decimal MonthlyRecurringRevenue { get; set; }

    public PlanAndRevenueDto SubscriptionPlanRevenue { get; set; }

    public List<PlanAndRevenueDto> AddOnRevenues { get; set; }
}

public class PlanAndRevenueDto
{
    public string PlanId { get; set; }

    public decimal MonthlyRecurringRevenue { get; set; }
}

public class CmsDailyAnalyticByContactOwnersDto
{
    public List<string> ContactOwnerIds { get; set; }

    public List<string> ContactOwnerNames { get; set; }

    public List<string> TeamNames { get; set; }

    public string ContactOwnerType { get; set; }

    public List<CmsDailyAnalyticDto> DailyAnalytics { get; set; } = new();
}

public class CmsDailyMonthlyRecurringRevenueAnalyticByContactOwnersDto
{
    public List<string> ContactOwnerIds { get; set; }

    public List<string> ContactOwnerNames { get; set; }

    public List<string> TeamNames { get; set; }

    public string ContactOwnerType { get; set; }

    public List<CmsDailyMonthlyRecurringRevenueAnalyticDto> DailyMonthlyRecurringRevenueAnalytics { get; set; } = new();
}

public class CmsDailyRevenueAnalyticByContactOwnersDto
{
    public List<string> ContactOwnerIds { get; set; }

    public List<string> ContactOwnerNames { get; set; }

    public List<string> TeamNames { get; set; }

    public string ContactOwnerType { get; set; }

    public List<CmsDailyRevenueAnalyticDto> DailyRevenueAnalytics { get; set; } = new();
}

public class CmsDailyPlanDistributionAnalyticByContactOwnersDto
{
    public List<string> ContactOwnerIds { get; set; }

    public List<string> ContactOwnerNames { get; set; }

    public List<string> TeamNames { get; set; }

    public string ContactOwnerType { get; set; }

    public List<CmsDailyPlanDistributionAnalyticDto> DailyPlanDistributionAnalytics { get; set; } = new();
}

public class CmsDailyAnalyticByPartnerStackGroupNamesDto
{
    public List<string> PartnerStackGroupNames { get; set; }

    public List<CmsDailyAnalyticDto> DailyAnalytics { get; set; } = new();
}

public class CmsDailyMonthlyRecurringRevenueAnalyticByPartnerStackGroupNamesDto
{
    public List<string> PartnerStackGroupNames { get; set; }

    public List<CmsDailyMonthlyRecurringRevenueAnalyticDto> DailyMonthlyRecurringRevenueAnalytics { get; set; } = new();
}

public class CmsDailyRevenueAnalyticByPartnerStackGroupNamesDto
{
    public List<string> PartnerStackGroupNames { get; set; }

    public List<CmsDailyRevenueAnalyticDto> DailyRevenueAnalytics { get; set; } = new();
}

public class CmsDailyPlanDistributionAnalyticByPartnerStackGroupNamesDto
{
    public List<string> PartnerStackGroupNames { get; set; }

    public List<CmsDailyPlanDistributionAnalyticDto> DailyPlanDistributionAnalytics { get; set; } = new();
}

public class CmsDailyAnalyticDto
{
    public string Date { get; set; }

    public decimal MonthlyRecurringRevenue { get; set; }

    public decimal DailyRevenue { get; set; }

    public decimal SubscriptionPlanRevenue { get; set; }

    public decimal OneTimeSetupFeeRevenue { get; set; }

    public decimal OneTimeSetupFeeRevenueBasedOnPaidAt { get; set; }

    public decimal MarkupRevenue { get; set; }

    public List<CmsCompanyRevenueBreakDownDto> CompanyRevenueBreakDowns { get; set; }
}

public class CmsDailyMonthlyRecurringRevenueAnalyticDto
{
    public string Date { get; set; }

    public decimal MonthlyRecurringRevenue { get; set; }

    public List<CmsCompanyRevenueBreakDownDto> CompanyRevenueBreakDowns { get; set; }
}

public class CmsDailyRevenueAnalyticDto
{
    public string Date { get; set; }

    public decimal DailyRevenue { get; set; }

    public decimal SubscriptionPlanRevenue { get; set; }

    public decimal OneTimeSetupFeeRevenue { get; set; }

    public decimal OneTimeSetupFeeRevenueBasedOnPaidAt { get; set; }

    public decimal MarkupRevenue { get; set; }
}

public class CmsDailyPlanDistributionAnalyticDto
{
    public string Date { get; set; }

    public List<CmsPlanDistributionDto> SubscriptionPlanDistribution { get; set; }

    public List<CmsPlanDistributionDto> AddOnPlanDistribution { get; set; }
}

public class CmsDailyRevenueDto
{
    public string Date { get; set; }

    public decimal DailyRevenue { get; set; }
}

public class MonthlyCompanyRevenueBreakDowns
{
    public string YearMonth { get; set; }

    public List<CmsCompanyRevenueBreakDownDto> CompanyRevenueBreakDowns { get; set; }
}

public class CmsDailyDistributionAnalyticDto
{
    public string Date { get; set; }

    public decimal MonthlyRecurringRevenue { get; set; }

    public List<CmsAnalyticDistributionDto> CountryDistribution { get; set; }

    public List<CmsAnalyticDistributionDto> LeadSourceDistribution { get; set; }

    public List<CmsAnalyticDistributionDto> IndustryDistribution { get; set; }
}

public class CmsCompanyRevenueBreakDownDto
{
    public string CompanyId { get; set; }

    public string CompanyName { get; set; }

    public string SubscriptionPlanId { get; set; }

    public decimal MonthlyRecurringRevenue { get; set; }
}

public class CmsPlanDistributionDto : CmsDistributionBase
{
    public string PlanId { get; set; }
}

public class CmsFreeTrialDistributionDto
{
    public string Plan { get; set; }

    public List<string> CompanyIds { get; set; }

    public int Count { get; set; }
}

public class CmsAnalyticDistributionDto : CmsDistributionBase
{
    public string AnalyticSourceName { get; set; }
}

public abstract class CmsDistributionBase
{
    public int Count { get; set; } = 0;

    public decimal MonthlyRecurringRevenue { get; set; } = 0;
}

public class RevenueBreakdown
{
    public decimal TotalRevenue => SubscriptionPlanRevenue + OneTimeSetupFeeRevenue + MarkupRevenue;

    public decimal SubscriptionPlanRevenue { get; set; } = 0;

    public decimal OneTimeSetupFeeRevenue { get; set; } = 0;

    public decimal OneTimeSetupFeeRevenueBasedOnPaidAt { get; set; }

    public decimal MarkupRevenue { get; set; } = 0;

    public static RevenueBreakdown operator +(RevenueBreakdown left, RevenueBreakdown right)
    {
        if (right == null)
        {
            return left;
        }

        var result = new RevenueBreakdown
        {
            SubscriptionPlanRevenue = left.SubscriptionPlanRevenue + right.SubscriptionPlanRevenue,
            OneTimeSetupFeeRevenue = left.OneTimeSetupFeeRevenue + right.OneTimeSetupFeeRevenue,
            OneTimeSetupFeeRevenueBasedOnPaidAt =
                left.OneTimeSetupFeeRevenueBasedOnPaidAt + right.OneTimeSetupFeeRevenueBasedOnPaidAt,
            MarkupRevenue = left.MarkupRevenue + right.MarkupRevenue
        };

        return result;
    }
}

public class CmsDailyAccruedRevenueAnalyticDto
{
    public string Date { get; set; }

    public decimal SubscriptionPlanRevenue { get; set; }

    public decimal OneTimeSetupFeeRevenue { get; set; }

    public decimal SleekPayRevenue { get; set; }

    public decimal TopUpRevenue { get; set; }

    public List<CmsAccruedRevenueBreakDownDto> CmsAccruedRevenueBreakDowns { get; set; }
}

public class CmsAccruedRevenueBreakDownDto
{
    public string CompanyId { get; set; }

    public string CompanyName { get; set; }

    public string FacebookBusinessId { get; set; }

    public string FacebookBusinessName { get; set; }

    public string Type { get; set; }

    public string TopUpType { get; set; }

    public decimal DailyAccruedRevenue { get; set; }

    public List<string> SleekFlowCompanyIds { get; set; }

    public List<string> SleekFlowCompanyNames { get; set; }
}

public class CmsSleekPayCompanyAnalyticDto
{
    public long Id { get; set; }

    public string CompanyName { get; set; }

    public string CompanyId { get; set; }

    public List<CmsSleekPayReportData> CmsSleekPayReportDatas { get; set; }
}

public class CmsWhatsApp360DialogUsageTopUpTransactionLogDto : WhatsApp360DialogUsageTransactionLogDto
{
    public string CompanyName { get; set; }

    public bool IsInternalTestingUse { get; set; }
}

public class CmsTwilioTopUpLogDto
{
    public long Id { get; set; }

    public string CompanyId { get; set; }

    public string CompanyName { get; set; }

    public decimal TopUpAmount { get; set; }

    public bool IsInternalTestingUse { get; set; }

    public string Currency { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}

public class CmsWhatsappCloudApiTopUpDto
{
    public string FacebookBusinessId { get; set; }

    public string FacebookBusinessName { get; set; }

    public string Currency { get; set; }

    public decimal CreditAmount { get; set; }

    public bool IsInternalTestingUse { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public List<string> SleekFlowCompanyIds { get; set; } = new();

    public List<string> SleekFlowCompanyNames { get; set; } = new();
}