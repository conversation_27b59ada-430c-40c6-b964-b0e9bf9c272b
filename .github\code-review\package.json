{"name": "sleekflow-code-review", "version": "1.0.0", "description": "Automated code review application using LangChain and Google Gemini", "main": "index.js", "type": "module", "scripts": {"start": "node index.js --github-token **************************************** --pr-url https://github.com/SleekFlow/Sleekflow/pull/5976", "review": "node index.js"}, "dependencies": {"@langchain/google-genai": "^0.0.16", "langchain": "^0.1.25", "commander": "^11.1.0", "@octokit/rest": "^20.0.2"}, "engines": {"node": ">=18.0.0"}, "keywords": ["code-review", "automation", "langchain", "gemini", "github"], "author": "SleekFlow", "license": "MIT"}