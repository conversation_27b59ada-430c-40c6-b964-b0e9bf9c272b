### Create a new tracking event for a user
POST https://{{host}}/LiveChatV2/Public/Senders/56a2d219-36f7-41a1-9cfa-83fa78657266/Tracking
content-type: application/json

{
  "name": "PAGE_VISITED",
  "channelIdentityId": "ce2f0c3c-82d1-40cd-b8cc-46b55ab60643",
  "startedAt": "2025-07-24T13:40:54.673Z",
  "endedAt": "2025-07-25T08:22:13.630Z",
  "metadata": {
    "visitedPage": {
      "url": "http://localhost:5174/fdsaf",
      "title": "SleekFlow LiveChat Widget - Development"
    },
    "referralPage": {
      "url": "http://localhost:5174/fdsaf",
      "title": "SleekFlow LiveChat Widget - Development"
    },
    "session": {
      "id": "dd5863a3-c3e9-4c0e-8b88-7e8f4edfd050",
      "startedAt": "2025-07-24T13:40:54.673Z",
      "endedAt": "2025-07-25T08:22:13.630Z",
      "landingPage": {
        "url": "http://localhost:5174/",
        "title": "SleekFlow LiveChat Widget - Development"
      },
      "dropoffPage": {
        "url": "http://localhost:5174/fdsaf",
        "title": "SleekFlow LiveChat Widget - Development"
      }
    },
    "lang": "en"
  }
}

