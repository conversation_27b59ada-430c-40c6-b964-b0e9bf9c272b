/**
 * Example usage of the GitHub API module
 * This file demonstrates how to use the three main functions
 */

import { fetchPRInfo, postInlineComment, postSummaryComment } from './github-api.js';

// Example configuration - replace with your actual values
const config = {
    prUrl: 'https://github.com/SleekFlow/Sleekflow/pull/6015',
    githubToken: process.env.GITHUB_TOKEN || '****************************************'
};

/**
 * Example 1: Fetch pull request information
 */
async function exampleFetchPRInfo() {
    try {
        console.log('Fetching PR information...');
        const prInfo = await fetchPRInfo(config.prUrl, config.githubToken);

        console.log('PR Data:', {
            title: prInfo.prData.title,
            author: prInfo.prData.author,
            changedFiles: prInfo.prData.changedFiles,
            additions: prInfo.prData.additions,
            deletions: prInfo.prData.deletions
        });

        console.log('Changed Files:');
        prInfo.files.forEach(file => {
            console.log(`  - ${file.filename} (${file.status}): +${file.additions} -${file.deletions}`);
        });

        return prInfo;
    } catch (error) {
        console.error('Error fetching PR info:', error.message);
        throw error;
    }
}

/**
 * Example 2: Post a summary comment
 */
async function examplePostSummaryComment() {
    try {
        console.log('Posting summary comment...');

        const comment = `
## 🤖 Automated Code Review Summary

This pull request has been automatically reviewed.

### Changes Overview
- **Files Changed**: Multiple files updated
- **Review Status**: ✅ Ready for review

### Key Findings
- Code structure looks good
- No major issues detected
- Consider reviewing the specific inline comments below

---
*This comment was generated automatically by SleekFlow Code Review Bot*
    `.trim();

        const result = await postSummaryComment(config.prUrl, comment, config.githubToken);
        console.log('Summary comment posted:', result.htmlUrl);

        return result;
    } catch (error) {
        console.error('Error posting summary comment:', error.message);
        throw error;
    }
}

/**
 * Example 3: Post an inline comment
 */
async function examplePostInlineComment() {
    try {
        console.log('Posting inline comment...');

        const filePath = '.github/workflows/pulumi_deploy.yml'; // Replace with actual file path
        const lineNumber = 227; // Replace with actual line number
        const comment = `
💡 **Code Review Suggestion**

Consider adding error handling here to make the code more robust:

\`\`\`javascript
try {
  // your existing code
} catch (error) {
  console.error('Error:', error);
  // handle error appropriately
}
\`\`\`

This will prevent unexpected crashes and improve user experience.
    `.trim();

        const result = await postInlineComment(
            config.prUrl,
            filePath,
            lineNumber,
            comment,
            config.githubToken
        );

        const result2 = await postInlineComment(
            config.prUrl,
            filePath,
            270,
            comment,
            config.githubToken
        );
        console.log('Inline comment posted:', result.htmlUrl);

        return result;
    } catch (error) {
        console.error('Error posting inline comment:', error.message);
        throw error;
    }
}

/**
 * Example 4: Complete code review workflow
 */
async function exampleCompleteWorkflow() {
    try {
        console.log('Starting complete code review workflow...');

        // Step 1: Fetch PR information
        const prInfo = await fetchPRInfo(config.prUrl, config.githubToken);
        console.log(`Reviewing PR: "${prInfo.prData.title}" by ${prInfo.prData.author}`);

        // Step 2: Analyze changes (simplified example)
        const issues = [];
        prInfo.files.forEach(file => {
            if (file.filename.endsWith('.js') && file.additions > 50) {
                issues.push(`Large changes in ${file.filename} (+${file.additions} lines)`);
            }
        });

        // Step 3: Post summary comment
        let summaryComment = `
## 🔍 Automated Code Review

### PR Summary
- **Title**: ${prInfo.prData.title}
- **Author**: ${prInfo.prData.author}
- **Files Changed**: ${prInfo.prData.changedFiles}
- **Lines Added**: ${prInfo.prData.additions}
- **Lines Deleted**: ${prInfo.prData.deletions}
    `;

        if (issues.length > 0) {
            summaryComment += `\n\n### ⚠️ Items to Review\n`;
            issues.forEach(issue => {
                summaryComment += `- ${issue}\n`;
            });
        } else {
            summaryComment += `\n\n### ✅ Review Status\nNo major issues detected. Changes look good!`;
        }

        summaryComment += `\n\n---\n*Automated review by SleekFlow Code Review Bot*`;

        await postSummaryComment(config.prUrl, summaryComment, config.githubToken);
        console.log('Code review workflow completed successfully!');

    } catch (error) {
        console.error('Error in complete workflow:', error.message);
        throw error;
    }
}

/**
 * Run examples
 * Uncomment the functions you want to test
 */
async function runExamples() {
    try {
        // Validate configuration
        if (!config.githubToken || config.githubToken === 'your-github-token-here') {
            console.error('Please set GITHUB_TOKEN environment variable or update the config');
            return;
        }

        // Run individual examples
        let info = await exampleFetchPRInfo();
        let sum = await examplePostSummaryComment();
        let inline = await examplePostInlineComment();

        // Run complete workflow
        // await exampleCompleteWorkflow();

        console.log('Examples completed successfully!');
    } catch (error) {
        console.error('Example execution failed:', error.message);
        process.exit(1);
    }
}

// Export functions for use in other modules
export {
    exampleCompleteWorkflow, exampleFetchPRInfo, examplePostInlineComment, examplePostSummaryComment
};

// Run examples if this file is executed directly
// if (import.meta.url === `file://${process.argv[1]}`) {
    runExamples();
// }
