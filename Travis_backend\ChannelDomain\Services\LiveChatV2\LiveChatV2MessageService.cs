using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Travis_backend.Cache;
using Travis_backend.ChannelDomain.Extensions.LiveChatV2Extensions;
using Travis_backend.ChannelDomain.ViewModels.LiveChatV2;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationServices;
using Travis_backend.Database.Services;
using Travis_backend.Enums;
using Travis_backend.MessageDomain.Extensions;
using Travis_backend.MessageDomain.Models;
using Travis_backend.Services;
using Travis_backend.Telemetries;
using Travis_backend.Telemetries.Constants;
using ChannelTypes = Travis_backend.Constants.ChannelTypes;

namespace Travis_backend.ChannelDomain.Services.LiveChatV2;

public interface ILiveChatV2MessageService
{
    public Task<SendLiveChatV2MessageOutput> SendMessageAsync(SendLiveChatV2MessageInput input);

    public Task<GetLiveChatV2MessagesOutput> GetMessagesAsync(GetLiveChatV2MessagesInput input);
}

public class LiveChatV2MessageService : ILiveChatV2MessageService
{
    private readonly IDbContextService _dbContextService;
    private readonly ICacheManagerService _cacheManagerService;
    private readonly IApplicationInsightsTelemetryTracer _applicationInsightsTelemetryTracer;
    private readonly IConversationMessageService _conversationMessageService;
    private readonly IUserProfileService _userProfileService;
    private readonly IIpLocationService _ipLocationService;
    private readonly IMapper _mapper;
    private readonly ILogger<LiveChatV2MessageService> _logger;

    public LiveChatV2MessageService(
        IDbContextService dbContextService,
        ICacheManagerService cacheManagerService,
        IApplicationInsightsTelemetryTracer applicationInsightsTelemetryTracer,
        IConversationMessageService conversationMessageService,
        IMapper mapper,
        IUserProfileService userProfileService,
        IIpLocationService ipLocationService,
        ILogger<LiveChatV2MessageService> logger)
    {
        _dbContextService = dbContextService;
        _cacheManagerService = cacheManagerService;
        _applicationInsightsTelemetryTracer = applicationInsightsTelemetryTracer;
        _conversationMessageService = conversationMessageService;
        _mapper = mapper;
        _userProfileService = userProfileService;
        _ipLocationService = ipLocationService;
        _logger = logger;
    }

    public async Task<SendLiveChatV2MessageOutput> SendMessageAsync(SendLiveChatV2MessageInput input)
    {
        var dbContext = _dbContextService.GetDbContext();
        var sender = await dbContext.SenderWebClientSenders.FirstOrDefaultAsync(
            sender => sender.WebClientUUID == input.SenderId &&
                      sender.ChannelIdentityId == input.ChannelIdentityId);

        if (sender is null)
        {
            return new SendLiveChatV2MessageOutput
            {
                ExitCode = ExitCode.Failure,
                Message = $"Sender {input.SenderId} not found"
            };
        }

        if (!string.IsNullOrEmpty(input.Message.MessageChecksum) &&
            await dbContext.ConversationMessages.AnyAsync(
                x =>
                    x.CompanyId == sender.CompanyId &&
                    x.MessageChecksum == input.Message.MessageChecksum))
        {
            return new SendLiveChatV2MessageOutput
            {
                ExitCode = ExitCode.Failure,
                Message = $"Message with checksum: {input.Message.MessageChecksum} sent before"
            };
        }

        var (userProfile, conversation) = await InitAndLookupContactAsync(sender);

        // transform conversation message view model to Conversation message
        var message = new ConversationMessage
        {
            CompanyId = sender.CompanyId,
            ConversationId = conversation.Id,
            MessageChecksum = input.Message.MessageChecksum,
            Channel = ChannelTypes.LiveChatV2,
            MessageType = input.Message.MessageType,
            MessageContent = input.Message.MessageContent,
            DeliveryType = DeliveryType.Normal,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
            LocalTimestamp = input.Message.LocalTimestamp,
            Status = MessageStatus.Sending,
            IsSentFromSleekflow = false,
            ChannelIdentityId = input.ChannelIdentityId,
            ExtendedMessagePayload = string.IsNullOrEmpty(input.Message.ExtendedMessagePayloadJson)
                ? null
                : JsonConvert.DeserializeObject<ExtendedMessagePayload>(input.Message.ExtendedMessagePayloadJson),
            WebClientSender = sender
        };

        // Pre send handling
        if (message.IsLiveChatV2ContactFormMessage())
        {
            var contactForm = message.ExtendedMessagePayload?
                .ExtendedMessagePayloadDetail?
                .LiveChatV2MessageObject?
                .ContactForm;

            if (contactForm is null)
            {
                return new SendLiveChatV2MessageOutput
                {
                    ExitCode = ExitCode.Failure,
                    Message = "contact form cannot be null"
                };
            }

            var email = contactForm.GetEmail();
            var phone = contactForm.GetPhone();

            var hasAtLeastOneIdentifier = !string.IsNullOrEmpty(email) || !string.IsNullOrEmpty(phone);
            var isDuplicatedContact = false;

            // Refinement 2: Make the query more robust.
            // Only check for an existing contact if we have an email or phone to check with.
            if (hasAtLeastOneIdentifier)
            {
                isDuplicatedContact = await dbContext.UserProfiles.AnyAsync(
                    contact => (!string.IsNullOrEmpty(email) && contact.Email == email) ||
                               (!string.IsNullOrEmpty(phone) && contact.PhoneNumber == phone));
            }

            _logger.LogInformation(
                "[{MethodName}] Save contact form to extended message payload. ContactForm: {ContactForm}, isDuplicatedContact: {IsDuplicatedContact}",
                nameof(SendMessageAsync),
                JsonConvert.SerializeObject(contactForm),
                isDuplicatedContact);

            contactForm.SetDuplicatedContactFlag(isDuplicatedContact);
            message.ExtendedMessagePayload!.ExtendedMessagePayloadDetail!.LiveChatV2MessageObject!.ContactForm =
                contactForm;
        }

        IList<ConversationMessage> result;
        if (input.Message.files is not null && input.Message.files.Any())
        {
            result = await _conversationMessageService.SendFileMessage(conversation, message, input.Message);
        }
        else
        {
            result = await _conversationMessageService.SendMessage(conversation, message);
        }

        if (result.Count <= 0)
        {
            return new SendLiveChatV2MessageOutput
            {
                ExitCode = ExitCode.Failure,
                Message = "Unable to send the message."
            };
        }

        // Post send handling - can be refactored to a new class
        if (message.IsLiveChatV2ContactFormMessage())
        {
            var contactForm = message.ExtendedMessagePayload?
                .ExtendedMessagePayloadDetail?
                .LiveChatV2MessageObject?
                .ContactForm;

            if (contactForm is null)
            {
                return new SendLiveChatV2MessageOutput
                {
                    ExitCode = ExitCode.Failure,
                    Message = "contact form cannot be null"
                };
            }

            var metadata = contactForm.ToMetadata();
            _logger.LogInformation(
                "[{MethodName}] Save contact form to sender metadata. Sender: {Sender}, Metadata: {Metadata}",
                nameof(SendMessageAsync),
                JsonConvert.SerializeObject(sender),
                JsonConvert.SerializeObject(metadata));

            sender.SetMetadata(metadata);
            dbContext.Entry(sender).Property(x => x.Metadata).IsModified = true;
            await dbContext.SaveChangesAsync();

            var isDuplicatedContact = contactForm.GetDuplicatedContactFlag();
            if (!isDuplicatedContact)
            {
                await _userProfileService.UpdateUserProfileCustomFields(
                    companyId: sender.CompanyId,
                    userProfileId: userProfile.Id,
                    staffId: null,
                    addCustomFieldsViewModels: contactForm.ToAddCustomFieldsViewModels(),
                    userProfileSource: UserProfileSource.LiveChatV2);
            }
        }

        // Add the geolocation information to the sender after the end user start a conversation with an agent
        // We can do it after we secure the approval to buy another plan for the geolocation service
        // if (sender.IPAddress is not null && !sender.HasGeoLocation())
        // {
        //     var ipAddressInfo = await _ipLocationService.GetLocationInfoByIpaddressAsync(sender.IPAddress);
        //     var geoLocation = new LiveChatV2SenderGeoLocation
        //     {
        //         CountryCode2 = ipAddressInfo.CountryCode2,
        //         CountryCode3 = ipAddressInfo.CountryCode3,
        //         CountryName = ipAddressInfo.CountryName
        //     };
        //     sender.SetGeoLocation(geoLocation);
        //     dbContext.Entry(sender).Property(x => x.Metadata).IsModified = true;
        //     await dbContext.SaveChangesAsync();
        // }

        return new SendLiveChatV2MessageOutput
        {
            MessageDto = message
        };
    }

    public async Task<GetLiveChatV2MessagesOutput> GetMessagesAsync(GetLiveChatV2MessagesInput input)
    {
        var dbContext = _dbContextService.GetDbContext();

        // Can be refactored here to a repository method
        var sender = await dbContext.SenderWebClientSenders
            .Where(s => s.WebClientUUID == input.SenderId && s.ChannelIdentityId == input.ChannelIdentityId)
            .FirstOrDefaultAsync();

        if (sender is null)
        {
            return new GetLiveChatV2MessagesOutput
            {
                ExitCode = ExitCode.Failure,
                Message = $"Sender {input.SenderId} not found"
            };
        }

        var queryable = dbContext.ConversationMessages
            .AsNoTracking()
            .Where(m =>
                m.CompanyId == sender.CompanyId &&
                m.Channel == ChannelTypes.LiveChatV2 &&
                m.ConversationId == sender.ConversationId);

        queryable = input.Order?.ToLower() == "asc"
            ? queryable.OrderBy(m => m.Timestamp)
            : queryable.OrderByDescending(m => m.Timestamp);

        var messages = await queryable
            .Skip(input.Offset)
            .Take(input.Limit)
            .Include(m => m.UploadedFiles)
            .Include(m => m.Sender)
            .Include(m => m.Receiver)
            .Include(m => m.WebClientSender)
            .Include(m => m.WebClientReceiver)
            .Include(m => m.ExtendedMessagePayload)
            .ToListAsync(cancellationToken: default);

        return new GetLiveChatV2MessagesOutput
        {
            MessageDto = messages
        };
    }

    private async Task<(UserProfile UserProfile, Conversation Conversation)> InitAndLookupContactAsync(
            WebClientSender sender)
    {
        var dbContext = _dbContextService.GetDbContext();
        var existingProfile = await dbContext.UserProfiles
            .Include(p => p.Conversation) // Assuming navigation property exists
            .FirstOrDefaultAsync(x => x.Id == sender.UserProfileId && x.CompanyId == sender.CompanyId);

        if (existingProfile != null)
        {
            return (existingProfile, existingProfile.Conversation);
        }

        var userProfile = new UserProfile
        {
            Id = Guid.NewGuid().ToString(),
            CompanyId = sender.CompanyId,
            FirstName = sender.Name,
            ActiveStatus = ActiveStatus.Inactive
        };

        var conversation = new Conversation
        {
            Id = Guid.NewGuid().ToString(),
            CompanyId = sender.CompanyId,
            MessageGroupName = sender.CompanyId,
            UserProfile = userProfile,
            ActiveStatus = ActiveStatus.Inactive
        };

        // Do all the things in on transaction
        dbContext.UserProfiles.Add(userProfile);
        dbContext.Conversations.Add(conversation);

        sender.UserProfileId = userProfile.Id;
        sender.ConversationId = conversation.Id;

        dbContext.SenderWebClientSenders.Update(sender);
        await dbContext.SaveChangesAsync();

        // Invalidate the cache since we've created a new profile
        var cacheKey = $"webclient_profile_exists_{sender.CompanyId}_{sender.WebClientUUID}";
        await _cacheManagerService.DeleteCacheWithConstantKeyAsync(cacheKey);

        _applicationInsightsTelemetryTracer.TraceEvent(
            TraceEventNames.ContactCreatedWithSource,
            new Dictionary<string, string>
            {
                    {
                        "source", UserProfileSource.WebClient.ToString()
                    },
                    {
                        "company_id", sender.CompanyId
                    }
            },
            new Dictionary<string, double>
            {
                    {
                        "count", 1
                    }
            });

        return (userProfile, conversation);
    }
}