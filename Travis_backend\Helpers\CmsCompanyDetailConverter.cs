﻿using System;
using System.Collections.Generic;
using System.Linq;
using GraphApi.Client.Const.WhatsappCloudApi;
using Sleekflow.Apis.MessagingHub.Model;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.Enums;
using Travis_backend.InternalDomain.Models;
using Travis_backend.Models.ChatChannelConfig;

namespace Travis_backend.Helpers
{
    public static class CmsCompanyDetailConverter
    {
        public static InternalHubSpotCompany ConvertToHubSpotCompany(CmsCompanyDetail companyDetail, IBillRecordRevenueCalculatorService billRecordRevenueCalculatorService)
        {
            if (companyDetail == null)
            {
                return null;
            }

            var hubspotCompany = new InternalHubSpotCompany();

            if (companyDetail.CmsHubSpotCompanyMap != null)
            {
                hubspotCompany.Id = companyDetail.CmsHubSpotCompanyMap.HubSpotCompanyObjectId;
            }
            else
            {
                // For Initial Sync
                hubspotCompany.Country = companyDetail.CompanyCountry;
            }

            // Company Info
            // hubspotCompany.HubSpotOwnerId = companyDetail.HubSpotCompanyOwnerId;
            hubspotCompany.Name = companyDetail.CompanyName;

            hubspotCompany.CompanyType = companyDetail.CompanyType switch
            {
                CompanyType.DirectClient => "DirectClient",
                CompanyType.Reseller => "Reseller",
                CompanyType.ResellerClient => "ResellerClient",
                _ => "DirectClient"
            };

            hubspotCompany.SleekflowCompanyId = companyDetail.CompanyId;
            hubspotCompany.SleekflowCompanyName = companyDetail.CompanyName;
            hubspotCompany.CreateAt = companyDetail.CreateAt.AddHours(8).ToString("yyyy-MM-dd");
            hubspotCompany.LastStaffLoginAt =
                companyDetail.LastStaffLoginAt?.AddHours(8).ToString("yyyy-MM-dd") ?? null;
            hubspotCompany.PowerflowLink = $"https://powerflow.sleekflow.io/companies/detail/{companyDetail.CompanyId}";
            hubspotCompany.CompanySize = companyDetail.CompanySize?.Replace(" ", string.Empty);

            // Subscription, MRR
            var mrrAndInitialPaidDay =
                billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                    companyDetail.BillRecords,
                    companyDetail.TimeZoneInfoId);
            var mrr = mrrAndInitialPaidDay;

            if (companyDetail.CmsCompanyAdditionalInfo?.AllTimeRevenueAnalyticData != null)
            {
                if (companyDetail.CmsCompanyAdditionalInfo.AllTimeRevenueAnalyticData.InitialPaidDate != null)
                {
                    hubspotCompany.InitialPaidDate = companyDetail.CmsCompanyAdditionalInfo.AllTimeRevenueAnalyticData
                        .InitialPaidDate;

                    try
                    {
                        hubspotCompany.LeadTimeForSignupToPaid = (int) (DateTime.ParseExact(
                            companyDetail.CmsCompanyAdditionalInfo.AllTimeRevenueAnalyticData.InitialPaidDate,
                            "yyyy-MM-dd",
                            null) - companyDetail.CreateAt).TotalDays;
                    }
                    catch (Exception e)
                    {
                        // ignored
                    }
                }

                hubspotCompany.InitialMonthlyRecurringRevenue = companyDetail.CmsCompanyAdditionalInfo
                    .AllTimeRevenueAnalyticData.InitialMonthlyRecurringRevenue;

                // hubspotCompany.InitialPaidSubscriptionPlanId = companyDetail.CmsCompanyAdditionalInfo.AllTimeRevenueAnalyticData.InitialPaidSubscriptionPlanId;
                // hubspotCompany.LastPaidSubscriptionPlanId = companyDetail.CmsCompanyAdditionalInfo.AllTimeRevenueAnalyticData.LastPaidSubscriptionPlanId;
                // hubspotCompany.TotalRevenue = companyDetail.CmsCompanyAdditionalInfo.AllTimeRevenueAnalyticData.TotalRevenue;
                // hubspotCompany.TotalSubscriptionPlanRevenue = companyDetail.CmsCompanyAdditionalInfo.AllTimeRevenueAnalyticData.TotalSubscriptionPlanRevenue;
                // hubspotCompany.TotalOneTimeSetupFeeRevenue = companyDetail.CmsCompanyAdditionalInfo.AllTimeRevenueAnalyticData.TotalOneTimeSetupFeeRevenue;
                // hubspotCompany.TotalMarkupRevenue = companyDetail.CmsCompanyAdditionalInfo.AllTimeRevenueAnalyticData.TotalMarkupRevenue;
            }

            var sub = companyDetail.BillRecords
                .OrderByDescending(br => br.created)
                .ThenByDescending(br => br.PayAmount)
                .FirstOrDefault(x => ValidSubscriptionPlan.SubscriptionPlan.Contains(x.SubscriptionPlanId));

            var subscriptionPlan = sub?.SubscriptionPlan;

            var addons = companyDetail.BillRecords
                .Where(b => b.PeriodStart < DateTime.UtcNow && b.PeriodEnd > DateTime.UtcNow)
                .Where(x => ValidSubscriptionPlan.CmsAllAddOn.Contains(x.SubscriptionPlanId))
                .Select(x => x.SubscriptionPlanId)
                .ToList();

            if (sub != null)
            {
                hubspotCompany.MonthlyRecurringRevenue = mrr;
                hubspotCompany.SubscriptionPlan = sub.SubscriptionPlanId;
                hubspotCompany.SubscriptionPlanStartDate = sub.PeriodStart.AddHours(8).ToString("yyyy-MM-dd");
                hubspotCompany.SubscriptionPlanEndDate = sub.PeriodEnd.AddHours(8).ToString("yyyy-MM-dd");
                hubspotCompany.IsBankTransfer =
                    sub.CmsSalesPaymentRecords.Any(x => x.PaymentMethod == PaymentMethod.Bank);
                hubspotCompany.AddOnPlans = string.Join(";", addons);

                if (ValidSubscriptionPlan.FreePlans.Contains(sub.SubscriptionPlanId))
                {
                    hubspotCompany.PaymentTerms = InternalHubSpotCompany.Property.PaymentTermsValue.Free;
                }
                else if (ValidSubscriptionPlan.EnterpriseTier.Contains(sub.SubscriptionPlanId))
                {
                    hubspotCompany.PaymentTerms = InternalHubSpotCompany.Property.PaymentTermsValue.Annually;
                }
                else if (ValidSubscriptionPlan.ProTier.Contains(sub.SubscriptionPlanId) ||
                         ValidSubscriptionPlan.PremiumTier.Contains(sub.SubscriptionPlanId))
                {
                    hubspotCompany.PaymentTerms = sub.SubscriptionPlanId.Contains("yearly")
                        ? InternalHubSpotCompany.Property.PaymentTermsValue.Annually
                        : InternalHubSpotCompany.Property.PaymentTermsValue.Monthly;
                }
            }

            var paidSubscriptionPlans = companyDetail.BillRecords
                .OrderByDescending(br => br.created)
                .ThenByDescending(br => br.PayAmount)
                .Where(
                    br => ValidSubscriptionPlan.SubscriptionPlan.Contains(br.SubscriptionPlanId) && (br.PayAmount > 0 ||
                        (br.CmsSalesPaymentRecords.Count > 0 &&
                         br.CmsSalesPaymentRecords.Exists(pr => pr.SubscriptionFee + pr.OneTimeSetupFee > 0))))
                .ToList();

            if (paidSubscriptionPlans.Count > 0)
            {
                hubspotCompany.InitialPaidSubscriptionPlan = paidSubscriptionPlans[^1].SubscriptionPlanId;
                hubspotCompany.InitialPaidSubscriptionPlanStartDate = paidSubscriptionPlans[^1].PeriodStart.AddHours(8)
                    .ToString("yyyy-MM-dd");
                hubspotCompany.LastPaidSubscriptionPlan = paidSubscriptionPlans[0].SubscriptionPlanId;
                hubspotCompany.LastPaidSubscriptionPlanEndDate = paidSubscriptionPlans[0].PeriodEnd.AddHours(8)
                    .ToString("yyyy-MM-dd");
            }

            // Owner
            hubspotCompany.CompanyOwnerName = companyDetail.Owner?.Identity?.DisplayName;
            hubspotCompany.CompanyOwnerPhoneNumber = companyDetail.Owner?.Identity?.PhoneNumber;
            hubspotCompany.CompanyOwnerPhoneEmail = companyDetail.Owner?.Identity?.Email;

            // Integrations
            hubspotCompany.ShoplineCount = companyDetail.ShoplineConfigCount;
            hubspotCompany.ShopifyCount = companyDetail.ShopifyConfigCount;
            hubspotCompany.ApiKeyCount = companyDetail.ApiKeyCount;
            hubspotCompany.ZapierIntegrationCount = companyDetail.ZapierIntegrationCount;

            // Sum
            hubspotCompany.NumberOfConnectedChannels = companyDetail.NumberOfConnectedChannels;
            hubspotCompany.TypesOfChannels = companyDetail.TypesOfChannels;

            hubspotCompany.TypesOfSoftwareIntegrations = companyDetail.TypesOfSoftwareIntegrations;

            // Usage
            hubspotCompany.ContactCount = companyDetail.ContactCount;
            hubspotCompany.ContactCountLimit = companyDetail.MaximumContacts ?? subscriptionPlan?.MaximumContact;
            hubspotCompany.StaffCount = companyDetail.StaffCount;
            hubspotCompany.StaffCountLimit = companyDetail.MaximumAgents != 0
                ? companyDetail.MaximumAgents
                : subscriptionPlan?.IncludedAgents;
            hubspotCompany.AutomationRuleCount = companyDetail.AutomationCount;
            hubspotCompany.LiveAutomationRuleCount = companyDetail.LiveAutomationCount;
            hubspotCompany.AutomationRuleCountLimit =
                companyDetail.MaximumAutomations ?? subscriptionPlan?.MaximumAutomation;
            hubspotCompany.ActiveAgentsInPastDay = companyDetail.ActiveAgentsInPastDay;
            hubspotCompany.LastAgentMessageSentAt =
                companyDetail.LastAgentMessageSentAt?.AddHours(8).ToString("yyyy-MM-dd");
            hubspotCompany.InboxMessagesInPast7Days = companyDetail.InboxMessagesInPast7Days;

            // Channel
            hubspotCompany.WhatsAppOfficialChannelCount = companyDetail.WhatsAppConfigCount +
                                                          companyDetail.WhatsApp360DialogConfigCount +
                                                          companyDetail.WhatsappCloudApiConfigCount;
            hubspotCompany.WhatsappCloudApiChannelCount = companyDetail.WhatsappCloudApiConfigCount;
            hubspotCompany.MessagingLimit = GetHighestMessagingTier(companyDetail.WhatsappCloudApiConfigs);

            hubspotCompany.WhatsAppChatApiCount = companyDetail.WhatsappChatAPIConfigCount;
            hubspotCompany.InstagramMessengerCount = companyDetail.InstagramConfigCount;
            hubspotCompany.FacebookMessengerCount = companyDetail.FacebookConfigCount;
            hubspotCompany.LiveChat = companyDetail.WebClientSenderCount;
            hubspotCompany.LineCount = companyDetail.LineConfigCount;
            hubspotCompany.SmsCount = companyDetail.SMSConfigCount;
            hubspotCompany.TelegramCount = companyDetail.TelegramConfigCount;
            hubspotCompany.ViberCount = companyDetail.ViberConfigCount;
            hubspotCompany.WeChatCount = companyDetail.WeChatConfigCount;
            hubspotCompany.EmailChannelCount = companyDetail.EmailConfigCount;

            // Twilio
            if (companyDetail.TwilioUsageRecord != null)
            {
                hubspotCompany.TwilioAccountSid = companyDetail.TwilioUsageRecord.TwilioAccountId;
                hubspotCompany.TwilioBalance = companyDetail.TwilioUsageRecord.Balance;
            }

            // 360 Dialog
            if (companyDetail.WhatsApp360DialogUsageRecord is { TopUpMode: TopUpMode.PartnerPayment })
            {
                hubspotCompany.N360dialogBalance = companyDetail.WhatsApp360DialogUsageRecord.Balance;
            }

            hubspotCompany.ConversationCount = companyDetail.ConversationCount;
            hubspotCompany.BroadcastCount = companyDetail.BroadcastCount;

            hubspotCompany.NewEnquiriesInPastDay = companyDetail.NewEnquiriesInPastDay;
            hubspotCompany.NewContactsInPastDay = companyDetail.NewContactsInPastDay;
            hubspotCompany.ActiveConversationsInPastDay = companyDetail.ActiveConversationsInPastDay;

            // Support Ticket
            hubspotCompany.SupportTicketCount = companyDetail.SupportTicketCount;
            hubspotCompany.SupportTicketCountInPastTwoWeek = companyDetail.SupportTicketCountInPastTwoWeek;
            hubspotCompany.SupportTicketCountInPastMonth = companyDetail.SupportTicketCountInPastMonth;

            // SnapShot Data Compare
            hubspotCompany.BroadcastCountInPastWeek = companyDetail.BroadcastCountInPastMonth;
            hubspotCompany.NewContactsDifferenceInPercentage = companyDetail.NewContactsDifferenceInPercentage;
            hubspotCompany.ActiveConversationsDifferenceInPercentage =
                companyDetail.ActiveConversationsDifferenceInPercentage;
            hubspotCompany.NewEnquiriesDifferenceInPercentage = companyDetail.NewEnquiriesDifferenceInPercentage;

            // Payment
            hubspotCompany.PaymentFailedCount = companyDetail.PaymentFailedCount;
            hubspotCompany.PaymentFailedCountInPastThreeMonth = companyDetail.PaymentFailedCountInPastThreeMonth;

            return hubspotCompany;
        }

        public static CmsCompanySubscriptionPlan ConvertToCmsCompanySubscriptionPlan(CmsCompanyDetail companyDetail)
        {
            if (companyDetail == null)
            {
                return null;
            }

            var companySubscriptionPlan = new CmsCompanySubscriptionPlan
            {
                // Company Info
                CompanyId = companyDetail.CompanyId,
                CompanyName = companyDetail.CompanyName
            };

            var sub = companyDetail.BillRecords
                .OrderByDescending(br => br.created)
                .ThenByDescending(br => br.PayAmount)
                .FirstOrDefault(x => ValidSubscriptionPlan.SubscriptionPlan.Contains(x.SubscriptionPlanId));

            companySubscriptionPlan.SubscriptionPlanId = sub?.SubscriptionPlanId;

            return companySubscriptionPlan;
        }

        public static CompanySnapshotData ConvertToCompanySnapshotData(CmsCompanyDetail companyDetail, IBillRecordRevenueCalculatorService billRecordRevenueCalculatorService)
        {
            if (companyDetail == null)
            {
                return null;
            }

            var companySnapshotData = new CompanySnapshotData();

            // Company Info
            companySnapshotData.CompanyId = companyDetail.CompanyId;
            companySnapshotData.CompanyName = companyDetail.CompanyName;
            companySnapshotData.CreateAt = companyDetail.CreateAt;
            companySnapshotData.LastStaffLoginAt = companyDetail.LastStaffLoginAt;
            companySnapshotData.CompanyCountry = companyDetail.CompanyCountry;
            companySnapshotData.HubSpotCompanyObjectId = companyDetail.CmsHubSpotCompanyMap?.HubSpotCompanyObjectId;
            companySnapshotData.CommunicationTools = companyDetail.CommunicationTools == null
                ? string.Empty
                : string.Join(",", companyDetail.CommunicationTools);
            companySnapshotData.Industry = companyDetail.Industry;
            companySnapshotData.OnlineShopSystem = companyDetail.OnlineShopSystem;
            companySnapshotData.CompanySize = companyDetail.CompanySize;
            companySnapshotData.CompanyWebsite = companyDetail.CompanyWebsite;
            companySnapshotData.PlatformUsageIntent = companyDetail.PlatformUsageIntent;

            // Subscription, MRR
            var mrrAndInitialPaidDay =
                billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                    companyDetail.BillRecords,
                    companyDetail.TimeZoneInfoId);
            var mrr = mrrAndInitialPaidDay;

            companySnapshotData.ChurnReason = companyDetail.CmsCompanyAdditionalInfo?.ChurnReason;
            companySnapshotData.CompanyTier = companyDetail.CmsCompanyAdditionalInfo?.CompanyTier;

            if (companyDetail.CmsCompanyAdditionalInfo?.AllTimeRevenueAnalyticData != null)
            {
                if (companyDetail.CmsCompanyAdditionalInfo.AllTimeRevenueAnalyticData.InitialPaidDate != null)
                {
                    try
                    {
                        companySnapshotData.InitialPaidDate = DateTime.ParseExact(
                            companyDetail.CmsCompanyAdditionalInfo.AllTimeRevenueAnalyticData.InitialPaidDate,
                            "yyyy-MM-dd",
                            null);
                        if (companySnapshotData.InitialPaidDate != null)
                        {
                            companySnapshotData.LeadTimeForSignupToPaid =
                                (int) (companySnapshotData.InitialPaidDate.Value - companyDetail.CreateAt).TotalDays;
                        }
                    }
                    catch (Exception e)
                    {
                        // ignored
                    }
                }

                companySnapshotData.InitialMonthlyRecurringRevenue = companyDetail.CmsCompanyAdditionalInfo
                    .AllTimeRevenueAnalyticData.InitialMonthlyRecurringRevenue;
                companySnapshotData.InitialPaidSubscriptionPlanId = companyDetail.CmsCompanyAdditionalInfo
                    .AllTimeRevenueAnalyticData.InitialPaidSubscriptionPlanId;
                companySnapshotData.LastPaidSubscriptionPlanId = companyDetail.CmsCompanyAdditionalInfo
                    .AllTimeRevenueAnalyticData.LastPaidSubscriptionPlanId;
                companySnapshotData.TotalRevenue =
                    companyDetail.CmsCompanyAdditionalInfo.AllTimeRevenueAnalyticData.TotalRevenue;
                companySnapshotData.TotalSubscriptionPlanRevenue = companyDetail.CmsCompanyAdditionalInfo
                    .AllTimeRevenueAnalyticData.TotalSubscriptionPlanRevenue;
                companySnapshotData.TotalOneTimeSetupFeeRevenue = companyDetail.CmsCompanyAdditionalInfo
                    .AllTimeRevenueAnalyticData.TotalOneTimeSetupFeeRevenue;
                companySnapshotData.TotalMarkupRevenue = companyDetail.CmsCompanyAdditionalInfo
                    .AllTimeRevenueAnalyticData.TotalMarkupRevenue;
            }

            var sub = companyDetail.BillRecords
                .OrderByDescending(br => br.created)
                .ThenByDescending(br => br.PayAmount)
                .FirstOrDefault(x => ValidSubscriptionPlan.SubscriptionPlan.Contains(x.SubscriptionPlanId));

            var subscriptionPlan = sub?.SubscriptionPlan;

            var addons = companyDetail.BillRecords
                .Where(b => b.PeriodStart < DateTime.UtcNow && b.PeriodEnd > DateTime.UtcNow)
                .Where(x => ValidSubscriptionPlan.CmsAllAddOn.Contains(x.SubscriptionPlanId))
                .Select(x => x.SubscriptionPlanId)
                .ToList();

            if (sub != null)
            {
                companySnapshotData.MonthlyRecurringRevenue = mrr;
                companySnapshotData.SubscriptionPlan = sub.SubscriptionPlanId;
                companySnapshotData.SubscriptionPlanStartDate = sub.PeriodStart;
                companySnapshotData.SubscriptionPlanEndDate = sub.PeriodEnd;
                companySnapshotData.IsBankTransfer =
                    sub.CmsSalesPaymentRecords.Any(x => x.PaymentMethod == PaymentMethod.Bank);
                companySnapshotData.AddOnPlans = string.Join(";", addons);
            }

            // Contact Owner
            companySnapshotData.ActivationOwnerName = companyDetail.ActivationOwnerName;
            companySnapshotData.CmsCompanyOwnerOwnerName = companyDetail.CmsCompanyOwnerOwnerName;
            companySnapshotData.CmsCsOwnerName = companyDetail.CmsCsOwnerName;
            companySnapshotData.HubSpotCompanyOwnerId = companyDetail.HubSpotCompanyOwnerId;
            companySnapshotData.HubSpotActivationOwnerId = companyDetail.HubSpotActivationOwnerId;
            companySnapshotData.HubSpotCsOwnerId = companyDetail.HubSpotCsOwnerId;

            // Owner
            companySnapshotData.CompanyOwnerName = companyDetail.Owner?.Identity?.DisplayName;
            companySnapshotData.CompanyOwnerPhoneNumber = companyDetail.Owner?.Identity?.PhoneNumber;
            companySnapshotData.CompanyOwnerPhoneEmail = companyDetail.Owner?.Identity?.Email;

            if (!string.IsNullOrWhiteSpace(companyDetail.Owner?.Identity?.Email))
            {
                companySnapshotData.CompanyEmailDomain =
                    EmailDomainNameHelper.Parse(companyDetail.Owner.Identity.Email)?.Domain;
            }

            // Usage
            companySnapshotData.ContactCount = companyDetail.ContactCount;
            companySnapshotData.ContactCountLimit =
                companyDetail.MaximumContacts ?? subscriptionPlan?.MaximumContact ?? 0;
            companySnapshotData.StaffCount = companyDetail.StaffCount;
            companySnapshotData.StaffCountLimit = companyDetail.MaximumAgents != 0
                ? companyDetail.MaximumAgents
                : subscriptionPlan?.IncludedAgents ?? 0;
            companySnapshotData.LiveAutomationRuleCount = companyDetail.LiveAutomationCount;
            companySnapshotData.AutomationRuleCount = companyDetail.AutomationCount;
            companySnapshotData.AutomationRuleCountLimit =
                companyDetail.MaximumAutomations ?? subscriptionPlan?.MaximumAutomation ?? 0;
            companySnapshotData.ActiveAgentsInPastDay = companyDetail.ActiveAgentsInPastDay;
            companySnapshotData.LastAgentMessageSentAt = companyDetail.LastAgentMessageSentAt;
            companySnapshotData.InboxMessagesInPast7Days = companyDetail.InboxMessagesInPast7Days;

            // Channel
            companySnapshotData.WhatsAppOfficialChannelCount = companyDetail.WhatsAppConfigCount +
                                                               companyDetail.WhatsApp360DialogConfigCount +
                                                               companyDetail.WhatsappCloudApiConfigCount;
            companySnapshotData.WhatsAppConfigCount = companyDetail.WhatsAppConfigCount;
            companySnapshotData.WhatsApp360DialogConfigCount = companyDetail.WhatsApp360DialogConfigCount;
            companySnapshotData.WhatsappCloudApiConfigCount = companyDetail.WhatsappCloudApiConfigCount;
            companySnapshotData.WhatsAppChatApiCount = companyDetail.WhatsappChatAPIConfigCount;
            companySnapshotData.InstagramMessengerCount = companyDetail.InstagramConfigCount;
            companySnapshotData.FacebookMessengerCount = companyDetail.FacebookConfigCount;
            companySnapshotData.LiveChat = companyDetail.WebClientSenderCount;
            companySnapshotData.LineCount = companyDetail.LineConfigCount;
            companySnapshotData.SmsCount = companyDetail.SMSConfigCount;
            companySnapshotData.TelegramCount = companyDetail.TelegramConfigCount;
            companySnapshotData.ViberCount = companyDetail.ViberConfigCount;
            companySnapshotData.WeChatCount = companyDetail.WeChatConfigCount;
            companySnapshotData.EmailCount = companyDetail.EmailConfigCount;

            // Sum
            companySnapshotData.NumberOfConnectedChannels = companyDetail.NumberOfConnectedChannels;
            companySnapshotData.TypesOfChannels = companyDetail.TypesOfChannels;

            companySnapshotData.TypesOfSoftwareIntegrations = companyDetail.TypesOfSoftwareIntegrations;

            // Others
            companySnapshotData.CmsLeadSource = companyDetail.CmsLeadSource;
            companySnapshotData.CmsCompanyIndustry = companyDetail.CmsCompanyIndustry;

            // Integrations
            companySnapshotData.ShoplineCount = companyDetail.ShoplineConfigCount;
            companySnapshotData.ShopifyCount = companyDetail.ShopifyConfigCount;
            companySnapshotData.ApiKeyCount = companyDetail.ApiKeyCount;
            companySnapshotData.ZapierIntegrationCount = companyDetail.ZapierIntegrationCount;
            companySnapshotData.StripePaymentCount = companyDetail.StripePaymentCount;

            // Twilio
            if (companyDetail.TwilioUsageRecord != null)
            {
                companySnapshotData.TwilioAccountSid = companyDetail.TwilioUsageRecord.TwilioAccountId;
                companySnapshotData.TwilioBalance = companyDetail.TwilioUsageRecord.Balance;
            }

            // 360
            if (companyDetail.WhatsApp360DialogUsageRecord != null)
            {
                companyDetail.Whatsapp360dialogBalance = companyDetail.WhatsApp360DialogUsageRecord.Balance;
            }

            companySnapshotData.ConversationCount = companyDetail.ConversationCount;
            companySnapshotData.BroadcastCount = companyDetail.BroadcastCount;

            // Analytics
            companySnapshotData.NewEnquiriesInPastDay = companyDetail.NewEnquiriesInPastDay;
            companySnapshotData.NewContactsInPastDay = companyDetail.NewContactsInPastDay;
            companySnapshotData.ActiveConversationsInPastDay = companyDetail.ActiveConversationsInPastDay;

            // Support Ticket
            companySnapshotData.SupportTicketCount = companyDetail.SupportTicketCount;
            companySnapshotData.SupportTicketCountInPastTwoWeek = companyDetail.SupportTicketCountInPastTwoWeek;
            companySnapshotData.SupportTicketCountInPastMonth = companyDetail.SupportTicketCountInPastMonth;

            // SnapShot Data Compare
            companySnapshotData.BroadcastCountInPastMonth = companyDetail.BroadcastCountInPastMonth;
            companySnapshotData.NewContactsDifference = companyDetail.NewContactsDifferenceInPercentage;
            companySnapshotData.NewEnquiriesDifference = companyDetail.NewEnquiriesDifferenceInPercentage;
            companySnapshotData.ActiveConversationsDifference = companyDetail.ActiveConversationsDifferenceInPercentage;

            // Payment
            companySnapshotData.PaymentFailedCount = companyDetail.PaymentFailedCount;
            companySnapshotData.PaymentFailedCountInPastThreeMonth = companyDetail.PaymentFailedCountInPastThreeMonth;
            companySnapshotData.IsDeleted = companyDetail.IsDeleted;

            companySnapshotData.CompanyType = companyDetail.CompanyType;

            companySnapshotData.ActiveContactsInCurrentMonth = companyDetail.ActiveContactsInCurrentMonth;
            companySnapshotData.ActiveContactsInPastMonth = companyDetail.ActiveContactsInPastMonth;
            companySnapshotData.IntegrationCount = companyDetail.IntegrationCount;

            return companySnapshotData;
        }

        public static CompanyBillRecordSnapshotData ConvertToCompanyBillRecordSnapshotData(
            CmsCompanyBillRecordDetail companyDetail)
        {
            var result = new CompanyBillRecordSnapshotData
            {
                Id = companyDetail.Id,
                CompanyId = companyDetail.CompanyId,
                CompanyName = companyDetail.CompanyName,
                ActivationOwnerId = companyDetail.ActivationOwnerId,
                ActivationOwnerName = companyDetail.ActivationOwnerName,
                CompanyOwnerOwnerId = companyDetail.CompanyOwnerOwnerId,
                CompanyOwnerOwnerName = companyDetail.CompanyOwnerOwnerName,
                SubscriptionPlanId = companyDetail.SubscriptionPlanId,
                PeriodStart = companyDetail.PeriodStart,
                PeriodEnd = companyDetail.PeriodEnd,
                Status = companyDetail.Status,
                PaymentStatus = companyDetail.PaymentStatus,
                PayAmount = companyDetail.PayAmount,
                Currency = companyDetail.currency,
                Quantity = companyDetail.quantity,
                UpgradeFromBillRecordId = companyDetail.UpgradeFromBillRecordId,
                DowngradeFromBillRecordId = companyDetail.DowngradeFromBillRecordId,
                StripeInvoiceId = companyDetail.invoice_Id,
                ShopifyChargeId = companyDetail.ShopifyChargeId,
                PurchaseStaffId = companyDetail.PurchaseStaffId,
                UpdatedAt = companyDetail.UpdatedAt,
                Created = companyDetail.created,
                SubscriptionTier = companyDetail.SubscriptionTier,
                PaidByReseller = companyDetail.PaidByReseller,
            };

            if (companyDetail.invoice_Id != null)
            {
                result.PaidByStripe = true;
            }

            if (companyDetail.ShopifyChargeId != null)
            {
                result.PaidByShopify = true;
            }

            if (companyDetail.CmsSalesPaymentRecords is { Count: > 0 })
            {
                result.HasInputPayment = true;

                var firstCmsSalesPaymentRecordsCurrency = companyDetail.CmsSalesPaymentRecords.First().Currency;

                var isAllCurrencySame =
                    companyDetail.CmsSalesPaymentRecords.TrueForAll(
                        x => x.Currency == firstCmsSalesPaymentRecordsCurrency);

                if (result.PayAmount > 0 && result.Currency != firstCmsSalesPaymentRecordsCurrency)
                {
                    if (result.Currency != firstCmsSalesPaymentRecordsCurrency)
                    {
                        isAllCurrencySame = false;
                        result.PayAmount = (double) CurrencyConverter.ConvertToUsd(
                            (decimal) result.PayAmount,
                            result.Currency);
                        result.Currency = "usd";
                    }
                }

                foreach (var record in companyDetail.CmsSalesPaymentRecords)
                {
                    if (isAllCurrencySame)
                    {
                        if (result.PayAmount == 0)
                        {
                            result.Currency = record.Currency;
                        }

                        result.PayAmount += (double) record.SubscriptionFee;
                        result.InputPaymentTotalSubscriptionFee += record.SubscriptionFee;
                        result.InputPaymentTotalOneTimeSetupFee += record.OneTimeSetupFee;
                    }
                    else
                    {
                        result.PayAmount += (double) CurrencyConverter.ConvertToUsd(
                            record.SubscriptionFee,
                            record.Currency);

                        result.InputPaymentTotalSubscriptionFee += CurrencyConverter.ConvertToUsd(
                            record.SubscriptionFee,
                            record.Currency);
                        result.InputPaymentTotalOneTimeSetupFee += CurrencyConverter.ConvertToUsd(
                            record.OneTimeSetupFee,
                            record.Currency);
                    }

                    if (record.PaymentMethod == PaymentMethod.Bank)
                    {
                        result.PaidByBankTransfer = true;
                    }

                    if (record.PaymentMethod == PaymentMethod.Stripe)
                    {
                        result.PaidByBankTransfer = true;
                    }
                }
            }

            return result;
        }

        public static WhatsappCloudApiConversationUsageAnalyticsSnapshotData
            ConvertToWhatsappCloudApiConversationUsageAnalyticsSnapshotData(
                ManagementWhatsappCloudApiConversationUsageAnalytic analytic)
        {
            var result = new WhatsappCloudApiConversationUsageAnalyticsSnapshotData
            {
                FacebookBusinessId = analytic.FacebookBusinessId,
                FacebookBusinessName = analytic.FacebookBusinessName,
                TotalConversations = analytic.SummarizedConversationUsageAnalytic.TotalBusinessInitiatedPaidQuantity +
                                     analytic.SummarizedConversationUsageAnalytic
                                         .TotalBusinessInitiatedFreeTierQuantity +
                                     analytic.SummarizedConversationUsageAnalytic.TotalUserInitiatedPaidQuantity +
                                     analytic.SummarizedConversationUsageAnalytic.TotalUserInitiatedFreeTierQuantity +
                                     analytic.SummarizedConversationUsageAnalytic
                                         .TotalUserInitiatedFreeEntryPointQuantity,
                TotalBusinessInitiatedConversations =
                    analytic.SummarizedConversationUsageAnalytic.TotalBusinessInitiatedPaidQuantity +
                    analytic.SummarizedConversationUsageAnalytic.TotalBusinessInitiatedFreeTierQuantity,
                TotalUserInitiatedConversations =
                    analytic.SummarizedConversationUsageAnalytic.TotalUserInitiatedPaidQuantity +
                    analytic.SummarizedConversationUsageAnalytic.TotalUserInitiatedFreeTierQuantity +
                    analytic.SummarizedConversationUsageAnalytic.TotalUserInitiatedFreeEntryPointQuantity,
                TotalBusinessInitiatedPaidQuantity =
                    analytic.SummarizedConversationUsageAnalytic.TotalBusinessInitiatedPaidQuantity,
                TotalBusinessInitiatedFreeTierQuantity =
                    analytic.SummarizedConversationUsageAnalytic.TotalBusinessInitiatedFreeTierQuantity,
                TotalUserInitiatedPaidQuantity =
                    analytic.SummarizedConversationUsageAnalytic.TotalUserInitiatedPaidQuantity,
                TotalUserInitiatedFreeTierQuantity =
                    analytic.SummarizedConversationUsageAnalytic.TotalUserInitiatedFreeTierQuantity,
                TotalUserInitiatedFreeEntryPointQuantity =
                    analytic.SummarizedConversationUsageAnalytic.TotalUserInitiatedFreeEntryPointQuantity,
                TotalUsedCurrencyIsoCode = analytic.SummarizedConversationUsageAnalytic.TotalUsed.CurrencyIsoCode,
                TotalUsedAmount = analytic.SummarizedConversationUsageAnalytic.TotalUsed.Amount,
                TotalMarkupCurrencyIsoCode = analytic.SummarizedConversationUsageAnalytic?.TotalMarkup?.CurrencyIsoCode,
                TotalMarkupAmount = analytic.SummarizedConversationUsageAnalytic?.TotalMarkup?.Amount,
                TotalTransactionHandlingFeeCurrencyIsoCode =
                    analytic.SummarizedConversationUsageAnalytic?.TotalTransactionHandlingFee?.CurrencyIsoCode,
                TotalTransactionHandlingFeeAmount =
                    analytic.SummarizedConversationUsageAnalytic?.TotalTransactionHandlingFee?.Amount,
            };

            if (analytic.SummarizedConversationUsageAnalytic.ConversationCategoryQuantities != null)
            {
                result.TotalAuthenticationTypeConversations = (analytic.SummarizedConversationUsageAnalytic
                    .ConversationCategoryQuantities
                    .TryGetValue(
                        WhatsappConversationAnalyticConversationCategoryConst.AUTHENTICATION,
                        out var authenticationConversations)
                    ? authenticationConversations
                    : 0) ?? 0;
                result.TotalMarketingTypeConversations = (analytic.SummarizedConversationUsageAnalytic
                    .ConversationCategoryQuantities
                    .TryGetValue(
                        WhatsappConversationAnalyticConversationCategoryConst.MARKETING,
                        out var marketingConversations)
                    ? marketingConversations
                    : 0) ?? 0;
                result.TotalServiceTypeConversations = (analytic.SummarizedConversationUsageAnalytic
                    .ConversationCategoryQuantities
                    .TryGetValue(
                        WhatsappConversationAnalyticConversationCategoryConst.SERVICE,
                        out var serviceConversations)
                    ? serviceConversations
                    : 0) ?? 0;
                result.TotalUtilityTypeConversations = (analytic.SummarizedConversationUsageAnalytic
                    .ConversationCategoryQuantities
                    .TryGetValue(
                        WhatsappConversationAnalyticConversationCategoryConst.UTILITY,
                        out var utilityConversations)
                    ? utilityConversations
                    : 0) ?? 0;
            }

            return result;
        }

        public static WhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData
            ConvertToWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData(
                ManagementWhatsappCloudApiConversationUsageAnalytic analytic,
                DateTime dateTime)
        {
            var result = new WhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData
            {
                FacebookBusinessId = analytic.FacebookBusinessId,
                FacebookBusinessName = analytic.FacebookBusinessName,
                TotalConversations = analytic.SummarizedConversationUsageAnalytic.TotalBusinessInitiatedPaidQuantity +
                                     analytic.SummarizedConversationUsageAnalytic
                                         .TotalBusinessInitiatedFreeTierQuantity +
                                     analytic.SummarizedConversationUsageAnalytic.TotalUserInitiatedPaidQuantity +
                                     analytic.SummarizedConversationUsageAnalytic.TotalUserInitiatedFreeTierQuantity +
                                     analytic.SummarizedConversationUsageAnalytic
                                         .TotalUserInitiatedFreeEntryPointQuantity,
                TotalBusinessInitiatedConversations =
                    analytic.SummarizedConversationUsageAnalytic.TotalBusinessInitiatedPaidQuantity +
                    analytic.SummarizedConversationUsageAnalytic.TotalBusinessInitiatedFreeTierQuantity,
                TotalUserInitiatedConversations =
                    analytic.SummarizedConversationUsageAnalytic.TotalUserInitiatedPaidQuantity +
                    analytic.SummarizedConversationUsageAnalytic.TotalUserInitiatedFreeTierQuantity +
                    analytic.SummarizedConversationUsageAnalytic.TotalUserInitiatedFreeEntryPointQuantity,
                TotalBusinessInitiatedPaidQuantity =
                    analytic.SummarizedConversationUsageAnalytic.TotalBusinessInitiatedPaidQuantity,
                TotalBusinessInitiatedFreeTierQuantity =
                    analytic.SummarizedConversationUsageAnalytic.TotalBusinessInitiatedFreeTierQuantity,
                TotalUserInitiatedPaidQuantity =
                    analytic.SummarizedConversationUsageAnalytic.TotalUserInitiatedPaidQuantity,
                TotalUserInitiatedFreeTierQuantity =
                    analytic.SummarizedConversationUsageAnalytic.TotalUserInitiatedFreeTierQuantity,
                TotalUserInitiatedFreeEntryPointQuantity =
                    analytic.SummarizedConversationUsageAnalytic.TotalUserInitiatedFreeEntryPointQuantity,
                TotalUsedCurrencyIsoCode = analytic.SummarizedConversationUsageAnalytic.TotalUsed.CurrencyIsoCode,
                TotalUsedAmount = analytic.SummarizedConversationUsageAnalytic.TotalUsed.Amount,
                TotalMarkupCurrencyIsoCode = analytic.SummarizedConversationUsageAnalytic?.TotalMarkup?.CurrencyIsoCode,
                TotalMarkupAmount = analytic.SummarizedConversationUsageAnalytic?.TotalMarkup?.Amount,
                TotalTransactionHandlingFeeCurrencyIsoCode =
                    analytic.SummarizedConversationUsageAnalytic?.TotalTransactionHandlingFee?.CurrencyIsoCode,
                TotalTransactionHandlingFeeAmount =
                    analytic.SummarizedConversationUsageAnalytic?.TotalTransactionHandlingFee?.Amount,
            };

            if (analytic.SummarizedConversationUsageAnalytic.ConversationCategoryQuantities != null)
            {
                result.TotalAuthenticationTypeConversations = (analytic.SummarizedConversationUsageAnalytic
                    .ConversationCategoryQuantities
                    .TryGetValue(
                        WhatsappConversationAnalyticConversationCategoryConst.AUTHENTICATION,
                        out var authenticationConversations)
                    ? authenticationConversations
                    : 0) ?? 0;
                result.TotalMarketingTypeConversations = (analytic.SummarizedConversationUsageAnalytic
                    .ConversationCategoryQuantities
                    .TryGetValue(
                        WhatsappConversationAnalyticConversationCategoryConst.MARKETING,
                        out var marketingConversations)
                    ? marketingConversations
                    : 0) ?? 0;
                result.TotalServiceTypeConversations = (analytic.SummarizedConversationUsageAnalytic
                    .ConversationCategoryQuantities
                    .TryGetValue(
                        WhatsappConversationAnalyticConversationCategoryConst.SERVICE,
                        out var serviceConversations)
                    ? serviceConversations
                    : 0) ?? 0;
                result.TotalUtilityTypeConversations = (analytic.SummarizedConversationUsageAnalytic
                    .ConversationCategoryQuantities
                    .TryGetValue(
                        WhatsappConversationAnalyticConversationCategoryConst.UTILITY,
                        out var utilityConversations)
                    ? utilityConversations
                    : 0) ?? 0;
            }

            result.DateTime = dateTime;
            result.Date = dateTime.ToString("yyyy-MM-dd");

            return result;
        }

        public static (ByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData
            ByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshot,
            List<ByWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData>
            ByWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshots)
            ConvertToInternalWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData(
                ManagementWhatsappCloudApiConversationUsageAnalytic analytic,
                DateTime dateTime)
        {
            var date = dateTime.ToString("yyyy-MM-dd");
            var byBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshot =
                new ByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData
                {
                    Date = date,
                    FacebookBusinessId = analytic.FacebookBusinessId,
                    FacebookBusinessName = analytic.FacebookBusinessName,
                    TotalConversations =
                        analytic.SummarizedConversationUsageAnalytic.TotalBusinessInitiatedPaidQuantity +
                        analytic.SummarizedConversationUsageAnalytic
                            .TotalBusinessInitiatedFreeTierQuantity +
                        analytic.SummarizedConversationUsageAnalytic.TotalUserInitiatedPaidQuantity +
                        analytic.SummarizedConversationUsageAnalytic.TotalUserInitiatedFreeTierQuantity +
                        analytic.SummarizedConversationUsageAnalytic
                            .TotalUserInitiatedFreeEntryPointQuantity,
                    TotalBusinessInitiatedConversations =
                        analytic.SummarizedConversationUsageAnalytic.TotalBusinessInitiatedPaidQuantity +
                        analytic.SummarizedConversationUsageAnalytic.TotalBusinessInitiatedFreeTierQuantity,
                    TotalUserInitiatedConversations =
                        analytic.SummarizedConversationUsageAnalytic.TotalUserInitiatedPaidQuantity +
                        analytic.SummarizedConversationUsageAnalytic.TotalUserInitiatedFreeTierQuantity +
                        analytic.SummarizedConversationUsageAnalytic.TotalUserInitiatedFreeEntryPointQuantity,
                    TotalBusinessInitiatedPaidQuantity =
                        analytic.SummarizedConversationUsageAnalytic.TotalBusinessInitiatedPaidQuantity,
                    TotalBusinessInitiatedFreeTierQuantity =
                        analytic.SummarizedConversationUsageAnalytic.TotalBusinessInitiatedFreeTierQuantity,
                    TotalUserInitiatedPaidQuantity =
                        analytic.SummarizedConversationUsageAnalytic.TotalUserInitiatedPaidQuantity,
                    TotalUserInitiatedFreeTierQuantity =
                        analytic.SummarizedConversationUsageAnalytic.TotalUserInitiatedFreeTierQuantity,
                    TotalUserInitiatedFreeEntryPointQuantity =
                        analytic.SummarizedConversationUsageAnalytic.TotalUserInitiatedFreeEntryPointQuantity,
                    TotalUsedCurrencyIsoCode = analytic.SummarizedConversationUsageAnalytic.TotalUsed.CurrencyIsoCode,
                    TotalUsedAmount = analytic.SummarizedConversationUsageAnalytic.TotalUsed.Amount,
                    TotalMarkupCurrencyIsoCode =
                        analytic.SummarizedConversationUsageAnalytic?.TotalMarkup?.CurrencyIsoCode,
                    TotalMarkupAmount = analytic.SummarizedConversationUsageAnalytic?.TotalMarkup?.Amount ?? 0,
                    TotalTransactionHandlingFeeCurrencyIsoCode =
                        analytic.SummarizedConversationUsageAnalytic?.TotalTransactionHandlingFee?.CurrencyIsoCode,
                    TotalTransactionHandlingFeeAmount =
                        analytic.SummarizedConversationUsageAnalytic?.TotalTransactionHandlingFee?.Amount ?? 0,
                    TotalAuthenticationTypeConversations = 0,
                    TotalMarketingTypeConversations = 0,
                    TotalMarketingLiteTypeConversations = 0,
                    TotalServiceTypeConversations = 0,
                    TotalUtilityTypeConversations = 0,
                    TotalAuthenticationPricingVolume = 0,
                    TotalAuthenticationInternationalPricingVolume = 0,
                    TotalMarketingPricingVolume = 0,
                    TotalMarketingLitePricingVolume = 0,
                    TotalReferralConversionPricingVolume = 0,
                    TotalServicePricingVolume = 0,
                    TotalUtilityPricingVolume = 0,
                };

            if (analytic.SummarizedConversationUsageAnalytic.ConversationCategoryQuantities is { Count: > 0 })
            {
                byBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshot.TotalAuthenticationTypeConversations =
                    (analytic.SummarizedConversationUsageAnalytic
                        .ConversationCategoryQuantities
                        .TryGetValue(
                            WhatsappConversationAnalyticConversationCategoryConst.AUTHENTICATION,
                            out var authenticationConversations)
                        ? authenticationConversations
                        : 0) ?? 0;
                byBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshot.TotalMarketingTypeConversations =
                    (analytic.SummarizedConversationUsageAnalytic
                        .ConversationCategoryQuantities
                        .TryGetValue(
                            WhatsappConversationAnalyticConversationCategoryConst.MARKETING,
                            out var marketingConversations)
                        ? marketingConversations
                        : 0) ?? 0;
                byBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshot.TotalMarketingLiteTypeConversations =
                    (analytic.SummarizedConversationUsageAnalytic
                        .ConversationCategoryQuantities
                        .TryGetValue(
                            WhatsappConversationAnalyticConversationCategoryConst.MARKETING_LITE,
                            out var marketingLiteConversations)
                        ? marketingLiteConversations
                        : 0) ?? 0;
                byBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshot.TotalServiceTypeConversations =
                    (analytic.SummarizedConversationUsageAnalytic
                        .ConversationCategoryQuantities
                        .TryGetValue(
                            WhatsappConversationAnalyticConversationCategoryConst.SERVICE,
                            out var serviceConversations)
                        ? serviceConversations
                        : 0) ?? 0;
                byBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshot.TotalUtilityTypeConversations =
                    (analytic.SummarizedConversationUsageAnalytic
                        .ConversationCategoryQuantities
                        .TryGetValue(
                            WhatsappConversationAnalyticConversationCategoryConst.UTILITY,
                            out var utilityConversations)
                        ? utilityConversations
                        : 0) ?? 0;
            }

            if (analytic.SummarizedConversationUsageAnalytic.WhatsappPricingAnalyticSummaries is { Count: > 0 })
            {
                analytic.SummarizedConversationUsageAnalytic.WhatsappPricingAnalyticSummaries.ForEach(pricingSummary =>
                {
                    switch (pricingSummary.PricingCategory)
                    {
                        case WhatsappPricingAnalyticPricingCategoryConst.Authentication:
                            byBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshot
                                .TotalAuthenticationPricingVolume += pricingSummary.Volume;
                            break;
                        case WhatsappPricingAnalyticPricingCategoryConst.AuthenticationInternational:
                            byBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshot
                                .TotalAuthenticationInternationalPricingVolume += pricingSummary.Volume;
                            break;
                        case WhatsappPricingAnalyticPricingCategoryConst.Marketing:
                            byBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshot
                                .TotalMarketingPricingVolume += pricingSummary.Volume;
                            break;
                        case WhatsappPricingAnalyticPricingCategoryConst.MarketingLite:
                            byBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshot
                                .TotalMarketingLitePricingVolume += pricingSummary.Volume;
                            break;
                        case WhatsappPricingAnalyticPricingCategoryConst.ReferralConversion:
                            byBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshot
                                .TotalReferralConversionPricingVolume += pricingSummary.Volume;
                            break;
                        case WhatsappPricingAnalyticPricingCategoryConst.Service:
                            byBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshot
                                .TotalServicePricingVolume += pricingSummary.Volume;
                            break;
                        case WhatsappPricingAnalyticPricingCategoryConst.Utility:
                            byBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshot
                                .TotalUtilityPricingVolume += pricingSummary.Volume;
                            break;
                    }
                });
            }

            var byWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshots =
                new List<ByWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData>();
            analytic.WabaConversationUsageAnalytics.ForEach(waba =>
            {
                var byWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshot =
                    new ByWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData
                    {
                        Date = date,
                        FacebookBusinessId = waba.FacebookBusinessId,
                        FacebookBusinessName = waba.FacebookBusinessName,
                        WabaId = waba.FacebookBusinessWaba.FacebookWabaId,
                        WabaName = waba.FacebookBusinessWaba.FacebookWabaName,
                        TotalConversations = waba.ConversationUsageAnalytic.TotalBusinessInitiatedPaidQuantity +
                                             waba.ConversationUsageAnalytic
                                                 .TotalBusinessInitiatedFreeTierQuantity +
                                             waba.ConversationUsageAnalytic.TotalUserInitiatedPaidQuantity +
                                             waba.ConversationUsageAnalytic.TotalUserInitiatedFreeTierQuantity +
                                             waba.ConversationUsageAnalytic
                                                 .TotalUserInitiatedFreeEntryPointQuantity,
                        TotalBusinessInitiatedConversations =
                            waba.ConversationUsageAnalytic.TotalBusinessInitiatedPaidQuantity +
                            waba.ConversationUsageAnalytic.TotalBusinessInitiatedFreeTierQuantity,
                        TotalUserInitiatedConversations =
                            waba.ConversationUsageAnalytic.TotalUserInitiatedPaidQuantity +
                            waba.ConversationUsageAnalytic.TotalUserInitiatedFreeTierQuantity +
                            waba.ConversationUsageAnalytic.TotalUserInitiatedFreeEntryPointQuantity,
                        TotalBusinessInitiatedPaidQuantity =
                            waba.ConversationUsageAnalytic.TotalBusinessInitiatedPaidQuantity,
                        TotalBusinessInitiatedFreeTierQuantity =
                            waba.ConversationUsageAnalytic.TotalBusinessInitiatedFreeTierQuantity,
                        TotalUserInitiatedPaidQuantity =
                            waba.ConversationUsageAnalytic.TotalUserInitiatedPaidQuantity,
                        TotalUserInitiatedFreeTierQuantity =
                            waba.ConversationUsageAnalytic.TotalUserInitiatedFreeTierQuantity,
                        TotalUserInitiatedFreeEntryPointQuantity =
                            waba.ConversationUsageAnalytic.TotalUserInitiatedFreeEntryPointQuantity,
                        TotalUsedCurrencyIsoCode = waba.ConversationUsageAnalytic.TotalUsed.CurrencyIsoCode,
                        TotalUsedAmount = waba.ConversationUsageAnalytic.TotalUsed.Amount,
                        TotalMarkupCurrencyIsoCode = waba.ConversationUsageAnalytic?.TotalMarkup?.CurrencyIsoCode,
                        TotalMarkupAmount = waba.ConversationUsageAnalytic?.TotalMarkup?.Amount ?? 0,
                        TotalTransactionHandlingFeeCurrencyIsoCode =
                            waba.ConversationUsageAnalytic?.TotalTransactionHandlingFee?.CurrencyIsoCode,
                        TotalTransactionHandlingFeeAmount =
                            waba.ConversationUsageAnalytic?.TotalTransactionHandlingFee?.Amount ?? 0,
                        TotalAuthenticationTypeConversations = 0,
                        TotalMarketingTypeConversations = 0,
                        TotalMarketingLiteTypeConversations = 0,
                        TotalServiceTypeConversations = 0,
                        TotalUtilityTypeConversations = 0,
                        TotalAuthenticationPricingVolume = 0,
                        TotalAuthenticationInternationalPricingVolume = 0,
                        TotalMarketingPricingVolume = 0,
                        TotalMarketingLitePricingVolume = 0,
                        TotalReferralConversionPricingVolume = 0,
                        TotalServicePricingVolume = 0,
                        TotalUtilityPricingVolume = 0,
                    };

                if (waba.ConversationUsageAnalytic.ConversationCategoryQuantities is { Count: > 0 })
                {
                    byWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshot.TotalAuthenticationTypeConversations =
                        (waba.ConversationUsageAnalytic
                            .ConversationCategoryQuantities
                            .TryGetValue(
                                WhatsappConversationAnalyticConversationCategoryConst.AUTHENTICATION,
                                out var authenticationConversations)
                            ? authenticationConversations
                            : 0) ?? 0;
                    byWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshot.TotalMarketingTypeConversations =
                        (waba.ConversationUsageAnalytic
                            .ConversationCategoryQuantities
                            .TryGetValue(
                                WhatsappConversationAnalyticConversationCategoryConst.MARKETING,
                                out var marketingConversations)
                            ? marketingConversations
                            : 0) ?? 0;
                    byWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshot.TotalMarketingLiteTypeConversations =
                        (waba.ConversationUsageAnalytic
                            .ConversationCategoryQuantities
                            .TryGetValue(
                                WhatsappConversationAnalyticConversationCategoryConst.MARKETING_LITE,
                                out var marketingLiteConversations)
                            ? marketingLiteConversations
                            : 0) ?? 0;
                    byWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshot.TotalServiceTypeConversations = (waba
                        .ConversationUsageAnalytic
                        .ConversationCategoryQuantities
                        .TryGetValue(
                            WhatsappConversationAnalyticConversationCategoryConst.SERVICE,
                            out var serviceConversations)
                        ? serviceConversations
                        : 0) ?? 0;
                    byWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshot.TotalUtilityTypeConversations = (waba
                        .ConversationUsageAnalytic
                        .ConversationCategoryQuantities
                        .TryGetValue(
                            WhatsappConversationAnalyticConversationCategoryConst.UTILITY,
                            out var utilityConversations)
                        ? utilityConversations
                        : 0) ?? 0;
                }

                if (waba.ConversationUsageAnalytic.WhatsappPricingAnalyticSummaries is { Count: > 0 })
                {
                    waba.ConversationUsageAnalytic.WhatsappPricingAnalyticSummaries.ForEach(pricingSummary =>
                    {
                        switch (pricingSummary.PricingCategory)
                        {
                            case WhatsappPricingAnalyticPricingCategoryConst.Authentication:
                                byWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshot
                                    .TotalAuthenticationPricingVolume += pricingSummary.Volume;
                                break;
                            case WhatsappPricingAnalyticPricingCategoryConst.AuthenticationInternational:
                                byWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshot
                                    .TotalAuthenticationInternationalPricingVolume += pricingSummary.Volume;
                                break;
                            case WhatsappPricingAnalyticPricingCategoryConst.Marketing:
                                byWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshot
                                    .TotalMarketingPricingVolume += pricingSummary.Volume;
                                break;
                            case WhatsappPricingAnalyticPricingCategoryConst.MarketingLite:
                                byWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshot
                                    .TotalMarketingLitePricingVolume += pricingSummary.Volume;
                                break;
                            case WhatsappPricingAnalyticPricingCategoryConst.ReferralConversion:
                                byWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshot
                                    .TotalReferralConversionPricingVolume += pricingSummary.Volume;
                                break;
                            case WhatsappPricingAnalyticPricingCategoryConst.Service:
                                byWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshot
                                    .TotalServicePricingVolume += pricingSummary.Volume;
                                break;
                            case WhatsappPricingAnalyticPricingCategoryConst.Utility:
                                byWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshot
                                    .TotalUtilityPricingVolume += pricingSummary.Volume;
                                break;
                        }
                    });
                }

                byWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshots.Add(
                    byWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshot);
            });

            return (byBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshot,
                byWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshots);
        }

        private static string GetHighestMessagingTier(List<WhatsappCloudApiConfig> whatsappCloudApiConfigs)
        {
            var tiers = whatsappCloudApiConfigs.Select(
                x => x.WabaPhoneNumber.FacebookPhoneNumberMessagingLimitTier).Where(string.IsNullOrWhiteSpace).ToList();


            if (tiers.Any(x => x == "TIER_UNLIMITED"))
            {
                return "TIER_UNLIMITED";
            }

            if (tiers.Any(x => x == "TIER_100K"))
            {
                return "TIER_100K";
            }

            if (tiers.Any(x => x == "TIER_10K"))
            {
                return "TIER_10K";
            }

            if (tiers.Any(x => x == "TIER_1K"))
            {
                return "TIER_1K";
            }

            if (tiers.Any(x => x == "TIER_250"))
            {
                return "TIER_250";
            }

            if (tiers.Any(x => x == "TIER_50"))
            {
                return "TIER_50";
            }

            return null;
        }
    }
}