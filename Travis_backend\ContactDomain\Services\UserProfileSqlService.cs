#nullable enable

using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ConversationServices.Operations;
using Travis_backend.Database.Services;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.Helpers;
using static Travis_backend.Constants.OrderCondition;
using static Travis_backend.Enums.SupportedOperator;

[assembly: InternalsVisibleTo("Sleekflow.Core.Tests")]

namespace Travis_backend.ContactDomain.Services;

public interface IUserProfileSqlService
{
    /// <summary>
    /// Asynchronously retrieves user profiles based on the provided conditions.
    /// This method returns a tuple containing
    /// a list of `UserProfile` objects,
    /// a total count of user profiles, and
    /// an `IQueryable<UserProfile>` object.
    /// It is the most flexible one, allowing you to control the sorting, pagination, and tracking of the query, and also to specify whether you want to get only the count or only the user profile IDs.
    /// </summary>
    /// <param name="companyId">The company identifier.</param>
    /// <param name="conditions">The conditions to filter the user profiles.</param>
    /// <param name="offset">The offset for pagination.</param>
    /// <param name="limit">The limit for pagination.</param>
    /// <param name="sortBy">The field to sort by.</param>
    /// <param name="order">The order of sorting.</param>
    /// <param name="assigneeId">The assignee identifier.</param>
    /// <param name="channels">The channels to filter by.</param>
    /// <param name="channelIds">The channel identifiers to filter by.</param>
    /// <param name="staffUserRole">The staff user role.</param>
    /// <param name="enableNoTracking">If set to <c>true</c>, no tracking is enabled.</param>
    /// <param name="getCountOnly">If set to <c>true</c>, only the count is retrieved.</param>
    /// <param name="getUserProfileIdOnly">If set to <c>true</c>, only the user profile identifier is retrieved.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a tuple with the list of user profiles, the total count, and the queryable object of user profiles.</returns>
    public Task<(List<UserProfile>? UserProfiles, int TotalCount, IQueryable<UserProfile>? UserProfileQueryable)>
        GetUserProfilesAsync(
            string companyId,
            List<Condition> conditions,
            int? offset = null,
            int? limit = null,
            string? sortBy = CreatedAt,
            string? order = DESC,
            long? assigneeId = null,
            List<string>? channels = null,
            List<string>? channelIds = null,
            StaffUserRole staffUserRole = StaffUserRole.Admin,
            bool enableNoTracking = false,
            bool getCountOnly = false,
            bool getUserProfileIdOnly = false);

    /// <summary>
    /// Asynchronously retrieves a queryable object of user profiles based on the provided conditions.
    /// This method returns an `IQueryable<UserProfile>` object, which allows for further querying before executing the database call.
    /// It takes the same parameters as `GetUserProfilesAsync` except for `enableNoTracking`, `getCountOnly`, and `getUserProfileIdOnly`.
    /// This method is useful when you want to perform additional operations on the queryable object before executing the query.
    /// </summary>
    /// <param name="companyId">The company identifier.</param>
    /// <param name="conditions">The conditions to filter the user profiles.</param>
    /// <param name="offset">The offset for pagination.</param>
    /// <param name="limit">The limit for pagination.</param>
    /// <param name="sortBy">The field to sort by.</param>
    /// <param name="order">The order of sorting.</param>
    /// <param name="assigneeId">The assignee identifier.</param>
    /// <param name="channels">The channels to filter by.</param>
    /// <param name="channelIds">The channel identifiers to filter by.</param>
    /// <param name="staffUserRole">The staff user role.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a queryable object of user profiles.</returns>
    public Task<IQueryable<UserProfile>>
        GetUserProfileQueryableAsync(
            string companyId,
            List<Condition> conditions,
            int? offset = null,
            int? limit = null,
            string? sortBy = CreatedAt,
            string? order = DESC,
            long? assigneeId = null,
            List<string>? channels = null,
            List<string>? channelIds = null,
            StaffUserRole staffUserRole = StaffUserRole.Admin);

    /// <summary>
    /// Asynchronously retrieves a queryable object of user profiles based on the provided conditions.
    /// This method also returns an `IQueryable<UserProfile>` object,
    /// but it takes fewer parameters than the previous methods.
    /// It only accepts `companyId`, `conditions`, `assigneeId`, `channels`, `channelIds`, and `staffUserRole`.
    /// This method is a simpler version of the previous one and is useful when you only need to filter user profiles based on these specific parameters.
    /// </summary>
    /// <param name="companyId">The company identifier.</param>
    /// <param name="conditions">The conditions to filter the user profiles.</param>
    /// <param name="assigneeId">The assignee identifier.</param>
    /// <param name="channels">The channels to filter by.</param>
    /// <param name="channelIds">The channel identifiers to filter by.</param>
    /// <param name="staffUserRole">The staff user role.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a queryable object of user profiles.</returns>
    public Task<IQueryable<UserProfile>> GetUserProfileQueryableAsync(
        string companyId,
        List<Condition> conditions,
        long? assigneeId = null,
        List<string>? channels = null,
        List<string>? channelIds = null,
        StaffUserRole staffUserRole = StaffUserRole.Admin);

    Task<List<UserProfile>> GetNewUserProfilesAsync(
        string companyId,
        DateTime fromWithTimeZone,
        DateTime toWithTimeZone,
        List<string> newUserProfileIds);
}

public class UserProfileSqlService : IUserProfileSqlService
{
    private const string UserProfileVarName = "UP";

    private readonly IDbContextService _dbContextService;
    private readonly IChannelOperation _channelOperation;
    private readonly IHashtagOperation _hashtagOperation;
    private readonly IShopifyOperation _shopifyOperation;
    private readonly IDateTimeOperation _dateTimeOperation;
    private readonly ILogger<UserProfileSqlService> _logger;
    private readonly IManageableOperation _manageableOperation;
    private readonly ICustomFieldOperation _customFieldOperation;
    private readonly IBroadcastHistoriesOperation _broadcastHistoriesOperation;

    private static readonly List<string> NonCustomFieldList = new List<string>
    {
        ConditionFields.FirstName,
        ConditionFields.DisplayName,
        ConditionFields.LastName,
        ConditionFields.ImportFrom,
        ConditionFields.CreatedAt,
        ConditionFields.UpdateAt,
        ConditionFields.ConversationStatus,
        ConditionFields.ContactOwner,
        ConditionFields.Collaborators,
        ConditionFields.PhoneNumber,
        ConditionFields.Email,
        ConditionFields.ShopifyOrderTags,
        ConditionFields.ShopifyProductTags,
        ConditionFields.ShopifyTotalOrderPrice,
        ConditionFields.ShopifyTotalOrderCount,
        ConditionFields.ShopifyOrderProductName
    };

    private static readonly List<string> SupportedOrConditions = new List<string>
    {
        ConditionFields.FirstName,
        ConditionFields.DisplayName,
        ConditionFields.LastName,
        ConditionFields.Email,
        ConditionFields.PhoneNumber
    };

    public UserProfileSqlService(
        IDbContextService dbContextService,
        IChannelOperation channelOperation,
        IHashtagOperation hashtagOperation,
        IShopifyOperation shopifyOperation,
        IDateTimeOperation dateTimeOperation,
        ILogger<UserProfileSqlService> logger,
        IManageableOperation manageableOperation,
        ICustomFieldOperation customFieldOperation,
        IBroadcastHistoriesOperation broadcastHistoriesOperation)
    {
        _dbContextService = dbContextService;
        _logger = logger;
        _channelOperation = channelOperation;
        _hashtagOperation = hashtagOperation;
        _shopifyOperation = shopifyOperation;
        _dateTimeOperation = dateTimeOperation;
        _manageableOperation = manageableOperation;
        _customFieldOperation = customFieldOperation;
        _broadcastHistoriesOperation = broadcastHistoriesOperation;
    }

    public async Task<(List<UserProfile>? UserProfiles, int TotalCount, IQueryable<UserProfile>? UserProfileQueryable)>
        GetUserProfilesAsync(
            string companyId,
            List<Condition> conditions,
            int? offset = null,
            int? limit = null,
            string? sortBy = CreatedAt,
            string? order = DESC,
            long? assigneeId = null,
            List<string>? channels = null,
            List<string>? channelIds = null,
            StaffUserRole staffUserRole = StaffUserRole.Admin,
            bool enableNoTracking = false,
            bool getCountOnly = false,
            bool getUserProfileIdOnly = false)
    {
        var dbContext = _dbContextService.GetDbContext();

        var (query, parameters) = await GetUserProfileFilteredQueryAndParametersAsync(
            companyId,
            conditions,
            sortBy,
            order,
            assigneeId,
            channels,
            channelIds,
            staffUserRole);

        var userProfileCount = await dbContext.UserProfiles.FromSqlRaw(query, parameters.ToArray()).CountAsync();

        if (getCountOnly)
        {
            return (null, userProfileCount, null);
        }

        var userProfileQueryable = await GetUserProfileQueryableAsync(
            query,
            parameters,
            offset,
            limit,
            sortBy,
            order,
            getUserProfileIdOnly);

        if (getUserProfileIdOnly)
        {
            return (await userProfileQueryable.ToListAsync(), userProfileCount, userProfileQueryable);
        }

        if (enableNoTracking)
        {
            userProfileQueryable = userProfileQueryable.AsNoTracking();
        }

        return (await userProfileQueryable.ToListAsync(), userProfileCount, userProfileQueryable);
    }

    public async Task<IQueryable<UserProfile>> GetUserProfileQueryableAsync(
        string companyId,
        List<Condition> conditions,
        int? offset = null,
        int? limit = null,
        string? sortBy = CreatedAt,
        string? order = DESC,
        long? assigneeId = null,
        List<string>? channels = null,
        List<string>? channelIds = null,
        StaffUserRole staffUserRole = StaffUserRole.Admin)
    {
        var (query, parameters) = await GetUserProfileFilteredQueryAndParametersAsync(
            companyId,
            conditions,
            sortBy,
            order,
            assigneeId,
            channels,
            channelIds,
            staffUserRole);

        return await GetUserProfileQueryableAsync(query, parameters, offset, limit, sortBy, order);
    }

    public async Task<IQueryable<UserProfile>> GetUserProfileQueryableAsync(
        string companyId,
        List<Condition> conditions,
        long? assigneeId = null,
        List<string>? channels = null,
        List<string>? channelIds = null,
        StaffUserRole staffUserRole = StaffUserRole.Admin)
    {
        var dbContext = _dbContextService.GetDbContext();

        var (query, parameters) = await GetUserProfileFilteredQueryAndParametersAsync(
            companyId,
            conditions,
            CreatedAt,
            DESC,
            assigneeId,
            channels,
            channelIds,
            staffUserRole);

        var userProfileQueryable =
            dbContext.UserProfiles.FromSqlRaw(query, parameters.ToArray());

        return userProfileQueryable;
    }

    // SELECT *
    //     FROM UserProfiles UP
    // WHERE UP.CompanyId = 'b6d7e442-38ae-4b9a-b100-2951729768bc'
    // AND UP.ActiveStatus = 1
    // AND '2024-06-15T16:00:00.0000000Z' <= C.CreatedAt
    // AND '2024-06-16T16:00:00.0000000Z' > C.CreatedAt
    // AND UP.Id IN ('0012e3cc-0a2c-401c-bec6-d99a574bf65d', '00288cc1-2886-4746-8246-7b9c76b14218')
    public async Task<List<UserProfile>> GetNewUserProfilesAsync(
        string companyId,
        DateTime fromWithTimeZone,
        DateTime toWithTimeZone,
        List<string> newUserProfileIds)
    {
        var whereClauses = new List<string>();
        var parameters = new List<SqlParameter>();
        var conditionParameters = new Dictionary<string, string>();
        var dbContext = _dbContextService.GetDbContext();
        const string prefix = "UP";
        const string companyParameter = $"{prefix}_CompanyId";
        const string activeStatusParameter = $"{prefix}_ActiveStatus";
        const string fromParameter = $"{prefix}_From";
        const string toParameter = $"{prefix}_To";

        if (!newUserProfileIds.IsNullOrEmpty())
        {
            const string userProfileIdsParameter = $"{prefix}_Ids";
            var (userProfileIdsPrefix, userProfileIdsParameters) = ConditionHelper.ConstructingIncludes(
                userProfileIdsParameter,
                newUserProfileIds);
            conditionParameters = ConditionHelper.ConcatConditionParameters(
                conditionParameters,
                userProfileIdsParameters);
            whereClauses.Add(
                ConditionHelper.ConditionClause(
                    SupportedOperator.Included,
                    $"{prefix}.Id",
                    includeParameter: userProfileIdsPrefix));
        }

        var query =
            $"SELECT {prefix}.* " +
            $"FROM UserProfiles {prefix} " +
            $"WHERE {ConditionHelper.ConditionClause(SupportedOperator.Equal, $"{prefix}.CompanyId", companyParameter)} " +
            $"AND {ConditionHelper.ConditionClause(SupportedOperator.Equal, $"{prefix}.ActiveStatus", activeStatusParameter)} " +
            $"AND @{fromParameter} <= {prefix}.CreatedAt " +
            $"AND @{toParameter} > {prefix}.CreatedAt " +
            $"{(whereClauses.Count > 0 ? $" AND {string.Join(" AND ", whereClauses)}" : string.Empty)}";

        parameters.Add(new SqlParameter(companyParameter, companyId));
        parameters.Add(new SqlParameter(activeStatusParameter, (int) ActiveStatus.Active));
        parameters.Add(
            new SqlParameter(fromParameter, fromWithTimeZone.ToString("yyyy-MM-ddTHH:mm:ss.fffffffZ")));
        parameters.Add(
            new SqlParameter(toParameter, toWithTimeZone.ToString("yyyy-MM-ddTHH:mm:ss.fffffffZ")));
        parameters.AddRange(conditionParameters.Select(c => new SqlParameter(c.Key, c.Value)));
        var userProfiles = dbContext.UserProfiles.FromSqlRaw(query, parameters.ToArray());
        _logger.LogInformation(
            "GetNewUserProfilesAsync: {Query}/{RawQuery}",
            userProfiles.ToQueryString(),
            parameters.Aggregate(
                query,
                (current, parameter)
                    => current.Replace(
                        $"@{parameter.ParameterName}",
                        parameter.Value is not null ? parameter.Value.ToString() : "null")));
        return await userProfiles.ToListAsync();
    }

    public Task<IQueryable<UserProfile>> GetUserProfileQueryableAsync(
        string query,
        List<SqlParameter> parameters,
        int? offset = null,
        int? limit = null,
        string? sortBy = CreatedAt,
        string? order = DESC,
        bool getUserProfileIdOnly = false)
    {
        var dbContext = _dbContextService.GetDbContext();

        // Since we force the order by in the query
        // and derived tables (joining tables) is not valid if we do not include offset
        // we need to force the offset to 0 as the default in order to by pass
        // SqlException (0x80131904): The ORDER BY clause is invalid
        offset ??= 0;

        if (!string.IsNullOrEmpty(sortBy))
        {
            var sorting = sortBy.ToLower();
            var by = order?.ToUpper() is DESC ? "DESC" : "ASC";
            var orderClause = sorting switch
            {
                UpdatedAt => $"{UserProfileVarName}.UpdatedAt",
                FirstName => $"{UserProfileVarName}.FirstName",
                LastName => $"{UserProfileVarName}.LastName",
                DisplayName => $"{UserProfileVarName}.Fullname",
                CreatedAt => $"{UserProfileVarName}.CreatedAt",
                LastContactFromCustomers => $"{UserProfileVarName}.LastContactFromCustomers",
                LastContact => $"{UserProfileVarName}.LastContact"
            };
            query += $"ORDER BY {orderClause} {by} ";
        }

        const string offsetVar = "OFFSET";

        parameters.Add(
            new SqlParameter($"@{offsetVar}", SqlDbType.Int)
            {
                Value = offset
            });

        // Default -> OFFSET 0 ROWS  -- Skips 0 rows, so all are returned, except when sorting is null
        if (!string.IsNullOrEmpty(sortBy))
        {
            query += $"OFFSET @{offsetVar} ROWS ";
        }

        if (!string.IsNullOrEmpty(sortBy) && limit != null)
        {
            const string limitVar = "FETCH_NEXT";

            parameters.Add(
                new SqlParameter($"@{limitVar}", SqlDbType.Int)
                {
                    Value = limit
                });

            query += $"FETCH NEXT @{limitVar} ROWS ONLY ";
        }

        var userProfileQueryable =
            dbContext.UserProfiles.FromSqlRaw(query, parameters.ToArray());

        if (!getUserProfileIdOnly)
        {
            userProfileQueryable = userProfileQueryable
                .Include(x => x.CustomFields)
                .Include(x => x.FacebookAccount)
                .Include(x => x.WeChatUser)
                .Include(x => x.EmailAddress)
                .Include(x => x.WhatsAppAccount)
                .Include(x => x.WebClient)
                .Include(x => x.LineUser)
                .Include(x => x.SMSUser)
                .Include(x => x.ViberUser)
                .Include(x => x.TelegramUser)
                .Include(x => x.TikTokUser)
                .Include(x => x.WhatsApp360DialogUser)
                .Include(x => x.WhatsappCloudApiUser);
        }

        if (!string.IsNullOrEmpty(sortBy))
        {
            var sorting = sortBy.ToLower();
            if (sorting == DisplayName)
            {
                userProfileQueryable = order == "desc"
                    ? userProfileQueryable.OrderByDescending(up => up.FullName)
                    : userProfileQueryable.OrderBy(up => up.FullName);
            }
            else
            {
                var orderByExpression = ConditionHelper.GetOrderedByExpression(sorting);

                userProfileQueryable = order == "desc"
                    ? userProfileQueryable.OrderByDescending(orderByExpression)
                    : userProfileQueryable.OrderBy(orderByExpression);
            }
        }

        return Task.FromResult(userProfileQueryable);
    }

    internal async Task<(string Query, List<SqlParameter> Parameters)> GetUserProfileFilteredQueryAndParametersAsync(
        string companyId,
        List<Condition> conditions,
        string? sortBy = CreatedAt,
        string? order = DESC,
        long? assigneeId = null,
        List<string>? channels = null,
        List<string>? channelIds = null,
        StaffUserRole staffUserRole = StaffUserRole.Admin)
    {
        var dbContext = _dbContextService.GetDbContext();

        var companyStaff = await dbContext.UserRoleStaffs
            .Where(x => x.CompanyId == companyId)
            .WhereIf(assigneeId.HasValue, x => x.Id == assigneeId)
            .AsNoTracking()
            .FirstOrDefaultAsync();

        if (companyStaff is null)
        {
            throw new Exception(
                $"Unable to locate any company staff information. CompanyId: {companyId}, AssigneeId: {assigneeId ?? -1}, RoleType: {staffUserRole}");
        }

        // mocking the role
        companyStaff.RoleType = staffUserRole;

        // Finding the stop point for the and or conditions note that the this condition stay true if and only if the or conditions is goes before all the end conditions;
        var stopPoint = conditions.FindIndex(c => c.NextOperator == SupportedNextOperator.And);

        if (stopPoint == -1)
        {
            stopPoint = conditions.Count;
        }
        else if (stopPoint != 0)
        {
            stopPoint += 1;
        }

        var andConditions = conditions.Skip(stopPoint).ToList();
        var orConditions = conditions.Take(stopPoint).ToList();

        if (orConditions.Any())
        {
            if (conditions.Take(stopPoint)
                .Select(
                    c =>
                        SupportedOrConditions.Any(s => s.ToLower().Equals(c.FieldName.ToLower())))
                .Any(o => o == false))
            {
                throw new Exception("Unsupported OR conditions found.");
            }
        }

        // Lower sortBy to Sorting
        var sorting = sortBy?.ToLower() ?? null;

        // Constructing the AND conditions
        var (query, parameters) = await ConstructParameterizeQuery(
            (stopPoint != 0) ? $"{UserProfileVarName}_AND" : UserProfileVarName,
            companyId,
            andConditions,
            sorting,
            companyStaff,
            assigneeId,
            channels,
            channelIds,
            staffUserRole);

        if (stopPoint != 0)
        {
            // Constructing the OR conditions
            const string userCompanyId = $"{UserProfileVarName}_Company_Id";
            const string activeStatus = $"{UserProfileVarName}_Active_Status";
            var whereClauses = new List<string>
            {
                ConditionHelper.ConditionClause(Equal, $"{UserProfileVarName}.CompanyId", userCompanyId),
                ConditionHelper.ConditionClause(Equal, $"{UserProfileVarName}.ActiveStatus", activeStatus)
            };
            var (orQuery, orParameters) = await ConstructParameterizeQuery(
                andConditions.Any() ? $"{UserProfileVarName}_OR" : UserProfileVarName,
                companyId,
                orConditions,
                sorting,
                companyStaff,
                assigneeId,
                channels,
                channelIds,
                staffUserRole,
                "OR");
            if (andConditions.Any())
            {
                // SELECT UP.*
                // FROM (SELECT UP_AND.*
                //     FROM UserProfiles UP_AND
                //          ..
                // INTERSECT
                //     SELECT UP_OR.*
                //     FROM UserProfiles UP_OR
                //         ..) AS UP
                // WHERE UP.CompanyId = @UP_Company_Id
                // AND UP.ActiveStatus = @UP_Active_Status
                parameters = parameters.Concat(orParameters).ToList();
                query =
                    $"SELECT UP.* FROM ({query} INTERSECT {orQuery}) AS UP " +
                    $"WHERE {string.Join(" AND ", whereClauses)} ";
                parameters.Add(new SqlParameter(userCompanyId, companyStaff.CompanyId));
                parameters.Add(new SqlParameter(activeStatus, ((int) ActiveStatus.Active).ToString()));
            }
            else
            {
                query = orQuery;
                parameters = orParameters;
            }
        }

        _logger.LogInformation(
            "User Profile Raw SQL {Query}/{RawQuery}",
            query,
            parameters.Aggregate(
                query,
                (current, parameter)
                    => current.Replace(
                        $"@{parameter.ParameterName}",
                        parameter.Value is not null ? parameter.Value.ToString() : "null")));

        return (query, parameters);
    }

    internal async
        Task<(List<string> WhereClauses, List<string> JoinClauses, List<SqlParameter> Parameters)>
        ConstructorQueryFromCondition(
            string prefix,
            string companyId,
            List<Condition> conditions,
            List<string>? channels = null,
            List<string>? channelIds = null)
    {
        var whereClauses = new List<string>();
        var joinClauses = new List<string>();
        var parameters = new List<SqlParameter>();
        var customFieldJoinParams = new List<string>();
        var modifiedChannels = new List<string>();
        var modifiedChannelIds = new List<string>();

        // var globalCompanyIdPrefix = $"Global_CompanyId";
        // Need GROUP BY for custom condition sub-query if there is only one custom field condition
        var customFieldCount = conditions.Count(
            c =>
                !NonCustomFieldList.Contains(c.FieldName.ToLower())
                && string.IsNullOrEmpty(c.CompanyMessageTemplateId)
                && c.BroadcastMessageStatus is null
                && string.IsNullOrEmpty(c.ContainHashTag));

        for (var i = 0; i < conditions.Count; i++)
        {
            try
            {
                // Obtain the condition for the current list cycles
                var condition = conditions[i];
                var field = condition.FieldName.ToLower();
                var conditionOperator = condition.ConditionOperator;

                // Hashmap to store the parameter prefix i.e. @FirstName_1 and value 'Test'
                var conditionParameters = new Dictionary<string, string>();

                // Early jumping out if it is a hashtag condition
                if (!string.IsNullOrEmpty(condition.ContainHashTag))
                {
                    continue;
                }

                switch (field)
                {
                    case ConditionFields.FirstName
                        or ConditionFields.LastName:
                        var dbConditionFieldName =
                            field == ConditionFields.LastName ? "LastName" : "FirstName";
                        if (conditionOperator is Contains or IsNotContains)
                        {
                            var dbConditionClauseFieldName = $"{prefix}.{dbConditionFieldName}";
                            var fieldName = field == ConditionFields.LastName ? "Last_Name" : "First_Name";
                            var (thisParameterPrefixes, thisConditionParameters) =
                                ConditionHelper.ConstructingIncludes(
                                    $"{prefix}_{fieldName}_{i}",
                                    condition.Values
                                        .Select(v => $"%{v.ToLower().EscapeSqlLikeValue()}%")
                                        .ToList());
                            conditionParameters =
                                ConditionHelper.ConcatConditionParameters(conditionParameters, thisConditionParameters);

                            var whereClause = $"{ConditionHelper.ConditionClause(
                                IsNotNull,
                                dbConditionClauseFieldName)} AND {
                                ConditionHelper.ConditionClause(
                                    conditionOperator == Contains ? LikeOrMultipleValues : IsNotLikeOrMultipleValues,
                                    dbConditionClauseFieldName,
                                    includeParameter: thisParameterPrefixes)}";

                            whereClauses.Add(whereClause);
                        }
                        else if (conditionOperator is IsNull or IsNotNull)
                        {
                            var mappedStringConditionOperator = MapToStringOperator(conditionOperator);

                            var dbConditionClauseFieldName = $"{prefix}.{dbConditionFieldName}";
                            whereClauses.Add(
                                ConditionHelper.ConditionClause(
                                    mappedStringConditionOperator,
                                    dbConditionClauseFieldName));
                        }

                        break;
                    case ConditionFields.DisplayName:
                        var fullNameField = $"{prefix}.FullName";
                        if (conditionOperator is Contains or IsNotContains)
                        {
                            var fullNamePrefix = $"{prefix}_FullName_{i}";
                            var displayNameCondition =
                                $"{ConditionHelper.ConditionClause(conditionOperator, fullNameField, fullNamePrefix)} ";
                            conditionParameters.Add(
                                fullNamePrefix,
                                $"%{condition.Values.FirstOrDefault().ToLower()}%");
                            whereClauses.Add(displayNameCondition);
                        }
                        else if (conditionOperator is IsNull or IsNotNull)
                        {
                            var mappedStringConditionOperator = MapToStringOperator(conditionOperator);

                            whereClauses.Add(
                                $"{ConditionHelper.ConditionClause(mappedStringConditionOperator, fullNameField)} ");
                        }

                        break;
                    case ConditionFields.ImportFrom:
                        if (condition.Values.Count == 0)
                        {
                            break;
                        }

                        var importHistory = $"{prefix}_I_{i}";
                        var subQueryImportHistory = $"{prefix}_SUB_{i}";
                        switch (condition.ConditionOperator)
                        {
                            case Contains or ContainsAny or IsNotContains or IsNotContainsAny:
                            {
                                // SELECT UP.* FROM UserProfiles UP
                                // (LEFT) JOIN (SELECT I_0.UserProfileId FROM CompanyImportedUserProfiles I_0
                                //      WHERE I_0.ImportContactHistoryId IN (@value1, @value2, ..)
                                //      GROUP BY I_0.UserProfileId) SUB
                                // ON SUB.UserProfileId = UP.Id
                                // (WHERE SUB.UserProfileId is null)
                                var (thisParameterPrefixes, thisConditionParameters) =
                                    ConditionHelper.ConstructingIncludes(
                                        $"{prefix}_ImportFrom_{i}",
                                        condition.Values);
                                conditionParameters =
                                    ConditionHelper.ConcatConditionParameters(
                                        conditionParameters,
                                        thisConditionParameters);
                                var thisJoinOperator = condition.ConditionOperator is Contains or ContainsAny
                                    ? "JOIN"
                                    : "LEFT JOIN";
                                joinClauses.Add(
                                    $"{thisJoinOperator} (SELECT {importHistory}.UserProfileId " +
                                    $"FROM CompanyImportedUserProfiles {importHistory} " +
                                    $"WHERE {ConditionHelper.ConditionClause(Included, $"{importHistory}.{Parameters.ImportContactHistoryId}", includeParameter: thisParameterPrefixes)} " +
                                    $"GROUP BY {importHistory}.UserProfileId) {subQueryImportHistory} " +
                                    $"ON {subQueryImportHistory}.UserProfileId = {prefix}.Id ");

                                if (condition.ConditionOperator is IsNotContains or IsNotContainsAny)
                                {
                                    whereClauses.Add($"{subQueryImportHistory}.UserProfileId IS NULL ");
                                }

                                break;
                            }
                            case ContainsAll or IsNotContainsAll:
                            {
                                // SELECT UP* FROM UserProfiles UP
                                // (LEFT) JOIN (
                                //     SELECT I_0.UserProfileId FROM CompanyImportedUserProfiles I_0
                                //     WHERE I_0.ImportContactHistoryId = @value0
                                //     AND EXISTS (SELECT 1 FROM CompanyImportedUserProfiles I_0_0
                                //                 WHERE I_0_0.UserProfileId = I_0.UserProfileId
                                //                 AND I_0_0.ImportContactHistoryId = @value1)
                                //     AND EXISTS (..)
                                //     GROUP BY I_0.UserProfileId) SUB
                                // ON SUB.UserProfileId = UP.Id
                                // (WHERE SUB.UserProfileId is null)
                                var importFromPrefix = $"{prefix}_ImportFrom_{i}";
                                var thisJoinOperator =
                                    condition.ConditionOperator is ContainsAll ? "JOIN" : "LEFT JOIN";
                                var thisJoinClause =
                                    $"{thisJoinOperator} (SELECT {importHistory}.UserProfileId " +
                                    $"FROM CompanyImportedUserProfiles {importHistory} ";

                                for (var j = 0; j < condition.Values.Count; j++)
                                {
                                    var thisPrefix = $"{importFromPrefix}_{j}";
                                    conditionParameters.Add(thisPrefix, condition.Values[j]);
                                    if (j == 0)
                                    {
                                        thisJoinClause +=
                                            $"WHERE {importHistory}.{Parameters.ImportContactHistoryId} = @{thisPrefix} ";
                                    }
                                    else
                                    {
                                        thisJoinClause +=
                                            $"AND EXISTS (SELECT 1 FROM CompanyImportedUserProfiles {importHistory}_{j} " +
                                            $"WHERE {importHistory}_{j}.UserProfileId = {importHistory}.UserProfileId " +
                                            $"AND {importHistory}_{j}.{Parameters.ImportContactHistoryId} = @{thisPrefix}) ";
                                    }
                                }

                                thisJoinClause +=
                                    $"GROUP BY {importHistory}.UserProfileId) {subQueryImportHistory} " +
                                    $"ON {subQueryImportHistory}.UserProfileId = {prefix}.Id ";
                                joinClauses.Add(thisJoinClause);

                                if (condition.ConditionOperator is IsNotContainsAll)
                                {
                                    whereClauses.Add($"{subQueryImportHistory}.UserProfileId IS NULL ");
                                }

                                break;
                            }
                        }

                        break;
                    case ConditionFields.CreatedAt
                        or ConditionFields.UpdateAt:
                        // may need handle null condition.TimeValueType, didn't find usage yet
                        var (dateTimeWhereClauses, dateTimeConditionParameters) = _dateTimeOperation.ConditionClause(
                            prefix,
                            i,
                            field,
                            companyId,
                            conditionOperator,
                            condition.Values,
                            condition.TimeValueType);
                        whereClauses.Add(dateTimeWhereClauses);
                        conditionParameters =
                            ConditionHelper.ConcatConditionParameters(conditionParameters, dateTimeConditionParameters);
                        break;

                    case ConditionFields.ConversationStatus:
                        // LEFT JOIN Conversations C
                        // ON C.UserProfileId = UP.Id AND LOWER(C.Status) IN ('closed');
                        if (condition.Values.Count > 0
                            && condition.ConditionOperator is Contains
                                or IsNotContains
                                or SupportedOperator.Equals)
                        {
                            var statusConditionOperator =
                                condition.ConditionOperator is IsNotContains ? NotIncluded : Included;
                            var tableIndex = $"{prefix}_C_{i}";
                            var (thisParameterPrefixes, thisConditionParameters) = ConditionHelper.ConstructingIncludes(
                                $"{prefix}_{Parameters.ConversationStatus}_{i}",
                                condition.Values);

                            conditionParameters =
                                ConditionHelper.ConcatConditionParameters(conditionParameters, thisConditionParameters);
                            joinClauses.Add(
                                $"JOIN Conversations {tableIndex} " +
                                $"ON {tableIndex}.UserProfileId = {prefix}.Id " +
                                $"AND {ConditionHelper.ConditionClause(statusConditionOperator, $"{tableIndex}.Status", includeParameter: thisParameterPrefixes)}");
                        }

                        break;

                    case ConditionFields.ContactOwner:
                        switch (condition.ConditionOperator)
                        {
                            case Contains or IsNotContains:
                                if (condition.Values.Count > 0)
                                {
                                    var thisConditionOperator =
                                        condition.ConditionOperator is IsNotContains ? NotIncluded : Included;
                                    var (thisParameterPrefixes, thisConditionParameters) =
                                        ConditionHelper.ConstructingIncludes(
                                            $"{prefix}_{Parameters.ConversationStatus}_{i}",
                                            condition.Values);

                                    conditionParameters =
                                        ConditionHelper.ConcatConditionParameters(
                                            conditionParameters,
                                            thisConditionParameters);
                                    var contactOwnerPrefix = $"{prefix}.{Parameters.ContactOwner}";

                                    var contactOwnerContainOrIsNotContainsWhereClause = ConditionHelper.ConditionClause(
                                        thisConditionOperator,
                                        contactOwnerPrefix,
                                        includeParameter: thisParameterPrefixes);
                                    if (condition.ConditionOperator == IsNotContains)
                                    {
                                        contactOwnerContainOrIsNotContainsWhereClause =
                                            $"({contactOwnerContainOrIsNotContainsWhereClause} " +
                                            $"OR {ConditionHelper.ConditionClause(IsNull, contactOwnerPrefix)}) ";
                                    }

                                    whereClauses.Add(contactOwnerContainOrIsNotContainsWhereClause);
                                }

                                // else: throw null value exception
                                break;
                            case IsNull or IsNotNull or StringIsNullOrEmpty:
                                var contactOwnerFieldName = $"{prefix}.{Parameters.ContactOwner}";
                                whereClauses.Add(
                                    ConditionHelper.ConditionClause(
                                        condition.ConditionOperator is IsNull or StringIsNullOrEmpty
                                            ? StringIsNullOrEmpty
                                            : IsNotNullOrEmpty,
                                        contactOwnerFieldName));
                                break;
                            case IsAway or IsActive:
                                // JOIN Conversations C on C.UserProfileId = UP.Id
                                // JOIN UserRoleStaffs URS on C.AssigneeId = URS.Id
                                // and URS.Status = 1
                                var tableIndexConversation = $"{prefix}_C_{i}";
                                var tableIndexAssignee = $"{prefix}_URS_{i}"; // tbl: UserRoleStaffs
                                joinClauses.Add(
                                    $"JOIN Conversations {tableIndexConversation} ON {tableIndexConversation}.UserProfileId = {prefix}.Id " +
                                    $"JOIN UserRoleStaffs {tableIndexAssignee} ON {tableIndexAssignee}.Id = {tableIndexConversation}.AssigneeId " +
                                    $"AND {tableIndexAssignee}.Status = {(condition.ConditionOperator == IsAway ? StaffStatus.Away : StaffStatus.Active)} ");
                                break;
                        }

                        break;
                    case ConditionFields.Collaborators:
                        switch (condition.ConditionOperator)
                        {
                            case ContainsAny:
                                // JOIN conversations C ON C.UserProfileId = UP.Id
                                // AND EXISTS(
                                //     SELECT 1 FROM ConversationAdditionalAssignees as CAA
                                //     JOIN UserRoleStaffs URS ON CAA.AssigneeId = URS.Id
                                //     JOIN AspNetUsers ANU ON URS.IdentityId = ANU.Id
                                //     WHERE C.Id = CAA.ConversationId AND ANU.Id in (@ConditionValue1, @ConditionValue2, ..)
                                //  )
                                // WHERE ..
                                if (condition.Values.Count > 0)
                                {
                                    var tableIndexConversation = $"{prefix}_C_{i}"; // tbl: Conversations
                                    var tableIndexAdditionalAssignee =
                                        $"{prefix}_CAA_{i}"; // tbl: ConversationAdditionalAssignees
                                    var tableIndexAssignee = $"{prefix}_URS_{i}"; // tbl: UserRoleStaffs
                                    var tableIndexAspUsers = $"{prefix}_ANU_{i}"; // tbl: AspNetUsers
                                    var tableIndexAdditionalAssigneeCompanyId = $"{prefix}_CAA_CompanyId_{i}";

                                    var (thisParameterPrefixes, thisConditionParameters) =
                                        ConditionHelper.ConstructingIncludes(
                                            $"{prefix}_{Parameters.Collaborators}_{i}",
                                            condition.Values);

                                    conditionParameters =
                                        ConditionHelper.ConcatConditionParameters(
                                            conditionParameters,
                                            thisConditionParameters);

                                    conditionParameters.Add(tableIndexAdditionalAssigneeCompanyId, companyId);
                                    joinClauses.Add(
                                        $"JOIN Conversations {tableIndexConversation} ON {tableIndexConversation}.UserProfileId = {prefix}.Id " +
                                        $"AND EXISTS( " +
                                        $"SELECT 1 FROM ConversationAdditionalAssignees as {tableIndexAdditionalAssignee} " +
                                        $"JOIN UserRoleStaffs {tableIndexAssignee} ON {tableIndexAdditionalAssignee}.AssigneeId = {tableIndexAssignee}.Id " +
                                        $"JOIN AspNetUsers {tableIndexAspUsers} ON {tableIndexAssignee}.IdentityId = {tableIndexAspUsers}.Id " +
                                        $"WHERE {tableIndexConversation}.Id = {tableIndexAdditionalAssignee}.ConversationId AND {tableIndexAdditionalAssignee}.CompanyId = @{tableIndexAdditionalAssigneeCompanyId} " +
                                        $"AND {ConditionHelper.ConditionClause(Included, $"{tableIndexAspUsers}.Id", includeParameter: thisParameterPrefixes)}) ");
                                }

                                break;
                            case ContainsAll:
                                // JOIN conversations AS C ON C.UserProfileId = UP.Id
                                // AND EXISTS(
                                //     SELECT 1 FROM ConversationAdditionalAssignees CAA_1
                                //     JOIN UserRoleStaffs URS_1 ON CAA_1.AssigneeId = URS_1.Id
                                //     JOIN AspNetUsers ANU_1 ON URS_1.IdentityId = ANU_1.Id
                                //     WHERE C.Id = CAA_1.ConversationId AND ANU_1.Id = @ConditionValue_1)
                                // AND EXISTS(
                                //     SELECT 1 FROM ConversationAdditionalAssignees AS CAA_2
                                //     JOIN UserRoleStaffs URS_2 ON CAA_2.AssigneeId = URS_2.Id
                                //     JOIN AspNetUsers ANU_2 ON URS_2.IdentityId = ANU_2.Id
                                //     WHERE C.Id = CAA_2.ConversationId AND ANU_2.Id = @ConditionValue_2)
                                // AND EXISTS( ... )
                                if (condition.Values.Count > 0)
                                {
                                    var tableIndexConversation = $"{prefix}_C_{i}"; // tbl: Conversations
                                    var collaboratorJoinClause =
                                        $"JOIN Conversations AS {tableIndexConversation} ON {tableIndexConversation}.UserProfileId = {prefix}.Id ";
                                    for (var j = 0; j < condition.Values.Count; j++)
                                    {
                                        var tableIndexAdditionalAssignee =
                                            $"{prefix}_CAA_{j}"; // tbl: ConversationAdditionalAssignees
                                        var tableIndexAdditionalAssigneeCompanyId = $"{prefix}_CAA_{j}_CompanyId";
                                        var tableIndexAssignee = $"{prefix}_URS_{j}"; // tbl: UserRoleStaffs
                                        var tableIndexAspUsers = $"{prefix}_ANU_{j}"; // tbl: AspNetUsers
                                        var parameterPrefix = $"{prefix}_{Parameters.Collaborators}_{i}_{j}";
                                        conditionParameters.Add(parameterPrefix, condition.Values[j]);
                                        collaboratorJoinClause +=
                                            $"AND EXISTS( " +
                                            $"SELECT 1 FROM ConversationAdditionalAssignees {tableIndexAdditionalAssignee} " +
                                            $"JOIN UserRoleStaffs {tableIndexAssignee} ON {tableIndexAdditionalAssignee}.AssigneeId = {tableIndexAssignee}.Id " +
                                            $"JOIN AspNetUsers {tableIndexAspUsers} ON {tableIndexAssignee}.IdentityId = {tableIndexAspUsers}.Id " +
                                            $"WHERE {tableIndexConversation}.Id = {tableIndexAdditionalAssignee}.ConversationId AND {tableIndexAdditionalAssignee}.CompanyId = @{tableIndexAdditionalAssigneeCompanyId} " +
                                            $"AND {tableIndexAspUsers}.Id = @{parameterPrefix} ) ";
                                        conditionParameters.Add(tableIndexAdditionalAssigneeCompanyId, companyId);
                                    }

                                    joinClauses.Add(collaboratorJoinClause);
                                }

                                break;
                        }

                        break;
                    case ConditionFields.PhoneNumber or ConditionFields.Email:
                        var thisFieldName = $"{prefix}.{condition.FieldName}";
                        switch (condition.ConditionOperator)
                        {
                            case Contains or IsNotContains or SupportedOperator.Equals:
                                // Currently we only need to deal with the first value
                                if (condition.Values.Count > 0)
                                {
                                    if (conditionOperator is not SupportedOperator.Equals)
                                    {
                                        condition.Values = condition.Values.Select(x => $"%{x.ToLower()}%").ToList();
                                    }

                                    var (thisParameterPrefixes, thisConditionParameters) =
                                        ConditionHelper.ConstructingIncludes(
                                            $"{prefix}_{field}_{i}",
                                            condition.Values);
                                    conditionParameters =
                                        ConditionHelper.ConcatConditionParameters(
                                            conditionParameters,
                                            thisConditionParameters);

                                    whereClauses.Add(
                                        ConditionHelper.ConditionClause(
                                            conditionOperator is Contains or SupportedOperator.Equals
                                                ? LikeOrMultipleValues
                                                : IsNotLikeOrMultipleValues,
                                            thisFieldName,
                                            includeParameter: thisParameterPrefixes));
                                }

                                // else: throw null value exception
                                break;
                            case IsNull or IsNotNull:
                                whereClauses.Add(
                                    ConditionHelper.ConditionClause(
                                        condition.ConditionOperator is IsNull
                                            ? StringIsNullOrEmpty
                                            : StringIsNotNullOrEmpty,
                                        thisFieldName));
                                break;
                            case ContainsAny:
                                var optimizedContainsAnyPrefix = $"{prefix}_{field}_{i}";

                                conditionParameters.Add(
                                    optimizedContainsAnyPrefix,
                                    JsonConvert.SerializeObject(condition.Values));

                                whereClauses.Add(
                                    ConditionHelper.ConditionClause(
                                        OptimizedContainsAny,
                                        thisFieldName,
                                        optimizedContainsAnyPrefix));

                                break;
                        }

                        break;
                    case ConditionFields.ShopifyOrderTags
                        or ConditionFields.ShopifyProductTags
                        or ConditionFields.ShopifyTotalOrderPrice
                        or ConditionFields.ShopifyTotalOrderCount
                        or ConditionFields.ShopifyOrderProductName:
                        var (shopifyWhereClauses,
                            shopifyConditionParameters) = _shopifyOperation.ConditionClause(
                            prefix,
                            field,
                            i,
                            condition.ConditionOperator,
                            companyId,
                            condition.Values);
                        whereClauses.Add(shopifyWhereClauses);
                        conditionParameters =
                            ConditionHelper.ConcatConditionParameters(conditionParameters, shopifyConditionParameters);
                        break;
                    default:
                        if (!string.IsNullOrEmpty(condition.CompanyMessageTemplateId)
                            && condition.BroadcastMessageStatus is not null)
                        {
                            var (broadcastHistoriesJoinClause, broadcastHistoriesConditionParameters) =
                                await _broadcastHistoriesOperation.ConditionClause(
                                    prefix,
                                    i,
                                    condition.BroadcastMessageStatus.Value,
                                    condition.CompanyMessageTemplateId,
                                    companyId);
                            joinClauses.Add(broadcastHistoriesJoinClause);
                            conditionParameters = ConditionHelper.ConcatConditionParameters(
                                conditionParameters,
                                broadcastHistoriesConditionParameters);
                        }
                        else
                        {
                            var (
                                    customFieldJoinClause,
                                    customFieldConditionParameters,
                                    customFieldModifiedChannels,
                                    customFieldModifiedChannelIds) =
                                await _customFieldOperation.ConditionClause(
                                    prefix,
                                    i,
                                    conditionOperator,
                                    condition.FieldName,
                                    companyId,
                                    condition.Values,
                                    condition.TimeValueType,
                                    channels,
                                    channelIds,
                                    customFieldCount == 1);
                            if (customFieldModifiedChannels is not null && customFieldModifiedChannels.Any())
                            {
                                modifiedChannels = modifiedChannels.Concat(customFieldModifiedChannels).ToList();
                            }

                            if (customFieldModifiedChannelIds is not null && customFieldModifiedChannelIds.Any())
                            {
                                modifiedChannelIds = modifiedChannelIds.Concat(customFieldModifiedChannelIds).ToList();
                            }

                            if (customFieldJoinClause != string.Empty)
                            {
                                customFieldJoinParams.Add(customFieldJoinClause);
                            }

                            conditionParameters =
                                ConditionHelper.ConcatConditionParameters(
                                    conditionParameters,
                                    customFieldConditionParameters);
                        }

                        break;
                }

                parameters.AddRange(conditionParameters.Select(c => new SqlParameter(c.Key, c.Value)));
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Unable to filter the contact by field name {FieldName}, field value {FieldValue}: {ExceptionMessage}",
                    nameof(ConstructorQueryFromCondition),
                    conditions.ElementAtOrDefault(i)?.FieldName,
                    conditions.ElementAtOrDefault(i)?.Values,
                    ex.Message);
            }
        }

        if (customFieldJoinParams.Count > 0)
        {
            const string userProfileCustomField = "UPCF";
            var companyIdPrefix = $"{prefix}_Custom_Field_Company_Id";

            // Custom Field Join Clauses
            var customFieldJoinClauses =
                $"JOIN ( {string.Join("INTERSECT ", customFieldJoinParams)} ) {userProfileCustomField} " +
                $"ON {prefix}.Id = {userProfileCustomField}.UserProfileId " +
                $"AND {ConditionHelper.ConditionClause(Equal, $"{prefix}.CompanyId", companyIdPrefix)}";
            joinClauses.Add(customFieldJoinClauses);
            parameters.Add(new SqlParameter(companyIdPrefix, companyId));
        }

        var hashtags = conditions.Where(x => !string.IsNullOrEmpty(x.ContainHashTag)).ToList();
        if (hashtags.Any())
        {
            var (hashtagJoinClauses, hashtagWhereClauses, hashtagParameters) =
                await _hashtagOperation.ConditionClause(prefix, hashtags, companyId, prefix);
            whereClauses.AddRange(hashtagWhereClauses);
            joinClauses.AddRange(hashtagJoinClauses);
            parameters.AddRange(hashtagParameters.Select(c => new SqlParameter(c.Key, c.Value)));
        }

        if (modifiedChannels?.Count > 0 && modifiedChannelIds?.Count > 0)
        {
            var (channelWhereClause, channelParameters) =
                await _channelOperation.ConditionClause(prefix, companyId, modifiedChannels, modifiedChannelIds);

            whereClauses.Add(channelWhereClause);
            parameters.AddRange(
                channelParameters.Select(c => new SqlParameter(c.Key, c.Value)));
        }

        _logger.LogInformation("Constructed {WhereClauses}, {JoinClause}", whereClauses, joinClauses);

        return (whereClauses, joinClauses, parameters);
    }

    internal async Task<(string Query, List<SqlParameter> Parameters)> ConstructParameterizeQuery(
        string prefix,
        string companyId,
        List<Condition> conditions,
        string? sorting,
        Staff staff,
        long? assigneeId = null,
        List<string>? channels = null,
        List<string>? channelIds = null,
        StaffUserRole staffUserRole = StaffUserRole.Admin,
        string operation = "AND")
    {
        var (manageableWhereClauses,
                manageableJoinClauses,
                manageableParameters) =
            await _manageableOperation.ConditionClause(prefix, companyId, staff, assigneeId, staffUserRole);
        var parameters = manageableParameters;
        var (conditionWhereClauses,
            conditionJoinClauses,
            conditionParameters) = await ConstructorQueryFromCondition(
            prefix,
            companyId,
            conditions,
            channels,
            channelIds);

        var joinClauses = manageableJoinClauses.Concat(conditionJoinClauses).ToList();
        parameters = parameters.Concat(conditionParameters).ToList();

        switch (sorting)
        {
            case LastContactFromCustomers:
                manageableWhereClauses.Add(
                    $"{ConditionHelper.ConditionClause(IsNotNull, $"{prefix}.LastContactFromCustomers")}");
                break;
            case LastContact:
                manageableWhereClauses.Add($"{ConditionHelper.ConditionClause(IsNotNull, $"{prefix}.LastContact")}");
                break;
        }

        // Constructing SQL query
        var query =
            $"SELECT {prefix}.* " +
            $"FROM UserProfiles {prefix} " +
            $"{string.Join(" ", joinClauses)} " +
            $"WHERE {ConstructWhereClauses(operation, manageableWhereClauses, conditionWhereClauses)} ";

        return (query, parameters);
    }

    private static string ConstructWhereClauses(
        string operation,
        List<string> manageableWhereClauses,
        List<string> conditionWhereClauses)
    {
        var isOrOperation = operation == "OR";
        var whereClauses = manageableWhereClauses.Count > 0 ? $"{string.Join(" AND ", manageableWhereClauses)} " : "";
        if (manageableWhereClauses.Count > 0 && conditionWhereClauses.Count > 0)
        {
            whereClauses += "AND ";
        }

        if (isOrOperation)
        {
            conditionWhereClauses = conditionWhereClauses.Select(c => $"({c})").ToList();
        }

        whereClauses +=
            $"{(isOrOperation ? "(" : "")} {string.Join($" {operation} ", conditionWhereClauses)} {(isOrOperation ? ")" : "")} ";

        return whereClauses;
    }

    private static SupportedOperator MapToStringOperator(SupportedOperator originalOperator)
    {
        return originalOperator switch
        {
            IsNull => StringIsNullOrEmpty,
            IsNotNull => IsNotNullOrEmpty,
            _ => originalOperator
        };
    }
}