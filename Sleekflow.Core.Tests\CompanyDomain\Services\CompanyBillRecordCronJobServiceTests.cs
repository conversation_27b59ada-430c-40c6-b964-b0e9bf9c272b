using FluentAssertions;
using Hangfire;
using Hangfire.Client;
using Hangfire.Common;
using Hangfire.MemoryStorage;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Helpers;
using Travis_backend.InternalDomain.Models;

namespace Sleekflow.Core.Tests.CompanyDomain.Services;

public class CompanyBillRecordCronJobServiceTests
{
    private sealed class CaptureEnqueueClientFilter : JobFilterAttribute, IClientFilter
    {
        public static int CreatedCount;
        public void OnCreating(CreatingContext filterContext) { }

        public void OnCreated(CreatedContext filterContext)
        {
            var methodName = filterContext.Job?.Method?.Name;
            var declaring = filterContext.Job?.Method?.DeclaringType?.Name;
            if (string.Equals(methodName, "CreateInvoiceAsync", StringComparison.Ordinal) &&
                declaring != null && declaring.Contains("InternalIntegration", StringComparison.Ordinal))
            {
                CreatedCount++;
            }
        }
    }

    #region Notice-date arithmetic edge cases (leap day, month-end, year-end, corrupted metadata)

    [Test]
    public async Task NoticeDate_LeapYear_Feb29_Monthly_Enqueues_Invoice()
    {
        // Arrange a future Feb 29 as the actual period end, and set NoticePeriod so that noticeDate == today.
        var today = DateTime.UtcNow.Date;
        var year = today.Year;
        // Find the next leap year with Feb 29 in the future
        while (!DateTime.IsLeapYear(year) || new DateTime(year, 2, 29) <= today)
        {
            year++;
        }

        var actualPeriodEnd = new DateTime(year, 2, 29);
        var noticeDays = (actualPeriodEnd - today).Days; // > 0

        var bill = new BillRecord
        {
            CompanyId = "C-leap",
            SubscriptionPlanId = "enterprise_pack", // must include lowercase "enterprise"
            IsIrregularPlan = true,
            NoticePeriod = noticeDays,
            IsAutoRenewalEnabled = true,
            Status = BillStatus.Active,
            PeriodStart = today.AddMonths(-1),
            PeriodEnd = actualPeriodEnd.AddMonths(1), // must be > today for eligibility
            PaymentIntervalType = PaymentIntervalType.Monthly,
            PaymentTerms = 7,
            PayAmount = 3000,
            currency = "usd",
            metadata = new Dictionary<string, string>
            {
                ["actual_period_end"] = actualPeriodEnd.ToString("O")
            }
        };

        _db.CompanyBillRecords.Add(bill);
        await _db.SaveChangesAsync();

        BillRecord? createdBillRecord = null;
        _companyBillRecordServiceMock
            .Setup(s => s.CreateBillRecordAsync(It.IsAny<BillRecord>()))
            .Callback<BillRecord>(br => createdBillRecord = br)
            .Returns(Task.CompletedTask);

        var svc = new CompanyBillRecordCronJobService(
            _companyBillRecordServiceMock.Object,
            _db,
            _logger,
            _revenueCalculator);

        // Act
        await svc.ProcessDailyNoticePeriodBillRecordsAsync();

        // Assert
        createdBillRecord.Should().NotBeNull();
        CaptureEnqueueClientFilter.CreatedCount.Should().Be(
            1,
            "monthly plans on leap-day notice should enqueue invoice");
    }

    [Test]
    public async Task NoticeDate_MonthEnd_NextMonthLastDay_Monthly_Enqueues_Invoice()
    {
        // Arrange the last day of next month as the actual period end, with noticeDate == today.
        var today = DateTime.UtcNow.Date;
        var firstOfThisMonth = new DateTime(today.Year, today.Month, 1);
        var firstOfNextMonth = firstOfThisMonth.AddMonths(1);
        var lastDayNextMonth = new DateTime(
            firstOfNextMonth.Year,
            firstOfNextMonth.Month,
            DateTime.DaysInMonth(firstOfNextMonth.Year, firstOfNextMonth.Month));
        var noticeDays = (lastDayNextMonth - today).Days; // > 0

        var bill = new BillRecord
        {
            CompanyId = "C-month-end",
            SubscriptionPlanId = "pro_enterprise_bundle",
            IsIrregularPlan = true,
            NoticePeriod = noticeDays,
            IsAutoRenewalEnabled = true,
            Status = BillStatus.Active,
            PeriodStart = today.AddMonths(-1),
            PeriodEnd = lastDayNextMonth.AddMonths(1),
            PaymentIntervalType = PaymentIntervalType.Monthly,
            PaymentTerms = 7,
            PayAmount = 3000,
            currency = "usd",
            metadata = new Dictionary<string, string>
            {
                ["actual_period_end"] = lastDayNextMonth.ToString("O")
            }
        };

        _db.CompanyBillRecords.Add(bill);
        await _db.SaveChangesAsync();

        _companyBillRecordServiceMock
            .Setup(s => s.CreateBillRecordAsync(It.IsAny<BillRecord>()))
            .Returns(Task.CompletedTask);

        var svc = new CompanyBillRecordCronJobService(
            _companyBillRecordServiceMock.Object,
            _db,
            _logger,
            _revenueCalculator);

        // Act
        await svc.ProcessDailyNoticePeriodBillRecordsAsync();

        // Assert
        CaptureEnqueueClientFilter.CreatedCount.Should().Be(
            1,
            "monthly plans on month-end notice should enqueue invoice");
    }

    [Test]
    public async Task NoticeDate_YearEnd_Dec31_Monthly_Enqueues_Invoice()
    {
        // Arrange Dec 31 (this year or next) as the actual period end; set NoticePeriod so noticeDate == today.
        var today = DateTime.UtcNow.Date;
        var dec31 = new DateTime(today.Year, 12, 31);
        if (dec31 <= today)
        {
            dec31 = new DateTime(today.Year + 1, 12, 31);
        }

        var actualPeriodEnd = dec31;
        var noticeDays = (actualPeriodEnd - today).Days; // > 0

        var bill = new BillRecord
        {
            CompanyId = "C-yearend",
            SubscriptionPlanId = "enterprise_annual",
            IsIrregularPlan = true,
            NoticePeriod = noticeDays,
            IsAutoRenewalEnabled = true,
            Status = BillStatus.Active,
            PeriodStart = today.AddMonths(-1),
            PeriodEnd = actualPeriodEnd.AddMonths(1),
            PaymentIntervalType = PaymentIntervalType.Monthly,
            PaymentTerms = 7,
            PayAmount = 3000,
            currency = "usd",
            metadata = new Dictionary<string, string>
            {
                ["actual_period_end"] = actualPeriodEnd.ToString("O")
            }
        };

        _db.CompanyBillRecords.Add(bill);
        await _db.SaveChangesAsync();

        _companyBillRecordServiceMock
            .Setup(s => s.CreateBillRecordAsync(It.IsAny<BillRecord>()))
            .Returns(Task.CompletedTask);

        var svc = new CompanyBillRecordCronJobService(
            _companyBillRecordServiceMock.Object,
            _db,
            _logger,
            _revenueCalculator);

        // Act
        await svc.ProcessDailyNoticePeriodBillRecordsAsync();

        // Assert
        CaptureEnqueueClientFilter.CreatedCount.Should().Be(
            1,
            "monthly plans on year-end notice should enqueue invoice");
    }

    [Test]
    public async Task Corrupted_Metadata_ActualPeriodEnd_Fallbacks_To_PeriodEnd_And_Enqueues_Invoice()
    {
        // Arrange corrupted metadata string; ensure PeriodEnd - NoticePeriod == today so fallback is exercised.
        var today = DateTime.UtcNow.Date;
        const int noticeDays = 10;
        var periodEnd = today.AddDays(noticeDays);

        var bill = new BillRecord
        {
            CompanyId = "C-bad-meta",
            SubscriptionPlanId = "enterprise_suite",
            IsIrregularPlan = true,
            NoticePeriod = noticeDays,
            IsAutoRenewalEnabled = true,
            Status = BillStatus.Active,
            PeriodStart = today.AddMonths(-1),
            PeriodEnd = periodEnd, // fallback target
            PaymentIntervalType = PaymentIntervalType.Monthly,
            PaymentTerms = 7,
            PayAmount = 3000,
            currency = "usd",
            metadata = new Dictionary<string, string>
            {
                ["actual_period_end"] = "bad"
            } // corrupted
        };

        _db.CompanyBillRecords.Add(bill);
        await _db.SaveChangesAsync();

        _companyBillRecordServiceMock
            .Setup(s => s.CreateBillRecordAsync(It.IsAny<BillRecord>()))
            .Returns(Task.CompletedTask);

        var svc = new CompanyBillRecordCronJobService(
            _companyBillRecordServiceMock.Object,
            _db,
            _logger,
            _revenueCalculator);

        // Act
        await svc.ProcessDailyNoticePeriodBillRecordsAsync();

        // Assert
        CaptureEnqueueClientFilter.CreatedCount.Should().Be(1, "fallback to PeriodEnd notice should enqueue invoice");
    }

    #endregion

    private ApplicationDbContext _db = null!;
    private Mock<ICompanyBillRecordService> _companyBillRecordServiceMock = null!;
    private ILogger<CompanyBillRecordCronJobService> _logger = null!;
    private static bool _filterAdded;
    private BillRecordRevenueCalculatorService _revenueCalculator = null!;

    [SetUp]
    public void Setup()
    {
        GlobalConfiguration.Configuration.UseMemoryStorage();
        JobStorage.Current = new MemoryStorage();

        if (!_filterAdded)
        {
            GlobalJobFilters.Filters.Add(new CaptureEnqueueClientFilter());
            _filterAdded = true;
        }

        CaptureEnqueueClientFilter.CreatedCount = 0;

        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString())
            .EnableSensitiveDataLogging()
            .Options;
        _db = new ApplicationDbContext(options);
        _companyBillRecordServiceMock = new Mock<ICompanyBillRecordService>();
        _logger = new LoggerFactory().CreateLogger<CompanyBillRecordCronJobService>();

        var revenueLogger = new LoggerFactory().CreateLogger<BillRecordRevenueCalculatorService>();
        var timezoneMock = new Mock<ITimezoneAwareMrrCalculationService>();
        _revenueCalculator = new BillRecordRevenueCalculatorService(revenueLogger, timezoneMock.Object);
    }

    [Test]
    public async Task Processes_Eligible_Enterprise_Monthly_On_NoticeDate_Creates_BillRecord_And_Invoice()
    {
        var today = DateTime.UtcNow.Date;
        var noticeDays = 14;
        var actualPeriodEnd = today.AddDays(noticeDays);

        var bill = new BillRecord
        {
            CompanyId = "C-1",
            SubscriptionPlanId = "sleekflow_enterprise",
            IsIrregularPlan = true,
            NoticePeriod = noticeDays,
            IsAutoRenewalEnabled = true,
            Status = BillStatus.Active,
            PeriodStart = today.AddMonths(-1),
            PeriodEnd = today.AddMonths(1),
            PaymentIntervalType = PaymentIntervalType.Monthly,
            PaymentTerms = 7,
            PayAmount = 3000,
            currency = "usd",
            metadata = new Dictionary<string, string>
            {
                ["actual_period_end"] = actualPeriodEnd.ToString("O")
            }
        };

        _db.CompanyBillRecords.Add(bill);
        await _db.SaveChangesAsync();

        BillRecord? createdBillRecord = null;
        _companyBillRecordServiceMock
            .Setup(s => s.CreateBillRecordAsync(It.IsAny<BillRecord>()))
            .Callback<BillRecord>(br => createdBillRecord = br)
            .Returns(Task.CompletedTask);

        var svc = new CompanyBillRecordCronJobService(
            _companyBillRecordServiceMock.Object,
            _db,
            _logger,
            _revenueCalculator);

        await svc.ProcessDailyNoticePeriodBillRecordsAsync();

        createdBillRecord.Should().NotBeNull();
        // The new period starts at actualPeriodEnd and ends at actualPeriodEnd + month diff (original period) + 7 days
        createdBillRecord!.PeriodStart.Date.Should().Be(actualPeriodEnd.Date);
        var originalMonths = BillRecordRevenueCalculatorHelper.GetMonthDiff(bill.PeriodStart, bill.PeriodEnd);
        createdBillRecord.PeriodEnd.Date.Should().Be(actualPeriodEnd.AddMonths(originalMonths).AddDays(7).Date);

        CaptureEnqueueClientFilter.CreatedCount.Should().Be(
            1,
            "non-custom plans should enqueue first invoice creation");
    }

    [Test]
    public async Task Custom_Interval_Skips_Invoice_But_Creates_New_BillRecord_With_Shifted_Splits()
    {
        var today = DateTime.UtcNow.Date;
        var noticeDays = 10;
        var actualPeriodEnd = today.AddDays(noticeDays);

        var bill = new BillRecord
        {
            CompanyId = "C-2",
            SubscriptionPlanId = "enterprise_plan_v2", // contains "enterprise"
            IsIrregularPlan = true,
            NoticePeriod = noticeDays,
            IsAutoRenewalEnabled = true,
            Status = BillStatus.Active,
            PeriodStart = today.AddMonths(-3),
            PeriodEnd = today.AddMonths(2),
            PaymentIntervalType = PaymentIntervalType.Custom,
            PaymentTerms = 7,
            PayAmount = 5000,
            currency = "usd",
            PaymentSplits =
            [
                new PaymentSplit
                {
                    PaymentDate = today.AddMonths(-1), SplitPercentage = 40
                },
                new PaymentSplit
                {
                    PaymentDate = today.AddDays(5), SplitPercentage = 60
                }
            ],
            metadata = new Dictionary<string, string>
            {
                ["actual_period_end"] = actualPeriodEnd.ToString("O")
            }
        };

        _db.CompanyBillRecords.Add(bill);
        await _db.SaveChangesAsync();

        BillRecord? createdBillRecord = null;
        _companyBillRecordServiceMock
            .Setup(s => s.CreateBillRecordAsync(It.IsAny<BillRecord>()))
            .Callback<BillRecord>(br => createdBillRecord = br)
            .Returns(Task.CompletedTask);

        var svc = new CompanyBillRecordCronJobService(
            _companyBillRecordServiceMock.Object,
            _db,
            _logger,
            _revenueCalculator);
        await svc.ProcessDailyNoticePeriodBillRecordsAsync();

        createdBillRecord.Should().NotBeNull();
        // Because it's Custom, invoice creation should be skipped
        CaptureEnqueueClientFilter.CreatedCount.Should().Be(0);

        // Splits should be shifted by original month diff and filtered within [actualPeriodEnd, newPeriodEnd]
        var expectedStart = actualPeriodEnd.Date;
        var originalMonths2 = BillRecordRevenueCalculatorHelper.GetMonthDiff(bill.PeriodStart, bill.PeriodEnd);
        var expectedEnd = actualPeriodEnd.AddMonths(originalMonths2).Date;
        createdBillRecord!.PaymentSplits.Should().NotBeNull();
        createdBillRecord.PaymentSplits!
            .All(s => s.PaymentDate.Date >= expectedStart && s.PaymentDate.Date <= expectedEnd)
            .Should().BeTrue();
    }

    [Test]
    public async Task Filters_Out_NonEligible_Records_And_Handles_Metadata_Fallback()
    {
        var today = DateTime.UtcNow.Date;

        var candidates = new List<BillRecord>
        {
            // NoticePeriod null
            new ()
            {
                CompanyId = "C-null-notice",
                SubscriptionPlanId = "sleekflow_enterprise",
                IsIrregularPlan = true,
                NoticePeriod = null,
                IsAutoRenewalEnabled = true,
                Status = BillStatus.Active,
                PeriodStart = today.AddMonths(-1),
                PeriodEnd = today.AddMonths(1),
                PaymentIntervalType = PaymentIntervalType.Monthly,
                PaymentTerms = 7,
                PayAmount = 1000,
                currency = "usd"
            },
            // NoticePeriod zero
            new ()
            {
                CompanyId = "C-zero-notice",
                SubscriptionPlanId = "sleekflow_enterprise",
                IsIrregularPlan = true,
                NoticePeriod = 0,
                IsAutoRenewalEnabled = true,
                Status = BillStatus.Active,
                PeriodStart = today.AddMonths(-1),
                PeriodEnd = today.AddMonths(1),
                PaymentIntervalType = PaymentIntervalType.Monthly,
                PaymentTerms = 7,
                PayAmount = 1000,
                currency = "usd"
            },
            // PeriodEnd <= today
            new ()
            {
                CompanyId = "C-expired",
                SubscriptionPlanId = "sleekflow_enterprise",
                IsIrregularPlan = true,
                NoticePeriod = 14,
                IsAutoRenewalEnabled = true,
                Status = BillStatus.Active,
                PeriodStart = today.AddMonths(-2),
                PeriodEnd = today,
                PaymentIntervalType = PaymentIntervalType.Monthly,
                PaymentTerms = 7,
                PayAmount = 1000,
                currency = "usd"
            },
            // AutoRenew disabled
            new ()
            {
                CompanyId = "C-no-autorenew",
                SubscriptionPlanId = "sleekflow_enterprise",
                IsIrregularPlan = true,
                NoticePeriod = 14,
                IsAutoRenewalEnabled = false,
                Status = BillStatus.Active,
                PeriodStart = today.AddMonths(-1),
                PeriodEnd = today.AddMonths(1),
                PaymentIntervalType = PaymentIntervalType.Monthly,
                PaymentTerms = 7,
                PayAmount = 1000,
                currency = "usd"
            },
            // Inactive status
            new ()
            {
                CompanyId = "C-inactive",
                SubscriptionPlanId = "sleekflow_enterprise",
                IsIrregularPlan = true,
                NoticePeriod = 14,
                IsAutoRenewalEnabled = true,
                Status = BillStatus.Canceled,
                PeriodStart = today.AddMonths(-1),
                PeriodEnd = today.AddMonths(1),
                PaymentIntervalType = PaymentIntervalType.Monthly,
                PaymentTerms = 7,
                PayAmount = 1000,
                currency = "usd"
            },
            // Case sensitivity: should NOT match because service checks for lowercase "enterprise"
            new ()
            {
                CompanyId = "C-case",
                SubscriptionPlanId = "Sleekflow_Enterprise", // uppercase E
                IsIrregularPlan = true,
                NoticePeriod = 14,
                IsAutoRenewalEnabled = true,
                Status = BillStatus.Active,
                PeriodStart = today.AddMonths(-1),
                PeriodEnd = today.AddMonths(1),
                PaymentIntervalType = PaymentIntervalType.Monthly,
                PaymentTerms = 7,
                PayAmount = 1000,
                currency = "usd"
            },
            // Metadata fallback: missing actual_period_end -> falls back to PeriodEnd; set PeriodEnd = today + 14 so noticeDate == today
            new ()
            {
                CompanyId = "C-fallback",
                SubscriptionPlanId = "sleekflow_enterprise",
                IsIrregularPlan = true,
                NoticePeriod = 14,
                IsAutoRenewalEnabled = true,
                Status = BillStatus.Active,
                PeriodStart = today.AddMonths(-1),
                PeriodEnd = today.AddDays(14),
                PaymentIntervalType = PaymentIntervalType.Monthly,
                PaymentTerms = 7,
                PayAmount = 1000,
                currency = "usd"
            }
        };

        _db.CompanyBillRecords.AddRange(candidates);
        await _db.SaveChangesAsync();

        var created = new List<BillRecord>();
        _companyBillRecordServiceMock
            .Setup(s => s.CreateBillRecordAsync(It.IsAny<BillRecord>()))
            .Callback<BillRecord>(br => created.Add(br))
            .Returns(Task.CompletedTask);

        var svc = new CompanyBillRecordCronJobService(
            _companyBillRecordServiceMock.Object,
            _db,
            _logger,
            _revenueCalculator);
        await svc.ProcessDailyNoticePeriodBillRecordsAsync();

        // Only the fallback candidate should be processed
        created.Count.Should().Be(1);
        var createdBr = created.Single();
        createdBr.CompanyId.Should().Be("C-fallback");
        CaptureEnqueueClientFilter.CreatedCount.Should().Be(1);
    }
}