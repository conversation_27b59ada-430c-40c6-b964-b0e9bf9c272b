﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using Hangfire;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Sleekflow.Apis.MessagingHub.Model;
using Sleekflow.Apis.UserEventHub.Api;
using Sleekflow.Apis.UserEventHub.Client;
using Sleekflow.Apis.UserEventHub.Model;
using Travis_backend.AutomationDomain.Models;
using Travis_backend.BackgroundTaskServices.Helpers;
using Travis_backend.BroadcastDomain.ViewModels;
using Travis_backend.Cache;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.CompanyDomain.ViewModels;
using Travis_backend.Configuration;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ConversationDomain.ConversationQueryables;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.Repositories;
using Travis_backend.ConversationDomain.Services;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationDomain.ViewModels.Mappers;
using Travis_backend.Data.BackgroundTask;
using Travis_backend.Database;
using Travis_backend.Database.Services;
using Travis_backend.Enums;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.ViewModels;
using Travis_backend.Models.BackgroundTask;
using Travis_backend.NotificationHubs;
using Travis_backend.Notifications;
using Travis_backend.PiiMasking.Models;
using Travis_backend.PiiMaskingDomain.Services;
using Travis_backend.StripeIntegrationDomain.Models;
using Travis_backend.StripeIntegrationDomain.Services;
using Travis_backend.Telemetries;
using Travis_backend.Telemetries.Constants;
using Travis_backend.TenantHubDomain.Models;
using Travis_backend.TenantHubDomain.Services;
using Travis_backend.UserEventHubDomain.Constants;

namespace Travis_backend.SignalR
{
    public interface ISignalRService
    {
        Task<List<string>> GetSignalRTargetUserIds(
            string companyId,
            string conversationId,
            long? conversationAssignedTeamId,
            long? conversationAssigneeId,
            bool isUnassignedConversations = false,
            bool isGetActiveUserOnly = true);

        Task<List<string>> GetAllStaffIds(
            string companyId);

        Task SignalROnConversationTyping(string companyId, ConversationTypingObject conversationTypingObject);

        Task SignalROnUserProfileAdded(
            UserProfile userProfile,
            UserProfileSource? userProfileSource = null);

        Task SignalROnUserProfileUpdated(
            UserProfile userProfile,
            UserProfileSource? userProfileSource = null);

        Task SignalROnUserProfileDeleted(
            Conversation conversation,
            UserProfile userProfile,
            Company company,
            UserProfileSource? source = null);

        Task SignalROnConversationAdded(Conversation conversation);

        Task SignalROnConversationStatusChanged(
            Conversation conversation,
            UserProfileSource? userProfileSource = null);

        Task SignalROnConversationBookmarkStatusChanged(
            string staffId,
            Conversation conversation);

        #region Conversation Assignment Status SignalR Events

        Task SignalROnConversationAssigneeChanged(
            Conversation targetConversation,
            bool pushNotification = true,
            UserProfileSource? userProfileSource = null);

        Task SignalROnConversationAssigneeDeleted(
            Conversation targetConversation,
            UserProfileSource? userProfileSource = null);

        Task SignalROnConversationAssigneeDeleted(
            Conversation targetConversation,
            List<string> listener,
            UserProfileSource? userProfileSource = null);

        Task SignalROnConversationAssignTeamChanged(
            Conversation targetConversation,
            UserProfileSource? userProfileSource = null);

        Task SignalROnConversationAdditionalAssigneeChanged(
            string conversationId,
            Staff staff,
            AdditionalAssigneeEvent additionalAssignee,
            UserProfileSource? userProfileSource = null);

        #endregion

        Task SignalROnConversationNoteReceived(Conversation conversation, ConversationMessage note);

        Task SignalROnRemarksReceived(RemarkResponse remarkResponse);

        Task SignalROnRemarkUpdated(UpdateRemarkResponse updateRemarkResponse, string companyId);

        Task SignalROnRemarkDeleted(DeleteRemarkResponse deleteRemarkResponse, string companyId);

        Task SignalROnMessageReceived(
            Conversation conversation,
            ConversationMessage message,
            bool isStaffReply = false);

        Task SignalROnBroadcastCampaignSending(CompanyMessageTemplate messageTemplate);

        Task SignalROnBroadcastCampaignSent(
            CompanyMessageTemplate messageTemplate,
            List<BroadcastHistory> broadcastHistories);

        Task SignalROnChannelAdded(Company company, ChannelSignal channelSignal);

        Task SignalROnChannelDeleted(Company company, ChannelSignal channelSignal);

        Task SignalROnAssignmentRuleAdded(Company company, AssignmentRule assignmentRuleResponse);

        Task SignalROnAssignmentRuleChanged(Company company, AssignmentRule assignmentRuleResponse);

        Task SignalROnAssignmentRuleDelete(Company company, List<AssignmentRule> assignmentRuleResponse);

        Task SiganlROnUserProfileFieldFormatChanged(
            Company company,
            List<CompanyCustomUserProfileFieldViewModel> userProfileCustomFieldViewModel);

        Task SignalROnMessageStatusChanged(ConversationMessage message);

        // Push notifications
        Task SendNewMessagePushNotification(Conversation conversation, ConversationMessage message);

        Task SendNewMessageNotificationOfUnassignedConversationToAllStaff(
            string conversationId,
            ConversationMessage message,
            List<ConversationUnreadRecord> subscribers,
            string messageBody);

        Task SendPushNotificationToSubscriber(
            Conversation conversation,
            ConversationMessage message,
            List<ConversationUnreadRecord> subscribers,
            string messageBody,
            bool isNewConversation = false);

        Task SendSnoozeCompletedReminder(Conversation conversation);

        Task SendTeamAssignedPushNotification(Conversation conversation);

        Task SignalForceLogout(string deviceUUID, ActiveSessionResponse activeSessionResponse);

        Task SignalRAutoLogout(string deviceUUID, ActiveSessionResponse activeSessionResponse);

        Task SignalROnWebclientInfoAdded(string webclientUUID, IPAddressInfo newAddressInfo);

        Task SendLiveChatOnlineStatus(WebClientResponse webClientResponse);

        Task TestNotification(string userId, string companyId);

        Task TestRegNotification(string id, DeviceRegistration deviceUpdate);

        Task SignalROnBackgroundTaskStatusChange(
            string userId,
            BackgroundTask backgroundTask,
            BackgroundTaskStatus status);

        Task SignalROnStripePaymentStatusUpdated(
            string companyId,
            StripePaymentRecordResponse stripePaymentRecordResponse);

        Task SignalROnStaffUpdated(string userId, StaffWithoutCompanyResponse staffResponse);

        Task SignalRResendOnMessageReceived(
            string identityId,
            ConversationMessageResponseViewModel conversationMessagesVm);

        Task SignalROnConversationUnreadStatusChanged(Conversation conversation, string userId);

        Task SignalROnWhatsappCloudApiBusinessBalanceChanged(
            string companyId,
            BusinessBalanceDto businessBalance);
    }

    public class SignalRService : ISignalRService
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly IMapper _mapper;
        private readonly IConfiguration _configuration;
        private readonly IHubContext<Chat> _hubContext;
        private readonly ILogger _logger;
        private readonly NotificationHubProxy _notificationHubProxy;
        private readonly ICompanyTeamService _companyTeamService;
        private readonly ISleekPayService _sleekPayService;
        private readonly IConversationService _conversationService;
        private readonly ILockService _lockService;
        private readonly IMessagesApi _messagesApi;
        private readonly IAssociationsApi _associationsApi;
        private readonly IDbContextService _dbContextService;
        private readonly IPiiMaskingService _piiMaskingService;
        private readonly INotificationsApi _notificationsApi;
        private readonly IApplicationInsightsTelemetryTracer _applicationInsightsTelemetryTracer;
        private readonly IServiceProvider _serviceProvider;
        private readonly IRbacNotificationService _rbacNotificationService;
        private readonly IEnabledFeaturesService _enabledFeaturesService;
        private readonly IMentionQueryableResolver _mentionQueryableResolver;
        private readonly IConversationReadOnlyRepository _conversationReadOnlyRepository;
        private readonly IConversationNoCompanyResponseViewModelMapper _conversationNoCompanyResponseViewModelMapper;

        public SignalRService(
            ApplicationDbContext appDbContext,
            IMapper mapper,
            IConfiguration configuration,
            ILogger<SignalRService> logger,
            IHubContext<Chat> hubContext,
            IOptions<NotificationHubConfiguration> standardNotificationHubConfiguration,
            ICompanyTeamService companyTeamService,
            ISleekPayService sleekPayService,
            IConversationService conversationService,
            ILockService lockService,
            IMessagesApi messagesApi,
            IAssociationsApi associationsApi,
            IDbContextService dbContextService,
            IPiiMaskingService piiMaskingService,
            INotificationsApi notificationApi,
            IApplicationInsightsTelemetryTracer applicationInsightsTelemetryTracer,
            IServiceProvider serviceProvider,
            IMentionQueryableResolver mentionQueryableResolver,
            IRbacNotificationService rbacNotificationService,
            IEnabledFeaturesService enabledFeaturesService,
            IConversationNoCompanyResponseViewModelMapper conversationNoCompanyResponseViewModelMapper,
            IConversationReadOnlyRepository conversationReadOnlyRepository)
        {
            _appDbContext = appDbContext;
            _mapper = mapper;
            _configuration = configuration;
            _hubContext = hubContext;
            _logger = logger;
            _notificationHubProxy = new NotificationHubProxy(standardNotificationHubConfiguration.Value);
            _companyTeamService = companyTeamService;
            _sleekPayService = sleekPayService;
            _conversationService = conversationService;
            _lockService = lockService;
            _messagesApi = messagesApi;
            _associationsApi = associationsApi;
            _dbContextService = dbContextService;
            _piiMaskingService = piiMaskingService;
            _notificationsApi = notificationApi;
            _applicationInsightsTelemetryTracer = applicationInsightsTelemetryTracer;
            _serviceProvider = serviceProvider;
            _rbacNotificationService = rbacNotificationService;
            _mentionQueryableResolver = mentionQueryableResolver;
            _enabledFeaturesService = enabledFeaturesService;
            _conversationNoCompanyResponseViewModelMapper = conversationNoCompanyResponseViewModelMapper;
            _conversationReadOnlyRepository = conversationReadOnlyRepository;
        }

        public async Task SignalROnUserProfileAdded(
            UserProfile userProfile,
            UserProfileSource? userProfileSource = null)
        {
            if (userProfile == null)
            {
                return;
            }

            if (await IsFlowBuilderContactUpdateLowBandwidthCondition(userProfile.CompanyId, userProfileSource))
            {
                _logger.LogInformation(
                    "[{MethodName}] Skipped due to low bandwidth mode. CompanyId: {CompanyId}, userProfileId : {UserProfile}, userProfileSource: {source}",
                    nameof(SignalROnUserProfileAdded),
                    userProfile.CompanyId,
                    userProfile.Id,
                    userProfileSource);
                return;
            }

            var profileViewModel = _mapper.Map<UserProfileNoCompanyResponse>(userProfile);

            await _hubContext.Clients
                .Group(userProfile.CompanyId)
                .SendAsync("OnUserProfileAdded", profileViewModel);
        }

        public async Task SignalROnUserProfileUpdated(UserProfile userProfile, UserProfileSource? userProfileSource = null)
        {
            if (userProfile == null)
            {
                _logger.LogWarning("[{MethodName}]SignalROnUserProfileUpdated: userProfile is null",
                    nameof(SignalROnUserProfileUpdated));
                return;
            }

            if (await IsFlowBuilderContactUpdateLowBandwidthCondition(userProfile.CompanyId, userProfileSource))
            {
                _logger.LogInformation(
                    "[{MethodName}] Skipped due to low bandwidth mode. CompanyId: {CompanyId}, userProfileId : {UserProfile}, userProfileSource: {source}",
                    nameof(SignalROnUserProfileUpdated),
                    userProfile.CompanyId,
                    userProfile.Id,
                    userProfileSource);
                return;
            }

            if (await IsContactImportLowBandwidthCondition(userProfile.CompanyId))
            {
                _logger.LogInformation(
                    "[{MethodName}] Skipped due to low bandwidth mode. CompanyId: {CompanyId}, userProfileId : {UserProfile}, userProfileSource: {source}",
                    nameof(SignalROnUserProfileUpdated),
                    userProfile.CompanyId,
                    userProfile.Id,
                    userProfileSource);
                return;
            }

            var profileViewModel = _mapper.Map<UserProfileNoCompanyResponse>(userProfile);

            // Attach Contact Listing in the signalR response
            var lists = await _appDbContext.CompanyImportContactHistories
                .Where(x => x.ImportedUserProfiles.Any(y => y.UserProfileId == profileViewModel.Id))
                .OrderByDescending(x => x.IsBookmarked)
                .ThenBy(x => x.Order)
                .ThenByDescending(x => x.CreatedAt)
                .Select(
                    x => new
                    {
                        x.Id,
                        x.ImportName
                    })
                .AsNoTracking()
                .ToListAsync();

            // Init ContactLists
            profileViewModel.ContactLists = [];

            foreach (var list in lists)
            {
                profileViewModel
                    .ContactLists.Add(
                        new ContactJoinedList
                        {
                            Id = list.Id,
                            ListName = list.ImportName
                        });
            }

            await _hubContext.Clients
                .Group(userProfile.CompanyId)
                .SendAsync("OnUserProfileUpdated", profileViewModel);

            var conversation = await _appDbContext.Conversations
                .Where(c => c.UserProfileId == userProfile.Id)
                .AsNoTracking()
                .Select(
                    x => new
                    {
                        x.Id,
                        x.AssigneeId,
                        x.AssignedTeamId,
                        x.CompanyId
                    })
                .FirstOrDefaultAsync();

            if (conversation != null)
            {
                await SendConversationStatusUpdateEventAsync(
                    conversation.CompanyId,
                    conversation.Id,
                    conversation.AssignedTeamId,
                    conversation.AssigneeId,
                    "OnConversationStatusChanged");
            }
        }

        public async Task SendOnConversationStatusChanged(
            string companyId,
            string conversationId,
            List<string> signalRTargetUserIds)
        {
            await SendPersonalizedConversationDataAsync(
                companyId,
                conversationId,
                signalRTargetUserIds,
                "OnConversationStatusChanged");
        }

        public async Task SendStandardizedConversationDataAsync(
            string companyId,
            string conversationId,
            List<string> signalRTargetUserIds,
            string eventName)
        {
            var rbacService = _serviceProvider.GetRequiredService<IRbacService>();
            var isRbacEnabled = await rbacService.IsRbacEnabled(companyId);
            if (isRbacEnabled)
            {
                await _rbacNotificationService.SendStandardizedConversationDataAsync(
                    companyId,
                    conversationId,
                    signalRTargetUserIds,
                    eventName);
            }
            else
            {
                await DefaultSendStandardizedConversationDataAsync(companyId, conversationId, signalRTargetUserIds, eventName);
            }
        }

        public async Task DefaultSendStandardizedConversationDataAsync(
            string companyId,
            string conversationId,
            List<string> signalRTargetUserIds,
            string eventName)
        {
            var conversation = await _conversationReadOnlyRepository.FindConversationByIdAsync(
                    conversationId,
                    companyId,
                    typeof(ConversationNoCompanyResponseViewModel));

            var responseVm = await _conversationNoCompanyResponseViewModelMapper.ToViewModelAsync(conversation);

            if (responseVm.LastMessageId.HasValue)
            {
                var lastMessage = await _appDbContext.ConversationMessages
                    .Where(x => x.Id == responseVm.LastMessageId)
                    .Include(x => x.UploadedFiles)
                    .Include(x => x.EmailFrom)
                    .Include(x => x.Sender)
                    .Include(x => x.Receiver)
                    .Include(x => x.SenderDevice)
                    .Include(x => x.ReceiverDevice)
                    .Include(x => x.facebookSender)
                    .Include(x => x.facebookReceiver)
                    .Include(x => x.whatsappSender)
                    .Include(x => x.whatsappReceiver)
                    .Include(x => x.WebClientSender)
                    .Include(x => x.WebClientReceiver)
                    .Include(x => x.MessageAssignee.Identity)
                    .Include(x => x.WeChatSender)
                    .Include(x => x.WeChatReceiver)
                    .Include(x => x.LineSender)
                    .Include(x => x.LineReceiver)
                    .Include(x => x.SMSSender)
                    .Include(x => x.SMSReceiver)
                    .Include(x => x.InstagramReceiver)
                    .Include(x => x.InstagramSender)
                    .Include(x => x.ViberSender)
                    .Include(x => x.ViberReceiver)
                    .Include(x => x.TelegramSender)
                    .Include(x => x.TelegramReceiver)
                    .Include(x => x.Whatsapp360DialogSender)
                    .Include(x => x.Whatsapp360DialogReceiver)
                    .Include(x => x.Whatsapp360DialogExtendedMessagePayload)
                    .Include(x => x.ExtendedMessagePayload)
                    .ProjectTo<ConversationMessageResponseViewModel>(_mapper.ConfigurationProvider)
                    .ToListAsync();
                responseVm.LastMessage = lastMessage;
            }

            await _hubContext.Clients.Users(signalRTargetUserIds).SendAsync(
                            eventName,
                            responseVm);
        }

        public async Task SendPersonalizedConversationDataAsync(
            string companyId,
            string conversationId,
            List<string> signalRTargetUserIds,
            string eventName,
            StaffWithoutCompanyResponse additionalAssigneeData = null)
        {
            // If RBAC enabled, use the new method
            var rbacService = _serviceProvider.GetRequiredService<IRbacService>();

            var isRbacEnabled = await rbacService.IsRbacEnabled(companyId);
            if (isRbacEnabled)
            {
                await _rbacNotificationService
                    .SendPersonalizedConversationDataAsync(companyId, conversationId, signalRTargetUserIds, eventName, additionalAssigneeData);
                return;
            }

            await DefaultSendPersonalizedConversationDataAsync(companyId, conversationId, signalRTargetUserIds, eventName, additionalAssigneeData);
        }

        public async Task DefaultSendPersonalizedConversationDataAsync(string companyId, string conversationId,
            List<string> signalRTargetUserIds, string eventName, StaffWithoutCompanyResponse additionalAssigneeData)
        {
            foreach (var signalRTargetUserId in signalRTargetUserIds)
            {
                var companyUser = await _appDbContext.UserRoleStaffs
                    .Where(x => x.CompanyId == companyId && x.IdentityId == signalRTargetUserId)
                    .Select(
                        x => new
                        {
                            x.CompanyId,
                            x.Id,
                            x.RoleType
                        })
                    .FirstOrDefaultAsync();

                var responseVm = await _conversationService.GetConversationDetails(
                    companyUser.CompanyId,
                    companyUser.Id,
                    companyUser.RoleType,
                    conversationId);

                if (additionalAssigneeData == null)
                    await _hubContext.Clients.Users(signalRTargetUserId).SendAsync(
                        eventName,
                        responseVm);
                else //Additional Assignee Event
                {
                    var additionalAssigneeChanged = new AdditionalAssigneeChanged
                    {
                        ConversationId = conversationId,
                        Staff = additionalAssigneeData,
                        Conversation = responseVm
                    };

                    await _hubContext.Clients.Users(signalRTargetUserId).SendAsync(
                        eventName,
                        additionalAssigneeChanged);
                }
            }
        }

        public async Task SignalROnUserProfileDeleted(
            Conversation conversation,
            UserProfile userProfile,
            Company company,
            UserProfileSource? source = null)
        {
            if (await IsFlowBuilderContactUpdateLowBandwidthCondition(company.Id, source))
            {
                _logger.LogInformation(
                    "[{MethodName}] Skipped due to low bandwidth mode. CompanyId: {CompanyId}, userProfileId : {UserProfile}, userProfileSource: {source}",
                    nameof(SignalROnUserProfileDeleted),
                    userProfile.CompanyId,
                    userProfile.Id,
                    source);

                return;
            }


            if (conversation != null)
            {
                var conversationViewModel = await _conversationNoCompanyResponseViewModelMapper.ToViewModelAsync(conversation);

                await _hubContext.Clients
                    .Group(company.SignalRGroupName)
                    .SendAsync("OnConversationDeleted", conversationViewModel);
            }

            if (userProfile != null)
            {
                var profileViewModel = _mapper.Map<UserProfileNoCompanyResponse>(userProfile);
                profileViewModel.ConversationId = conversation?.Id;

                await _hubContext.Clients
                    .Group(company.SignalRGroupName)
                    .SendAsync("OnUserProfileDeleted", profileViewModel);
            }
        }

        public async Task SignalROnConversationAdded(Conversation conversation)
        {
            var responseVM = await _conversationNoCompanyResponseViewModelMapper.ToViewModelAsync(conversation);

            // Do not change it, will cause EC heakth care can see wrong conversations problem
            var target = await GetSignalRTargetUserIds(
                conversation.CompanyId,
                conversation.Id,
                conversation.AssignedTeamId,
                conversation.AssigneeId);

            await _hubContext.Clients
                .Users(target)
                .SendAsync("OnConversationAdded", responseVM);
        }

        public async Task SignalROnConversationBookmarkStatusChanged(string staffId, Conversation conversation)
        {
            try
            {
                if (conversation != null)
                {
                    await SendOnConversationStatusChanged(
                        conversation.CompanyId,
                        conversation.Id,
                        new List<string>
                        {
                            staffId
                        });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Company {CompanyId} SignalR error for staff {StaffId}. {ExceptionMessage}",
                    nameof(SignalROnConversationBookmarkStatusChanged),
                    conversation?.CompanyId,
                    staffId,
                    ex.Message);
            }
        }

        public async Task SignalROnConversationStatusChanged(
            Conversation conversation,
            UserProfileSource? userProfileSource = null)
        {
            try
            {
                if (await IsFlowBuilderContactUpdateLowBandwidthCondition(conversation.CompanyId, userProfileSource))
                {
                    _logger.LogInformation(
                        "[{MethodName}] FlowBuilder conversation status changed SignalR skipped due to low bandwidth mode. CompanyId: {CompanyId}, Source: {Source}",
                        nameof(SignalROnConversationStatusChanged),
                        conversation.CompanyId,
                        userProfileSource);
                    return;
                }

                await SendConversationStatusUpdateEventAsync(
                    conversation.CompanyId,
                    conversation.Id,
                    conversation.AssignedTeamId,
                    conversation.AssigneeId,
                    "OnConversationStatusChanged");
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "{MethodName} Company {CompanyId} SignalR error for conversation {ConversationId}: {ExceptionMessage}",
                    nameof(SignalROnConversationStatusChanged),
                    conversation?.CompanyId,
                    conversation?.Id,
                    ex.Message);
            }
        }

        private async Task SendConversationStatusUpdateEventAsync(
            string companyId,
            string conversationId,
            long? assignedTeamId,
            long? assignedId,
            string eventName,
            StaffWithoutCompanyResponse additionalAssigneeData = null,
            List<string> targetedUserIds = null)
        {
            var myLock = await _lockService.AcquireLockAsync(
                $"{eventName}:{conversationId}",
                TimeSpan.FromSeconds(1));

            if (myLock == null)
            {
                return;
            }

            if (await IsContactImportLowBandwidthCondition(companyId))
            {
                return;
            }

            // Do not change it, will cause EC heakth care can see wrong conversations problem
            var signalRTargetUserIds = targetedUserIds ?? await GetSignalRTargetUserIds(
                companyId,
                conversationId,
                assignedTeamId,
                assignedId);

            BackgroundJob.Enqueue(
                () => SendPersonalizedConversationDataAsync(
                    companyId,
                    conversationId,
                    signalRTargetUserIds,
                    eventName,
                    additionalAssigneeData));
        }

        public async Task SignalROnConversationUnreadStatusChanged(Conversation conversation, string userId)
        {
            try
            {
                if (conversation != null)
                {
                    await SendOnConversationStatusChanged(
                        conversation.CompanyId,
                        conversation.Id,
                        new List<string>
                        {
                            userId
                        });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "{MethodName} Company {CompanyId} SignalR error for staff {StaffIdentityId}: {ExceptionMessage}",
                    nameof(SignalROnConversationUnreadStatusChanged),
                    conversation?.CompanyId,
                    userId,
                    ex.Message);
            }
        }

        public async Task SignalROnConversationAssigneeDeleted(
            Conversation targetConversation,
            UserProfileSource? userProfileSource = null)
        {
            if (await IsFlowBuilderContactUpdateLowBandwidthCondition(targetConversation.CompanyId, userProfileSource))
            {
                _logger.LogInformation(
                    "[{MethodName}] FlowBuilder conversation assignee deleted SignalR skipped due to low bandwidth mode. CompanyId: {CompanyId}, Source: {Source}",
                    nameof(SignalROnConversationAssigneeDeleted),
                    targetConversation.CompanyId,
                    userProfileSource);
                return;
            }

            var signalRTargetUserIds = await GetAllStaffIds(
                targetConversation.CompanyId);

            if (!signalRTargetUserIds.Any())
            {
                return;
            }

            var myLock = await _lockService.AcquireLockAsync(
                $"OnConversationAssigneeDeleted:{targetConversation.Id}",
                TimeSpan.FromSeconds(1));

            if (myLock == null)
            {
                return;
            }

            BackgroundJob.Enqueue(
                () => SendStandardizedConversationDataAsync(
                    targetConversation.CompanyId,
                    targetConversation.Id,
                    signalRTargetUserIds,
                    "OnConversationAssigneeDeleted"));
        }

        public async Task SignalROnConversationAssigneeDeleted(
            Conversation targetConversation,
            List<string> listener,
            UserProfileSource? userProfileSource = null)
        {
            if (await IsFlowBuilderContactUpdateLowBandwidthCondition(targetConversation.CompanyId, userProfileSource))
            {
                _logger.LogInformation(
                    "[{MethodName}] FlowBuilder conversation assignee deleted SignalR skipped due to low bandwidth mode. CompanyId: {CompanyId}, Source: {Source}",
                    nameof(SignalROnConversationAssigneeDeleted),
                    targetConversation.CompanyId,
                    userProfileSource);
                return;
            }

            if (!listener.Any())
                return;

            var myLock = await _lockService.AcquireLockAsync(
                $"OnConversationAssigneeDeleted:{targetConversation.Id}",
                TimeSpan.FromSeconds(1));

            if (myLock == null)
                return;

            BackgroundJob.Enqueue(
                () => SendStandardizedConversationDataAsync(
                    targetConversation.CompanyId,
                    targetConversation.Id,
                    listener,
                    "OnConversationAssigneeDeleted"));
        }

        public async Task SignalROnConversationAssignTeamChanged(
            Conversation targetConversation,
            UserProfileSource? userProfileSource = null)
        {
            if (await IsFlowBuilderContactUpdateLowBandwidthCondition(targetConversation.CompanyId, userProfileSource))
            {
                _logger.LogInformation(
                    "[{MethodName}] FlowBuilder conversation assign team changed SignalR skipped due to low bandwidth mode. CompanyId: {CompanyId}, Source: {Source}",
                    nameof(SignalROnConversationAssignTeamChanged),
                    targetConversation.CompanyId,
                    userProfileSource);
                return;
            }

            var signalRTargetUserIds = await GetAllStaffIds(
                targetConversation.CompanyId);

            if (!signalRTargetUserIds.Any())
            {
                return;
            }

            var myLock = await _lockService.AcquireLockAsync(
                $"OnConversationAssignTeamChanged:{targetConversation.Id}",
                TimeSpan.FromSeconds(1));

            if (myLock == null)
            {
                return;
            }

            BackgroundJob.Enqueue(
                () => SendStandardizedConversationDataAsync(
                    targetConversation.CompanyId,
                    targetConversation.Id,
                    signalRTargetUserIds,
                    "OnConversationAssignTeamChanged"));
        }

        public async Task SignalROnConversationAdditionalAssigneeChanged(
            string conversationId,
            Staff staff,
            AdditionalAssigneeEvent additionalAssignee,
            UserProfileSource? userProfileSource = null)
        {
            var targetConversation = await _appDbContext.Conversations
                .AsNoTracking()
                .Where(x => x.CompanyId == staff.CompanyId)
                .Where(convo => convo.Id == conversationId)
                .FirstOrDefaultAsync();

            if (await IsFlowBuilderContactUpdateLowBandwidthCondition(targetConversation.CompanyId, userProfileSource))
            {
                _logger.LogInformation(
                    "[{MethodName}] FlowBuilder conversation additional assignee changed SignalR skipped due to low bandwidth mode. CompanyId: {CompanyId}, Source: {Source}",
                    nameof(SignalROnConversationAdditionalAssigneeChanged),
                    targetConversation.CompanyId,
                    userProfileSource);
                return;
            }

            var targetStaffIds = await GetAllStaffIds(targetConversation.CompanyId);

            string eventName;
            switch (additionalAssignee)
            {
                case AdditionalAssigneeEvent.Added:
                    eventName = "OnConversationAdditionalAssigneeAdded";
                    break;
                case AdditionalAssigneeEvent.Removed:
                    eventName = "OnConversationAdditionalAssigneeDeleted";
                    break;
                case AdditionalAssigneeEvent.Exceeded:
                    eventName = "OnConversationAdditionalAssigneeExceeded";
                    break;
                default:
                    return;
            }

            await SendConversationStatusUpdateEventAsync(
                targetConversation.CompanyId,
                targetConversation.Id,
                targetConversation.AssignedTeamId,
                targetConversation.AssigneeId,
                eventName,
                _mapper.Map<StaffWithoutCompanyResponse>(staff),
                targetedUserIds: targetStaffIds);

            if (eventName == "OnConversationAdditionalAssigneeAdded")
            {
                await SendAdditionalAssigneeChangedPushNotification(conversationId, staff.IdentityId);
            }
        }

        public async Task SignalROnConversationAssigneeChanged(
            Conversation targetConversation,
            bool pushNotification = true,
            UserProfileSource? userProfileSource = null)
        {
            // Remove this will not able to trigger OnConversationAssigneeChanged
            // if (targetConversation.UpdatedTime < DateTime.UtcNow.AddMonths(-1))
            // {
            //     return;
            // }

            if (await IsFlowBuilderContactUpdateLowBandwidthCondition(targetConversation.CompanyId, userProfileSource))
            {
                _logger.LogInformation(
                    "[{MethodName}] FlowBuilder conversation assignee changed SignalR skipped due to low bandwidth mode. CompanyId: {CompanyId}, Source: {Source}",
                    nameof(SignalROnConversationAssigneeChanged),
                    targetConversation.CompanyId,
                    userProfileSource);
                return;
            }

            var targetedUserIds = await GetAllStaffIds(targetConversation.CompanyId);

            await SendConversationStatusUpdateEventAsync(
                targetConversation.CompanyId,
                targetConversation.Id,
                targetConversation.AssignedTeamId,
                targetConversation.AssigneeId,
                "OnConversationAssigneeChanged",
                targetedUserIds: targetedUserIds);

            try
            {
                if (pushNotification)
                {
                    await SendAssigneeChangedPushNotification(targetConversation.Id);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Unable to send assignee changed PushNotification: {ex.Message}");
            }
        }

        public async Task SignalROnBroadcastCampaignSending(CompanyMessageTemplate messageTemplate)
        {
            var responseVM = _mapper.Map<CompanyMessageTemplateResponse>(messageTemplate);

            await _hubContext.Clients
                .Group(responseVM.CompanyId)
                .SendAsync("OnBroadcastCampaignSending", responseVM);
        }

        public async Task SignalROnBroadcastCampaignSent(
            CompanyMessageTemplate messageTemplate,
            List<BroadcastHistory> broadcastHistories)
        {
            var company = await _appDbContext.CompanyCompanies
                .FirstOrDefaultAsync(x => x.Id == messageTemplate.CompanyId);

            var responseVM = _mapper.Map<CompanyMessageTemplateResponse>(messageTemplate);
            responseVM.BroadcastSentCount = broadcastHistories.Count;

            await _hubContext.Clients
                .Group(company.SignalRGroupName)
                .SendAsync("OnBroadcastCampaignSent", responseVM);

            // var staffIds = await _appDbContext.UserRoleStaffs.Where(x => x.CompanyId == messageTemplate.CompanyId).Select(x => x.Id).ToListAsync();
        }

        public async Task<List<string>> GetSignalRDefaultChannelTargetedUserIds(
            Conversation conversation,
            ConversationMessage conversationMessage)
        {
            // If RBAC enabled, use the new method
            var rbacService = _serviceProvider.GetRequiredService<IRbacService>();
            var isRbacEnabled = await rbacService.IsRbacEnabled(conversation.CompanyId);
            if (isRbacEnabled)
            {
                return await _rbacNotificationService
                    .GetConversationTargetedUserIdsByDefaultChannel(conversation, conversationMessage);
            }

            return await DefaultSignalRDefaultChannelTargetedUserIds(conversation);
        }

        public async Task<List<string>> DefaultSignalRDefaultChannelTargetedUserIds(Conversation conversation)
        {
            try
            {
                if (conversation.AssigneeId.HasValue
                    && conversation.Assignee == null)
                {
                    conversation.Assignee = await _appDbContext.UserRoleStaffs
                        .AsNoTracking()
                        .FirstOrDefaultAsync(x => x.Id == conversation.AssigneeId);
                }

                if (conversation.UserProfile == null)
                {
                    conversation.UserProfile = await _appDbContext.UserProfiles
                        .AsNoTracking()
                        .Include(x => x.WhatsAppAccount)
                        .Include(x => x.WhatsApp360DialogUser)
                        .Include(x => x.WhatsappCloudApiUser)
                        .Include(x => x.FacebookAccount)
                        .FirstOrDefaultAsync(x => x.Id == conversation.UserProfileId);
                }

                var tags = new List<string>();

                try
                {
                    conversation.AdditionalAssignees = await _appDbContext.ConversationAdditionalAssignees
                        .AsNoTracking()
                        .Where(X => X.ConversationId == conversation.Id)
                        .Include(x => x.Assignee)
                        .ToListAsync();

                    if (!string.IsNullOrEmpty(conversation.Assignee?.IdentityId)
                        || conversation.AdditionalAssignees?.Count > 0)
                    {
                        var assigneeShowDefaultChannelOnly = false;

                        if (conversation.Assignee != null)
                        {
                            var rolePermission = await _appDbContext.CompanyRolePermissions
                                .AsNoTracking()
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.CompanyId == conversation.CompanyId
                                        && x.StaffUserRole == conversation.Assignee.RoleType);

                            if (rolePermission != null)
                            {
                                assigneeShowDefaultChannelOnly =
                                    rolePermission.Permission.IsShowDefaultChannelMessagesOnly;
                            }
                        }

                        if (conversation.Assignee?.RoleType == StaffUserRole.Admin
                            || !assigneeShowDefaultChannelOnly)
                        {
                            tags.Add(conversation.Assignee?.IdentityId);
                        }
                        else
                        {
                            var teams = await _companyTeamService.GetCompanyteamFromStaffId(
                                conversation.CompanyId,
                                conversation.Assignee?.IdentityId);

                            var teamDefaultChannelId = new List<string>();
                            var teamWhatsapp360dialogDefaultChannelIds = new List<long>();
                            var teamWhatsappCloudDefaultChannelIds = new List<string>();

                            foreach (var team in teams)
                            {
                                if (team.DefaultChannels?.Count > 0)
                                {
                                    foreach (var defaultchannel in team.DefaultChannels
                                                 .Where(
                                                     x =>
                                                         x.channel != ChannelTypes.Whatsapp360Dialog
                                                         && x.channel != ChannelTypes.WhatsappCloudApi))
                                    {
                                        teamDefaultChannelId.AddRange(defaultchannel.ids);
                                    }

                                    foreach (var defaultchannel in team.DefaultChannels
                                                 .Where(x => x.channel == ChannelTypes.Whatsapp360Dialog))
                                    {
                                        foreach (var channelId in defaultchannel.ids)
                                        {
                                            var validLong = long.TryParse(
                                                channelId,
                                                out var whatsapp360dialogChannelId);

                                            if (validLong)
                                            {
                                                teamWhatsapp360dialogDefaultChannelIds.Add(whatsapp360dialogChannelId);
                                            }
                                        }
                                    }

                                    foreach (var defaultChannel in team.DefaultChannels
                                                 .Where(x => x.channel == ChannelTypes.WhatsappCloudApi))
                                    {
                                        foreach (var channelId in defaultChannel.ids)
                                        {
                                            teamWhatsappCloudDefaultChannelIds.Add(channelId);
                                        }
                                    }
                                }
                            }

                            if (teamDefaultChannelId.Count == 0
                                && teamWhatsapp360dialogDefaultChannelIds.Count == 0)
                            {
                                tags.Add(conversation.Assignee?.IdentityId);
                            }
                            else
                            {
                                foreach (var channelId in teamDefaultChannelId)
                                {
                                    var twilioInstance = channelId.Split(";", StringSplitOptions.RemoveEmptyEntries);

                                    if (twilioInstance.Count() > 1)
                                    {
                                        if (twilioInstance[0] == conversation.UserProfile.WhatsAppAccount.InstanceId &&
                                            twilioInstance[1] == conversation.UserProfile.WhatsAppAccount.InstaneSender)
                                        {
                                            tags.Add(conversation.Assignee?.IdentityId);
                                        }
                                    }
                                    else if (teamDefaultChannelId.Contains(
                                                 conversation.UserProfile.WhatsAppAccount?.InstanceId))
                                    {
                                        tags.Add(conversation.Assignee?.IdentityId);
                                    }
                                    else if (teamDefaultChannelId.Contains(
                                                 conversation.UserProfile.FacebookAccount?.pageId))
                                    {
                                        tags.Add(conversation.Assignee?.IdentityId);
                                    }
                                    else if (teamDefaultChannelId.Contains(
                                                 conversation.UserProfile.InstagramUser?.InstagramPageId))
                                    {
                                        tags.Add(conversation.Assignee?.IdentityId);
                                    }
                                    else if
                                        (string.IsNullOrEmpty(conversation.UserProfile.WhatsAppAccount?.InstanceId)
                                         && string.IsNullOrEmpty(
                                             conversation.UserProfile.FacebookAccount
                                                 ?.pageId)) // defaulf channel no supported for others
                                    {
                                        tags.Add(conversation.Assignee?.IdentityId);
                                    }
                                }

                                // default channel for 360dialog
                                if (conversation.UserProfile?.WhatsApp360DialogUser?.ChannelId != null
                                    && teamWhatsapp360dialogDefaultChannelIds.Contains(
                                        conversation.UserProfile.WhatsApp360DialogUser.ChannelId.Value))
                                {
                                    tags.Add(conversation.Assignee?.IdentityId);
                                }
                            }
                        }

                        foreach (var additionalAssignee in conversation.AdditionalAssignees)
                        {
                            var additionalAssigneeShowDefaultChannelOnly = false;

                            if (additionalAssignee.Assignee != null)
                            {
                                var rolePermission = await _appDbContext.CompanyRolePermissions
                                    .AsNoTracking()
                                    .FirstOrDefaultAsync(
                                        x =>
                                            x.CompanyId == conversation.CompanyId
                                            && x.StaffUserRole == additionalAssignee.Assignee.RoleType);

                                if (rolePermission != null)
                                {
                                    additionalAssigneeShowDefaultChannelOnly =
                                        rolePermission.Permission.IsShowDefaultChannelMessagesOnly;
                                }
                            }

                            if (additionalAssignee.Assignee.RoleType == StaffUserRole.Admin
                                || !additionalAssigneeShowDefaultChannelOnly)
                            {
                                tags.Add(additionalAssignee.Assignee?.IdentityId);
                            }
                            else
                            {
                                var teams = await _companyTeamService.GetCompanyteamFromStaffId(
                                    conversation.CompanyId,
                                    additionalAssignee.Assignee.IdentityId);

                                var teamDefaultChannelId = new List<string>();
                                var teamWhatsapp360dialogDefaultChannelIds = new List<long>();
                                var teamWhatsappCloudDefaultChannelIds = new List<string>();

                                foreach (var team in teams)
                                {
                                    if (team.DefaultChannels?.Count > 0
                                        || teamWhatsapp360dialogDefaultChannelIds.Count == 0)
                                    {
                                        foreach (var defaultchannel in team.DefaultChannels
                                                     .Where(
                                                         x =>
                                                             x.channel != ChannelTypes.Whatsapp360Dialog
                                                             && x.channel != ChannelTypes.WhatsappCloudApi))
                                        {
                                            teamDefaultChannelId.AddRange(defaultchannel.ids);
                                        }

                                        foreach (var defaultchannel in team.DefaultChannels
                                                     .Where(x => x.channel == ChannelTypes.Whatsapp360Dialog))
                                        {
                                            foreach (var channelId in defaultchannel.ids)
                                            {
                                                var validLong = long.TryParse(
                                                    channelId,
                                                    out var whatsapp360dialogChannelId);

                                                if (validLong)
                                                {
                                                    teamWhatsapp360dialogDefaultChannelIds.Add(
                                                        whatsapp360dialogChannelId);
                                                }
                                            }
                                        }

                                        foreach (var defaultChannel in team.DefaultChannels
                                                     .Where(x => x.channel == ChannelTypes.WhatsappCloudApi))
                                        {
                                            foreach (var channelId in defaultChannel.ids)
                                            {
                                                teamWhatsappCloudDefaultChannelIds.Add(channelId);
                                            }
                                        }
                                    }
                                }

                                if (teamDefaultChannelId.Count == 0
                                    && teamWhatsapp360dialogDefaultChannelIds.Count == 0
                                    && teamWhatsappCloudDefaultChannelIds.Count == 0)
                                {
                                    tags.Add(additionalAssignee.Assignee?.IdentityId);
                                }
                                else
                                {
                                    foreach (var channelId in teamDefaultChannelId)
                                    {
                                        var twilioInstance = channelId.Split(
                                            ";",
                                            StringSplitOptions.RemoveEmptyEntries);

                                        if (twilioInstance.Count() > 1)
                                        {
                                            if (twilioInstance[0] == conversation.UserProfile.WhatsAppAccount.InstanceId
                                                && twilioInstance[1] == conversation.UserProfile.WhatsAppAccount
                                                    .InstaneSender)
                                            {
                                                tags.Add(additionalAssignee.Assignee?.IdentityId);
                                            }
                                        }
                                        else if (teamDefaultChannelId.Contains(
                                                     conversation.UserProfile.WhatsAppAccount?.InstanceId))
                                        {
                                            tags.Add(additionalAssignee.Assignee?.IdentityId);
                                        }
                                        else if (teamDefaultChannelId.Contains(
                                                     conversation.UserProfile.FacebookAccount?.pageId))
                                        {
                                            tags.Add(additionalAssignee.Assignee?.IdentityId);
                                        }
                                        else if (teamDefaultChannelId.Contains(
                                                     conversation.UserProfile.InstagramUser?.InstagramPageId))
                                        {
                                            tags.Add(additionalAssignee.Assignee?.IdentityId);
                                        }
                                        else if (string.IsNullOrEmpty(
                                                     conversation.UserProfile.WhatsAppAccount?.InstanceId)
                                                 && string.IsNullOrEmpty(
                                                     conversation.UserProfile.FacebookAccount
                                                         ?.pageId)) // defaulf channel no supported for others
                                        {
                                            tags.Add(additionalAssignee.Assignee?.IdentityId);
                                        }
                                    }

                                    // default channel for 360dialog
                                    if (conversation.UserProfile?.WhatsApp360DialogUser?.ChannelId != null
                                        && teamWhatsapp360dialogDefaultChannelIds.Contains(
                                            conversation.UserProfile.WhatsApp360DialogUser.ChannelId.Value))
                                    {
                                        tags.Add(conversation.Assignee?.IdentityId);
                                    }

                                    // default channel for cloud api
                                    if (conversation.UserProfile?.WhatsApp360DialogUser?.ChannelId != null
                                        && teamWhatsappCloudDefaultChannelIds.Contains(
                                            conversation.UserProfile.WhatsappCloudApiUser.WhatsappChannelPhoneNumber))
                                    {
                                        tags.Add(conversation.Assignee?.IdentityId);
                                    }
                                }
                            }
                        }

                        if (conversation.AssignedTeamId.HasValue)
                        {
                            // add all team
                            var conversationTeam = await _appDbContext.CompanyStaffTeams
                                .AsNoTracking()
                                .Include(x => x.Members)
                                .ThenInclude(x => x.Staff)
                                .FirstOrDefaultAsync(x => x.Id == conversation.AssignedTeamId);

                            var teamDefaultChannelId = new List<string>();
                            var teamWhatsapp360dialogDefaultChannelIds = new List<long>();
                            var teamWhatsappCloudDefaultChannelIds = new List<string>();

                            if (conversationTeam.DefaultChannels?.Count > 0)
                            {
                                foreach (var defaultchannel in conversationTeam.DefaultChannels
                                             .Where(
                                                 x =>
                                                     x.channel != ChannelTypes.Whatsapp360Dialog
                                                     && x.channel != ChannelTypes.WhatsappCloudApi))
                                {
                                    teamDefaultChannelId.AddRange(defaultchannel.ids);
                                }

                                foreach (var defaultchannel in conversationTeam.DefaultChannels
                                             .Where(x => x.channel == ChannelTypes.Whatsapp360Dialog))
                                {
                                    foreach (var channelId in defaultchannel.ids)
                                    {
                                        var validLong = long.TryParse(channelId, out var whatsapp360dialogChannelId);

                                        if (validLong)
                                        {
                                            teamWhatsapp360dialogDefaultChannelIds.Add(whatsapp360dialogChannelId);
                                        }
                                    }
                                }

                                foreach (var defaultChannel in conversationTeam.DefaultChannels
                                             .Where(x => x.channel == ChannelTypes.WhatsappCloudApi))
                                {
                                    foreach (var channelId in defaultChannel.ids)
                                    {
                                        teamWhatsappCloudDefaultChannelIds.Add(channelId);
                                    }
                                }
                            }
                            else
                            {
                                tags.AddRange(
                                    conversationTeam.Members
                                        .Where(x => x.Staff.RoleType == StaffUserRole.TeamAdmin)
                                        .Select(x => x.Staff.IdentityId));
                            }

                            foreach (var channelId in teamDefaultChannelId)
                            {
                                var twilioInstance = channelId
                                    .Split(";", StringSplitOptions.RemoveEmptyEntries);

                                if (twilioInstance.Count() > 1)
                                {
                                    if (twilioInstance[0] == conversation.UserProfile.WhatsAppAccount.InstanceId
                                        && twilioInstance[1] == conversation.UserProfile.WhatsAppAccount.InstaneSender)
                                    {
                                        tags.AddRange(
                                            conversationTeam.Members
                                                .Where(x => x.Staff.RoleType == StaffUserRole.TeamAdmin)
                                                .Select(x => x.Staff.IdentityId));
                                    }
                                }
                                else if (teamDefaultChannelId.Contains(
                                             conversation.UserProfile.WhatsAppAccount?.InstanceId))
                                {
                                    tags.AddRange(
                                        conversationTeam.Members
                                            .Where(x => x.Staff.RoleType == StaffUserRole.TeamAdmin)
                                            .Select(x => x.Staff.IdentityId));
                                }
                                else if (teamDefaultChannelId.Contains(
                                             conversation.UserProfile.FacebookAccount?.pageId))
                                {
                                    tags.AddRange(
                                        conversationTeam.Members
                                            .Where(x => x.Staff.RoleType == StaffUserRole.TeamAdmin)
                                            .Select(x => x.Staff.IdentityId));
                                }
                                else if (teamDefaultChannelId.Contains(
                                             conversation.UserProfile.InstagramUser?.InstagramPageId))
                                {
                                    tags.AddRange(
                                        conversationTeam.Members
                                            .Where(x => x.Staff.RoleType == StaffUserRole.TeamAdmin)
                                            .Select(x => x.Staff.IdentityId));
                                }
                                else if (string.IsNullOrEmpty(conversation.UserProfile.WhatsAppAccount?.InstanceId)
                                         && string.IsNullOrEmpty(
                                             conversation.UserProfile.FacebookAccount
                                                 ?.pageId)) // defaulf channel no supported for others
                                {
                                    tags.AddRange(
                                        conversationTeam.Members
                                            .Where(x => x.Staff.RoleType == StaffUserRole.TeamAdmin)
                                            .Select(x => x.Staff.IdentityId));
                                }
                            }

                            // default channel for 360dialog
                            if (conversation.UserProfile?.WhatsApp360DialogUser?.ChannelId != null
                                && teamWhatsapp360dialogDefaultChannelIds.Contains(
                                    conversation.UserProfile.WhatsApp360DialogUser.ChannelId.Value))
                            {
                                tags.AddRange(
                                    conversationTeam.Members
                                        .Where(x => x.Staff.RoleType == StaffUserRole.TeamAdmin)
                                        .Select(x => x.Staff.IdentityId));
                            }

                            // default channel for cloud api
                            if (conversation.UserProfile?.WhatsappCloudApiUser?.WhatsappChannelPhoneNumber != null
                                && teamWhatsappCloudDefaultChannelIds.Contains(
                                    conversation.UserProfile.WhatsappCloudApiUser.WhatsappChannelPhoneNumber))
                            {
                                tags.AddRange(
                                    conversationTeam.Members
                                        .Where(x => x.Staff.RoleType == StaffUserRole.TeamAdmin)
                                        .Select(x => x.Staff.IdentityId));
                            }
                        }
                    }
                    else
                    {
                        // if (!company.PushGroupAssignedConversationsNotifications)
                        //    return;
                        var adminUnassingedMessage = true;
                        var teamAdminUnassignedMessage = true;
                        var staffUnassignedMessage = true;

                        var rolePermissions = await _appDbContext.CompanyRolePermissions
                            .AsNoTracking()
                            .Where(x => x.CompanyId == conversation.CompanyId)
                            .ToListAsync();

                        var adminPermission = rolePermissions
                            .FirstOrDefault(x => x.StaffUserRole == StaffUserRole.Admin);

                        if (adminPermission != null)
                        {
                            adminUnassingedMessage = adminPermission.Permission.ReceiveUnassignedNotifications;
                        }

                        var teamadminPermission = rolePermissions
                            .FirstOrDefault(x => x.StaffUserRole == StaffUserRole.TeamAdmin);

                        if (teamadminPermission != null)
                        {
                            teamAdminUnassignedMessage = teamadminPermission.Permission.ReceiveUnassignedNotifications;
                        }

                        var staffPermission = rolePermissions
                            .FirstOrDefault(x => x.StaffUserRole == StaffUserRole.Staff);

                        if (staffPermission != null)
                        {
                            staffUnassignedMessage = staffPermission.Permission.ReceiveUnassignedNotifications;
                        }

                        if (conversation.AssignedTeamId.HasValue)
                        {
                            var teamMembers = await _appDbContext.CompanyTeamMembers
                                .AsNoTracking()
                                .Where(x => x.CompanyTeamId == conversation.AssignedTeamId)
                                .Include(x => x.Staff)
                                .ToListAsync();

                            foreach (var teamMember in teamMembers)
                            {
                                switch (teamMember.Staff.RoleType)
                                {
                                    case StaffUserRole.Admin:
                                        if (adminUnassingedMessage)
                                        {
                                            tags.Add(teamMember.Staff.IdentityId);
                                        }

                                        break;
                                    case StaffUserRole.TeamAdmin:
                                        if (teamAdminUnassignedMessage)
                                        {
                                            tags.Add(teamMember.Staff.IdentityId);
                                        }

                                        break;
                                    case StaffUserRole.Staff:
                                        if (staffUnassignedMessage)
                                        {
                                            tags.Add(teamMember.Staff.IdentityId);
                                        }

                                        break;
                                }
                            }
                        }
                        else
                        {
                            var allStaff = await _appDbContext.UserRoleStaffs
                                .AsNoTracking()
                                .Where(x => x.CompanyId == conversation.CompanyId)
                                .Select(
                                    x => new
                                    {
                                        x.RoleType,
                                        x.IdentityId
                                    })
                                .ToListAsync();

                            foreach (var staff in allStaff)
                            {
                                switch (staff.RoleType)
                                {
                                    case StaffUserRole.Admin:
                                        if (adminUnassingedMessage)
                                        {
                                            tags.Add(staff.IdentityId);
                                        }

                                        break;
                                    case StaffUserRole.TeamAdmin:
                                        if (teamAdminUnassignedMessage)
                                        {
                                            tags.Add(staff.IdentityId);
                                        }

                                        break;
                                    case StaffUserRole.Staff:
                                        if (staffUnassignedMessage)
                                        {
                                            tags.Add(staff.IdentityId);
                                        }

                                        break;
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Company {CompanyId} SignalR add tags error for conversation {ConversationId}: {ExceptionString}",
                        nameof(GetSignalRDefaultChannelTargetedUserIds),
                        conversation?.CompanyId,
                        conversation?.Id,
                        ex.ToString());
                }

                tags = tags.Distinct().ToList();

                var targetId = await _appDbContext.UserRoleStaffs
                    .Where(
                        x =>
                            x.CompanyId == conversation.CompanyId
                            && x.RoleType == StaffUserRole.Admin)
                    .Select(x => x.IdentityId)
                    .ToListAsync();

                tags.AddRange(targetId);
                tags = tags.Distinct().ToList();

                return tags;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Company {CompanyId} SignalR error for conversation {ConversationId}: {ExceptionString}",
                    nameof(GetSignalRDefaultChannelTargetedUserIds),
                    conversation.CompanyId,
                    conversation.Id,
                    ex.ToString());
            }

            return null;
        }

        public async Task<List<string>> GetSignalRTargetUserIds(
            string companyId,
            string conversationId,
            long? conversationAssignedTeamId,
            long? conversationAssigneeId,
            bool isUnassignedConversations = false,
            bool isGetActiveUserOnly = true)
        {
            // If RBAC enabled, use the new method
            var rbacService = _serviceProvider.GetRequiredService<IRbacService>();
            var isRbacEnabled = await rbacService.IsRbacEnabled(companyId);
            if (isRbacEnabled)
            {
                return await _rbacNotificationService
                     .GetConversationTargetedUserIds(companyId, conversationId, isGetActiveUserOnly);
            }

            return await DefaultGetSignalRTargetUserIds(companyId, conversationId, conversationAssignedTeamId, conversationAssigneeId, isUnassignedConversations, isGetActiveUserOnly);
        }

        public async Task<List<string>> DefaultGetSignalRTargetUserIds(
            string companyId,
            string conversationId,
            long? conversationAssignedTeamId,
            long? conversationAssigneeId,
            bool isUnassignedConversations,
            bool isGetActiveUserOnly)
        {
            var dbContext = _dbContextService.GetDbContext();
            var userIds = await dbContext.UserRoleStaffs
                .Where(
                    x =>
                        x.CompanyId == companyId
                        && x.RoleType == StaffUserRole.Admin)
                .Select(x => x.IdentityId)
                .ToListAsync();
            if (conversationAssignedTeamId.HasValue)
            {
                var teamAdminIds = await dbContext.CompanyTeamMembers
                    .Where(
                        x =>
                            x.CompanyTeamId == conversationAssignedTeamId
                            && x.Staff.RoleType == StaffUserRole.TeamAdmin)
                    .Select(x => x.Staff.IdentityId)
                    .ToListAsync();
                userIds.AddRange(teamAdminIds);
            }

            var additionalAssigneeIds = await dbContext.ConversationAdditionalAssignees
                .Where(x => x.ConversationId == conversationId)
                .Select(x => x.Assignee.IdentityId)
                .ToListAsync();

            userIds.AddRange(additionalAssigneeIds);

            if (conversationAssigneeId.HasValue)
            {
                var assigneeId = await dbContext.UserRoleStaffs
                    .Where(x => x.Id == conversationAssigneeId)
                    .Select(x => x.IdentityId)
                    .ToListAsync();

                userIds.AddRange(assigneeId);
            }
            else if (conversationAssignedTeamId.HasValue)
            {
                var allMemberIds = await dbContext.CompanyTeamMembers
                    .Where(x => x.CompanyTeamId == conversationAssignedTeamId)
                    .Select(x => x.Staff.IdentityId)
                    .ToListAsync();

                userIds.AddRange(allMemberIds);
            }
            else
            {
                // Unassigned
                var allAdmin = await dbContext.UserRoleStaffs
                    .Where(
                        x =>
                            x.CompanyId == companyId &&
                            x.RoleType == StaffUserRole.Admin)
                    .Select(x => x.IdentityId)
                    .ToListAsync();
                userIds.AddRange(allAdmin);

                if (isUnassignedConversations)
                {
                    var allStaff = await dbContext.UserRoleStaffs
                        .Where(x => x.CompanyId == companyId)
                        .ToListAsync();

                    foreach (var staff in allStaff)
                    {
                        switch (staff.RoleType)
                        {
                            case StaffUserRole.TeamAdmin:
                                userIds.Add(staff.IdentityId);

                                break;
                            case StaffUserRole.Staff:
                                userIds.Add(staff.IdentityId);

                                break;
                        }
                    }
                }
            }

            userIds = userIds.Distinct().ToList();

            var activeUserIds = userIds;
            if (isGetActiveUserOnly)
            {
                // Return active user only (login within 1 day)
                activeUserIds =
                    await dbContext.Users
                        .Where(x => userIds.Contains(x.Id))
                        .Where(
                            x =>
                                x.LastLoginAt > DateTime.UtcNow.AddDays(-1))
                        .Select(x => x.Id)
                        .ToListAsync();
            }

            return activeUserIds;
        }

        public async Task<List<string>> GetAllStaffIds(string companyId)
        {
            var dbContext = _dbContextService.GetDbContext();
            return await dbContext.UserRoleStaffs
                .AsNoTracking()
                .Where(x => x.CompanyId == companyId)
                .Select(x => x.IdentityId)
                .ToListAsync();
        }

        public async Task SignalROnConversationNoteReceived(Conversation conversation, ConversationMessage note)
        {
            var noteVM = _mapper.Map<ConversationMessageResponseViewModel>(note);

            // Do not change it, will cause EC heakth care can see wrong conversations problem
            var target = await GetSignalRTargetUserIds(
                conversation.CompanyId,
                conversation.Id,
                conversation.AssignedTeamId,
                conversation.AssigneeId);

            noteVM.Metadata.Add("conversationLastMessageChannel", conversation.LastMessageChannel);
            noteVM.Metadata.Add("conversationLastChannelIdentityId", conversation.LastChannelIdentityId);

            await _hubContext.Clients
                .Users(target)
                .SendAsync("OnConversationNoteReceived", noteVM);

            try
            {
                await SendConversationNotePushNotification(conversation, note);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Company {CompanyId} error sending conversation note push notification for conversation {ConversationId}: {ExceptionMessage}",
                    nameof(SignalROnConversationNoteReceived),
                    conversation.CompanyId,
                    conversation.Id,
                    ex.Message);
            }
        }

        public async Task SignalROnRemarksReceived(RemarkResponse remarkResponse)
        {
            var companyId = await _appDbContext.UserProfiles
                .Where(x => x.Id == remarkResponse.UserProfileId)
                .Select(x => x.CompanyId)
                .FirstOrDefaultAsync();

            await _hubContext.Clients
                .Group(companyId)
                .SendAsync("OnRemarksReceived", remarkResponse);
        }

        public async Task SignalROnRemarkUpdated(UpdateRemarkResponse updateRemarkResponse, string companyId)
        {
            await _hubContext.Clients
                .Group(companyId)
                .SendAsync("OnRemarkUpdated", updateRemarkResponse);
        }

        public async Task SignalROnRemarkDeleted(DeleteRemarkResponse deleteRemarkResponse, string companyId)
        {
            await _hubContext.Clients
                .Group(companyId)
                .SendAsync("OnRemarkDeleted", deleteRemarkResponse);
        }

        public async Task SignalROnMessageStatusChanged(ConversationMessage message)
        {
            if (await IsBroadcastLowBandwidthCondition(message.CompanyId, message.DeliveryType))
            {
                return;
            }

            var conversationMessagesVM = _mapper.Map<ConversationMessageResponseViewModel>(message);

            _logger.LogInformation(
                "SignalR [{MethodName}] PII pattern matching started for company {CompanyId}",
                nameof(SignalROnMessageStatusChanged),
                message.CompanyId);

            await MaskConversationMessageResponseViewModelAsync(conversationMessagesVM);

            _logger.LogInformation(
                "SignalR [{MethodName}] PII pattern matching ended for company {CompanyId}",
                nameof(SignalROnMessageStatusChanged),
                message.CompanyId);

            await _sleekPayService.AddSleekPayRecord(conversationMessagesVM);

            var conversation = await _appDbContext.Conversations
                .AsNoTracking()
                .FirstOrDefaultAsync(x => x.Id == message.ConversationId);

            // Do not change it, will cause EC heakth care can see wrong conversations problem
            var target = await GetSignalRDefaultChannelTargetedUserIds(
                conversation,
                message);

            if (message.SenderId != null)
            {
                target.Add(message.SenderId);
            }

            target = target.Distinct().ToList();

            await _applicationInsightsTelemetryTracer.InvokeAndTraceDependencyCallAsync(
                TraceDependencyTypes.SignalR,
                "OnMessageStatusChanged",
                nameof(SignalRService),
                null,
                async () =>
                {
                    await _hubContext.Clients
                        .Users(target)
                        .SendAsync("OnMessageStatusChanged", conversationMessagesVM);
                },
                new Dictionary<string, string>
                {
                    {
                        "channel_type", conversationMessagesVM.Channel
                    },
                    {
                        "direction", conversationMessagesVM.IsSentFromSleekflow ? "sent" : "received"
                    },
                    {
                        "message_type", conversationMessagesVM.MessageType
                    },
                });

            await _messagesApi.MessagesSendMessageToUsersPostAsync(
                sendMessageToUsersInput: new SendMessageToUsersInput(
                    target,
                    "Classic.OnMessageStatusChanged",
                    conversationMessagesVM));

            _logger.LogInformation(
                "SignalR [{MethodName}] Completed {CompanyId} {Message}",
                nameof(SignalROnMessageStatusChanged),
                message.CompanyId,
                JsonConvert.SerializeObject(conversationMessagesVM));
        }

        public async Task SignalForceLogout(string deviceUUID, ActiveSessionResponse activeSessionResponse)
        {
            await _hubContext.Clients
                .Group($"{deviceUUID}")
                .SendAsync("ForceLogout", activeSessionResponse);
        }

        public async Task SignalRAutoLogout(string deviceUUID, ActiveSessionResponse activeSessionResponse)
        {
            await _hubContext.Clients
                .Group($"{deviceUUID}")
                .SendAsync("AutoLogout", activeSessionResponse);
        }

        public async Task SignalROnMessageReceived(
            Conversation conversation,
            ConversationMessage message,
            bool isStaffReply = false)
        {
            if (message.UpdatedAt < DateTime.UtcNow.AddDays(-1))
            {
                return;
            }

            // Do not notify
            if (message.DeliveryType is DeliveryType.Broadcast)
            {
                return;
            }

            if (await IsBroadcastLowBandwidthCondition(message.CompanyId, message.DeliveryType))
            {
                return;
            }

            var conversationMessagesVM = _mapper.Map<ConversationMessageResponseViewModel>(message);
            conversationMessagesVM.ConversationId = conversation.Id;

            _logger.LogInformation(
                "SignalR [{MethodName}] PII pattern matching started for company {CompanyId}",
                nameof(SignalROnMessageReceived),
                message.CompanyId);

            await MaskConversationMessageResponseViewModelAsync(conversationMessagesVM);

            _logger.LogInformation(
                "SignalR [{MethodName}] PII pattern matching ended for company {CompanyId}",
                nameof(SignalROnMessageReceived),
                message.CompanyId);

            if (conversation.ActiveStatus == ActiveStatus.Active && message.MessageType != "system")
            {
                await SendNewMessagePushNotification(conversation, message);
            }

            if (message.Channel == "naive")
            {
                if (message.SenderId != "staff" && !isStaffReply)
                {
                    if (!string.IsNullOrEmpty(message.SenderId))
                    {
                        await _hubContext.Clients
                            .User(message.SenderId)
                            .SendAsync("OnMessageReceived", conversationMessagesVM);
                    }
                    else
                    {
                        await _hubContext.Clients
                            .Client(message.SenderDevice?.SignalRConnectionId)
                            .SendAsync("OnMessageReceived", conversationMessagesVM);
                    }
                }

                if (!string.IsNullOrEmpty(message.ReceiverId))
                {
                    await _hubContext.Clients
                        .User(message.ReceiverId)
                        .SendAsync("OnMessageReceived", conversationMessagesVM);
                }
                else
                {
                    await _hubContext.Clients
                        .Client(message.ReceiverDevice?.SignalRConnectionId)
                        .SendAsync("OnMessageReceived", conversationMessagesVM);
                }
            }
            else if (message.Channel == ChannelTypes.LiveChat)
            {
                // Remove sensitive information for LiveChat
                if (conversationMessagesVM.Sender != null)
                {
                    conversationMessagesVM.Sender =
                        SensitiveInformationHelper.RemoveSensitiveInformationFromUserInfo(
                            conversationMessagesVM.Sender);
                }

                if (message.WebClientSender?.WebClientUUID != null)
                {
                    await _hubContext.Clients
                        .Group($"{conversation.CompanyId}_{message.WebClientSender?.WebClientUUID}")
                        .SendAsync("OnMessageReceived", conversationMessagesVM);
                }

                if (message.WebClientReceiver?.WebClientUUID != null)
                {
                    await _hubContext.Clients
                        .Group($"{conversation.CompanyId}_{message.WebClientReceiver?.WebClientUUID}")
                        .SendAsync("OnMessageReceived", conversationMessagesVM);
                }
            }
            // [LiveChatV2]
            else if (message.Channel == ChannelTypes.LiveChatV2)
            {
                if (message.WebClientSender?.WebClientUUID != null)
                {
                    await _hubContext.Clients
                        .Group($"{conversation.CompanyId}_livechat_{message.WebClientSender?.WebClientUUID}_{message.ChannelIdentityId}")
                        .SendAsync("OnMessageReceived", conversationMessagesVM);
                }

                if (message.WebClientReceiver?.WebClientUUID != null)
                {
                    await _hubContext.Clients
                        .Group($"{conversation.CompanyId}_livechat_{message.WebClientReceiver?.WebClientUUID}_{message.ChannelIdentityId}")
                        .SendAsync("OnMessageReceived", conversationMessagesVM);
                }
            }
            else if (message.MessageType == "system")
            {
                await SignalRResendOnMessageReceived(message.SenderId, conversationMessagesVM);
            }
        }

        public async Task SignalRResendOnMessageReceived(
            string identityId,
            ConversationMessageResponseViewModel conversationMessagesVm)
        {
            await _hubContext.Clients
                .User(identityId)
                .SendAsync("OnMessageReceived", conversationMessagesVm);
        }

        public async Task SignalROnChannelAdded(Company company, ChannelSignal channelSignal)
        {
            await _hubContext.Clients
                .Group(company.SignalRGroupName)
                .SendAsync("OnChannelAdded", channelSignal);
        }

        public async Task SignalROnChannelDeleted(Company company, ChannelSignal channelSignal)
        {
            await _hubContext.Clients
                .Group(company.SignalRGroupName)
                .SendAsync("OnChannelDeleted", channelSignal);
        }

        public async Task SignalROnAssignmentRuleAdded(Company company, AssignmentRule assignmentRule)
        {
            var responseVM = _mapper.Map<AssignmentRuleResponse>(assignmentRule);

            await _hubContext.Clients
                .Group(company.SignalRGroupName)
                .SendAsync("OnAssignmentRuleAdded", responseVM);
        }

        public async Task SignalROnAssignmentRuleChanged(Company company, AssignmentRule assignmentRule)
        {
            var responseVM = _mapper.Map<AssignmentRuleResponse>(assignmentRule);

            await _hubContext.Clients
                .Group(company.SignalRGroupName)
                .SendAsync("OnAssignmentRuleChanged", responseVM);
        }

        public async Task SignalROnAssignmentRuleDelete(Company company, List<AssignmentRule> assignmentRules)
        {
            var responseVM = _mapper.Map<List<AssignmentRuleResponse>>(assignmentRules);

            await _hubContext.Clients
                .Group(company.SignalRGroupName)
                .SendAsync("OnAssignmentRuleDelete", responseVM);
        }

        public async Task SignalROnImportFailed(Company company, ImportContactHistory importResult)
        {
            var response = _mapper.Map<ImportContactHistoryResponse>(importResult);

            await _hubContext.Clients
                .Group(company.SignalRGroupName)
                .SendAsync("OnImportFailed", response);
        }

        public async Task SiganlROnUserProfileFieldFormatChanged(
            Company company,
            List<CompanyCustomUserProfileFieldViewModel> userProfileCustomFieldViewModel)
        {
            await _hubContext.Clients
                .Group(company.SignalRGroupName)
                .SendAsync("OnUserProfileFieldFormatChanged", userProfileCustomFieldViewModel);
        }

        private async Task SendConversationNotePushNotification(Conversation conversation, ConversationMessage message)
        {
            var company = await _appDbContext.CompanyCompanies
                .AsNoTracking()
                .FirstOrDefaultAsync(x => x.Id == conversation.CompanyId);

            if (!company.PushAssignedConversationsNotifications)
            {
                return;
            }

            // Add mentioned unread Record
            if (message.MessageAssigneeId.HasValue)
            {
                var basicUnreadRecord = new ConversationUnreadRecord()
                {
                    CompanyId = conversation.CompanyId,
                    ConversationId = conversation.Id,
                    MessageId = message.Id,
                };

                await _appDbContext.ConversationUnreadRecords.AddAsync(
                    GetConversationUnreadRecord(
                        basicUnreadRecord,
                        message.MessageAssignee,
                        NotificationDeliveryType.Mentioned));

                await _appDbContext.SaveChangesAsync();
            }

            // Avoid null on message.MessageAssignee
            if (message.MessageAssigneeId.HasValue && message?.MessageAssignee?.Identity == null)
            {
                message.MessageAssignee = await _appDbContext.UserRoleStaffs
                    .AsNoTracking()
                    .Include(x => x.Identity)
                    .FirstOrDefaultAsync(x => x.CompanyId == message.CompanyId && x.Id == message.MessageAssigneeId);
            }

            // Avoid null on conversation.UserProfile
            if (conversation.UserProfile == null)
            {
                conversation.UserProfile = await _appDbContext.UserProfiles
                    .AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == conversation.UserProfileId);
            }

            // Prepare the notification receivers user ids
            var targetUserIds = new List<string>();

            // Mentioned assignee
            if (message.MessageAssigneeId.HasValue)
            {
                targetUserIds.Add(message.MessageAssignee?.IdentityId);
            }

            // Conversation Assignee
            if (conversation.AssigneeId.HasValue)
            {
                var staffIdentityId = await _appDbContext.UserRoleStaffs
                    .Where(x => x.Id == conversation.AssigneeId)
                    .Select(x => x.IdentityId)
                    .FirstOrDefaultAsync();

                targetUserIds.Add(staffIdentityId);
            }

            // Collaborators
            var additionalAssigneeIdentityIds = await _appDbContext.ConversationAdditionalAssignees
                .Where(x => x.ConversationId == conversation.Id)
                .Select(x => x.Assignee.IdentityId)
                .ToListAsync();

            if (additionalAssigneeIdentityIds.Count > 0)
            {
                targetUserIds.AddRange(additionalAssigneeIdentityIds);
            }

            // Mentioned assignee in last 2 days
            var mentionedStaffQueryable =
                _mentionQueryableResolver.GetStaffQueryable(
                    _appDbContext,
                    conversationId: conversation.Id,
                    companyId: conversation.CompanyId);

            var mentionedAssigneeIdentityIds = await mentionedStaffQueryable
                .Select(staff => staff.IdentityId)
                .ToListAsync();

            if (mentionedAssigneeIdentityIds.Count > 0)
            {
                targetUserIds.AddRange(mentionedAssigneeIdentityIds);
            }

            targetUserIds = targetUserIds.Distinct().ToList();

            // Remove send to sender themself
            targetUserIds.RemoveAll(id => id == message.SenderId);


            // Mentioned notification
            if (message.MessageAssigneeId.HasValue && message?.MessageAssignee?.Identity != null)
            {
                var notToYouUsers = targetUserIds.Where(x => x != message.MessageAssignee.IdentityId).ToList();

                var notificationTitle = $"{message.Sender.DisplayName} mentioned {message.MessageAssignee.Identity.DisplayName} - {conversation.UserProfile.FirstName} {conversation.UserProfile.LastName}";

                await _hubContext.Clients.Users(notToYouUsers).SendAsync(
                    "PushNotitions",
                    new WebPushNotification
                    {
                        Event = $"Note",
                        Title = notificationTitle,
                        Body = message.MessageContent,
                        ConversationId = conversation.Id,
                        ProfileName = conversation.UserProfile.FirstName,
                        AssigneeId = conversation.Assignee?.IdentityId,
                        CompanyId = conversation.CompanyId,
                        ChannelType = message.Channel,
                        ChannelIdentityId = message.ChannelIdentityId,
                    });

                var distinctNotificationUsers = notToYouUsers.Distinct().ToList();

                foreach (var notificationUser in distinctNotificationUsers)
                {
                    var sendPushNotificationInput = new SendPushNotificationInput(
                        "notification:direct_mention",
                        new List<string>
                        {
                            notificationUser
                        },
                        notificationTitle,
                        message.MessageContent,
                        null,
                        conversation.Id,
                        conversation.CompanyId,
                        notificationUser);

                    var res = await SendPushNotificationToSleekFlowCore(sendPushNotificationInput);
                    if (res)
                    {
                        continue;
                    }

                    await _notificationHubProxy.SendNotification(
                        new Notification
                        {
                            Platform = MobilePlatform.apns,
                            Tags = new[]
                            {
                                notificationUser
                            },
                            Title = notificationTitle,
                            Body = message.MessageContent,
                            ConversationId = conversation.Id,
                            CompanyId = conversation.CompanyId
                        });

                    await _notificationHubProxy.SendNotification(
                        new Notification
                        {
                            Platform = MobilePlatform.fcm,
                            Tags = new[]
                            {
                                notificationUser
                            },
                            Title = notificationTitle,
                            Body = message.MessageContent,
                            ConversationId = conversation.Id,
                            CompanyId = conversation.CompanyId
                        });
                }

                if (targetUserIds.Any(x => x == message.MessageAssignee.IdentityId))
                {
                    var notificationToYouTitle = $"{message.Sender.DisplayName} mentioned you - {conversation.UserProfile.FirstName} {conversation.UserProfile.LastName}";

                    await _hubContext.Clients.User(message.MessageAssignee.IdentityId).SendAsync(
                        "PushNotitions",
                        new WebPushNotification
                        {
                            Event = $"Note",
                            Title = notificationToYouTitle,
                            Body = message.MessageContent,
                            ConversationId = conversation.Id,
                            ProfileName = conversation.UserProfile.FirstName,
                            AssigneeId = conversation.Assignee?.IdentityId,
                            CompanyId = conversation.CompanyId,
                            ChannelType = message.Channel,
                            ChannelIdentityId = message.ChannelIdentityId,
                        });

                    var sendPushNotificationInput = new SendPushNotificationInput(
                        "notification:direct_mention",
                        new List<string>
                        {
                            message.MessageAssignee.IdentityId
                        },
                        notificationTitle,
                        message.MessageContent,
                        null,
                        conversation.Id,
                        conversation.CompanyId,
                        message.MessageAssignee.IdentityId);

                    var res = await SendPushNotificationToSleekFlowCore(sendPushNotificationInput);

                    if (res == false)
                    {
                        await _notificationHubProxy.SendNotification(
                            new Notification
                            {
                                Platform = MobilePlatform.apns,
                                Tags = new[]
                                {
                                    message.MessageAssignee.IdentityId
                                },
                                Title = notificationToYouTitle,
                                Body = message.MessageContent,
                                ConversationId = conversation.Id,
                                CompanyId = conversation.CompanyId
                            });

                        await _notificationHubProxy.SendNotification(
                            new Notification
                            {
                                Platform = MobilePlatform.fcm,
                                Tags = new[]
                                {
                                    message.MessageAssignee.IdentityId
                                },
                                Title = notificationToYouTitle,
                                Body = message.MessageContent,
                                ConversationId = conversation.Id,
                                CompanyId = conversation.CompanyId
                            });
                    }
                }
            }
            else
            {
                // Normal Note notification
                var notificationTitle =
                    $"New internal note - {conversation.UserProfile.FirstName} {conversation.UserProfile.LastName}";

                await _hubContext.Clients.Users(targetUserIds).SendAsync(
                    "PushNotitions",
                    new WebPushNotification
                    {
                        Event = $"Note",
                        Title = notificationTitle,
                        Body = message.MessageContent,
                        ConversationId = conversation.Id,
                        ProfileName = conversation.UserProfile.FirstName,
                        AssigneeId = conversation.Assignee?.IdentityId,
                        CompanyId = conversation.CompanyId,
                        ChannelType = message.Channel,
                        ChannelIdentityId = message.ChannelIdentityId,
                    });

                foreach (var targetUserId in targetUserIds)
                {
                    var sendPushNotificationInput = new SendPushNotificationInput(
                        "notification:internal_note",
                        new List<string>
                        {
                            targetUserId
                        },
                        notificationTitle,
                        message.MessageContent,
                        null,
                        conversation.Id,
                        conversation.CompanyId,
                        targetUserId
                    );

                    var res = await SendPushNotificationToSleekFlowCore(sendPushNotificationInput);

                    if (res)
                    {
                        continue;
                    }

                    await _notificationHubProxy.SendNotification(
                        new Notification
                        {
                            Platform = MobilePlatform.apns,
                            Tags = new[]
                            {
                                targetUserId
                            },
                            Title = notificationTitle,
                            Body = message.MessageContent,
                            ConversationId = conversation.Id,
                            CompanyId = conversation.CompanyId
                        });

                    await _notificationHubProxy.SendNotification(
                        new Notification
                        {
                            Platform = MobilePlatform.fcm,
                            Tags = new[]
                            {
                                targetUserId
                            },
                            Title = notificationTitle,
                            Body = message.MessageContent,
                            ConversationId = conversation.Id,
                            CompanyId = conversation.CompanyId
                        });
                }
            }
        }

        private async Task SendAssigneeChangedPushNotification(string conversationId, string tags = null)
        {
            var conversation = await _appDbContext.Conversations
                .Where(convo => convo.Id == conversationId)
                .AsNoTracking()
                .Select(
                    x => new
                    {
                        x.Id,
                        x.AssigneeId,
                        x.AssignedTeamId,
                        x.CompanyId,
                        x.LastMessageChannel,
                        x.UserProfile.FirstName,
                        x.UserProfile.LastName,
                        x.Assignee.IdentityId
                    })
                .FirstOrDefaultAsync();

            if (conversation.LastMessageChannel == ChannelTypes.Email)
            {
                return;
            }

            var company = await _appDbContext.CompanyCompanies
                .FirstOrDefaultAsync(x => x.Id == conversation.CompanyId);

            if (!company.PushAssignedConversationsNotifications)
            {
                return;
            }

            if (string.IsNullOrEmpty(tags))
            {
                tags = string.IsNullOrEmpty(conversation.IdentityId)
                    ? conversation.CompanyId
                    : conversation.IdentityId;

                if (string.IsNullOrEmpty(conversation.IdentityId))
                {
                    _logger.LogWarning(
                        "[{MethodName}] New Assignment Notification for conversation {conversationId} will send to all members of the company {companyId}. The message body is '{message}'.",
                        nameof(SendAssigneeChangedPushNotification),
                        conversationId,
                        conversation.CompanyId,
                        $"Conversation from {ConversationHelper.GetChannelName(conversation.LastMessageChannel)} by {conversation.FirstName}");
                }
            }

            if (string.IsNullOrEmpty(tags))
            {
                return;
            }

            var notificationTitle = $"{conversation.FirstName} {conversation.LastName}";
            var notificationBody = "Conversation assigned to you";

            var sendPushNotificationInput = new SendPushNotificationInput(
                "notification:assignee_changed",
                new List<string>
                {
                    tags
                },
                notificationTitle,
                notificationBody,
                null,
                conversation.Id,
                conversation.CompanyId,
                tags);
            var res = await SendPushNotificationToSleekFlowCore(sendPushNotificationInput);

            if (res == false)
            {
                await _notificationHubProxy.SendNotification(
                    new Notification
                    {
                        Platform = MobilePlatform.apns,
                        Tags = new[]
                        {
                            tags
                        },
                        Title = notificationTitle,
                        Body = notificationBody,
                        ConversationId = conversation.Id,
                        CompanyId = conversation.CompanyId
                    });

                await _notificationHubProxy.SendNotification(
                    new Notification
                    {
                        Platform = MobilePlatform.fcm,
                        Tags = new string[]
                        {
                            tags
                        },
                        Title = notificationTitle,
                        Body = notificationBody,
                        ConversationId = conversation.Id,
                        CompanyId = conversation.CompanyId
                    });
            }

            await _hubContext.Clients.Users(tags).SendAsync(
                "PushNotitions",
                new WebPushNotification
                {
                    Event = $"Assignment",
                    Title = notificationTitle,
                    Body = notificationBody,
                    ConversationId = conversation.Id,
                    ProfileName = conversation.FirstName,
                    AssigneeId = conversation.IdentityId,
                    CompanyId = conversation.CompanyId
                });
        }

        private async Task SendAdditionalAssigneeChangedPushNotification(string conversationId, string tags = null)
        {
            var conversation = await _appDbContext.Conversations
                .Where(convo => convo.Id == conversationId)
                .AsNoTracking()
                .Select(
                    x => new
                    {
                        x.Id,
                        x.AssigneeId,
                        x.AssignedTeamId,
                        x.CompanyId,
                        x.LastMessageChannel,
                        x.UserProfile.FirstName,
                        x.UserProfile.LastName
                    })
                .FirstOrDefaultAsync();

            if (conversation.LastMessageChannel == ChannelTypes.Email)
            {
                return;
            }

            var company = await _appDbContext.CompanyCompanies
                .FirstOrDefaultAsync(x => x.Id == conversation.CompanyId);

            if (!company.PushAssignedConversationsNotifications)
            {
                return;
            }

            if (string.IsNullOrEmpty(tags))
            {
                return;
            }

            var staffId = await _appDbContext.UserRoleStaffs
                .Where(
                    x =>
                        x.CompanyId == conversation.CompanyId
                        && x.IdentityId == tags)
                .Select(x => x.Id)
                .FirstOrDefaultAsync();

            var notificationTitle = $"{conversation.FirstName} {conversation.LastName}";
            var notificationBody = "Conversation assigned to you as collaborator";

            var sendPushNotificationInput = new SendPushNotificationInput(
                "notification:collaborator_changed",
                new List<string>
                {
                    tags
                },
                notificationTitle,
                notificationBody,
                null,
                conversation.Id,
                conversation.CompanyId,
                tags);

            var res = await SendPushNotificationToSleekFlowCore(sendPushNotificationInput);

            if (res == false)
            {
                var unReadMessages = await GetUnreadConversationCountAsync(
                    conversation.CompanyId,
                    staffId);

                await _notificationHubProxy.SendNotification(
                    new Notification
                    {
                        Platform = MobilePlatform.apns,
                        Tags = new[]
                        {
                            tags
                        },
                        Title = notificationTitle,
                        Body = notificationBody,
                        Badge = unReadMessages,
                        ConversationId = conversation.Id,
                        CompanyId = conversation.CompanyId
                    });

                await _notificationHubProxy.SendNotification(
                    new Notification
                    {
                        Platform = MobilePlatform.fcm,
                        Tags = new[]
                        {
                            tags
                        },
                        Title = notificationTitle,
                        Body = notificationBody,
                        Badge = unReadMessages,
                        ConversationId = conversation.Id,
                        CompanyId = conversation.CompanyId
                    });
            }

            await _hubContext.Clients.Users(tags).SendAsync(
                "PushNotitions",
                new WebPushNotification
                {
                    Event = $"Assignment",
                    Title = notificationTitle,
                    Body = notificationBody,
                    ConversationId = conversation.Id,
                    ProfileName = conversation.FirstName,
                    AssigneeId = tags,
                    CompanyId = conversation.CompanyId
                });
        }

        public async Task SendTeamAssignedPushNotification(Conversation conversation)
        {
            try
            {
                if (conversation.LastMessageChannel == ChannelTypes.Email)
                {
                    return;
                }

                if (conversation.AssigneeId.HasValue
                    && conversation.Assignee == null)
                {
                    conversation.Assignee = await _appDbContext.UserRoleStaffs
                        .Include(x => x.Identity)
                        .FirstOrDefaultAsync(x => x.Id == conversation.AssigneeId);
                }

                if (conversation.AssigneeId.HasValue)
                {
                    return;
                }

                var company = await _appDbContext.CompanyCompanies
                    .FirstOrDefaultAsync(x => x.Id == conversation.CompanyId);

                var tags = new List<string>();
                try
                {
                    if (!string.IsNullOrEmpty(conversation.Assignee?.IdentityId))
                    {
                        if (!company.PushAssignedConversationsNotifications)
                        {
                            return;
                        }

                        tags.Add(conversation.Assignee?.IdentityId);
                    }
                    else if (conversation.AssignedTeamId.HasValue)
                    {
                        if (!company.PushGroupAssignedConversationsNotifications)
                        {
                            return;
                        }

                        var teamMembers = await _appDbContext.CompanyTeamMembers
                            .Where(x => x.CompanyTeamId == conversation.AssignedTeamId)
                            .Select(x => x.Staff.IdentityId)
                            .ToListAsync();

                        tags.AddRange(teamMembers);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Company {CompanyId} SignalR add tags error for conversation {ConversationId}: {ExceptionString}",
                        nameof(SendTeamAssignedPushNotification),
                        conversation.CompanyId,
                        conversation.Id,
                        ex.ToString());
                }

                if (company.PushUnassignedConversationsNotifications && tags.Count == 0)
                {
                    var teamMembers = await _appDbContext.UserRoleStaffs
                        .Where(
                            x =>
                                x.CompanyId == conversation.CompanyId
                                && x.Status == StaffStatus.Active)
                        .Select(x => x.IdentityId)
                        .ToListAsync();

                    tags.AddRange(teamMembers);
                }
                else if (!company.PushUnassignedConversationsNotifications && tags.Count == 0)
                {
                    return;
                }

                var notificationTitle = $"{conversation.UserProfile.FirstName} {conversation.UserProfile.LastName}";
                var notificationBody = $"Conversation assigned to team {conversation.AssignedTeam.TeamName}";

                await _hubContext.Clients
                    .Users(tags)
                    .SendAsync(
                        "PushNotitions",
                        new WebPushNotification
                        {
                            Event = $"TeamAssignment",
                            Title = notificationTitle,
                            Body = notificationBody,

                            // Badge = unReadMessages,
                            ConversationId = conversation.Id,
                            ProfileName = conversation.UserProfile.FirstName,
                            AssigneeId = conversation.Assignee?.IdentityId,
                            CompanyId = conversation.CompanyId
                        });

                if (!string.IsNullOrEmpty(conversation.Assignee?.IdentityId))
                {
                    // remove assignee
                    tags.RemoveAll(Id => Id == conversation.Assignee.IdentityId);

                    var sendPushNotificationInput = new SendPushNotificationInput(
                        "notification:team_assignment",
                        new List<string>
                        {
                            conversation.Assignee.IdentityId
                        },
                        notificationTitle,
                        notificationBody,
                        null,
                        conversation.Id,
                        conversation.CompanyId,
                        conversation.Assignee.IdentityId);

                    var res = await SendPushNotificationToSleekFlowCore(sendPushNotificationInput);

                    if (res == false)
                    {
                        await _notificationHubProxy.SendNotification(
                            new Notification
                            {
                                Platform = MobilePlatform.apns,
                                Tags = new[]
                                {
                                    conversation.Assignee.IdentityId
                                },
                                Title = notificationTitle,
                                Body = notificationBody,

                                // Badge = unReadMessages,
                                ConversationId = conversation.Id,
                                CompanyId = conversation.CompanyId
                            });

                        await _notificationHubProxy.SendNotification(
                            new Notification
                            {
                                Platform = MobilePlatform.fcm,
                                Tags = new[]
                                {
                                    conversation.Assignee.IdentityId
                                },
                                Title = notificationTitle,
                                Body = notificationBody,
                                // Badge = unReadMessages,
                                ConversationId = conversation.Id,
                                CompanyId = conversation.CompanyId
                            });
                    }
                }

                if (tags.Count > 0)
                {
                    var distinctNotificationUser = tags.Distinct().ToList();
                    foreach (var notificationUser in distinctNotificationUser)
                    {
                        var sendPushNotificationInput = new SendPushNotificationInput(
                            "notification:team_assignment",
                            new List<string>
                            {
                                notificationUser
                            },
                            notificationTitle,
                            notificationBody,
                            null,
                            conversation.Id,
                            conversation.CompanyId,
                            notificationUser);

                        var res = await SendPushNotificationToSleekFlowCore(sendPushNotificationInput);

                        if (res)
                        {
                            continue;
                        }

                        await _notificationHubProxy.SendNotification(
                            new Notification
                            {
                                Platform = MobilePlatform.apns,
                                Tags = new[]
                                {
                                    notificationUser
                                },
                                Title = notificationTitle,
                                Body = notificationBody,

                                // Badge = unReadMessages,
                                ConversationId = conversation.Id,
                                CompanyId = conversation.CompanyId
                            });

                        await _notificationHubProxy.SendNotification(
                            new Notification
                            {
                                Platform = MobilePlatform.fcm,
                                Tags = new[]
                                {
                                    notificationUser
                                },
                                Title = notificationTitle,
                                Body = notificationBody,

                                // Badge = unReadMessages,
                                ConversationId = conversation.Id,
                                CompanyId = conversation.CompanyId
                            });
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Company {CompanyId} SignalR error for conversation {ConversationId}: {ExceptionString}",
                    nameof(SendTeamAssignedPushNotification),
                    conversation.CompanyId,
                    conversation.Id,
                    ex.ToString());
            }
        }

        public async Task SendLiveChatOnlineStatus(WebClientResponse webClientResponse)
        {
            await _hubContext.Clients
                .Group(webClientResponse.WebClientUUID)
                .SendAsync("OnLiveChatOnlineStatusChanged", webClientResponse);
        }

        private bool IsMessageBelongsToTeamDefaultChannel(
            ConversationMessage message,
            List<string> teamWhatsappTwilioDefaultChannelIds,
            List<string> teamWhatsapp360DialogDefaultChannelIds,
            List<string> teamWhatsappCloudDefaultChannelIds,
            List<string> teamFacebookDefaultChannelIds,
            List<string> teamInstagramDefaultChannelIds)
        {
            return message.Channel switch
            {
                ChannelTypes.WhatsappTwilio => teamWhatsappTwilioDefaultChannelIds.Contains(message.ChannelIdentityId),
                ChannelTypes.Whatsapp360Dialog => teamWhatsapp360DialogDefaultChannelIds.Contains(
                    message.ChannelIdentityId),
                ChannelTypes.WhatsappCloudApi => teamWhatsappCloudDefaultChannelIds.Contains(message.ChannelIdentityId),
                ChannelTypes.Facebook => teamFacebookDefaultChannelIds.Contains(message.ChannelIdentityId),
                ChannelTypes.Instagram => teamInstagramDefaultChannelIds.Contains(message.ChannelIdentityId),
                _ => false
            };
        }

        public async Task SendNewMessagePushNotification(Conversation conversation, ConversationMessage message)
        {
            // If RBAC enabled, use the new method
            var rbacService = _serviceProvider.GetRequiredService<IRbacService>();

            var isRbacEnabled = await rbacService.IsRbacEnabled(conversation.CompanyId);
            if (isRbacEnabled)
            {
                var (notifiedUserIds, responseVm) = await _rbacNotificationService.SendNewMessagePushNotification(conversation, message);
                await _messagesApi.MessagesSendMessageToUsersPostAsync(
                    sendMessageToUsersInput: new SendMessageToUsersInput(
                        notifiedUserIds,
                        "Classic.OnMessageReceived",
                        responseVm));
                return;
            }

            await DefaultSendNewMessagePushNotification(conversation, message);
        }

        public async Task DefaultSendNewMessagePushNotification(Conversation conversation, ConversationMessage message)
        {
            try
            {
                var conversationUnreadRecords = new List<ConversationUnreadRecord>();
                var basicUnreadRecord = new ConversationUnreadRecord()
                {
                    CompanyId = conversation.CompanyId,
                    ConversationId = conversation.Id,
                    MessageId = message.Id
                };

                if (message.Channel == ChannelTypes.Email)
                {
                    return;
                }

                if (conversation.AssigneeId.HasValue
                    && conversation.Assignee == null)
                {
                    conversation.Assignee = await _appDbContext.UserRoleStaffs
                        .Include(x => x.Identity)
                        .FirstOrDefaultAsync(x => x.Id == conversation.AssigneeId);
                }

                // Retrive a new message object in case the original one does not have sender
                var messageWithSenders = await _appDbContext.ConversationMessages
                    .FirstOrDefaultAsync(x => x.Id == message.Id);

                var isSentToAllStaff = false;
                try
                {
                    conversation.AdditionalAssignees = await _appDbContext.ConversationAdditionalAssignees
                        .Where(X => X.ConversationId == conversation.Id)
                        .Include(x => x.Assignee)
                        .ToListAsync();

                    if (!string.IsNullOrEmpty(conversation.Assignee?.IdentityId)
                        || conversation.AdditionalAssignees?.Count > 0)
                    {
                        var assigneeShowDefaultChannelOnly = false;

                        if (conversation.Assignee != null)
                        {
                            var rolePermission = await _appDbContext.CompanyRolePermissions
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.CompanyId == conversation.CompanyId
                                        && x.StaffUserRole == conversation.Assignee.RoleType);

                            if (rolePermission != null)
                            {
                                assigneeShowDefaultChannelOnly =
                                    rolePermission.Permission.IsShowDefaultChannelMessagesOnly;
                            }
                        }

                        #region Contact Owner Notification

                        if (conversation.Assignee?.RoleType == StaffUserRole.Admin
                            || !assigneeShowDefaultChannelOnly)
                        {
                            conversationUnreadRecords.Add(
                                GetConversationUnreadRecord(
                                    basicUnreadRecord,
                                    conversation.Assignee,
                                    NotificationDeliveryType.ContactOwner));
                        }
                        else
                        {
                            var teams = await _companyTeamService.GetCompanyteamFromStaffId(
                                conversation.CompanyId,
                                conversation.Assignee?.IdentityId);

                            var (whatsappTwilioDefaultChannelIds, teamWhatsapp360dialogDefaultChannelIds,
                                    teamWhatsappCloudDefaultChannelIds, teamFacebookDefaultChannelIds,
                                    teamInstagramDefaultChannelIds) =
                                await _companyTeamService.GetTeamsDefaultChannelIdentityIdsAsync(teams);

                            if (whatsappTwilioDefaultChannelIds.Count == 0
                                && teamWhatsapp360dialogDefaultChannelIds.Count == 0
                                && teamWhatsappCloudDefaultChannelIds.Count == 0
                                && teamFacebookDefaultChannelIds.Count == 0
                                && teamInstagramDefaultChannelIds.Count == 0)
                            {
                                conversationUnreadRecords.Add(
                                    GetConversationUnreadRecord(
                                        basicUnreadRecord,
                                        conversation.Assignee,
                                        NotificationDeliveryType.ContactOwner));
                            }
                            else if (IsMessageBelongsToTeamDefaultChannel(
                                         messageWithSenders,
                                         whatsappTwilioDefaultChannelIds,
                                         teamWhatsapp360dialogDefaultChannelIds,
                                         teamWhatsappCloudDefaultChannelIds,
                                         teamFacebookDefaultChannelIds,
                                         teamInstagramDefaultChannelIds))
                            {
                                conversationUnreadRecords.Add(
                                    GetConversationUnreadRecord(
                                        basicUnreadRecord,
                                        conversation.Assignee,
                                        NotificationDeliveryType.ContactOwner));
                            }
                        }

                        #endregion

                        #region Collaborator Notifications

                        foreach (var additionalAssignee in conversation.AdditionalAssignees)
                        {
                            if (conversationUnreadRecords
                                .Any(x => x.StaffId == additionalAssignee.AssigneeId))
                            {
                                continue;
                            }

                            var additionalAssigneeShowDefaultChannelOnly = false;
                            if (additionalAssignee.Assignee != null)
                            {
                                var rolePermission = await _appDbContext.CompanyRolePermissions
                                    .FirstOrDefaultAsync(
                                        x =>
                                            x.CompanyId == conversation.CompanyId
                                            && x.StaffUserRole == additionalAssignee.Assignee.RoleType);

                                if (rolePermission != null)
                                {
                                    additionalAssigneeShowDefaultChannelOnly =
                                        rolePermission.Permission.IsShowDefaultChannelMessagesOnly;
                                }
                            }

                            if (additionalAssignee.Assignee.RoleType == StaffUserRole.Admin
                                || !additionalAssigneeShowDefaultChannelOnly)
                            {
                                conversationUnreadRecords.Add(
                                    GetConversationUnreadRecord(
                                        basicUnreadRecord,
                                        additionalAssignee.Assignee,
                                        NotificationDeliveryType.Collaborator));
                            }
                            else
                            {
                                var teams = await _companyTeamService.GetCompanyteamFromStaffId(
                                    conversation.CompanyId,
                                    additionalAssignee.Assignee.IdentityId);

                                var (whatsappTwilioDefaultChannelIds, teamWhatsapp360dialogDefaultChannelIds,
                                        teamWhatsappCloudDefaultChannelIds, teamFacebookDefaultChannelIds,
                                        teamInstagramDefaultChannelIds) =
                                    await _companyTeamService.GetTeamsDefaultChannelIdentityIdsAsync(teams);

                                if (whatsappTwilioDefaultChannelIds.Count == 0
                                    && teamWhatsapp360dialogDefaultChannelIds.Count == 0
                                    && teamWhatsappCloudDefaultChannelIds.Count == 0
                                    && teamFacebookDefaultChannelIds.Count == 0
                                    && teamInstagramDefaultChannelIds.Count == 0)
                                {
                                    conversationUnreadRecords.Add(
                                        GetConversationUnreadRecord(
                                            basicUnreadRecord,
                                            additionalAssignee.Assignee,
                                            NotificationDeliveryType.Collaborator));
                                }
                                else if (IsMessageBelongsToTeamDefaultChannel(
                                             messageWithSenders,
                                             whatsappTwilioDefaultChannelIds,
                                             teamWhatsapp360dialogDefaultChannelIds,
                                             teamWhatsappCloudDefaultChannelIds,
                                             teamFacebookDefaultChannelIds,
                                             teamInstagramDefaultChannelIds))
                                {
                                    conversationUnreadRecords.Add(
                                        GetConversationUnreadRecord(
                                            basicUnreadRecord,
                                            additionalAssignee.Assignee,
                                            NotificationDeliveryType.Collaborator));
                                }
                            }
                        }

                        #endregion

                        #region Collaborator Team Admin Notifications

                        // BugFix: DEVS-9686 - Collaborator Team Admins should receive messages

                        // Find all collaborator team admins
                        foreach (var collaborator in conversation.AdditionalAssignees)
                        {
                            var collaboratorTeams = await _appDbContext.CompanyStaffTeams
                                .Where(
                                    x => x.CompanyId == conversation.CompanyId
                                         && x.Members.Any(y => y.StaffId == collaborator.AssigneeId))
                                .Include(x => x.Members)
                                .ThenInclude(x => x.Staff)
                                .ToListAsync();

                            foreach (var collaboratorTeam in collaboratorTeams)
                            {
                                var whatsappTwilioDefaultChannelIds = new List<string>();
                                var teamWhatsappCloudDefaultChannelIds = new List<string>();
                                var teamWhatsapp360dialogDefaultChannelIds = new List<string>();
                                var teamFacebookDefaultChannelIds = new List<string>();
                                var teamInstagramDefaultChannelIds = new List<string>();

                                if (collaboratorTeam.DefaultChannels.Count > 0)
                                {
                                    (whatsappTwilioDefaultChannelIds, teamWhatsapp360dialogDefaultChannelIds,
                                            teamWhatsappCloudDefaultChannelIds, teamFacebookDefaultChannelIds,
                                            teamInstagramDefaultChannelIds) =
                                        await _companyTeamService.GetTeamsDefaultChannelIdentityIdsAsync(
                                            new List<CompanyTeam>()
                                            {
                                                collaboratorTeam
                                            });
                                }

                                // exclude the existing subscriber
                                var collaboratorTeamAdmins = collaboratorTeam.Members
                                    .Where(x => x.Staff.RoleType == StaffUserRole.TeamAdmin
                                                && !conversationUnreadRecords.Select(y => y.StaffId).Contains(x.StaffId))
                                    .Select(x => x.Staff)
                                    .ToList();

                                if (whatsappTwilioDefaultChannelIds.Count == 0
                                    && teamWhatsapp360dialogDefaultChannelIds.Count == 0
                                    && teamWhatsappCloudDefaultChannelIds.Count == 0
                                    && teamFacebookDefaultChannelIds.Count == 0
                                    && teamInstagramDefaultChannelIds.Count == 0)
                                {
                                    conversationUnreadRecords.AddRange(
                                        collaboratorTeamAdmins.Select(
                                            collaboratorTeamAdmin => GetConversationUnreadRecord(
                                                basicUnreadRecord,
                                                collaboratorTeamAdmin,
                                                NotificationDeliveryType.TeamMemberAsCollaborator)));
                                }
                                else if (IsMessageBelongsToTeamDefaultChannel(
                                             messageWithSenders,
                                             whatsappTwilioDefaultChannelIds,
                                             teamWhatsapp360dialogDefaultChannelIds,
                                             teamWhatsappCloudDefaultChannelIds,
                                             teamFacebookDefaultChannelIds,
                                             teamInstagramDefaultChannelIds))
                                {
                                    conversationUnreadRecords.AddRange(
                                        collaboratorTeamAdmins.Select(
                                            collaboratorTeamAdmin => GetConversationUnreadRecord(
                                                basicUnreadRecord,
                                                collaboratorTeamAdmin,
                                                NotificationDeliveryType.TeamMemberAsCollaborator)));
                                }
                            }
                        }

                        #endregion

                        #region TeamAdmin Notifications

                        if (conversation.AssignedTeamId.HasValue)
                        {
                            // add all team
                            var conversationTeam = await _appDbContext.CompanyStaffTeams
                                .Include(x => x.Members)
                                .ThenInclude(x => x.Staff)
                                .FirstOrDefaultAsync(x => x.Id == conversation.AssignedTeamId);

                            var whatsappTwilioDefaultChannelIds = new List<string>();
                            var teamWhatsappCloudDefaultChannelIds = new List<string>();
                            var teamWhatsapp360dialogDefaultChannelIds = new List<string>();
                            var teamFacebookDefaultChannelIds = new List<string>();
                            var teamInstagramDefaultChannelIds = new List<string>();

                            // Get team admin and exclude the existing subscriber
                            var teamAdminList = conversationTeam.Members
                                .Where(
                                    x =>
                                        x.Staff.RoleType == StaffUserRole.TeamAdmin
                                        && !conversationUnreadRecords
                                            .Select(y => y.StaffId)
                                            .Contains(x.StaffId))
                                .Select(x => x.Staff)
                                .ToList();

                            if (conversationTeam.DefaultChannels?.Count > 0)
                            {
                                (whatsappTwilioDefaultChannelIds, teamWhatsapp360dialogDefaultChannelIds,
                                        teamWhatsappCloudDefaultChannelIds, teamFacebookDefaultChannelIds,
                                        teamInstagramDefaultChannelIds) =
                                    await _companyTeamService.GetTeamsDefaultChannelIdentityIdsAsync(
                                        new List<CompanyTeam>()
                                        {
                                            conversationTeam
                                        });
                            }
                            else if (!conversation.AssigneeId.HasValue)
                            {
                                conversationUnreadRecords.AddRange(
                                    teamAdminList.Select(
                                        teamAdmin => GetConversationUnreadRecord(
                                            basicUnreadRecord,
                                            teamAdmin,
                                            NotificationDeliveryType.TeamAssigned)));
                            }

                            if (IsMessageBelongsToTeamDefaultChannel(
                                    messageWithSenders,
                                    whatsappTwilioDefaultChannelIds,
                                    teamWhatsapp360dialogDefaultChannelIds,
                                    teamWhatsappCloudDefaultChannelIds,
                                    teamFacebookDefaultChannelIds,
                                    teamInstagramDefaultChannelIds))
                            {
                                conversationUnreadRecords.AddRange(
                                    teamAdminList.Select(
                                        teamAdmin => GetConversationUnreadRecord(
                                            basicUnreadRecord,
                                            teamAdmin,
                                            NotificationDeliveryType.TeamAssigned)));
                            }
                        }

                        #endregion
                    }
                    else
                    {
                        #region Unassigned Notifications

                        var adminUnassignedMessage = true;
                        var teamAdminUnassignedMessage = true;
                        var staffUnassignedMessage = true;

                        var rolePermissions = await _appDbContext.CompanyRolePermissions
                            .Where(x => x.CompanyId == conversation.CompanyId)
                            .ToListAsync();

                        var adminPermission = rolePermissions
                            .FirstOrDefault(x => x.StaffUserRole == StaffUserRole.Admin);

                        if (adminPermission != null)
                        {
                            adminUnassignedMessage = adminPermission.Permission.ReceiveUnassignedNotifications;
                        }

                        var teamAdminPermission = rolePermissions
                            .FirstOrDefault(x => x.StaffUserRole == StaffUserRole.TeamAdmin);

                        if (teamAdminPermission != null)
                        {
                            teamAdminUnassignedMessage = teamAdminPermission.Permission.ReceiveUnassignedNotifications;
                        }

                        var staffPermission = rolePermissions
                            .FirstOrDefault(x => x.StaffUserRole == StaffUserRole.Staff);

                        if (staffPermission != null)
                        {
                            staffUnassignedMessage = staffPermission.Permission.ReceiveUnassignedNotifications;
                        }

                        if (conversation.AssignedTeamId.HasValue)
                        {
                            var teamMembers = await _appDbContext.CompanyTeamMembers
                                .Where(x => x.CompanyTeamId == conversation.AssignedTeamId)
                                .Include(x => x.Staff)
                                .ToListAsync();

                            foreach (var teamMember in teamMembers)
                            {
                                // Don't need to add again if exists
                                if (conversationUnreadRecords
                                    .Any(x => x.StaffId == teamMember.StaffId))
                                {
                                    continue;
                                }

                                switch (teamMember.Staff.RoleType)
                                {
                                    case StaffUserRole.Admin:
                                        if (adminUnassignedMessage)
                                        {
                                            conversationUnreadRecords.Add(
                                                GetConversationUnreadRecord(
                                                    basicUnreadRecord,
                                                    teamMember.Staff,
                                                    NotificationDeliveryType.TeamUnassigned));
                                        }

                                        break;
                                    case StaffUserRole.TeamAdmin:
                                        if (teamAdminUnassignedMessage)
                                        {
                                            conversationUnreadRecords.Add(
                                                GetConversationUnreadRecord(
                                                    basicUnreadRecord,
                                                    teamMember.Staff,
                                                    NotificationDeliveryType.TeamUnassigned));
                                        }

                                        break;
                                    case StaffUserRole.Staff:
                                        if (staffUnassignedMessage)
                                        {
                                            conversationUnreadRecords.Add(
                                                GetConversationUnreadRecord(
                                                    basicUnreadRecord,
                                                    teamMember.Staff,
                                                    NotificationDeliveryType.TeamUnassigned));
                                        }

                                        break;
                                }
                            }
                        }
                        else
                        {
                            isSentToAllStaff = true;
                            var allStaff = await _appDbContext.UserRoleStaffs
                                .Where(x => x.CompanyId == conversation.CompanyId)
                                .ToListAsync();

                            foreach (var staff in allStaff)
                            {
                                // Don't need to add again if exists
                                if (conversationUnreadRecords
                                    .Any(x => x.StaffId == staff.Id))
                                {
                                    continue;
                                }

                                switch (staff.RoleType)
                                {
                                    case StaffUserRole.Admin:
                                        if (adminUnassignedMessage)
                                        {
                                            conversationUnreadRecords.Add(
                                                GetConversationUnreadRecord(
                                                    basicUnreadRecord,
                                                    staff,
                                                    NotificationDeliveryType.Unassigned));
                                        }

                                        break;
                                    case StaffUserRole.TeamAdmin:
                                        if (teamAdminUnassignedMessage)
                                        {
                                            conversationUnreadRecords.Add(
                                                GetConversationUnreadRecord(
                                                    basicUnreadRecord,
                                                    staff,
                                                    NotificationDeliveryType.Unassigned));
                                        }

                                        break;
                                    case StaffUserRole.Staff:
                                        if (staffUnassignedMessage)
                                        {
                                            conversationUnreadRecords.Add(
                                                GetConversationUnreadRecord(
                                                    basicUnreadRecord,
                                                    staff,
                                                    NotificationDeliveryType.Unassigned));
                                        }

                                        break;
                                }
                            }
                        }

                        #endregion
                    }

                    #region Mentioned Notifications

                    // Add mention
                    var mentionedStaffQueryable =
                        _mentionQueryableResolver.GetStaffQueryable(
                            _appDbContext,
                            conversation.Id,
                            conversation.CompanyId);

                    var mentionedStaffIds = await mentionedStaffQueryable
                        .Select(x => x.Id)
                        .Distinct()
                        .ToListAsync();

                    foreach (var mentionedStaffId in mentionedStaffIds)
                    {
                        var mentionedStaff = await _appDbContext.UserRoleStaffs
                            .FirstOrDefaultAsync(
                                x =>
                                    x.CompanyId == conversation.CompanyId
                                    && x.Id == mentionedStaffId);

                        var teams = await _companyTeamService.GetCompanyteamFromStaffId(
                            conversation.CompanyId,
                            mentionedStaff.IdentityId);

                        var (whatsappTwilioDefaultChannelIds, teamWhatsapp360dialogDefaultChannelIds,
                                teamWhatsappCloudDefaultChannelIds, teamFacebookDefaultChannelIds,
                                teamInstagramDefaultChannelIds) =
                            await _companyTeamService.GetTeamsDefaultChannelIdentityIdsAsync(teams);
                        if (whatsappTwilioDefaultChannelIds.Count == 0
                            && teamWhatsapp360dialogDefaultChannelIds.Count == 0
                            && teamWhatsappCloudDefaultChannelIds.Count == 0
                            && teamFacebookDefaultChannelIds.Count == 0
                            && teamInstagramDefaultChannelIds.Count == 0)
                        {
                            conversationUnreadRecords.Add(
                                GetConversationUnreadRecord(
                                    basicUnreadRecord,
                                    mentionedStaff,
                                    NotificationDeliveryType.Mentioned));
                        }
                        else if (IsMessageBelongsToTeamDefaultChannel(
                                     messageWithSenders,
                                     whatsappTwilioDefaultChannelIds,
                                     teamWhatsapp360dialogDefaultChannelIds,
                                     teamWhatsappCloudDefaultChannelIds,
                                     teamFacebookDefaultChannelIds,
                                     teamInstagramDefaultChannelIds))
                        {
                            conversationUnreadRecords.Add(
                                GetConversationUnreadRecord(
                                    basicUnreadRecord,
                                    mentionedStaff,
                                    NotificationDeliveryType.Mentioned));
                        }
                    }

                    #endregion
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Company {CompanyId} SignalR add tags error for conversation {ConversationId}: {ExceptionString}",
                        nameof(SendNewMessagePushNotification),
                        conversation.CompanyId,
                        conversation.Id,
                        ex.ToString());
                }

                // add to database
                // avoid adding unread record for unassigned contacts
                if (!message.IsSentFromSleekflow
                    && !conversation.IsNewCreatedConversation
                    && (conversation.AssigneeId.HasValue
                        || conversation.AdditionalAssignees.Any()))
                {
                    // Only store incoming messages
                    await _appDbContext.ConversationUnreadRecords.AddRangeAsync(conversationUnreadRecords);

                    await _appDbContext.SaveChangesAsync();
                }
                else
                {
                    // Add mentioned unread record for unassigned conversations
                    await _appDbContext.ConversationUnreadRecords.AddRangeAsync(
                        conversationUnreadRecords
                            .Where(x => x.NotificationDeliveryType == NotificationDeliveryType.Mentioned));

                    await _appDbContext.SaveChangesAsync();
                }

                var notifiedUserIds = conversationUnreadRecords
                    .Select(x => x.StaffIdentityId)
                    .Distinct()
                    .ToList();

                var messageBody = message.MessageContent;

                // PII Masking for incoming messages.
                if (!message.IsSentFromSleekflow
                    && messageBody is not null
                    && await _piiMaskingService.IsConfiguredAsync(message.CompanyId))
                {
                    _logger.LogInformation(
                        "SignalR [{MethodName}] for message body; PII pattern matching started for company {CompanyId}",
                        nameof(SendNewMessagePushNotification),
                        message.CompanyId);

                    messageBody = await _piiMaskingService.MaskPiiIfEnabledAsync(
                        message.CompanyId,
                        messageBody,
                        MaskingLocations.IncomingMessage,
                        MaskingRoles.Staff);

                    _logger.LogInformation(
                        "SignalR [{MethodName}] for message body; PII pattern matching ended for company {CompanyId}",
                        nameof(SendNewMessagePushNotification),
                        message.CompanyId);
                }

                if (message.UploadedFiles?.Count > 0)
                {
                    if (message.UploadedFiles.FirstOrDefault().MIMEType.Contains("image"))
                    {
                        messageBody = "[Image Message]";
                    }
                    else if (message.UploadedFiles.FirstOrDefault().MIMEType.Contains("audio"))
                    {
                        messageBody = "[Voice Message]";
                    }
                    else if (message.UploadedFiles.FirstOrDefault().MIMEType.Contains("video"))
                    {
                        messageBody = "[Video Message]";
                    }
                    else
                    {
                        messageBody = "[File Message]";
                    }
                }

                if (!conversation.IsNewCreatedConversation
                    && notifiedUserIds.Count > 0
                    && !message.IsSentFromSleekflow
                    && conversation.Status == "open"
                    && message.DeliveryType == DeliveryType.Normal
                    && DateTime.UtcNow.AddHours(-1) < message.UpdatedAt)
                {
                    var pushNotificationTitle = $"{conversation?.UserProfile?.FirstName}: {messageBody}";

                    if (isSentToAllStaff)
                    {
                        var notificationDelay = TimeSpan.FromSeconds(8);

                        // Bug Fix: DEVS-3168 Notification for Unassigned Contact sent before rule assignment
                        // Previously, we added an 8-second delay for notifications of new messages to allow for automation assignment.
                        // However, some companies received notifications before assignments, so we extended the delay for them.
                        // For more details, refer to the RequiresExtraNotificationDelay() method.
                        if (conversation.CompanyId.RequiresExtraNotificationDelay())
                        {
                            notificationDelay = TimeSpan.FromSeconds(45);
                        }

                        BackgroundJob.Schedule<ISignalRService>(
                            x => x.SendNewMessageNotificationOfUnassignedConversationToAllStaff(
                                conversation.Id,
                                message,
                                conversationUnreadRecords,
                                pushNotificationTitle),
                            notificationDelay);
                    }
                    else
                    {
                        BackgroundJob.Enqueue<ISignalRService>(
                            x => x.SendPushNotificationToSubscriber(
                                conversation,
                                message,
                                conversationUnreadRecords,
                                pushNotificationTitle,
                                false));
                    }
                }

                // TECH-1133 Send push notification to both contact owner and collaborators
                else if (message.IsSentFromSleekflow
                         && message.Sender != null
                         && message.DeliveryType == DeliveryType.Normal
                         && conversation.CompanyId is "7dceb5fd-4baa-4c4c-bd66-00b11d7c20c7"
                             or "471a6289-b9b7-43c3-b6ad-395a1992baea")
                {
                    var staffName =
                        message?.Sender?.FirstName +
                        ((!string.IsNullOrEmpty(message?.Sender?.LastName))
                            ? $" {message?.Sender?.LastName}"
                            : string.Empty);

                    var pushNotificationTitle =
                        $"[{staffName}] replied to {conversation?.UserProfile?.FirstName}: {messageBody}";

                    var excludeToMessageSender = conversationUnreadRecords
                        .Where(x => x.StaffIdentityId != message.SenderId)
                        .ToList();

                    if (excludeToMessageSender.Any())
                    {
                        if (isSentToAllStaff)
                        {
                            var notificationDelay = TimeSpan.FromSeconds(8);

                            // Bug Fix: DEVS-3168 Notification for Unassigned Contact sent before rule assignment
                            // Previously, we added an 8-second delay for notifications of new messages to allow for automation assignment.
                            // However, some companies received notifications before assignments, so we extended the delay for them.
                            // For more details, refer to the RequiresExtraNotificationDelay() method.
                            if (conversation.CompanyId.RequiresExtraNotificationDelay())
                            {
                                notificationDelay = TimeSpan.FromSeconds(45);
                            }

                            BackgroundJob.Schedule<ISignalRService>(
                                x => x.SendNewMessageNotificationOfUnassignedConversationToAllStaff(
                                    conversation.Id,
                                    message,
                                    conversationUnreadRecords,
                                    pushNotificationTitle),
                                notificationDelay);
                        }
                        else
                        {
                            BackgroundJob.Enqueue<ISignalRService>(
                                x => x.SendPushNotificationToSubscriber(
                                    conversation,
                                    message,
                                    excludeToMessageSender,
                                    pushNotificationTitle,
                                    false));
                        }
                    }
                }

                // add admin for signalr notification
                // Removed admin notification 10 Jan 2023
                // Reenable admin notification 4 Feb 2023
                // Removed admin notification again 21 Jun 2023
                // Reenable admin notification again 24 Jun 2023
                // if (targetConversation.CompanyId is "c19a7107-ae89-449f-aa57-2097e18b9c88"
                //     or "6dfa7d60-4440-4b3a-aec2-af675d794f62" or "7f5ae26e-e131-4212-bd8f-faa245546f37")
                // {
                var targetId = await _appDbContext.UserRoleStaffs
                    .Where(
                        x =>
                            x.CompanyId == conversation.CompanyId
                            && x.RoleType == StaffUserRole.Admin)
                    .Select(x => x.IdentityId)
                    .ToListAsync();

                notifiedUserIds.AddRange(targetId);

                // }

                // Ronald request add OnMessageReceived to TeamAdmin for "4c543d7e-bb9c-43f0-87b7-cb9fe90870b0"
                if (conversation.CompanyId is
                        "4c543d7e-bb9c-43f0-87b7-cb9fe90870b0"
                        or "820a5a3f-1f6a-4d58-8247-cb8b81655c80"
                    && conversation.AssignedTeamId.HasValue)
                {
                    var teamAdminIds = await _appDbContext.CompanyTeamMembers
                        .Where(
                            x =>
                                x.CompanyTeamId == conversation.AssignedTeamId
                                && x.Staff.RoleType == StaffUserRole.TeamAdmin)
                        .Select(x => x.Staff.IdentityId)
                        .ToListAsync();

                    notifiedUserIds.AddRange(teamAdminIds);
                }

                notifiedUserIds = notifiedUserIds
                    .Distinct()
                    .ToList();

                var conversationMessagesVm = _mapper.Map<ConversationMessageResponseViewModel>(message);

                _logger.LogInformation(
                    "SignalR [{MethodName}] PII pattern matching started for company {CompanyId}",
                    nameof(SendNewMessagePushNotification),
                    message.CompanyId);

                await MaskConversationMessageResponseViewModelAsync(conversationMessagesVm);

                _logger.LogInformation(
                    "SignalR [{MethodName}] PII pattern matching ended for company {CompanyId}",
                    nameof(SendNewMessagePushNotification),
                    message.CompanyId);

                await _sleekPayService.AddSleekPayRecord(conversationMessagesVm);

                await _applicationInsightsTelemetryTracer.InvokeAndTraceDependencyCallAsync(
                    TraceDependencyTypes.SignalR,
                    "OnMessageReceived",
                    nameof(SignalRService),
                    null,
                    async () =>
                    {
                        await _hubContext.Clients
                            .Users(notifiedUserIds)
                            .SendAsync("OnMessageReceived", conversationMessagesVm);
                    },
                    new Dictionary<string, string>
                    {
                        {
                            "channel_type", conversationMessagesVm.Channel
                        },
                        {
                            "direction", conversationMessagesVm.IsSentFromSleekflow ? "sent" : "received"
                        },
                        {
                            "message_type", conversationMessagesVm.MessageType
                        },
                    });

                await _messagesApi.MessagesSendMessageToUsersPostAsync(
                    sendMessageToUsersInput: new SendMessageToUsersInput(
                        notifiedUserIds,
                        "Classic.OnMessageReceived",
                        conversationMessagesVm));
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "SendNewMessagePushNotification Error: {CompanyId} {ConversationId} {MessageId}",
                    conversation.CompanyId,
                    conversation.Id,
                    message.Id);
            }
        }

        public async Task SendNewMessageNotificationOfUnassignedConversationToAllStaff(
            string conversationId,
            ConversationMessage message,
            List<ConversationUnreadRecord> subscribers,
            string messageBody)
        {
            var conversation = await _appDbContext.Conversations
                .AsNoTracking()
                .Include(x => x.AdditionalAssignees)
                .Include(x => x.UserProfile)
                .FirstOrDefaultAsync(x => x.Id == conversationId);

            // Check again is unassigned targetConversation
            if (conversation.AssigneeId.HasValue == false
                && conversation.AssignedTeamId.HasValue == false
                && conversation.AdditionalAssignees.Any() == false)
            {
                BackgroundJob.Enqueue<ISignalRService>(
                    x => x.SendPushNotificationToSubscriber(
                        conversation,
                        message,
                        subscribers,
                        messageBody,
                        true));

                _logger.LogInformation(
                    "Push notification to all staff of message {messageId} at {time}",
                    message.Id,
                    DateTime.UtcNow);
            }
        }

        private ConversationUnreadRecord GetConversationUnreadRecord(
            ConversationUnreadRecord basicUnreadRecord,
            Staff subscriber,
            NotificationDeliveryType notificationDeliveryType)
        {
            return new ConversationUnreadRecord()
            {
                CompanyId = basicUnreadRecord.CompanyId,
                ConversationId = basicUnreadRecord.ConversationId,
                MessageId = basicUnreadRecord.MessageId,
                NotificationDeliveryType = notificationDeliveryType,
                StaffId = subscriber.Id,
                StaffIdentityId = subscriber.IdentityId,
            };
        }

        public async Task SendPushNotificationToSubscriber(
            Conversation conversation,
            ConversationMessage message,
            List<ConversationUnreadRecord> subscribers,
            string messageBody,
            bool isNewConversation = false)
        {
            var distinctSubscribers = subscribers.Distinct().ToList();
            foreach (var subscriber in distinctSubscribers)
            {
                var unReadConversationCountForSubscriber =
                    await GetUnreadConversationCountAsync(conversation.CompanyId, subscriber.StaffId);

                var prefix = string.Empty;

                if (isNewConversation)
                {
                    prefix = "[Unassigned] ";
                }

                if (subscriber.NotificationDeliveryType == NotificationDeliveryType.Mentioned)
                {
                    prefix = "[Mentioned] ";
                }

                var notificationTitle = $"{conversation.UserProfile.FirstName} {conversation.UserProfile.LastName}";

                notificationTitle = await _piiMaskingService.MaskPiiIfEnabledAsync(
                    message.CompanyId,
                    notificationTitle,
                    MaskingLocations.IncomingMessage,
                    MaskingRoles.Staff);

                await _hubContext.Clients
                    .Users(subscriber.StaffIdentityId)
                    .SendAsync(
                        "PushNotitions",
                        new WebPushNotification
                        {
                            Event = $"NewMessage",
                            Title = notificationTitle,
                            Body = messageBody,
                            Badge = unReadConversationCountForSubscriber,
                            ConversationId = conversation.Id,
                            ProfileName = conversation.UserProfile.FirstName,
                            AssigneeId = conversation.Assignee?.IdentityId,
                            CompanyId = conversation.CompanyId,
                            ChannelType = message.Channel,
                            ChannelIdentityId = message.ChannelIdentityId,
                        });

                var sendPushNotificationInput = new SendPushNotificationInput(
                    "notification:new_message",
                    new List<string>
                    {
                        subscriber.StaffIdentityId
                    },
                    notificationTitle,
                    messageBody,
                    unReadConversationCountForSubscriber,
                    conversation.Id,
                    conversation.CompanyId,
                    subscriber.StaffIdentityId);

                var res = await SendPushNotificationToSleekFlowCore(sendPushNotificationInput);

                if (res == false)
                {
                    await _notificationHubProxy.SendNotification(
                        new Notification
                        {
                            Platform = MobilePlatform.apns,
                            Tags = new[]
                            {
                                subscriber.StaffIdentityId
                            },
                            Title = notificationTitle,
                            Body = messageBody,
                            Badge = unReadConversationCountForSubscriber,
                            ConversationId = conversation.Id,
                            CompanyId = conversation.CompanyId
                        });

                    await _notificationHubProxy.SendNotification(
                        new Notification
                        {
                            Platform = MobilePlatform.fcm,
                            Tags = new[]
                            {
                                subscriber.StaffIdentityId
                            },
                            Title = notificationTitle,
                            Body = messageBody,
                            Badge = unReadConversationCountForSubscriber,
                            ConversationId = conversation.Id,
                            CompanyId = conversation.CompanyId
                        });
                }

                _logger.LogInformation(
                    "[PushNotification Sent] [ConversationId:{conversationId}] [MessageUniqueId:{messageUniqueID}] [StaffId:{staffId}], [ApnsResult:{apnsResult}], [FcmResult:{fcmResult}]",
                    conversation.Id,
                    message.MessageUniqueID,
                    subscriber.StaffId,
                    JsonConvert.SerializeObject(res),
                    JsonConvert.SerializeObject(res));
            }
        }

        private async Task<int> GetUnreadConversationCountAsync(string companyId, long staffId)
        {
            // Count open targetConversation only
            var openConversationQ = _appDbContext.Conversations
                .Where(
                    x => x.CompanyId == companyId
                         && x.Status == "open"
                         && x.ActiveStatus == ActiveStatus.Active
                         && (x.AssigneeId == staffId
                             || x.AdditionalAssignees.Any(y => y.CompanyId == companyId && y.AssigneeId == staffId)));

            var unReadConversationCountForSubscriber = await _appDbContext.ConversationUnreadRecords.Where(
                    x =>
                        x.CompanyId == companyId &&
                        x.StaffId == staffId &&
                        x.NotificationDeliveryStatus != NotificationDeliveryStatus.Read &&
                        openConversationQ.Select(y => y.Id).Contains(x.ConversationId))
                .GroupBy(x => x.ConversationId)
                .CountAsync();

            return unReadConversationCountForSubscriber;
        }

        public async Task SendSnoozeCompletedReminder(Conversation conversation)
        {
            try
            {
                if (conversation.AssigneeId.HasValue
                    && conversation.Assignee == null)
                {
                    conversation.Assignee = await _appDbContext.UserRoleStaffs
                        .Include(x => x.Identity)
                        .FirstOrDefaultAsync(x => x.Id == conversation.AssigneeId);
                }

                var company = await _appDbContext.CompanyCompanies
                    .FirstOrDefaultAsync(x => x.Id == conversation.CompanyId);

                var tags = new List<string>();

                try
                {
                    if (!string.IsNullOrEmpty(conversation.Assignee?.IdentityId))
                    {
                        if (!company.PushAssignedConversationsNotifications)
                        {
                            return;
                        }

                        tags.Add(conversation.Assignee?.IdentityId);
                    }
                    else if (conversation.AssignedTeamId.HasValue)
                    {
                        if (!company.PushGroupAssignedConversationsNotifications)
                        {
                            return;
                        }

                        var teamMembers = await _appDbContext.CompanyTeamMembers
                            .Where(x => x.CompanyTeamId == conversation.AssignedTeamId)
                            .Select(x => x.Staff.IdentityId)
                            .ToListAsync();

                        tags.AddRange(teamMembers);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Company {CompanyId} SignalR add tags error for conversation {ConversationId}: {ExceptionString}",
                        nameof(SendSnoozeCompletedReminder),
                        conversation.CompanyId,
                        conversation.Id,
                        ex.ToString());
                }

                if (company.PushUnassignedConversationsNotifications
                    && tags.Count == 0)
                {
                    tags.Add(company.Id);
                }
                else if (!company.PushUnassignedConversationsNotifications
                         && tags.Count == 0)
                {
                    return;
                }

                var message = await _appDbContext.ConversationMessages
                    .OrderByDescending(x => x.Id)
                    .FirstOrDefaultAsync(
                        x =>
                            x.ConversationId == conversation.Id
                            && x.Channel != ChannelTypes.Note);

                var notificationTitle = $"{conversation.UserProfile.FirstName} {conversation.UserProfile.LastName}";
                var notificationBody = "Conversation snooze period ended";

                await _hubContext.Clients
                    .Users(tags)
                    .SendAsync(
                        "PushNotitions",
                        new WebPushNotification
                        {
                            Event = $"SnoozeCompleted",
                            Title = notificationTitle,
                            Body = notificationBody,

                            // Badge = unReadMessages,
                            CompanyId = conversation.CompanyId,
                            ConversationId = conversation.Id,
                            ProfileName = conversation.UserProfile.FirstName,
                            AssigneeId = conversation.Assignee?.IdentityId,
                            ChannelType = message.Channel,
                            ChannelIdentityId = message.ChannelIdentityId,
                        });

                if (!string.IsNullOrEmpty(conversation.Assignee?.IdentityId))
                {
                    // remove assignee
                    tags.RemoveAll(Id => Id == conversation.Assignee.IdentityId);

                    var sendPushNotificationInput = new SendPushNotificationInput(
                        "notification:snooze_completed",
                        new List<string>
                        {
                            conversation.Assignee.IdentityId
                        },
                        notificationTitle,
                        notificationBody,
                        null,
                        conversation.Id,
                        conversation.CompanyId,
                        conversation.Assignee.IdentityId);

                    var res = await SendPushNotificationToSleekFlowCore(sendPushNotificationInput);

                    if (res == false)
                    {
                        await _notificationHubProxy.SendNotification(
                            new Notification
                            {
                                Platform = MobilePlatform.apns,
                                Tags = new[]
                                {
                                    conversation.Assignee.IdentityId
                                },
                                Title = notificationTitle,
                                Body = notificationBody,

                                // Badge = unReadMessages,
                                ConversationId = conversation.Id,
                                CompanyId = conversation.CompanyId
                            });

                        await _notificationHubProxy.SendNotification(
                            new Notification
                            {
                                Platform = MobilePlatform.fcm,
                                Tags = new[]
                                {
                                    conversation.Assignee.IdentityId
                                },
                                Title = notificationTitle,
                                Body = notificationBody,

                                // Badge = unReadMessages,
                                ConversationId = conversation.Id,
                                CompanyId = conversation.CompanyId
                            });
                    }
                }

                if (tags.Count > 0)
                {
                    var distinctNotificationUser = tags.Distinct().ToList();
                    foreach (var notificationUser in distinctNotificationUser)
                    {
                        var sendPushNotificationInput = new SendPushNotificationInput(
                            "notification:snooze_completed",
                            new List<string>
                            {
                                notificationUser
                            },
                            notificationTitle,
                            notificationBody,
                            null,
                            conversation.Id,
                            conversation.CompanyId,
                            notificationUser);

                        var res = await SendPushNotificationToSleekFlowCore(sendPushNotificationInput);

                        if (res)
                        {
                            continue;
                        }

                        await _notificationHubProxy.SendNotification(
                            new Notification
                            {
                                Platform = MobilePlatform.apns,
                                Tags = new[]
                                {
                                    notificationUser
                                },
                                Title = notificationTitle,
                                Body = notificationBody,

                                // Badge = unReadMessages,
                                ConversationId = conversation.Id,
                                CompanyId = conversation.CompanyId
                            });

                        await _notificationHubProxy.SendNotification(
                            new Notification
                            {
                                Platform = MobilePlatform.fcm,
                                Tags = new[]
                                {
                                    notificationUser
                                },
                                Title = notificationTitle,
                                Body = notificationBody,

                                // Badge = unReadMessages,
                                ConversationId = conversation.Id,
                                CompanyId = conversation.CompanyId
                            });
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Company {CompanyId} SignalR error for conversation {ConversationId}: {ExceptionString}",
                    nameof(SendSnoozeCompletedReminder),
                    conversation.CompanyId,
                    conversation.Id,
                    ex.ToString());
            }
        }

        public async Task TestNotification(string userId, string companyId)
        {
            var sendPushNotificationInput = new SendPushNotificationInput(
                "notification:internal_note",
                new List<string>
                {
                    userId
                },
                "Testing",
                "Testing message body",
                99,
                string.Empty,
                companyId,
                userId);

            var res = await SendPushNotificationToSleekFlowCore(sendPushNotificationInput);

            if (res == false)
            {
                await _notificationHubProxy.SendNotification(
                    new Notification
                    {
                        Platform = MobilePlatform.apns,
                        Tags = new[]
                        {
                            userId
                        },
                        Title = $"Testing",
                        Body = $"Testing message body",
                        Badge = 99
                    });

                await _notificationHubProxy.SendNotification(
                    new Notification
                    {
                        Platform = MobilePlatform.fcm,
                        Tags = new[]
                        {
                            userId
                        },
                        Title = $"Testing",
                        Body = $"Testing message body",
                        Badge = 99
                    });
            }
        }

        public async Task TestRegNotification(string id, DeviceRegistration deviceUpdate)
        {
            await _notificationHubProxy.RegisterForPushNotifications(id, deviceUpdate);
        }

        public async Task SignalROnConversationTyping(
            string companyId,
            ConversationTypingObject conversationTypingObject)
        {
            // Input validation with logging
            if (string.IsNullOrEmpty(companyId))
            {
                _logger.LogError(
                    "[{MethodName}] CompanyId is null or empty. Payload: {Payload}",
                    nameof(SignalROnConversationTyping),
                    JsonConvert.SerializeObject(
                        conversationTypingObject,
                        new JsonSerializerSettings
                        {
                            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                        }));
                return;
            }

            if (conversationTypingObject == null)
            {
                _logger.LogError(
                    "[{MethodName}] ConversationTypingObject is null for companyId: {CompanyId}",
                    nameof(SignalROnConversationTyping),
                    companyId);
                return;
            }

            var payload = JsonConvert.SerializeObject(
                conversationTypingObject,
                new JsonSerializerSettings
                {
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                });

            // Log request information
            _logger.LogInformation(
                "[{MethodName}] Processing typing notification for companyId: {CompanyId}, ChannelType: {ChannelType}. Payload: {Payload}",
                nameof(SignalROnConversationTyping),
                companyId,
                conversationTypingObject.ChannelType,
                payload);

            try
            {
                // Backward-compatible default routing:
                // Historically, OnConversationTyping did not include ChannelType.
                // If ChannelType is missing or not LiveChatV2, preserve the legacy behavior by
                // broadcasting OnConversationTyping to the company group.
                // The frontend should now include ChannelType for precise routing; LiveChatV2 is handled separately.
                if (string.IsNullOrEmpty(conversationTypingObject.ChannelType) || conversationTypingObject.ChannelType != ChannelTypes.LiveChatV2)
                {
                    await _hubContext.Clients
                        .Group(companyId)
                        .SendAsync("OnConversationTyping", conversationTypingObject);
                    return;
                }

                // LiveChatV2 specific handling
                if (conversationTypingObject.ChannelType == ChannelTypes.LiveChatV2)
                {
                    await HandleLiveChatV2Typing(companyId, conversationTypingObject, payload);
                    return;
                }

                // Log unsupported channel type
                _logger.LogWarning(
                    "[{MethodName}] Unsupported channel type: {ChannelType} for companyId: {CompanyId}. Payload: {Payload}",
                    nameof(SignalROnConversationTyping),
                    conversationTypingObject.ChannelType,
                    companyId,
                    payload);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Error sending typing notification for companyId: {CompanyId}, ChannelType: {ChannelType}. Payload: {Payload}",
                    nameof(SignalROnConversationTyping),
                    companyId,
                    conversationTypingObject.ChannelType,
                    payload);
            }
        }

        private async Task HandleLiveChatV2Typing(
            string companyId,
            ConversationTypingObject conversationTypingObject,
            string payload)
        {
            // Validate required fields for LiveChatV2
            if (string.IsNullOrEmpty(conversationTypingObject.WebClientUuid))
            {
                _logger.LogError(
                    "[{MethodName}] WebClientUuid is null or empty for LiveChatV2 in companyId: {CompanyId}. Payload: {Payload}",
                    nameof(SignalROnConversationTyping),
                    companyId,
                    payload);
                return;
            }

            if (string.IsNullOrEmpty(conversationTypingObject.ChannelIdentityId))
            {
                _logger.LogError(
                    "[{MethodName}] ChannelIdentityId is null or empty for LiveChatV2 in companyId: {CompanyId}. Payload: {Payload}",
                    nameof(SignalROnConversationTyping),
                    companyId,
                    payload);
                return;
            }

            // Construct group name for LiveChatV2
            var groupName =
                $"{companyId}_livechat_{conversationTypingObject.WebClientUuid}_{conversationTypingObject.ChannelIdentityId}";

            _logger.LogInformation(
                "[{MethodName}] Sending LiveChatV2 typing notification to group: {GroupName}. Payload: {Payload}",
                nameof(SignalROnConversationTyping),
                groupName,
                payload);

            await _hubContext.Clients
                .Group(groupName)
                .SendAsync("OnConversationTyping", conversationTypingObject);
        }

        public async Task SignalROnWebclientInfoAdded(string webclientUUID, IPAddressInfo newAddressInfo)
        {
            await _hubContext.Clients
                .Group(webclientUUID)
                .SendAsync("OnLiveChatInfoChanged", newAddressInfo);
        }

        public async Task SignalROnBackgroundTaskStatusChange(
            string userId,
            BackgroundTask backgroundTask,
            BackgroundTaskStatus status)
        {
            await _hubContext.Clients
                .User(userId)
                .SendAsync("OnBackgroundTaskStatusChange", backgroundTask.MapToResultViewModel(status));
        }

        public async Task SignalROnStripePaymentStatusUpdated(
            string companyId,
            StripePaymentRecordResponse stripePaymentRecordResponse)
        {
            await _hubContext.Clients
                .Group(companyId)
                .SendAsync("OnStripePaymentStatusUpdated", stripePaymentRecordResponse);
        }

        public async Task SignalROnStaffUpdated(string userId, StaffWithoutCompanyResponse staffResponse)
        {
            await _hubContext.Clients
                .User(userId)
                .SendAsync("OnStaffInfoUpdated", staffResponse);
        }

        public async Task SignalROnWhatsappCloudApiBusinessBalanceChanged(
            string companyId,
            BusinessBalanceDto businessBalance)
        {
            var companyUserIdentityIds = await _appDbContext.UserRoleStaffs
                .AsNoTracking()
                .Where(x => x.CompanyId == companyId)
                .Select(x => x.Identity.Id)
                .ToListAsync();

            await _hubContext.Clients
                .Users(companyUserIdentityIds)
                .SendAsync("OnWhatsappCloudApiBusinessBalanceChanged", businessBalance);
        }

        /// <summary>
        /// Mask MessageContent in the view model if enabled for the company.
        /// </summary>
        /// <remarks>
        /// Only mask incoming messages.
        /// </remarks>
        /// <param name="vm"><see cref="ConversationMessageResponseViewModel"/></param>
        private async Task MaskConversationMessageResponseViewModelAsync(ConversationMessageResponseViewModel vm)
        {
            if (!vm.IsSentFromSleekflow
                && vm.MessageContent is not null
                && await _piiMaskingService.IsConfiguredAsync(vm.CompanyId))
            {
                vm.MessageContent = await _piiMaskingService.MaskPiiIfEnabledAsync(
                    vm.CompanyId,
                    vm.MessageContent,
                    MaskingLocations.IncomingMessage,
                    MaskingRoles.Staff);
            }
        }

        public class WebPushNotification
        {
            public string Event { get; set; }

            public string Title { get; set; }

            public string Body { get; set; }

            public string CompanyId { get; set; }

            public string ConversationId { get; set; }

            public string AssigneeId { get; set; }

            public string ProfileName { get; set; }

            public int Badge { get; set; }

            public string ChannelType { get; set; } = string.Empty;

            public string ChannelIdentityId { get; set; } = string.Empty;
        }

        private async Task<bool> SendPushNotificationToSleekFlowCore(
            SendPushNotificationInput sendPushNotificationInput)
        {
            try
            {
                var res = await _notificationsApi.NotificationsSendPushNotificationPostAsync(
                    sendPushNotificationInput: sendPushNotificationInput);
                return res.Success;
            }
            catch (SleekflowErrorCodeException ex)
            {
                switch (ex.ErrorCode)
                {
                    case ErrorCodeConstants.SfNotificationEventNotEnabledException:
                        return true;
                    case ErrorCodeConstants.SfSendNotificationFail:
                    case ErrorCodeConstants.SfNotFoundObjectException:
                        _logger.LogInformation(
                            "SendPushNotificationToSleekFlowCore Error, process with old version: {ErrorCode}",
                            ex.ErrorCode);
                        return false;
                    default:
                        throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "SendPushNotificationToSleekFlowCore Error: {SendPushNotificationInput}",
                    JsonConvert.SerializeObject(sendPushNotificationInput));
                return false;
            }
        }

        private async Task<bool> IsBroadcastLowBandwidthCondition(string companyId, DeliveryType deliveryType)
        {
            var isBroadcastRelatedDeliveryType = deliveryType
                is DeliveryType.Broadcast or DeliveryType.AutomatedMessage;

            if (!isBroadcastRelatedDeliveryType)
            {
                return false;
            }

            var isLowBandwidthMode = await _enabledFeaturesService.GetIsFeatureEnabledForCompany(
                companyId,
                FeatureFlags.LowBandwidthMode);

            return isLowBandwidthMode;
        }

        private async Task<bool> IsFlowBuilderContactUpdateLowBandwidthCondition(
            string companyId,
            UserProfileSource? source)
        {
            if (source == null)
            {
                return false;
            }

            var isLowBandwidthMode = await _enabledFeaturesService.GetIsFeatureEnabledForCompany(
                companyId,
                FeatureFlags.LowBandwidthMode);

            if (!isLowBandwidthMode)
            {
                return false;
            }

            if (source != UserProfileSource.FlowHub)
            {
                return false;
            }

            _logger.LogInformation(
                "[{MethodName}] FlowBuilder contact update skipped due to low bandwidth mode. CompanyId: {CompanyId}, Source: {Source}",
                nameof(IsFlowBuilderContactUpdateLowBandwidthCondition),
                companyId,
                source);

            return true;
        }

        private async Task<bool> IsContactImportLowBandwidthCondition(string companyId)
        {
            var isLowBandwidthMode = await _enabledFeaturesService.GetIsFeatureEnabledForCompany(
                companyId,
                FeatureFlags.LowBandwidthMode);

            if (!isLowBandwidthMode)
            {
                return false;
            }

            var importContactTaskTypes = new List<BackgroundTaskType>
            {
                BackgroundTaskType.ImportContacts,
                BackgroundTaskType.AddContactsToList,
                BackgroundTaskType.BulkUpdateContactsCustomFields,
                BackgroundTaskType.BulkImportContacts
            };

            var isImporting = await _appDbContext.BackgroundTasks
                .Where(x => x.CompanyId == companyId)
                .Where(x => importContactTaskTypes.Contains(x.TaskType))

                // Will skip task that create for two weeks but didn't failed correctly
                .Where(x => x.CreatedAt < DateTime.UtcNow.AddDays(-14))
                .AnyAsync(x => !x.IsCompleted && x.ErrorMessage == null);

            return isImporting;
        }
    }
}