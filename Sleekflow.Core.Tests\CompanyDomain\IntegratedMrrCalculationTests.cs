using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.InternalDomain.Models;

namespace Sleekflow.Core.Tests.CompanyDomain;

/// <summary>
/// Integration tests for the complete timezone-aware MRR calculation flow,
/// testing real-world scenarios and edge cases.
/// </summary>
[TestFixture]
public class IntegratedMrrCalculationTests
{
    private Mock<ILogger<TimezoneAwareMrrCalculationService>> _mockLogger = null!;
    private Mock<ILogger<BillRecordRevenueCalculatorService>> _mockBillRecordRevenueCalculatorServiceLogger = null!;
    private BillRecordRevenueCalculatorService _billRecordRevenueCalculatorService = null!;
    private TimezoneAwareMrrCalculationService _timezoneService = null!;

    [SetUp]
    public void Setup()
    {
        _mockLogger = new Mock<ILogger<TimezoneAwareMrrCalculationService>>();
        _mockBillRecordRevenueCalculatorServiceLogger = new Mock<ILogger<BillRecordRevenueCalculatorService>>();
        _billRecordRevenueCalculatorService = new BillRecordRevenueCalculatorService(
            _mockBillRecordRevenueCalculatorServiceLogger.Object,
            _timezoneService);
        _timezoneService = new TimezoneAwareMrrCalculationService(
            _mockLogger.Object);
    }

    #region Real-World Timezone Scenarios

    [Test]
    public void EndToEndMrrCalculation_WithChineseCompany_ShouldCalculateCorrectly()
    {
        // Arrange
        var companyId = "chinese-company-id";
        var company = new Company
        {
            Id = companyId, TimeZoneInfoId = "China Standard Time"
        };

        var billRecords = new List<BillRecord>
        {
            new ()
            {
                Id = 1,
                CompanyId = companyId,
                PayAmount = 1000,
                currency = "usd",
                SubscriptionPlanId = "sleekflow_v10_startup",
                created = new DateTime(2023, 1, 1, 16, 0, 0, DateTimeKind.Utc),
                // UTC times that span across days in Chinese timezone
                PeriodStart = new DateTime(2023, 1, 1, 16, 0, 0, DateTimeKind.Utc), // 12 AM next day in China
                PeriodEnd = new DateTime(2023, 2, 1, 16, 0, 0, DateTimeKind.Utc), // 12 AM next day in China
                CmsSalesPaymentRecords = new List<CmsSalesPaymentRecord>()
            }
        };

        // Act
        var testDateTime = billRecords[0].PeriodStart.AddDays(7); // Mid-point of the partial month period
        var result = _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
            billRecords,
            company.TimeZoneInfoId,
            testDateTime);

        // Assert
        result.Should().BeGreaterThan(0);
        result.Should().Be(1000); // Full month period should result in exact PayAmount
    }

    [Test]
    public void EndToEndMrrCalculation_WithUsEasternCompany_DstTransition_ShouldHandleCorrectly()
    {
        // Arrange
        var companyId = "us-eastern-company-id";
        var company = new Company
        {
            Id = companyId, TimeZoneInfoId = "Eastern Standard Time"
        };

        var billRecords = new List<BillRecord>
        {
            new ()
            {
                Id = 1,
                CompanyId = companyId,
                PayAmount = 1200,
                currency = "usd",
                SubscriptionPlanId = "sleekflow_v10_startup",
                created = new DateTime(2023, 3, 1, 5, 0, 0, DateTimeKind.Utc),
                // Period that crosses DST transition (March 2023)
                PeriodStart = new DateTime(2023, 3, 1, 5, 0, 0, DateTimeKind.Utc), // EST
                PeriodEnd = new DateTime(2023, 4, 1, 4, 0, 0, DateTimeKind.Utc), // EDT
                CmsSalesPaymentRecords = new List<CmsSalesPaymentRecord>()
            }
        };

        // Act
        var testDateTime = new DateTime(2023, 3, 15, 12, 0, 0, DateTimeKind.Utc);
        var result = _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
            billRecords,
            company.TimeZoneInfoId,
            testDateTime);

        // Assert
        result.Should().BeGreaterThan(0);
        result.Should().Be(1200);
    }

    #endregion

    #region Edge Cases

    [Test]
    public void EndToEndMrrCalculation_WithLeapYearFebruary_ShouldCalculateAccurately()
    {
        // Arrange
        var companyId = "leap-year-company";
        var company = new Company
        {
            Id = companyId, TimeZoneInfoId = "UTC"
        };

        var billRecords = new List<BillRecord>
        {
            new ()
            {
                Id = 1,
                CompanyId = companyId,
                PayAmount = 2900, // 29 * 100 for Feb 29
                currency = "usd",
                SubscriptionPlanId = "sleekflow_v10_startup",
                created = new DateTime(2024, 1, 29, 0, 0, 0, DateTimeKind.Utc),
                // Leap year February 2024
                PeriodStart = new DateTime(2024, 1, 29, 0, 0, 0, DateTimeKind.Utc),
                PeriodEnd = new DateTime(2024, 2, 29, 0, 0, 0, DateTimeKind.Utc), // Feb 29 exists
                CmsSalesPaymentRecords = new List<CmsSalesPaymentRecord>()
            }
        };

        // Act
        var testDateTime = new DateTime(2024, 2, 1, 0, 0, 0, DateTimeKind.Utc);
        var result = _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
            billRecords,
            company.TimeZoneInfoId,
            testDateTime);

        // Assert
        result.Should().Be(2900); // Should be exactly 1 month, so no adjustment
    }

    [Test]
    public void EndToEndMrrCalculation_WithYearBoundary_ShouldCalculateCorrectly()
    {
        // Arrange
        var companyId = "year-boundary-company";
        var company = new Company
        {
            Id = companyId, TimeZoneInfoId = "UTC"
        };

        var billRecords = new List<BillRecord>
        {
            new ()
            {
                Id = 1,
                CompanyId = companyId,
                PayAmount = 3000,
                currency = "usd",
                SubscriptionPlanId = "sleekflow_v10_startup",
                created = new DateTime(2023, 11, 15, 0, 0, 0, DateTimeKind.Utc),
                // Cross year boundary
                PeriodStart = new DateTime(2023, 11, 15, 0, 0, 0, DateTimeKind.Utc),
                PeriodEnd = new DateTime(2024, 2, 15, 0, 0, 0, DateTimeKind.Utc), // 3 months
                CmsSalesPaymentRecords = new List<CmsSalesPaymentRecord>()
            }
        };

        // Act
        var testDateTime = new DateTime(2023, 12, 1, 0, 0, 0, DateTimeKind.Utc);
        var result = _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
            billRecords,
            company.TimeZoneInfoId,
            testDateTime);

        // Assert
        result.Should().Be(1000); // 3000 / 3 months = 1000 per month
    }

    #endregion

    #region Error Recovery Scenarios

    [Test]
    public void EndToEndMrrCalculation_WithDatabaseTimeout_ShouldFallbackGracefully()
    {
        // Arrange
        var companyId = "timeout-company";

        var billRecords = new List<BillRecord>
        {
            new ()
            {
                Id = 1,
                CompanyId = companyId,
                PayAmount = 1000,
                currency = "usd",
                SubscriptionPlanId = "sleekflow_v10_startup",
                created = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                PeriodStart = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                PeriodEnd = new DateTime(2023, 2, 1, 0, 0, 0, DateTimeKind.Utc),
                CmsSalesPaymentRecords = new List<CmsSalesPaymentRecord>()
            }
        };

        // Act
        var testDateTime = new DateTime(2023, 1, 15, 0, 0, 0, DateTimeKind.Utc);
        var result =
            _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(billRecords, null, testDateTime);

        // Assert
        result.Should().Be(1000); // Should fallback to original calculation
    }

    [Test]
    public void EndToEndMrrCalculation_WithCorruptedTimezoneData_ShouldUseFallback()
    {
        // Arrange
        var companyId = "corrupted-timezone-company";
        var company = new Company
        {
            Id = companyId, TimeZoneInfoId = "Invalid/Timezone/Data"
        };

        var billRecords = new List<BillRecord>
        {
            new ()
            {
                Id = 1,
                CompanyId = companyId,
                PayAmount = 1000,
                currency = "usd",
                SubscriptionPlanId = "sleekflow_v10_startup",
                created = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                PeriodStart = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                PeriodEnd = new DateTime(2023, 2, 1, 0, 0, 0, DateTimeKind.Utc),
                CmsSalesPaymentRecords = new List<CmsSalesPaymentRecord>()
            }
        };

        // Act
        var testDateTime = new DateTime(2023, 1, 15, 0, 0, 0, DateTimeKind.Utc);
        var result = _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
            billRecords,
            company.TimeZoneInfoId,
            testDateTime);

        // Assert
        result.Should().Be(1000); // Should use UTC fallback and original calculation
    }

    #endregion

    #region Multi-Currency Scenarios

    [Test]
    public void EndToEndMrrCalculation_WithMultipleCurrencies_ShouldConvertCorrectly()
    {
        // Arrange
        var companyId = "multi-currency-company";
        var company = new Company
        {
            Id = companyId, TimeZoneInfoId = "UTC"
        };

        var billRecords = new List<BillRecord>
        {
            new ()
            {
                Id = 1,
                CompanyId = companyId,
                PayAmount = 7850, // HKD
                currency = "hkd",
                SubscriptionPlanId = "sleekflow_v10_startup",
                created = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                PeriodStart = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                PeriodEnd = new DateTime(2023, 2, 1, 0, 0, 0, DateTimeKind.Utc),
                CmsSalesPaymentRecords = new List<CmsSalesPaymentRecord>()
            },
            new ()
            {
                Id = 2,
                CompanyId = companyId,
                PayAmount = 1000, // USD
                currency = "usd",
                SubscriptionPlanId = "sleekflow_v10_countrytier1_whatsapp_phone_number_monthly_aed",
                created = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                PeriodStart = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                PeriodEnd = new DateTime(2023, 2, 1, 0, 0, 0, DateTimeKind.Utc),
                CmsSalesPaymentRecords = new List<CmsSalesPaymentRecord>()
            }
        };

        // Act
        var testDateTime = new DateTime(2023, 1, 15, 0, 0, 0, DateTimeKind.Utc);
        var result = _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
            billRecords,
            company.TimeZoneInfoId,
            testDateTime);

        // Assert
        // 7850 HKD / 7.85 = 1000 USD, plus 1000 USD = 2000 USD total
        result.Should().Be(2000);
    }

    #endregion

    #region Performance and Load Testing

    [Test]
    public void EndToEndMrrCalculation_WithHighVolumeData_ShouldMaintainPerformance()
    {
        // Arrange
        var companies = new List<Company>();

        // Create 100 companies with 10 bill records each
        for (var companyIndex = 0; companyIndex < 100; companyIndex++)
        {
            var companyId = $"company-{companyIndex}";
            var company = new Company
            {
                Id = companyId, TimeZoneInfoId = "UTC", BillRecords = []
            };

            for (var billIndex = 0; billIndex < 10; billIndex++)
            {
                company.BillRecords.Add(
                    new BillRecord
                    {
                        Id = companyIndex * 10 + billIndex,
                        CompanyId = companyId,
                        PayAmount = 100,
                        currency = "usd",
                        SubscriptionPlanId = "sleekflow_v10_countrytier1_whatsapp_phone_number_monthly_aed",
                        created = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                        PeriodStart = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                        PeriodEnd = new DateTime(2023, 2, 1, 0, 0, 0, DateTimeKind.Utc),
                        CmsSalesPaymentRecords = new List<CmsSalesPaymentRecord>()
                    });
            }

            companies.Add(company);
        }

        // Act
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var testDateTime = new DateTime(2023, 1, 15, 0, 0, 0, DateTimeKind.Utc);
        var result = companies.Sum(company =>
            _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                company.BillRecords.ToList(),
                company.TimeZoneInfoId,
                testDateTime));
        stopwatch.Stop();

        // Assert
        result.Should().Be(100000); // 100 companies * 10 bills * 100 = 100,000
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(30000); // Should complete within 30 seconds
    }

    #endregion

    #region Precision and Rounding Tests

    [Test]
    public void EndToEndMrrCalculation_WithVerySmallAmounts_ShouldMaintainPrecision()
    {
        // Arrange
        var companyId = "precision-test-company";
        var company = new Company
        {
            Id = companyId, TimeZoneInfoId = "UTC"
        };

        var billRecords = new List<BillRecord>
        {
            new ()
            {
                Id = 1,
                CompanyId = companyId,
                PayAmount = 0.01, // 1 cent
                currency = "usd",
                SubscriptionPlanId = "sleekflow_v10_startup",
                created = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                PeriodStart = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                PeriodEnd = new DateTime(2023, 2, 1, 0, 0, 0, DateTimeKind.Utc), // 1 hour
                CmsSalesPaymentRecords = new List<CmsSalesPaymentRecord>()
            }
        };

        // Act
        var testDateTime = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        var result = _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
            billRecords,
            company.TimeZoneInfoId,
            testDateTime);

        // Assert
        result.Should().BeGreaterThan(0);
        result.Should().Be(0.01m);
    }

    [Test]
    public void EndToEndMrrCalculation_WithVeryLargeAmounts_ShouldHandleCorrectly()
    {
        // Arrange
        var companyId = "large-amount-company";
        var company = new Company
        {
            Id = companyId, TimeZoneInfoId = "UTC"
        };

        var billRecords = new List<BillRecord>
        {
            new ()
            {
                Id = 1,
                CompanyId = companyId,
                PayAmount = 1000000, // 1 million
                currency = "usd",
                SubscriptionPlanId = "sleekflow_v10_startup",
                created = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                PeriodStart = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                PeriodEnd = new DateTime(2023, 2, 1, 0, 0, 0, DateTimeKind.Utc),
                CmsSalesPaymentRecords = new List<CmsSalesPaymentRecord>()
            }
        };

        // Act
        var testDateTime = new DateTime(2023, 1, 1, 0, 30, 0, DateTimeKind.Utc);
        var result = _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
            billRecords,
            company.TimeZoneInfoId,
            testDateTime);

        // Assert
        result.Should().Be(1000000m); // Should handle large amounts without overflow
    }

    #endregion

    #region Grace Period and Complex Payment Scenarios

    [Test]
    public void EndToEndMrrCalculation_WithGracePeriodAndTimezone_ShouldCalculateCorrectly()
    {
        // Arrange
        var companyId = "grace-period-company";
        var company = new Company
        {
            Id = companyId, TimeZoneInfoId = "Asia/Tokyo"
        };

        var billRecords = new List<BillRecord>
        {
            new ()
            {
                Id = 1,
                CompanyId = companyId,
                PayAmount = 1000, // Stripe payment
                currency = "usd",
                SubscriptionPlanId = "sleekflow_v10_startup",
                created = new DateTime(2023, 1, 1, 15, 0, 0, DateTimeKind.Utc),
                PeriodStart = new DateTime(2023, 1, 1, 15, 0, 0, DateTimeKind.Utc), // Next day in Tokyo
                PeriodEnd = new DateTime(2023, 2, 1, 15, 0, 0, DateTimeKind.Utc),
                metadata = new Dictionary<string, string>
                {
                    {
                        "grace_period_start_time", "2023-01-05T15:00:00Z"
                    }
                },
                CmsSalesPaymentRecords = new List<CmsSalesPaymentRecord>
                {
                    new ()
                    {
                        Id = 1,
                        SubscriptionFee = 500, // Additional payment
                        Currency = "usd"
                    }
                }
            }
        };

        // Act
        var testDateTime = new DateTime(2023, 1, 1, 15, 0, 0, DateTimeKind.Utc);
        var result = _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
            billRecords,
            company.TimeZoneInfoId,
            testDateTime);

        // Assert
        result.Should().BeGreaterThan(0);
        result.Should().Be(1500); // 1000 + 500, assuming exact month calculation
    }

    #endregion
}