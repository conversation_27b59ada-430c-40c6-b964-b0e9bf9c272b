using AutoMapper;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.ConversationAccessControl;
using Travis_backend.ConversationDomain.ConversationPermissionConstants;
using Travis_backend.ConversationDomain.ConversationSettingsConstants;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.Repositories;
using Travis_backend.ConversationDomain.Services;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationDomain.ViewModels.Mappers;
using Travis_backend.Database;
using Travis_backend.Database.Services;
using Travis_backend.MessageDomain.Models;
using Travis_backend.Notifications;
using Travis_backend.PiiMaskingDomain.Services;
using Travis_backend.SignalR;
using Travis_backend.StripeIntegrationDomain.Services;
using Travis_backend.Telemetries;
using Is = NUnit.Framework.Is;

namespace Sleekflow.Core.Tests.Conversations.Rbac.RbacNotification
{
    [TestFixture]
    public class RbacNotificationServiceTests
    {
        private string _companyId;

        private ApplicationDbContext _appDbContext;
        private Mock<IDbContextService> _dbContextServiceMock;
        private Mock<IAccessControlAggregationService> _accessControlAggregationServiceMock;
        private IRbacConversationPermissionManager _rbacConversationPermissionManager;
        private Mock<IMapper> _mapperMock;
        private Mock<IHubContext<Chat>> _hubContextMock;
        private Mock<ILogger<RbacNotificationService>> _loggerMock;
        private Mock<ISleekPayService> _sleekPayServiceMock;
        private Mock<IPiiMaskingService> _piiMaskingServiceMock;
        private Mock<IApplicationInsightsTelemetryTracer> _applicationInsightsTelemetryTracerMock;
        private Mock<IConversationService> _conversationServiceMock;
        private Mock<IConversationNoCompanyResponseViewModelMapper> _conversationNoCompanyResponseViewModelMapperMock;
        private RbacNotificationService _rbacNotificationService;
        private IRbacDefaultChannelPermissionManager _rbacDefaultChannelPermissionManager;
        private IInboxSettingManager _inboxSettingManager;
        private IRbacNotificationManager _notificationManager;
        private Mock<IConversationNoCompanyResponseViewModelMapper> _conversationNoCompanyResponseViewModelMapper;

        [OneTimeSetUp]
        public void OneTimeSetUp()
        {
            var serviceProvider = new ServiceCollection()
                .AddEntityFrameworkInMemoryDatabase()
                .BuildServiceProvider();

            var options = new DbContextOptionsBuilder<ApplicationDbContext>()
                .UseInMemoryDatabase(databaseName: "TestDatabase")
                .UseInternalServiceProvider(serviceProvider)
                .Options;

            _appDbContext = new ApplicationDbContext(options);

            _companyId = "sleekflow";

            _dbContextServiceMock = new Mock<IDbContextService>();
            _accessControlAggregationServiceMock = new Mock<IAccessControlAggregationService>();
            _rbacDefaultChannelPermissionManager = new RbacDefaultChannelPermissionManager();
            _inboxSettingManager = new InboxSettingManager();
            _rbacConversationPermissionManager =
                new RbacConversationPermissionManager(_rbacDefaultChannelPermissionManager);
            _mapperMock = new Mock<IMapper>();
            _hubContextMock = new Mock<IHubContext<Chat>>();
            _loggerMock = new Mock<ILogger<RbacNotificationService>>();
            _sleekPayServiceMock = new Mock<ISleekPayService>();
            _piiMaskingServiceMock = new Mock<IPiiMaskingService>();
            _applicationInsightsTelemetryTracerMock = new Mock<IApplicationInsightsTelemetryTracer>();
            _conversationServiceMock = new Mock<IConversationService>();
            _conversationNoCompanyResponseViewModelMapperMock =
                new Mock<IConversationNoCompanyResponseViewModelMapper>();
            _notificationManager = new RbacNotificationManager();
            _conversationNoCompanyResponseViewModelMapper = new Mock<IConversationNoCompanyResponseViewModelMapper>();


            _dbContextServiceMock.Setup(x => x.GetDbContext()).Returns(_appDbContext);
            _rbacNotificationService = new RbacNotificationService(
                _appDbContext,
                _accessControlAggregationServiceMock.Object,
                _dbContextServiceMock.Object,
                _rbacConversationPermissionManager,
                _mapperMock.Object,
                _hubContextMock.Object,
                _loggerMock.Object,
                _sleekPayServiceMock.Object,
                _piiMaskingServiceMock.Object,
                _applicationInsightsTelemetryTracerMock.Object,
                _conversationServiceMock.Object,
                _rbacDefaultChannelPermissionManager,
                _notificationManager,
                _conversationNoCompanyResponseViewModelMapper.Object,
                new Mock<IConversationReadOnlyRepository>().Object);
        }

        [Test]
        public void UnassignConversation()
        {
            const string conversationId = "conversationWithNoAssignment";
            var conversationWithNoAssignment = new Conversation()
            {
                CompanyId = _companyId, Id = conversationId
            };

            const int conversationMessageId = 1;

            var conversationMessage = new ConversationMessage()
            {
                CompanyId = _companyId,
                ConversationId = conversationId,
                Id = conversationMessageId,
                Channel = ChannelTypes.WhatsappCloudApi
            };

            const int expectedStaffIdWithAllUnassignConversationPermission = 1;
            const int expectedStaffIdWithAllConversationPermission = 3;

            var aggregatedStaffs = new Dictionary<long, StaffAccessControlAggregate>
            {
                {
                    expectedStaffIdWithAllUnassignConversationPermission, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = expectedStaffIdWithAllUnassignConversationPermission,
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions = [RbacViewConversationsPermissions.AllUnassignedConversations]
                            }
                        ]
                    }
                },
                {
                    2, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 2,
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions = [RbacViewConversationsPermissions.AllAssignedConversations]
                            }
                        ]
                    }
                },
                {
                    expectedStaffIdWithAllConversationPermission, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = expectedStaffIdWithAllConversationPermission,
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions =
                                [
                                    RbacViewConversationsPermissions.AssignedToMe,
                                    RbacViewConversationsPermissions.AssignedToMyTeam,
                                    RbacViewConversationsPermissions.AllAssignedConversations,
                                    RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
                                    RbacViewConversationsPermissions.AllUnassignedConversations
                                ]
                            }
                        ]
                    }
                },
                {
                    4, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 4,
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions = [RbacViewConversationsPermissions.AssignedToMe]
                            }
                        ]
                    }
                },
                {
                    5, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 5,
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions = [RbacViewConversationsPermissions.AssignedToMyTeam]
                            }
                        ]
                    }
                },
                {
                    6, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 6,
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions =
                                    [RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam]
                            }
                        ]
                    }
                },
            };

            // Act
            var result = _rbacNotificationService
                .GetUnreadRecordsByPermissions(
                    conversationWithNoAssignment,
                    aggregatedStaffs,
                    conversationMessage);

            // Assert
            Assert.That(result, Is.InstanceOf<List<ConversationUnreadRecord>>());
            Assert.That(result, Has.Count.EqualTo(2));
            Assert.Multiple(() =>
            {
                Assert.That(result[0].StaffId, Is.EqualTo(expectedStaffIdWithAllUnassignConversationPermission));
                Assert.That(result[0].NotificationDeliveryType, Is.EqualTo(NotificationDeliveryType.Unassigned));
            });

            Assert.Multiple(() =>
            {
                Assert.That(result[1].StaffId, Is.EqualTo(expectedStaffIdWithAllConversationPermission));
                Assert.That(result[1].NotificationDeliveryType, Is.EqualTo(NotificationDeliveryType.Unassigned));
            });
        }

        [Test]
        public void UnassignConversationWithOverlapPermissions_ShouldReturnUniqueRecord()
        {
            const string conversationId = "conversationWithNoAssignment";
            var conversationWithNoAssignment = new Conversation()
            {
                CompanyId = _companyId, Id = conversationId
            };

            const int conversationMessageId = 1;

            var conversationMessage = new ConversationMessage()
            {
                CompanyId = _companyId,
                ConversationId = conversationId,
                Id = conversationMessageId,
                Channel = ChannelTypes.WhatsappCloudApi
            };

            const int expectedStaffIdWithAllUnassignConversationPermission = 1;
            const int expectedStaffIdWithAllConversationPermission = 3;

            var aggregatedStaffs = new Dictionary<long, StaffAccessControlAggregate>
            {
                {
                    expectedStaffIdWithAllUnassignConversationPermission, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = expectedStaffIdWithAllUnassignConversationPermission,
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions = [RbacViewConversationsPermissions.AllUnassignedConversations]
                            }
                        ]
                    }
                },
                {
                    2, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 2,
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions = [RbacViewConversationsPermissions.AllAssignedConversations]
                            }
                        ]
                    }
                },
                {
                    expectedStaffIdWithAllConversationPermission, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = expectedStaffIdWithAllConversationPermission,
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions =
                                [
                                    RbacViewConversationsPermissions.AssignedToMe,
                                    RbacViewConversationsPermissions.AssignedToMyTeam,
                                    RbacViewConversationsPermissions.AllAssignedConversations,
                                    RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
                                    RbacViewConversationsPermissions.AllUnassignedConversations
                                ]
                            }
                        ]
                    }
                },
                {
                    4, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 4,
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions = [RbacViewConversationsPermissions.AssignedToMe]
                            }
                        ]
                    }
                },
                {
                    5, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 5,
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions = [RbacViewConversationsPermissions.AssignedToMyTeam]
                            }
                        ]
                    }
                },
                {
                    6, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 6,
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions =
                                    [RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam]
                            }
                        ]
                    }
                },
            };

            // Act
            var result = _rbacNotificationService
                .GetUnreadRecordsByPermissions(
                    conversationWithNoAssignment,
                    aggregatedStaffs,
                    conversationMessage);

            // Assert
            Assert.That(result, Is.InstanceOf<List<ConversationUnreadRecord>>());
            Assert.That(result, Has.Count.EqualTo(2));
            Assert.Multiple(() =>
            {
                Assert.That(result[0].StaffId, Is.EqualTo(expectedStaffIdWithAllUnassignConversationPermission));
                Assert.That(result[0].NotificationDeliveryType, Is.EqualTo(NotificationDeliveryType.Unassigned));
            });

            Assert.Multiple(() =>
            {
                Assert.That(result[1].StaffId, Is.EqualTo(expectedStaffIdWithAllConversationPermission));
                Assert.That(result[1].NotificationDeliveryType, Is.EqualTo(NotificationDeliveryType.Unassigned));
            });
        }

        [Test]
        public void ConversationWithAssignee()
        {
            const int contactOwnerId = 4;

            const string conversationId = "conversationWithAssignee";
            var conversationWithContactOwner = new Conversation()
            {
                CompanyId = _companyId, Id = conversationId, AssigneeId = contactOwnerId
            };

            const int conversationMessageId = 1;

            var conversationMessage = new ConversationMessage()
            {
                CompanyId = _companyId,
                ConversationId = conversationId,
                Id = conversationMessageId,
                Channel = ChannelTypes.WhatsappCloudApi,
            };

            const int expectedStaffIdWithAllAssignedConversationPermission = 2;
            const int expectedStaffIdWithAllConversationPermission = 3;

            var aggregatedStaffs = new Dictionary<long, StaffAccessControlAggregate>
            {
                {
                    1, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 1,
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions =
                                [
                                    RbacViewConversationsPermissions.AssignedToMe,
                                    RbacViewConversationsPermissions.AllUnassignedConversations
                                ]
                            }
                        ]
                    }
                },
                {
                    expectedStaffIdWithAllAssignedConversationPermission, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = expectedStaffIdWithAllAssignedConversationPermission,
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions =
                                [
                                    RbacViewConversationsPermissions.AssignedToMe,
                                    RbacViewConversationsPermissions.AllAssignedConversations
                                ]
                            }
                        ]
                    }
                },
                {
                    expectedStaffIdWithAllConversationPermission, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = expectedStaffIdWithAllConversationPermission,
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions =
                                [
                                    RbacViewConversationsPermissions.AssignedToMe,
                                    RbacViewConversationsPermissions.AssignedToMyTeam,
                                    RbacViewConversationsPermissions.AllAssignedConversations,
                                    RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
                                    RbacViewConversationsPermissions.AllUnassignedConversations
                                ]
                            }
                        ]
                    }
                },
                {
                    contactOwnerId, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = contactOwnerId,
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions = [RbacViewConversationsPermissions.AssignedToMe]
                            }
                        ]
                    }
                },
                {
                    5, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 5,
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions =
                                [
                                    RbacViewConversationsPermissions.AssignedToMe,
                                    RbacViewConversationsPermissions.AssignedToMyTeam
                                ]
                            }
                        ]
                    }
                },
                {
                    6, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 6,
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions =
                                [
                                    RbacViewConversationsPermissions.AssignedToMe,
                                    RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam
                                ]
                            }
                        ]
                    }
                },
            };

            // Act
            var result = _rbacNotificationService
                .GetUnreadRecordsByPermissions(
                    conversationWithContactOwner,
                    aggregatedStaffs,
                    conversationMessage);

            // Assert
            Assert.That(result, Is.InstanceOf<List<ConversationUnreadRecord>>());
            Assert.That(result, Has.Count.EqualTo(1));

            Assert.Multiple(() =>
            {
                Assert.That(result[0].StaffId, Is.EqualTo(contactOwnerId));
                Assert.That(result[0].NotificationDeliveryType, Is.EqualTo(NotificationDeliveryType.ContactOwner));
            });
        }


        [Test]
        public void ConversationWithTeamAssignment()
        {
            const string conversationId = "conversation";
            var conversationWithNoAssignment = new Conversation()
            {
                CompanyId = _companyId, Id = conversationId, AssignedTeamId = 1
            };

            const int conversationMessageId = 1;

            var conversationMessage = new ConversationMessage()
            {
                CompanyId = _companyId,
                ConversationId = conversationId,
                Id = conversationMessageId,
                Channel = ChannelTypes.WhatsappCloudApi
            };

            var teamA = new TeamAccessControlAggregate()
            {
                Id = 1, TeamMemberStaffIds = [0, 1, 2]
            };

            var teamB = new TeamAccessControlAggregate()
            {

                Id = 2, TeamMemberStaffIds = [1, 3]
            };

            var teamC = new TeamAccessControlAggregate()
            {

                Id = 3, TeamMemberStaffIds = [4]
            };

            var aggregatedStaffs = new Dictionary<long, StaffAccessControlAggregate>
            {

                {
                    0, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 0,
                        AssociatedTeams = [teamA],
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions = [RbacViewConversationsPermissions.AssignedToMe]
                            }
                        ]
                    }
                },
                {
                    1, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 1,
                        AssociatedTeams = [teamA],
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions = [RbacViewConversationsPermissions.AssignedToMe]
                            }
                        ]
                    }
                },
                {
                    2, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 2,
                        AssociatedTeams = [teamA],
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions =
                                [
                                    RbacViewConversationsPermissions.AssignedToMe,
                                    RbacViewConversationsPermissions.AssignedToMyTeam
                                ]
                            }
                        ]
                    }
                },
                {
                    3, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 3,
                        AssociatedTeams = [teamB],
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions =
                                [
                                    RbacViewConversationsPermissions.AssignedToMe,
                                    RbacViewConversationsPermissions.AssignedToMyTeam
                                ]
                            }
                        ]
                    }
                },
                {
                    4, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 4,
                        AssociatedTeams = [teamC],
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions =
                                [
                                    RbacViewConversationsPermissions.AssignedToMe,
                                    RbacViewConversationsPermissions.AssignedToMyTeam
                                ]
                            }
                        ]
                    }
                },
                {
                    5, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 5,
                        AssociatedTeams = [teamA],
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions =
                                [
                                    RbacViewConversationsPermissions.AssignedToMe,
                                    RbacViewConversationsPermissions.AssignedToMyTeam,
                                    RbacViewConversationsPermissions.AllAssignedConversations
                                ]
                            }
                        ]
                    }
                },
                {
                    6, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 6,
                        AssociatedTeams = [teamA],
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions =
                                [
                                    RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
                                    RbacViewConversationsPermissions.AllUnassignedConversations
                                ]
                            }
                        ]
                    }
                },
                {
                    7, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 7,
                        AssociatedTeams = [teamC],
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions =
                                [
                                    RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
                                    RbacViewConversationsPermissions.AllUnassignedConversations
                                ]
                            }
                        ]
                    }
                },
                {
                    8, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 8,
                        AssociatedTeams = [],
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions =
                                [
                                    RbacViewConversationsPermissions.AssignedToMe,
                                    RbacViewConversationsPermissions.AssignedToMyTeam,
                                    RbacViewConversationsPermissions.AllAssignedConversations,
                                    RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
                                    RbacViewConversationsPermissions.AllUnassignedConversations
                                ]
                            }
                        ]
                    }
                },
            };

            // Act
            var result = _rbacNotificationService
                .GetUnreadRecordsByPermissions(
                    conversationWithNoAssignment,
                    aggregatedStaffs,
                    conversationMessage);

            // Assert
            Assert.That(result, Is.InstanceOf<List<ConversationUnreadRecord>>());
            Assert.That(result, Has.Count.EqualTo(3));

            Assert.Multiple(() =>
            {
                Assert.That(result[0].StaffId, Is.EqualTo(2));
                Assert.That(result[0].NotificationDeliveryType, Is.EqualTo(NotificationDeliveryType.TeamAssigned));
            });

            Assert.Multiple(() =>
            {
                Assert.That(result[1].StaffId, Is.EqualTo(5));
                Assert.That(result[1].NotificationDeliveryType, Is.EqualTo(NotificationDeliveryType.TeamAssigned));
            });

            Assert.Multiple(() =>
            {
                Assert.That(result[2].StaffId, Is.EqualTo(8));
                Assert.That(result[2].NotificationDeliveryType, Is.EqualTo(NotificationDeliveryType.TeamAssigned));
            });
        }

        [Test]
        public void ConversationWithContactOwnerAndTeamAssignment()
        {
            const string conversationId = "conversation";
            var conversationWithNoAssignment = new Conversation()
            {
                CompanyId = _companyId, Id = conversationId, AssignedTeamId = 1, AssigneeId = 1,
            };

            const int conversationMessageId = 1;

            var conversationMessage = new ConversationMessage()
            {
                CompanyId = _companyId,
                ConversationId = conversationId,
                Id = conversationMessageId,
                Channel = ChannelTypes.WhatsappCloudApi
            };

            var teamA = new TeamAccessControlAggregate()
            {
                Id = 1, TeamMemberStaffIds = [0, 1, 2]
            };

            var teamB = new TeamAccessControlAggregate()
            {

                Id = 2, TeamMemberStaffIds = [1, 3]
            };

            var teamC = new TeamAccessControlAggregate()
            {

                Id = 3, TeamMemberStaffIds = [4]
            };

            var aggregatedStaffs = new Dictionary<long, StaffAccessControlAggregate>
            {

                {
                    0, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 0,
                        AssociatedTeams = [teamA],
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions = [RbacViewConversationsPermissions.AssignedToMe]
                            }
                        ]
                    }
                },
                {
                    1, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 1,
                        AssociatedTeams = [teamA],
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions = [RbacViewConversationsPermissions.AssignedToMe]
                            }
                        ]
                    }
                },
                {
                    2, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 2,
                        AssociatedTeams = [teamA],
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions =
                                [
                                    RbacViewConversationsPermissions.AssignedToMe,
                                    RbacViewConversationsPermissions.AssignedToMyTeam
                                ]
                            }
                        ]
                    }
                },
                {
                    3, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 3,
                        AssociatedTeams = [teamB],
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions =
                                [
                                    RbacViewConversationsPermissions.AssignedToMe,
                                    RbacViewConversationsPermissions.AssignedToMyTeam
                                ]
                            }
                        ]
                    }
                },
                {
                    4, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 4,
                        AssociatedTeams = [teamC],
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions =
                                [
                                    RbacViewConversationsPermissions.AssignedToMe,
                                    RbacViewConversationsPermissions.AssignedToMyTeam
                                ]
                            }
                        ]
                    }
                },
                {
                    5, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 5,
                        AssociatedTeams = [teamA],
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions =
                                [
                                    RbacViewConversationsPermissions.AssignedToMe,
                                    RbacViewConversationsPermissions.AssignedToMyTeam,
                                    RbacViewConversationsPermissions.AllAssignedConversations
                                ]
                            }
                        ]
                    }
                },
                {
                    6, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 6,
                        AssociatedTeams = [teamA],
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions =
                                [
                                    RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
                                    RbacViewConversationsPermissions.AllUnassignedConversations
                                ]
                            }
                        ]
                    }
                },
                {
                    7, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 7,
                        AssociatedTeams = [teamC],
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions =
                                [
                                    RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
                                    RbacViewConversationsPermissions.AllUnassignedConversations
                                ]
                            }
                        ]
                    }
                },
                {
                    8, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 8,
                        AssociatedTeams = [],
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions =
                                [
                                    RbacViewConversationsPermissions.AssignedToMe,
                                    RbacViewConversationsPermissions.AssignedToMyTeam,
                                    RbacViewConversationsPermissions.AllAssignedConversations,
                                    RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
                                    RbacViewConversationsPermissions.AllUnassignedConversations
                                ]
                            }
                        ]
                    }
                },
            };

            // Act
            var result = _rbacNotificationService
                .GetUnreadRecordsByPermissions(
                    conversationWithNoAssignment,
                    aggregatedStaffs,
                    conversationMessage);

            // Assert
            Assert.That(result, Is.InstanceOf<List<ConversationUnreadRecord>>());
            Assert.That(result, Has.Count.EqualTo(5));

            Assert.Multiple(() =>
            {
                Assert.That(result[0].StaffId, Is.EqualTo(1));
                Assert.That(result[0].NotificationDeliveryType, Is.EqualTo(NotificationDeliveryType.ContactOwner));
            });

            Assert.Multiple(() =>
            {
                Assert.That(result[1].StaffId, Is.EqualTo(2));
                Assert.That(result[1].NotificationDeliveryType, Is.EqualTo(NotificationDeliveryType.TeamAssigned));
            });

            Assert.Multiple(() =>
            {
                Assert.That(result[2].StaffId, Is.EqualTo(3));
                Assert.That(result[2].NotificationDeliveryType, Is.EqualTo(NotificationDeliveryType.TeamAssigned));
            });

            Assert.Multiple(() =>
            {
                Assert.That(result[3].StaffId, Is.EqualTo(5));
                Assert.That(result[3].NotificationDeliveryType, Is.EqualTo(NotificationDeliveryType.TeamAssigned));
            });

            Assert.Multiple(() =>
            {
                Assert.That(result[4].StaffId, Is.EqualTo(8));
                Assert.That(result[4].NotificationDeliveryType, Is.EqualTo(NotificationDeliveryType.TeamAssigned));
            });
        }

        [Test]
        public void ConversationWithCollaboratorAndTeamAssignment()
        {
            const string conversationId = "conversation";
            var conversationWithNoAssignment = new Conversation()
            {
                CompanyId = _companyId,
                Id = conversationId,
                AssignedTeamId = 2,
                AdditionalAssignees =
                [
                    new AdditionalAssignee()
                    {
                        AssigneeId = 3,
                        Assignee = new Staff()
                        {
                            Id = 3
                        }
                    }
                ]
            };

            const int conversationMessageId = 1;

            var conversationMessage = new ConversationMessage()
            {
                CompanyId = _companyId,
                ConversationId = conversationId,
                Id = conversationMessageId,
                Channel = ChannelTypes.WhatsappCloudApi
            };

            var teamA = new TeamAccessControlAggregate()
            {
                Id = 1, TeamMemberStaffIds = [0, 1, 2]
            };

            var teamB = new TeamAccessControlAggregate()
            {

                Id = 2, TeamMemberStaffIds = [1, 3]
            };

            var teamC = new TeamAccessControlAggregate()
            {

                Id = 3, TeamMemberStaffIds = [4]
            };

            var aggregatedStaffs = new Dictionary<long, StaffAccessControlAggregate>
            {

                {
                    0, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 0,
                        AssociatedTeams = [teamA],
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions = [RbacViewConversationsPermissions.AssignedToMe]
                            }
                        ]
                    }
                },
                {
                    1, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 1,
                        AssociatedTeams = [teamB],
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions = [RbacViewConversationsPermissions.AssignedToMe]
                            }
                        ]
                    }
                },
                {
                    2, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 2,
                        AssociatedTeams = [teamB],
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions =
                                [
                                    RbacViewConversationsPermissions.AssignedToMe,
                                    RbacViewConversationsPermissions.AssignedToMyTeam
                                ]
                            }
                        ]
                    }
                },
                {
                    3, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 3,
                        AssociatedTeams = [teamB],
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions = [RbacViewConversationsPermissions.AssignedToMe]
                            }
                        ]
                    }
                },
                {
                    4, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 4,
                        AssociatedTeams = [teamC],
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions =
                                [
                                    RbacViewConversationsPermissions.AssignedToMe,
                                    RbacViewConversationsPermissions.AssignedToMyTeam
                                ]
                            }
                        ]
                    }
                },
                {
                    5, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 5,
                        AssociatedTeams = [teamA],
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions =
                                [
                                    RbacViewConversationsPermissions.AssignedToMe,
                                    RbacViewConversationsPermissions.AssignedToMyTeam,
                                    RbacViewConversationsPermissions.AllAssignedConversations
                                ]
                            }
                        ]
                    }
                },
                {
                    6, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 6,
                        AssociatedTeams = [teamA],
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions =
                                [
                                    RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
                                    RbacViewConversationsPermissions.AllUnassignedConversations
                                ]
                            }
                        ]
                    }
                },
                {
                    7, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 7,
                        AssociatedTeams = [teamC],
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions =
                                [
                                    RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
                                    RbacViewConversationsPermissions.AllUnassignedConversations
                                ]
                            }
                        ]
                    }
                },
                {
                    8, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 8,
                        AssociatedTeams = [],
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions =
                                [
                                    RbacViewConversationsPermissions.AssignedToMe,
                                    RbacViewConversationsPermissions.AssignedToMyTeam,
                                    RbacViewConversationsPermissions.AllAssignedConversations,
                                    RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
                                    RbacViewConversationsPermissions.AllUnassignedConversations
                                ]
                            }
                        ]
                    }
                },
            };

            // Act
            var result = _rbacNotificationService
                .GetUnreadRecordsByPermissions(
                    conversationWithNoAssignment,
                    aggregatedStaffs,
                    conversationMessage);

            // Assert
            Assert.That(result, Is.InstanceOf<List<ConversationUnreadRecord>>());
            Assert.That(result, Has.Count.EqualTo(4));

            Assert.Multiple(() =>
            {
                Assert.That(result[0].StaffId, Is.EqualTo(3));
                Assert.That(result[0].NotificationDeliveryType, Is.EqualTo(NotificationDeliveryType.Collaborator));
            });

            Assert.Multiple(() =>
            {
                Assert.That(result[1].StaffId, Is.EqualTo(2));
                Assert.That(result[1].NotificationDeliveryType, Is.EqualTo(NotificationDeliveryType.TeamAssigned));
            });

            Assert.Multiple(() =>
            {
                Assert.That(result[2].StaffId, Is.EqualTo(5));
                Assert.That(result[2].NotificationDeliveryType, Is.EqualTo(NotificationDeliveryType.TeamAssigned));
            });

            Assert.Multiple(() =>
            {
                Assert.That(result[3].StaffId, Is.EqualTo(8));
                Assert.That(result[3].NotificationDeliveryType, Is.EqualTo(NotificationDeliveryType.TeamAssigned));
            });
        }

        [Test]
        public void ConversationWithAssignTeam()
        {
            const string conversationId = "conversation";
            var conversationWithNoAssignment = new Conversation()
            {
                CompanyId = _companyId,
                Id = conversationId,
                Mentions =
                [
                    new Mention()
                    {
                        MentionedStaffId = 1, CreatedAt = DateTime.UtcNow
                    },
                    new Mention()
                    {
                        MentionedStaffId = 2, CreatedAt = DateTime.UtcNow
                    }
                ],
            };

            const int conversationMessageId = 1;

            var conversationMessage = new ConversationMessage()
            {
                CompanyId = _companyId,
                ConversationId = conversationId,
                Id = conversationMessageId,
                Channel = ChannelTypes.WhatsappCloudApi
            };

            var teamA = new TeamAccessControlAggregate()
            {
                Id = 1, TeamMemberStaffIds = [1, 2, 3]
            };

            var aggregatedStaffs = new Dictionary<long, StaffAccessControlAggregate>
            {

                {
                    1, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 1,
                        AssociatedTeams = [teamA],
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions = [RbacViewConversationsPermissions.AssignedToMe]
                            }
                        ]
                    }
                },
                {
                    2, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 2,
                        AssociatedTeams = [teamA],
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions =
                                [
                                    RbacViewConversationsPermissions.AssignedToMe,
                                    RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam
                                ]
                            }
                        ]
                    }
                },
                {
                    3, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 3,
                        AssociatedTeams = [teamA],
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions =
                                [
                                    RbacViewConversationsPermissions.AssignedToMe,
                                    RbacViewConversationsPermissions.AssignedToMyTeam
                                ]
                            }
                        ]
                    }
                },
            };

            // Act
            var result = _rbacNotificationService
                .GetUnreadRecordsByPermissions(
                    conversationWithNoAssignment,
                    aggregatedStaffs,
                    conversationMessage);

            // Assert
            Assert.That(result, Is.InstanceOf<List<ConversationUnreadRecord>>());
            Assert.That(result, Has.Count.EqualTo(2));

            Assert.Multiple(() =>
            {
                Assert.That(result[0].StaffId, Is.EqualTo(1));
                Assert.That(result[0].NotificationDeliveryType, Is.EqualTo(NotificationDeliveryType.Mentioned));
            });

            Assert.Multiple(() =>
            {
                Assert.That(result[1].StaffId, Is.EqualTo(2));
                Assert.That(result[1].NotificationDeliveryType, Is.EqualTo(NotificationDeliveryType.Mentioned));
            });
        }

        [Test]
        public void DefaultChannel_AllHaveNoAccess()
        {
            const string conversationId = "conversation";
            var conversationWithNoAssignment = new Conversation()
            {
                CompanyId = _companyId,
                Id = conversationId,
                AssignedTeamId = 1,
                AssigneeId = 1,
                ChatHistory =
                [
                    new ConversationMessage()
                    {
                        MessageAssigneeId = 3, Channel = ChannelTypes.Note, UpdatedAt = DateTime.Now
                    }
                ]
            };

            const int conversationMessageId = 1;

            var conversationMessage = new ConversationMessage()
            {
                CompanyId = _companyId,
                ConversationId = conversationId,
                Id = conversationMessageId,
                Channel = ChannelTypes.WhatsappCloudApi,
                ChannelIdentityId = "channel3"
            };

            var teamA = new TeamAccessControlAggregate()
            {
                Id = 1,
                TeamMemberStaffIds = [1, 2, 3],
                TeamDefaultChannels = new TeamDefaultChannels()
                {
                    ChannelTypeToChannelIdentityIds =
                    {
                        {
                            ChannelTypes.WhatsappCloudApi, ["channel1", "channel2"]
                        }
                    }
                }

            };

            var aggregatedStaffs = new Dictionary<long, StaffAccessControlAggregate>
            {

                {
                    1, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 1,
                        AssociatedTeams = [teamA],
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions =
                                [
                                    RbacViewConversationsPermissions.AssignedToMe,
                                    RbacInboxSettings.ViewDefaultChannelMessagesOnly
                                ]
                            }
                        ]
                    }
                },
                {
                    2, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 2,
                        AssociatedTeams = [teamA],
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions =
                                [
                                    RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
                                    RbacInboxSettings.ViewDefaultChannelMessagesOnly
                                ]
                            }
                        ]
                    }
                },
                {
                    3, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 3,
                        AssociatedTeams = [teamA],
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions =
                                [
                                    RbacViewConversationsPermissions.AssignedToMe,
                                    RbacViewConversationsPermissions.AssignedToMyTeam,
                                    RbacInboxSettings.ViewDefaultChannelMessagesOnly
                                ]
                            }
                        ]
                    }
                },
                {
                    4, new StaffAccessControlAggregate()
                    {
                        CompanyId = _companyId,
                        StaffId = 4,
                        AssociatedTeams = [teamA],
                        RbacRoles =
                        [
                            new RbacRole()
                            {
                                SleekflowCompanyId = _companyId,
                                RbacRolePermissions =
                                [
                                    RbacViewConversationsPermissions.AssignedToMe,
                                    RbacViewConversationsPermissions.AssignedToMyTeam,
                                    RbacViewConversationsPermissions.AllAssignedConversations,
                                    RbacInboxSettings.ViewDefaultChannelMessagesOnly
                                ]
                            }
                        ]
                    }
                },
            };

            // Act
            var result = _rbacNotificationService
                .GetUnreadRecordsByPermissions(
                    conversationWithNoAssignment,
                    aggregatedStaffs,
                    conversationMessage);

            // Assert
            Assert.That(result, Is.InstanceOf<List<ConversationUnreadRecord>>());
            Assert.That(result, Has.Count.EqualTo(0));
        }

        [Test]
        public void DefaultChannel_AllHaveAccess()
        {
            const string conversationId = "conversation";
            var conversationWithNoAssignment = new Conversation()
            {
                CompanyId = _companyId,
                Id = conversationId,
                AssignedTeamId = 1,
                AssigneeId = 1,
                Mentions =
                [
                    new Mention()
                    {
                        MentionedStaffId = 3, CreatedAt = DateTime.UtcNow
                    }
                ],
            };

            const int conversationMessageId = 1;
            {
                var conversationMessage = new ConversationMessage()
                {
                    CompanyId = _companyId,
                    ConversationId = conversationId,
                    Id = conversationMessageId,
                    Channel = ChannelTypes.WhatsappCloudApi,
                    ChannelIdentityId = "channel1"
                };

                var teamA = new TeamAccessControlAggregate()
                {
                    Id = 1,
                    TeamMemberStaffIds = [1, 2, 3],
                    TeamDefaultChannels = new TeamDefaultChannels()
                    {
                        ChannelTypeToChannelIdentityIds =
                        {
                            {
                                ChannelTypes.WhatsappCloudApi, ["channel1", "channel2"]
                            }
                        }
                    }

                };

                var aggregatedStaffs = new Dictionary<long, StaffAccessControlAggregate>
                {

                    {
                        1, new StaffAccessControlAggregate()
                        {
                            CompanyId = _companyId,
                            StaffId = 1,
                            AssociatedTeams = [teamA],
                            RbacRoles =
                            [
                                new RbacRole()
                                {
                                    SleekflowCompanyId = _companyId,
                                    RbacRolePermissions =
                                    [
                                        RbacViewConversationsPermissions.AssignedToMe,
                                        RbacInboxSettings.ViewDefaultChannelMessagesOnly
                                    ]
                                }
                            ]
                        }
                    },
                    {
                        2, new StaffAccessControlAggregate()
                        {
                            CompanyId = _companyId,
                            StaffId = 2,
                            AssociatedTeams = [teamA],
                            RbacRoles =
                            [
                                new RbacRole()
                                {
                                    SleekflowCompanyId = _companyId,
                                    RbacRolePermissions =
                                    [
                                        RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
                                        RbacInboxSettings.ViewDefaultChannelMessagesOnly
                                    ]
                                }
                            ]
                        }
                    },
                    {
                        3, new StaffAccessControlAggregate()
                        {
                            CompanyId = _companyId,
                            StaffId = 3,
                            AssociatedTeams = [teamA],
                            RbacRoles =
                            [
                                new RbacRole()
                                {
                                    SleekflowCompanyId = _companyId,
                                    RbacRolePermissions =
                                    [
                                        RbacViewConversationsPermissions.AssignedToMe,
                                        RbacViewConversationsPermissions.AssignedToMyTeam,
                                        RbacInboxSettings.ViewDefaultChannelMessagesOnly
                                    ]
                                }
                            ]
                        }
                    },
                    {
                        4, new StaffAccessControlAggregate()
                        {
                            CompanyId = _companyId,
                            StaffId = 4,
                            AssociatedTeams = [teamA],
                            RbacRoles =
                            [
                                new RbacRole()
                                {
                                    SleekflowCompanyId = _companyId,
                                    RbacRolePermissions =
                                    [
                                        RbacViewConversationsPermissions.AssignedToMe,
                                        RbacViewConversationsPermissions.AssignedToMyTeam,
                                        RbacViewConversationsPermissions.AllAssignedConversations,
                                        RbacInboxSettings.ViewDefaultChannelMessagesOnly
                                    ]
                                }
                            ]
                        }
                    },
                };

                // Act
                var result = _rbacNotificationService
                    .GetUnreadRecordsByPermissions(
                        conversationWithNoAssignment,
                        aggregatedStaffs,
                        conversationMessage);

                // Assert
                Assert.That(result, Is.InstanceOf<List<ConversationUnreadRecord>>());
                Assert.That(result, Has.Count.EqualTo(3));


                Assert.Multiple(() =>
                {
                    Assert.That(result[0].StaffId, Is.EqualTo(1));
                    Assert.That(result[0].NotificationDeliveryType, Is.EqualTo(NotificationDeliveryType.ContactOwner));
                });


                Assert.Multiple(() =>
                {
                    Assert.That(result[1].StaffId, Is.EqualTo(3));
                    Assert.That(result[1].NotificationDeliveryType, Is.EqualTo(NotificationDeliveryType.Mentioned));
                });

                Assert.Multiple(() =>
                {
                    Assert.That(result[2].StaffId, Is.EqualTo(4));
                    Assert.That(result[2].NotificationDeliveryType, Is.EqualTo(NotificationDeliveryType.TeamAssigned));
                });
            }
        }
    }
}