#!/usr/bin/env node

import { ChatGoogleGenerativeAI } from '@langchain/google-genai';
import { Command } from 'commander';
import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { fetchPRInfo, postInlineComment, postSummaryComment } from './github-api.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class CodeReviewApp {
    constructor() {
        this.geminiApiKey = process.env.GEMINI_API_KEY;
        this.githubToken = null;
        this.prUrl = null;
        this.llm = null;
        this.prompts = {};
    }

    async initialize() {
        // Validate environment variables
        if (!this.geminiApiKey) {
            throw new Error('GEMINI_API_KEY environment variable is required');
        }

        // Initialize Gemini LLM
        this.llm = new ChatGoogleGenerativeAI({
            apiKey: this.gemini<PERSON>piKey,
            modelName: "gemini-2.5-flash",
            temperature: 0.5,
            maxOutputTokens: 8192,
        });

        // Load prompt templates
        await this.loadPrompts();
    }

    async loadPrompts() {
        const promptsDir = path.join(__dirname, 'prompts');

        try {
            const overallSummaryPrompt = await fs.readFile(
                path.join(promptsDir, 'overall-summary.txt'),
                'utf-8'
            );
            const inlineCommentsPrompt = await fs.readFile(
                path.join(promptsDir, 'inline-comments.txt'),
                'utf-8'
            );
            const overallSummaryTemplate = await fs.readFile(
                path.join(promptsDir, 'overall-summary-template.txt'),
                'utf-8'
            );
            const inlineCommentsTemplate = await fs.readFile(
                path.join(promptsDir, 'inline-comments-template.txt'),
                'utf-8'
            );

            this.prompts = {
                overallSummary: overallSummaryPrompt,
                inlineComments: inlineCommentsPrompt,
                overallSummaryTemplate: overallSummaryTemplate,
                inlineCommentsTemplate: inlineCommentsTemplate
            };
        } catch (error) {
            throw new Error(`Failed to load prompt templates: ${error.message}`);
        }
    }

    substituteTemplate(template, variables) {
        let result = template;
        for (const [key, value] of Object.entries(variables)) {
            result = result.replace(new RegExp(`{${key}}`, 'g'), value);
        }
        return result;
    }

    async fetchPRData() {
        console.log('📥 Fetching PR information...');

        try {
            const prInfo = await fetchPRInfo(this.prUrl, this.githubToken);
            console.log(`✅ Successfully fetched PR #${prInfo.prData.number}: "${prInfo.prData.title}"`);
            console.log(`📊 Changes: +${prInfo.prData.additions} -${prInfo.prData.deletions} across ${prInfo.files.length} files`);

            return prInfo;
        } catch (error) {
            throw new Error(`Failed to fetch PR data: ${error.message}`);
        }
    }

    formatPRDataForLLM(prInfo) {
        const { prData, files, commits } = prInfo;

        return {
            metadata: {
                title: prData.title,
                description: prData.body || 'No description provided',
                author: prData.author,
                baseRef: prData.baseRef,
                headRef: prData.headRef,
                additions: prData.additions,
                deletions: prData.deletions,
                changedFiles: prData.changedFiles
            },
            files: files.map(file => ({
                filename: file.filename,
                status: file.status,
                additions: file.additions,
                deletions: file.deletions,
                patch: file.patch
            })),
            commits: commits.map(commit => ({
                message: commit.message,
                author: commit.author.name
            }))
        };
    }

    async generateOverallSummary(prData) {
        console.log('🤖 Generating overall PR summary...');

        const commitMessages = prData.commits.map(commit =>
            `- ${commit.message} (by ${commit.author})`
        ).join('\n');

        const fileChanges = prData.files.map(file => `
**${file.filename}** (${file.status})
- Changes: +${file.additions} -${file.deletions}

\`\`\`diff
${file.patch || 'No diff available'}
\`\`\`
`).join('\n');

        const templateVariables = {
            basePrompt: this.prompts.overallSummary,
            title: prData.metadata.title,
            description: prData.metadata.description,
            author: prData.metadata.author,
            headRef: prData.metadata.headRef,
            baseRef: prData.metadata.baseRef,
            additions: prData.metadata.additions,
            deletions: prData.metadata.deletions,
            changedFiles: prData.metadata.changedFiles,
            commitMessages: commitMessages,
            fileChanges: fileChanges
        };

        const prompt = this.substituteTemplate(this.prompts.overallSummaryTemplate, templateVariables);

        try {
            const response = await this.llm.invoke(prompt);
            const summary = response.content;
            console.log('✅ Overall summary generated successfully');
            return summary;
        } catch (error) {
            throw new Error(`Failed to generate overall summary: ${error.message}`);
        }
    }

    async generateInlineComments(prData) {
        console.log('🔍 Generating inline comments...');

        const allComments = [];

        for (const file of prData.files) {
            if (!file.patch) {
                console.log(`⏭️  Skipping ${file.filename} (no diff available)`);
                continue;
            }

            const templateVariables = {
                basePrompt: this.prompts.inlineComments,
                filePath: file.filename,
                status: file.status,
                additions: file.additions,
                deletions: file.deletions,
                patch: file.patch
            };

            const prompt = this.substituteTemplate(this.prompts.inlineCommentsTemplate, templateVariables);

            try {
                const response = await this.llm.invoke(prompt);
                let comments = [];

                try {
                    // Try to parse JSON response
                    const content = response.content.trim();
                    const jsonMatch = content.match(/\[[\s\S]*\]/);
                    if (jsonMatch) {
                        comments = JSON.parse(jsonMatch[0]);
                    }
                } catch (parseError) {
                    console.log(`⚠️  Failed to parse JSON for ${file.filename}, skipping inline comments`);
                    continue;
                }

                if (Array.isArray(comments) && comments.length > 0) {
                    allComments.push(...comments);
                    console.log(`✅ Generated ${comments.length} inline comments for ${file.filename}`);
                } else {
                    console.log(`✅ No issues found in ${file.filename}`);
                }
            } catch (error) {
                console.log(`⚠️  Error analyzing ${file.filename}: ${error.message}`);
                continue;
            }
        }

        console.log(`✅ Generated ${allComments.length} total inline comments`);
        return allComments;
    }

    async postOverallSummary(summary) {
        console.log('📤 Posting overall summary to PR...');

        try {
            const result = await postSummaryComment(this.prUrl, summary, this.githubToken);
            console.log(`✅ Posted overall summary: ${result.htmlUrl}`);
            return result;
        } catch (error) {
            throw new Error(`Failed to post overall summary: ${error.message}`);
        }
    }

    async postInlineComments(comments) {
        console.log(`📤 Posting ${comments.length} inline comments...`);

        const results = [];
        let successCount = 0;
        let failureCount = 0;

        for (const comment of comments) {
            try {
                const result = await postInlineComment(
                    this.prUrl,
                    comment.filePath,
                    comment.lineNumber,
                    `**${comment.severity.toUpperCase()}**: ${comment.comment}`,
                    this.githubToken
                );
                results.push(result);
                successCount++;
                console.log(`✅ Posted comment on ${comment.filePath}:${comment.lineNumber}`);
            } catch (error) {
                failureCount++;
                console.log(`⚠️  Failed to post comment on ${comment.filePath}:${comment.lineNumber}: ${error.message}`);
            }
        }

        console.log(`✅ Posted ${successCount}/${comments.length} inline comments successfully`);
        if (failureCount > 0) {
            console.log(`⚠️  Failed to post ${failureCount} comments`);
        }

        return results;
    }

    async run(githubToken, prUrl) {
        this.githubToken = githubToken;
        this.prUrl = prUrl;

        try {
            console.log('🚀 Starting automated code review...');
            console.log(`📋 PR URL: ${prUrl}`);

            // Initialize the application
            await this.initialize();

            // Step 1: Fetch PR data
            const prInfo = await this.fetchPRData();
            const formattedData = this.formatPRDataForLLM(prInfo);

            // Step 2: Generate overall summary
            const overallSummary = await this.generateOverallSummary(formattedData);

            // Step 3: Post overall summary
            await this.postOverallSummary(overallSummary);

            // Step 4: Generate inline comments
            const inlineComments = await this.generateInlineComments(formattedData);

            // Step 5: Post inline comments
            if (inlineComments.length > 0) {
                await this.postInlineComments(inlineComments);
            } else {
                console.log('✅ No inline comments to post');
            }

            console.log('🎉 Code review completed successfully!');
        } catch (error) {
            console.error('❌ Code review failed:', error.message);
            process.exit(1);
        }
    }
}

// CLI setup
const program = new Command();
program
    .name('code-review')
    .description('Automated code review using LangChain and Google Gemini')
    .version('1.0.0')
    .requiredOption('-t, --github-token <token>', 'GitHub personal access token')
    .requiredOption('-p, --pr-url <url>', 'GitHub pull request URL')
    .action(async (options) => {
        const app = new CodeReviewApp();
        await app.run(options.githubToken, options.prUrl);
    });

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});

// Parse command line arguments
program.parse();

// Export the CodeReviewApp class for programmatic usage
export { CodeReviewApp };
