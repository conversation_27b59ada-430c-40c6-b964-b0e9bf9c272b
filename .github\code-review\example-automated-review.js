#!/usr/bin/env node

/**
 * Example usage of the automated code review application
 * This script demonstrates how to use the CodeReviewApp class programmatically
 */

import { CodeReviewApp } from './index.js';

// Example 1: Basic usage with environment variables
async function basicExample() {
    console.log('🔧 Example 1: Basic automated review');

    const app = new CodeReviewApp();

    // These would typically come from command line arguments or environment variables
    const githubToken = process.env.GITHUB_TOKEN;
    const prUrl = 'https://github.com/owner/repo/pull/123';

    if (!githubToken) {
        console.error('❌ GITHUB_TOKEN environment variable is required');
        return;
    }

    if (!process.env.GEMINI_API_KEY) {
        console.error('❌ GEMINI_API_KEY environment variable is required');
        return;
    }

    try {
        await app.run(githubToken, prUrl);
        console.log('✅ Review completed successfully!');
    } catch (error) {
        console.error('❌ Review failed:', error.message);
    }
}

// Example 2: Batch processing multiple PRs
async function batchExample() {
    console.log('🔧 Example 2: Batch processing multiple PRs');

    const githubToken = process.env.GITHUB_TOKEN;
    const prUrls = [
        'https://github.com/owner/repo/pull/123',
        'https://github.com/owner/repo/pull/124',
        'https://github.com/owner/repo/pull/125'
    ];

    if (!githubToken || !process.env.GEMINI_API_KEY) {
        console.error('❌ Required environment variables not set');
        return;
    }

    for (const prUrl of prUrls) {
        console.log(`\n🔍 Processing ${prUrl}...`);

        const app = new CodeReviewApp();

        try {
            await app.run(githubToken, prUrl);
            console.log(`✅ Completed review for ${prUrl}`);
        } catch (error) {
            console.error(`❌ Failed to review ${prUrl}:`, error.message);
        }

        // Add delay between requests to respect rate limits
        await new Promise(resolve => setTimeout(resolve, 2000));
    }
}

// Example 3: Custom configuration
async function customConfigExample() {
    console.log('🔧 Example 3: Custom configuration (conceptual)');

    // Note: This example shows how you might extend the application
    // for custom configurations. The current implementation doesn't
    // support all these options directly, but could be extended.

    console.log(`
📋 Custom Configuration Options:

1. **Custom Prompts**: Edit files in prompts/ directory
   - overall-summary.txt: Modify overall review format
   - inline-comments.txt: Customize inline comment generation

2. **Environment Variables**:
   - GEMINI_API_KEY: Your Google Gemini API key
   - GITHUB_TOKEN: Your GitHub personal access token (or pass via CLI)

3. **CLI Usage**:
   node index.js --github-token <token> --pr-url <url>

4. **Programmatic Usage**:
   import { CodeReviewApp } from './index.js';
   const app = new CodeReviewApp();
   await app.run(githubToken, prUrl);
  `);
}

// Example 4: Error handling patterns
async function errorHandlingExample() {
    console.log('🔧 Example 4: Error handling patterns');

    const app = new CodeReviewApp();
    const githubToken = 'invalid_token';
    const prUrl = 'https://github.com/owner/repo/pull/999';

    try {
        await app.run(githubToken, prUrl);
    } catch (error) {
        console.log('📚 Common error patterns:');

        if (error.message.includes('GEMINI_API_KEY')) {
            console.log('- Set GEMINI_API_KEY environment variable');
        }

        if (error.message.includes('Invalid GitHub token')) {
            console.log('- Check GitHub token permissions');
        }

        if (error.message.includes('Pull request not found')) {
            console.log('- Verify PR URL is correct and accessible');
        }

        if (error.message.includes('Failed to parse JSON')) {
            console.log('- Check prompt templates for correct format');
        }

        console.log(`\n❌ Actual error: ${error.message}`);
    }
}

// Main function to run examples
async function main() {
    const example = process.argv[2] || 'help';

    switch (example) {
        case 'basic':
            await basicExample();
            break;
        case 'batch':
            await batchExample();
            break;
        case 'config':
            await customConfigExample();
            break;
        case 'errors':
            await errorHandlingExample();
            break;
        case 'help':
        default:
            console.log(`
🚀 Automated Code Review Examples

Usage: node example-automated-review.js <example>

Available examples:
  basic    - Basic automated review of a single PR
  batch    - Process multiple PRs in sequence
  config   - Show custom configuration options
  errors   - Demonstrate error handling patterns
  help     - Show this help message

Prerequisites:
  - Set GEMINI_API_KEY environment variable
  - Set GITHUB_TOKEN environment variable (for basic/batch examples)
  - Ensure you have valid PR URLs to test with

Example:
  export GEMINI_API_KEY="your_key_here"
  export GITHUB_TOKEN="your_token_here"
  node example-automated-review.js basic
      `);
            break;
    }
}

// Run the example
main().catch(console.error);
