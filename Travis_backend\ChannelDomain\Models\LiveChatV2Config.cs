using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Travis_backend.ChannelDomain.Models.Interfaces;
using Travis_backend.CommonDomain.Models;
using Travis_backend.Constants;

namespace Travis_backend.ChannelDomain.Models;

public class LiveChatV2Config : DateAuditedEntity<long>, IMessagingChannel
{
    [MaxLength(400)]
    public string CompanyId { get; set; }

    public bool IsDeleted { get; set; } = false;

    public string Settings { get; set; } = null;

    [NotMapped]
    public string ChannelType => ChannelTypes.LiveChatV2;

    // It is the Id of the LiveChatV2Config
    [MaxLength(400)]
    public string ChannelIdentityId { get; set; }

    public string ChannelDisplayName { get; set; }
}