using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using CsvHelper;
using CsvHelper.Configuration;
using Force.DeepCloner;
using GraphApi.Client.Const.WhatsappCloudApi;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.Apis.MessagingHub.Api;
using Sleekflow.Apis.MessagingHub.Client;
using Sleekflow.Apis.MessagingHub.Model;
using Travis_backend.AutomationDomain.Models;
using Travis_backend.Cache;
using Travis_backend.Constants;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Exceptions;
using Travis_backend.Extensions;
using Travis_backend.Helpers;
using Travis_backend.InternalDomain.Models;
using Travis_backend.InternalDomain.ViewModels;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.ViewModels;
using WABA360Dialog.ApiClient.Payloads.Converters;
using WABA360Dialog.ApiClient.Payloads.Enums;
using WABA360Dialog.Common.Converters;
using TargetedChannelModel = Travis_backend.AutomationDomain.Models.TargetedChannelModel;

namespace Travis_backend.InternalDomain.Services;

public interface IInternalWhatsappCloudApiService
{
    Task<GetManagementWhatsappCloudApiBusinessBalancesOutput> GetInternalBusinessBalances(string sleekflowCompanyId);

    Task<GetAllManagementWhatsappCloudApiBusinessBalancesOutput> GetAllWhatsappCloudApiBusinessBalances();

    Task<GetAllManagementWhatsappCloudApiWabasOutput> GetAllWhatsappCloudApiWabas();

    Task<UpdateWhatsappCloudApiBusinessBalanceMarkupProfileOutputOutput>
        UpdateWhatsappCloudApiBusinessBalanceMarkupProfile(
            string businessBalanceId,
            MarkupProfile markupProfile,
            DateTime? lastRecalculateBusinessBalanceTransactionLogTimestamp,
            string sleekflowStaffId);

    Task<GetManagementWhatsappCloudApiConversationUsageAnalyticOutput> GetWhatsappCloudApiConversationUsageAnalytic(
        string facebookBusinessId,
        DateTime start,
        DateTime end,
        string granularity);

    Task<GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogsOutput>
        GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogs(
            BusinessBalanceTransactionLogFilter businessBalanceTransactionLogFilter,
            int limit = 1000,
            string continuationToken = null);

    Task<Migrated360DialogAssignmentRulesResponse> Migrate360DialogAssignmentRulesAsync(string companyId);

    Task<MigratedWhatsapp360DialogDefaultChannelResponse> MigrateWhatsapp360DialogDefaultChannelAsync(string companyId);

    Task<MigratedWhatsapp360DialogLastChannelResponse> MigrateWhatsapp360DialogLastChannelAsync(string companyId);

    Task MigrateWhatsapp360DialogConversationMessagesAsync(
        string companyId,
        bool isConfigExist = true);

    Task<List<string>> PatchBusinessBalanceMarkupProfile(
        List<string> whiteListSleekflowCompanyIds,
        MarkupProfile markupProfile,
        DateTimeOffset effectiveDate,
        string sleekflowStaffId);

    Task<GetAllManagementWhatsappCloudApiConversationUsageAnalyticsOutput>
        GetAllWhatsappCloudApiConversationUsageAnalytics(DateTime start, DateTime end, string granularity);

    Task<(List<InternalByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsDto> ByBusiness,
          List<InternalByWabaWhatsappCloudApiDailyConversationUsageAnalyticsDto> ByWaba)>
        GetAllWhatsappCloudApiConversationUsageAnalyticsInternal(DateTime start, DateTime end, string granularity);

    Task<DisassociateFacebookBusinessAccountFromCompanyOutputOutput>
        DisassociateFacebookBusinessAccountFromCompany(
            string sleekflowCompanyId,
            string facebookBusinessId,
            string sleekflowStaffId);


    Task<GetPhoneNumberWhatsappBusinessProfileOutputOutput> GetPhoneNumberWhatsappBusinessProfileAsync(
        string sleekflowCompanyId,
        string messagingHubWabaId,
        string messagingHubPhoneNumberId);

    Task<UpdatePhoneNumberWhatsappBusinessProfileOutputOutput> UpdatePhoneNumberWhatsappBusinessProfileAsync(
        string sleekflowCompanyId,
        string messagingHubWabaId,
        string messagingHubPhoneNumberId,
        string sleekflowStaffId,
        UpdatePhoneNumberBusinessProfileRequest updatePhoneNumberBusinessProfileRequest);

    Task<UpdatePhoneNumberSettingsOutputOutput> UpdatePhoneNumberSettingsAsync(
        string sleekflowCompanyId,
        string messagingHubWabaId,
        string messagingHubPhoneNumberId,
        StorageConfiguration storageConfiguration,
        string sleekflowStaffId,
        CancellationToken cancellationToken = default);

    Task<GetAuditLogsOutputOutput> GetAuditLogsAsync(
        List<string> auditingOperations,
        string sleekflowCompanyId,
        bool? hasCloudApiAuditsException,
        string continuationToken,
        int limit = 100,
        CancellationToken cancellationToken = default);

    Task<List<WhatsAppCloudApiMonthlySummarizedConversationUsageAnalytic>>
        GetMonthlyWhatsappCloudApiConversationUsageAnalytics(DateTime start, DateTime end);

    Task<(List<InternalByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsDto> ByBusiness,
          List<InternalByWabaWhatsappCloudApiDailyConversationUsageAnalyticsDto> ByWaba)>
        GetInternalWhatsappCloudApiConversationUsageAnalytics(DateTime start, DateTime end);
}

public class InternalWhatsappCloudApiService : IInternalWhatsappCloudApiService
{
    private ILogger<InternalWhatsappCloudApiService> _logger;
    private readonly IBalancesApi _balancesApi;
    private readonly IManagementsApi _managementsApi;
    private readonly ITemplatesApi _templatesApi;
    private readonly ApplicationDbContext _appDbContext;
    private readonly IMapper _mapper;
    private readonly IChannelsApi _channelsApi;
    private readonly IAzureBlobStorageService _azureBlobStorageService;
    private readonly ICacheManagerService _cacheManagerService;

    public InternalWhatsappCloudApiService(
        IBalancesApi balancesApi,
        ILogger<InternalWhatsappCloudApiService> logger,
        IManagementsApi managementsApi,
        ITemplatesApi templatesApi,
        ApplicationDbContext appDbContext,
        IMapper mapper,
        IChannelsApi channelsApi,
        IAzureBlobStorageService azureBlobStorageService,
        ICacheManagerService cacheManagerService)
    {
        _balancesApi = balancesApi;
        _logger = logger;
        _managementsApi = managementsApi;
        _templatesApi = templatesApi;
        _appDbContext = appDbContext;
        _mapper = mapper;
        _channelsApi = channelsApi;
        _azureBlobStorageService = azureBlobStorageService;
        _cacheManagerService = cacheManagerService;
    }

    public async Task<GetManagementWhatsappCloudApiBusinessBalancesOutput> GetInternalBusinessBalances(
        string sleekflowCompanyId)
    {
        var result = await _managementsApi.ManagementsGetManagementWhatsappCloudApiBusinessBalancesPostAsync(
            getManagementWhatsappCloudApiBusinessBalancesInput: new GetManagementWhatsappCloudApiBusinessBalancesInput(
                sleekflowCompanyId));

        return result.Data;
    }

    public async Task<GetAllManagementWhatsappCloudApiBusinessBalancesOutput> GetAllWhatsappCloudApiBusinessBalances()
    {
        var result =
            await _managementsApi.ManagementsGetAllManagementWhatsappCloudApiBusinessBalancesPostAsync(
                body: new
                {
                });

        return result.Data;
    }

    public async Task<GetAllManagementWhatsappCloudApiWabasOutput> GetAllWhatsappCloudApiWabas()
    {
        var result = await _managementsApi.ManagementsGetAllManagementWhatsappCloudApiWabasPostAsync(
            body: new
            {
            });

        return result.Data;
    }

    public async Task<GetManagementWhatsappCloudApiConversationUsageAnalyticOutput>
        GetWhatsappCloudApiConversationUsageAnalytic(
            string facebookBusinessId,
            DateTime start,
            DateTime end,
            string granularity)
    {
        return (await _managementsApi.ManagementsGetManagementWhatsappCloudApiConversationUsageAnalyticPostAsync(
            getManagementWhatsappCloudApiConversationUsageAnalyticInput: new
                GetManagementWhatsappCloudApiConversationUsageAnalyticInput(
                    facebookBusinessId,
                    start,
                    end,
                    granularity))).Data;
    }

    public async Task<GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogsOutput>
        GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogs(
            BusinessBalanceTransactionLogFilter businessBalanceTransactionLogFilter,
            int limit = 1000,
            string continuationToken = null)
    {
        return (await _managementsApi
            .ManagementsGetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogsPostAsync(
                getFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogsInput: new
                    GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogsInput(
                        businessBalanceTransactionLogFilter,
                        limit,
                        continuationToken))).Data;
    }

    public async Task<UpdateWhatsappCloudApiBusinessBalanceMarkupProfileOutputOutput>
        UpdateWhatsappCloudApiBusinessBalanceMarkupProfile(
            string businessBalanceId,
            MarkupProfile markupProfile,
            DateTime? lastRecalculateBusinessBalanceTransactionLogTimestamp,
            string sleekflowStaffId)
    {
        UpdateWhatsappCloudApiBusinessBalanceMarkupProfileInput input;

        if (lastRecalculateBusinessBalanceTransactionLogTimestamp == null)
        {
            input = new UpdateWhatsappCloudApiBusinessBalanceMarkupProfileInput(
                businessBalanceId,
                markupProfile,
                false,
                null,
                sleekflowStaffId);
        }
        else
        {
            input = new UpdateWhatsappCloudApiBusinessBalanceMarkupProfileInput(
                businessBalanceId,
                markupProfile,
                true,
                DateTimeOffset.FromUnixTimeSeconds(
                    lastRecalculateBusinessBalanceTransactionLogTimestamp.Value
                        .ToUnixTime()),
                sleekflowStaffId);
        }

        var result = await _managementsApi.ManagementsUpdateWhatsappCloudApiBusinessBalanceMarkupProfilePostAsync(
            updateWhatsappCloudApiBusinessBalanceMarkupProfileInput: input);

        return result;
    }

    public async Task<Migrated360DialogAssignmentRulesResponse> Migrate360DialogAssignmentRulesAsync(string companyId)
    {
        var response = new Migrated360DialogAssignmentRulesResponse();

        try
        {
            var company360Channels = await _appDbContext.ConfigWhatsApp360DialogConfigs.AsNoTracking()
                .Where(x => x.CompanyId == companyId).ToListAsync();

            if (company360Channels != null && company360Channels.Any())
            {
                var allCloudApiConfigs = await _appDbContext.ConfigWhatsappCloudApiConfigs.AsNoTracking()
                    .Where(x => x.CompanyId == companyId)
                    .OrderByDescending(x => x.UpdatedAt)
                    .ToListAsync();

                // Migrate Last Channel Condition
                if (allCloudApiConfigs?.Any() == true)
                {
                    var cloudApiWhatsAppPhoneNumberList =
                        allCloudApiConfigs.Select(x => x.WhatsappPhoneNumber).Distinct().ToList();

                    var whatsApp360IdDict = company360Channels
                        .Where(x => cloudApiWhatsAppPhoneNumberList.Contains(x.WhatsAppPhoneNumber))
                        .ToDictionary(x => x.Id.ToString(), x => x.WhatsAppPhoneNumber);

                    var companyAssignmentRules = await _appDbContext.CompanyAssignmentRules
                        .Where(x => x.CompanyId == companyId).ToListAsync();

                    if (companyAssignmentRules != null)
                    {
                        companyAssignmentRules = companyAssignmentRules.Where(x => x.Conditions != null &&
                            JsonConvert.SerializeObject(x.Conditions).Contains("LastChannel") &&
                            x.TargetedChannelWithIds != null && JsonConvert
                                .SerializeObject(x.TargetedChannelWithIds)
                                .Contains(ChannelTypes.Whatsapp360Dialog)).ToList();
                    }

                    var migratedConditionRuleIds = new HashSet<long>();

                    foreach (var rule in companyAssignmentRules)
                    {
                        var targetChannelIds = rule.TargetedChannelWithIds
                            .Where(x => x.channel == ChannelTypes.Whatsapp360Dialog)
                            .ToList();
                        var updatedCloudApiTargetIds = new HashSet<string>();

                        targetChannelIds.ForEach(x =>
                        {
                            foreach (var id in whatsApp360IdDict.Keys)
                            {
                                if (x.ids.Contains(id))
                                {
                                    updatedCloudApiTargetIds.Add(whatsApp360IdDict[id]);
                                    x.ids.Remove(id);
                                }
                            }
                        });

                        if (updatedCloudApiTargetIds.Any())
                        {
                            if (rule.TargetedChannelWithIds.Any(x => x.channel == ChannelTypes.WhatsappCloudApi))
                            {
                                foreach (var cloudApiTargetId in updatedCloudApiTargetIds)
                                {
                                    rule.TargetedChannelWithIds
                                        .FirstOrDefault(x => x.channel == ChannelTypes.WhatsappCloudApi)
                                        .ids.Add(cloudApiTargetId);
                                }
                            }
                            else
                            {
                                rule.TargetedChannelWithIds.Add(
                                    new TargetedChannelModel()
                                    {
                                        channel = ChannelTypes.WhatsappCloudApi, ids = updatedCloudApiTargetIds.ToList()
                                    });
                            }

                            migratedConditionRuleIds.Add(rule.Id);

                            rule.TargetedChannelWithIds.RemoveAll(x => x.channel == ChannelTypes.Whatsapp360Dialog);

                            if (targetChannelIds.Any(x => x.channel == ChannelTypes.Whatsapp360Dialog && x.ids.Any()))
                            {
                                rule.TargetedChannelWithIds.AddRange(targetChannelIds);
                            }

                            _appDbContext.Entry(rule).Property(x => x.TargetedChannelWithIds).IsModified = true;
                            await _appDbContext.SaveChangesAsync();
                        }
                    }

                    response.MigratedAssignmentRuleConditionsCount = migratedConditionRuleIds.Count;

                    response.MigratedAssignmentConditionNames = companyAssignmentRules
                        .Where(x => migratedConditionRuleIds.Contains(x.Id)).Select(x => x.AssignmentRuleName).ToList();
                }
                else
                {
                    return response;
                }

                // Migrate Send 360 Dialog Message Action Payload
                var channelWabaIds = company360Channels.Select(x => x.WabaAccountId).Distinct().ToList();

                var whatsAppPhoneNumberList =
                    allCloudApiConfigs.Select(x => x.WhatsappPhoneNumber).Distinct().ToList();

                var whatsApp360ConfigIdDict = company360Channels
                    .Where(x => whatsAppPhoneNumberList.Contains(x.WhatsAppPhoneNumber))
                    .ToDictionary(x => x.Id.ToString(), x => x.WhatsAppPhoneNumber);

                var send360MessageActions = await _appDbContext.CompanyAutomationActions
                    .Where(x => x.CompanyId == companyId &&
                                (x.AutomatedTriggerType == AutomatedTriggerType.SendMessage ||
                                 x.AutomatedTriggerType == AutomatedTriggerType.SendMedia))
                    .ToListAsync();

                if (send360MessageActions != null)
                {
                    send360MessageActions = send360MessageActions.Where(x =>
                        (x.WhatsApp360DialogExtendedAutomationMessages != null &&
                         x.WhatsApp360DialogExtendedAutomationMessages.Select(y => y.WabaAccountId).ToList()
                             .Any(y => channelWabaIds.Contains(y))) ||
                        (x.TargetedChannelWithIds != null && x.TargetedChannelWithIds.Any(y =>
                            y.channel == ChannelTypes.Whatsapp360Dialog &&
                            y.ids.Any(z => whatsApp360ConfigIdDict.Keys.Contains(z))))).ToList();
                }

                var dict = new Dictionary<string, GetWhatsappCloudApiTemplatesOutputOutput>();

                foreach (var config in allCloudApiConfigs.DistinctBy(x => x.MessagingHubWabaId))
                {
                    try
                    {
                        GetWhatsappCloudApiTemplatesOutputOutput templatesResponse =
                            await _templatesApi.TemplatesGetWhatsappCloudApiTemplatesPostAsync(
                                getWhatsappCloudApiTemplatesInput: new GetWhatsappCloudApiTemplatesInput(
                                    config.MessagingHubWabaId,
                                    companyId));
                        dict.Add(config.MessagingHubWabaId, templatesResponse);
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(
                            e,
                            "Get TemplatesGetWhatsappCloudApiTemplatesPostAsync Exception with {MessagingHubWabaId}",
                            config.MessagingHubWabaId);
                    }
                }

                var migratedActionIds = new HashSet<long>();

                foreach (var action in send360MessageActions)
                {
                    // migrate send message action
                    if (action.TargetedChannelWithIds != null && action.TargetedChannelWithIds.Any(x =>
                            x.channel == ChannelTypes.Whatsapp360Dialog &&
                            x.ids.Any(y => whatsApp360ConfigIdDict.Keys.Contains(y))))
                    {
                        var targetChannelIds = action.TargetedChannelWithIds
                            .Where(x => x.channel == ChannelTypes.Whatsapp360Dialog)
                            .ToList();
                        var updatedCloudApiTargetIds = new HashSet<string>();

                        targetChannelIds.ForEach(x =>
                        {
                            foreach (var id in whatsApp360ConfigIdDict.Keys)
                            {
                                if (x.ids.Contains(id))
                                {
                                    updatedCloudApiTargetIds.Add(whatsApp360ConfigIdDict[id]);
                                    x.ids.Remove(id);
                                }
                            }
                        });

                        if (updatedCloudApiTargetIds.Any())
                        {
                            action.TargetedChannelWithIds.Add(
                                new TargetedChannelModel()
                                {
                                    channel = ChannelTypes.WhatsappCloudApi, ids = updatedCloudApiTargetIds.ToList()
                                });

                            migratedActionIds.Add(action.Id);

                            action.TargetedChannelWithIds.RemoveAll(x => x.channel == ChannelTypes.Whatsapp360Dialog);

                            if (targetChannelIds.Any(x => x.channel == ChannelTypes.Whatsapp360Dialog && x.ids.Any()))
                            {
                                action.TargetedChannelWithIds.AddRange(targetChannelIds);
                            }

                            _appDbContext.Entry(action).Property(x => x.TargetedChannelWithIds).IsModified = true;
                            await _appDbContext.SaveChangesAsync();
                        }
                    }

                    if (action.WhatsApp360DialogExtendedAutomationMessages != null &&
                        action.WhatsApp360DialogExtendedAutomationMessages.Any() && action
                            .WhatsApp360DialogExtendedAutomationMessages.Select(y => y.WabaAccountId).ToList()
                            .Any(y => channelWabaIds.Contains(y)))
                    {
                        foreach (var sendMessage in action.WhatsApp360DialogExtendedAutomationMessages)
                        {
                            var phoneNumber = company360Channels
                                .Where(x => x.WabaAccountId == sendMessage.WabaAccountId)
                                .OrderByDescending(x => x.CreatedAt).Select(x => x.WhatsAppPhoneNumber)
                                .FirstOrDefault();

                            if (!string.IsNullOrEmpty(phoneNumber))
                            {
                                var cloudApiConfigs = allCloudApiConfigs
                                    .FirstOrDefault(x =>
                                        x.CompanyId == companyId && x.WhatsappPhoneNumber == phoneNumber);

                                if (cloudApiConfigs == null)
                                {
                                    continue;
                                }

                                var messagingHubWabaId = cloudApiConfigs.MessagingHubWabaId;

                                switch (sendMessage.MessageType)
                                {
                                    case "template":
                                        GetWhatsappCloudApiTemplatesOutputOutput templatesResponse =
                                            new GetWhatsappCloudApiTemplatesOutputOutput();

                                        try
                                        {
                                            templatesResponse = dict[messagingHubWabaId];
                                        }
                                        catch (Exception ex)
                                        {
                                            _logger.LogError(
                                                ex,
                                                "Something goes wrong when fetching templates for messagingHubWabaId {MessagingHubWabaId} in migrating 360 dialog send message automation action id {AutomationActionId}",
                                                messagingHubWabaId,
                                                action.Id);

                                            continue;
                                        }

                                        if (templatesResponse.Success && templatesResponse.Data != null &&
                                            templatesResponse.Data.MessageTemplates != null &&
                                            templatesResponse.Data.MessageTemplates.Any())
                                        {
                                            var cloudApiTemplate =
                                                templatesResponse.Data.MessageTemplates.FirstOrDefault(x =>
                                                    x.Name == sendMessage.Whatsapp360DialogTemplateMessage
                                                        .TemplateName &&
                                                    x.Language == sendMessage.Whatsapp360DialogTemplateMessage
                                                        .Language
                                                        .GetString());

                                            if (action.ExtendedAutomationMessage == null)
                                            {
                                                action.ExtendedAutomationMessage = new ExtendedAutomationMessage()
                                                {
                                                    Channel = ChannelTypes.WhatsappCloudApi, MessageType = "template"
                                                };

                                                action.ExtendedAutomationMessage
                                                        .WhatsappCloudApiByWabaExtendedAutomationMessages =
                                                    new List<WhatsappCloudApiByWabaExtendedAutomationMessage>();
                                            }

                                            action.ExtendedAutomationMessage
                                                .WhatsappCloudApiByWabaExtendedAutomationMessages.Add(
                                                    new WhatsappCloudApiByWabaExtendedAutomationMessage()
                                                    {
                                                        MessagingHubWabaId = messagingHubWabaId,
                                                        ExtendedMessagePayloadDetail =
                                                            new ExtendedMessagePayloadDetail()
                                                            {
                                                                WhatsappCloudApiTemplateMessageObject =
                                                                    new WhatsappCloudApiTemplateMessageViewModel()
                                                                    {
                                                                        TemplateName = cloudApiTemplate.Name,
                                                                        Language = cloudApiTemplate.Language
                                                                    }
                                                            }
                                                    });

                                            foreach (var component in cloudApiTemplate.Components)
                                            {
                                                if (sendMessage.Whatsapp360DialogTemplateMessage.Components != null)
                                                {
                                                    if (sendMessage.Whatsapp360DialogTemplateMessage.Components.Any(x =>
                                                            x.Type.GetString() == component.Type.ToLower()))
                                                    {
                                                        var templateComponentObject =
                                                            new WhatsappCloudApiTemplateMessageComponentObject()
                                                            {
                                                                Type = component.Type,
                                                                Parameters = new List<WhatsappCloudApiParameterObject>()
                                                            };

                                                        foreach (var parameterObject in action
                                                                     .WhatsApp360DialogExtendedAutomationMessages
                                                                     .FirstOrDefault(x =>
                                                                         x.Whatsapp360DialogTemplateMessage
                                                                             .TemplateName ==
                                                                         sendMessage
                                                                             .Whatsapp360DialogTemplateMessage
                                                                             .TemplateName &&
                                                                         x.Whatsapp360DialogTemplateMessage.Language
                                                                             .GetString() ==
                                                                         sendMessage
                                                                             .Whatsapp360DialogTemplateMessage
                                                                             .Language.GetString())
                                                                     .Whatsapp360DialogTemplateMessage.Components
                                                                     .FirstOrDefault(x =>
                                                                         x.Type.GetString() ==
                                                                         component.Type.ToLower())
                                                                     .Parameters)
                                                        {
                                                            switch (parameterObject.Type)
                                                            {
                                                                case WABA360Dialog.ApiClient.Payloads.Enums
                                                                    .ParameterType
                                                                    .text:
                                                                    templateComponentObject.Parameters.Add(
                                                                        new WhatsappCloudApiParameterObject()
                                                                        {
                                                                            Type = "text", Text = parameterObject.Text
                                                                        });

                                                                    break;

                                                                case WABA360Dialog.ApiClient.Payloads.Enums
                                                                    .ParameterType
                                                                    .image:
                                                                    templateComponentObject.Parameters.Add(
                                                                        new WhatsappCloudApiParameterObject()
                                                                        {
                                                                            Type = "image",
                                                                            Image = new WhatsappCloudApiMediaObject()
                                                                            {
                                                                                Id = parameterObject.Image.Id,
                                                                                Link = parameterObject.Image.Link,
                                                                                Caption = parameterObject.Image.Caption
                                                                            }
                                                                        });

                                                                    break;

                                                                case WABA360Dialog.ApiClient.Payloads.Enums
                                                                    .ParameterType
                                                                    .video:
                                                                    templateComponentObject.Parameters.Add(
                                                                        new WhatsappCloudApiParameterObject()
                                                                        {
                                                                            Type = "video",
                                                                            Video = new WhatsappCloudApiMediaObject()
                                                                            {
                                                                                Id = parameterObject.Video.Id,
                                                                                Link = parameterObject.Video.Link,
                                                                                Caption = parameterObject.Video.Caption
                                                                            }
                                                                        });

                                                                    break;

                                                                case WABA360Dialog.ApiClient.Payloads.Enums
                                                                    .ParameterType
                                                                    .document:
                                                                    templateComponentObject.Parameters.Add(
                                                                        new WhatsappCloudApiParameterObject()
                                                                        {
                                                                            Type = "document",
                                                                            Document = new WhatsappCloudApiMediaObject()
                                                                            {
                                                                                Id = parameterObject.Document.Id,
                                                                                Link = parameterObject.Document.Link,
                                                                                Caption = parameterObject.Document
                                                                                    .Caption,
                                                                                Filename = parameterObject.Document
                                                                                    .Filename
                                                                            }
                                                                        });

                                                                    break;

                                                                case WABA360Dialog.ApiClient.Payloads.Enums
                                                                    .ParameterType
                                                                    .date_time:
                                                                    templateComponentObject.Parameters.Add(
                                                                        new WhatsappCloudApiParameterObject()
                                                                        {
                                                                            Type = "date_time",
                                                                            DateTime =
                                                                                new WhatsappCloudApiDateTimeObject()
                                                                                {
                                                                                    FallbackValue =
                                                                                        $"{parameterObject.DateTime.Component.Month.ToString()} {parameterObject.DateTime.Component.DayOfMonth}, {parameterObject.DateTime.Component.Year}"
                                                                                }
                                                                        });

                                                                    break;

                                                                case WABA360Dialog.ApiClient.Payloads.Enums
                                                                    .ParameterType
                                                                    .payload:
                                                                    templateComponentObject.Parameters.Add(
                                                                        new WhatsappCloudApiParameterObject()
                                                                        {
                                                                            Type = "payload",
                                                                            Payload = parameterObject.Payload
                                                                        });

                                                                    break;
                                                            }
                                                        }

                                                        if (action.ExtendedAutomationMessage
                                                                .WhatsappCloudApiByWabaExtendedAutomationMessages
                                                                .LastOrDefault().ExtendedMessagePayloadDetail
                                                                .WhatsappCloudApiTemplateMessageObject.Components ==
                                                            null)
                                                        {
                                                            action.ExtendedAutomationMessage
                                                                    .WhatsappCloudApiByWabaExtendedAutomationMessages
                                                                    .LastOrDefault().ExtendedMessagePayloadDetail
                                                                    .WhatsappCloudApiTemplateMessageObject.Components =
                                                                new List<
                                                                    WhatsappCloudApiTemplateMessageComponentObject>();
                                                        }

                                                        action.ExtendedAutomationMessage
                                                            .WhatsappCloudApiByWabaExtendedAutomationMessages
                                                            .LastOrDefault().ExtendedMessagePayloadDetail
                                                            .WhatsappCloudApiTemplateMessageObject.Components
                                                            .Add(templateComponentObject);
                                                    }
                                                }
                                            }
                                        }

                                        break;

                                    case "interactive":
                                        action.ExtendedAutomationMessage ??= new ExtendedAutomationMessage()
                                        {
                                            Channel = ChannelTypes.WhatsappCloudApi,
                                            MessageType = "interactive",
                                            WhatsappCloudApiByWabaExtendedAutomationMessages =
                                                new List<WhatsappCloudApiByWabaExtendedAutomationMessage>()
                                        };

                                        action.ExtendedAutomationMessage
                                            .WhatsappCloudApiByWabaExtendedAutomationMessages
                                            .Add(
                                                new WhatsappCloudApiByWabaExtendedAutomationMessage()
                                                {
                                                    MessagingHubWabaId = messagingHubWabaId,
                                                    ExtendedMessagePayloadDetail = new ExtendedMessagePayloadDetail()
                                                    {
                                                        WhatsappCloudApiInteractiveObject =
                                                            new WhatsappCloudApiInteractiveObject()
                                                            {
                                                                Type = sendMessage.Whatsapp360DialogInteractiveObject
                                                                    .Type
                                                                    .GetString()
                                                            }
                                                    }
                                                });

                                        if (sendMessage.Whatsapp360DialogInteractiveObject.Header != null)
                                        {
                                            switch (sendMessage.Whatsapp360DialogInteractiveObject.Header.Type)
                                            {
                                                case HeaderType.text:
                                                    action.ExtendedAutomationMessage
                                                        .WhatsappCloudApiByWabaExtendedAutomationMessages
                                                        .LastOrDefault()
                                                        .ExtendedMessagePayloadDetail.WhatsappCloudApiInteractiveObject
                                                        .Header = new WhatsappCloudApiHeaderObject()
                                                    {
                                                        Type = "text",
                                                        Text = sendMessage.Whatsapp360DialogInteractiveObject.Header
                                                            .Text
                                                    };

                                                    break;

                                                case HeaderType.image:
                                                    action.ExtendedAutomationMessage
                                                        .WhatsappCloudApiByWabaExtendedAutomationMessages
                                                        .LastOrDefault()
                                                        .ExtendedMessagePayloadDetail.WhatsappCloudApiInteractiveObject
                                                        .Header = new WhatsappCloudApiHeaderObject()
                                                    {
                                                        Type = "image",
                                                        Image = new WhatsappCloudApiMediaObject()
                                                        {
                                                            Id = sendMessage.Whatsapp360DialogInteractiveObject.Header
                                                                .Image
                                                                .Id,
                                                            Link = sendMessage.Whatsapp360DialogInteractiveObject.Header
                                                                .Image.Link,
                                                            Caption = sendMessage.Whatsapp360DialogInteractiveObject
                                                                .Header
                                                                .Image.Caption
                                                        }
                                                    };

                                                    break;

                                                case HeaderType.video:
                                                    action.ExtendedAutomationMessage
                                                        .WhatsappCloudApiByWabaExtendedAutomationMessages
                                                        .LastOrDefault()
                                                        .ExtendedMessagePayloadDetail.WhatsappCloudApiInteractiveObject
                                                        .Header = new WhatsappCloudApiHeaderObject()
                                                    {
                                                        Type = "video",
                                                        Video = new WhatsappCloudApiMediaObject()
                                                        {
                                                            Id = sendMessage.Whatsapp360DialogInteractiveObject.Header
                                                                .Video
                                                                .Id,
                                                            Link = sendMessage.Whatsapp360DialogInteractiveObject.Header
                                                                .Video.Link,
                                                            Caption = sendMessage.Whatsapp360DialogInteractiveObject
                                                                .Header
                                                                .Video.Caption
                                                        }
                                                    };

                                                    break;

                                                case HeaderType.document:
                                                    action.ExtendedAutomationMessage
                                                        .WhatsappCloudApiByWabaExtendedAutomationMessages
                                                        .LastOrDefault()
                                                        .ExtendedMessagePayloadDetail.WhatsappCloudApiInteractiveObject
                                                        .Header = new WhatsappCloudApiHeaderObject()
                                                    {
                                                        Type = "document",
                                                        Document = new WhatsappCloudApiMediaObject()
                                                        {
                                                            Id = sendMessage.Whatsapp360DialogInteractiveObject.Header
                                                                .Document.Id,
                                                            Link = sendMessage.Whatsapp360DialogInteractiveObject.Header
                                                                .Document.Link,
                                                            Filename = sendMessage.Whatsapp360DialogInteractiveObject
                                                                .Header
                                                                .Document.Filename,
                                                            Caption = sendMessage.Whatsapp360DialogInteractiveObject
                                                                .Header
                                                                .Document.Caption
                                                        }
                                                    };

                                                    break;
                                            }
                                        }

                                        if (sendMessage.Whatsapp360DialogInteractiveObject.Body != null)
                                        {
                                            action.ExtendedAutomationMessage
                                                    .WhatsappCloudApiByWabaExtendedAutomationMessages.LastOrDefault()
                                                    .ExtendedMessagePayloadDetail.WhatsappCloudApiInteractiveObject
                                                    .Body =
                                                new WhatsappCloudApiTextBodyObject()
                                                {
                                                    Text = sendMessage.Whatsapp360DialogInteractiveObject.Body.Text
                                                };
                                        }

                                        if (sendMessage.Whatsapp360DialogInteractiveObject.Footer != null)
                                        {
                                            action.ExtendedAutomationMessage
                                                    .WhatsappCloudApiByWabaExtendedAutomationMessages.LastOrDefault()
                                                    .ExtendedMessagePayloadDetail.WhatsappCloudApiInteractiveObject
                                                    .Footer =
                                                new WhatsappCloudApiTextFooterObject()
                                                {
                                                    Text = sendMessage.Whatsapp360DialogInteractiveObject.Footer.Text
                                                };
                                        }

                                        if (sendMessage.Whatsapp360DialogInteractiveObject.Action != null)
                                        {
                                            action.ExtendedAutomationMessage
                                                    .WhatsappCloudApiByWabaExtendedAutomationMessages.LastOrDefault()
                                                    .ExtendedMessagePayloadDetail.WhatsappCloudApiInteractiveObject
                                                    .Action =
                                                new WhatsappCloudApiActionObject()
                                                {
                                                    Button = sendMessage.Whatsapp360DialogInteractiveObject.Action
                                                        .Button,
                                                    Buttons = _mapper.Map<List<WhatsappCloudApiButtonObject>>(
                                                        sendMessage
                                                            .Whatsapp360DialogInteractiveObject.Action.Buttons),
                                                    CatalogId = sendMessage.Whatsapp360DialogInteractiveObject.Action
                                                        .CatalogId,
                                                    ProductRetailerId = sendMessage.Whatsapp360DialogInteractiveObject
                                                        .Action
                                                        .ProductRetailerId,
                                                    Sections = _mapper.Map<List<WhatsappCloudApiSectionObject>>(
                                                        sendMessage
                                                            .Whatsapp360DialogInteractiveObject.Action.Sections)
                                                };
                                        }

                                        break;
                                }

                                if (action.TargetedChannelWithIds == null)
                                {
                                    action.TargetedChannelWithIds = new List<TargetedChannelModel>();
                                }

                                if (!action.TargetedChannelWithIds.Any(x => x.channel == ChannelTypes.WhatsappCloudApi))
                                {
                                    action.TargetedChannelWithIds.Add(
                                        new TargetedChannelModel()
                                        {
                                            channel = ChannelTypes.WhatsappCloudApi, ids = new List<string>()
                                        });
                                }

                                var cloudApiChannelWithIds = action.TargetedChannelWithIds
                                    .FirstOrDefault(x => x.channel == ChannelTypes.WhatsappCloudApi);

                                if (cloudApiChannelWithIds.ids.All(x => x != phoneNumber))
                                {
                                    cloudApiChannelWithIds.ids.Add(phoneNumber);

                                    action.TargetedChannelWithIds
                                        .FirstOrDefault(x => x.channel == ChannelTypes.WhatsappCloudApi)
                                        .ids = cloudApiChannelWithIds.ids;

                                    _appDbContext.Entry(action).Property(x => x.TargetedChannelWithIds).IsModified =
                                        true;
                                    await _appDbContext.SaveChangesAsync();
                                }
                            }
                        }

                        if (action.ExtendedAutomationMessage != null &&
                            action.ExtendedAutomationMessage.WhatsappCloudApiByWabaExtendedAutomationMessages != null &&
                            action.ExtendedAutomationMessage.WhatsappCloudApiByWabaExtendedAutomationMessages.Any(x =>
                                x.MessagingHubWabaId != null))
                        {
                            action.WhatsApp360DialogExtendedAutomationMessages = null;

                            _appDbContext.Entry(action).Property(x => x.WhatsApp360DialogExtendedAutomationMessages)
                                .IsModified = true;
                            _appDbContext.Entry(action).Property(x => x.ExtendedAutomationMessage).IsModified = true;
                            await _appDbContext.SaveChangesAsync();
                            migratedActionIds.Add(action.Id);
                        }
                    }
                }

                response.MigratedActionsCount = migratedActionIds.Count;
                var actionIdList = migratedActionIds.ToList();

                response.MigratedAssignmentRuleNames = await _appDbContext.CompanyAssignmentRules.AsNoTracking()
                    .Include(x => x.AutomationActions)
                    .Where(x => x.CompanyId == companyId && x.AutomationActions.Any(y => actionIdList.Contains(y.Id)))
                    .Select(x => x.AssignmentRuleName).ToListAsync();
            }
        }
        catch (SleekflowErrorCodeException ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Error occured during migrating automation data company id {CompanyId}",
                nameof(Migrate360DialogAssignmentRulesAsync),
                companyId);

            if (ex.Output != null && ex.Output.ErrorContext != null)
            {
                JObject errorObject = (JObject) ex.Output.ErrorContext["error"];

                if (errorObject != null)
                {
                    throw new SleekflowUiException($"{JsonConvert.SerializeObject(errorObject)}");
                }
            }

            throw new SleekflowUiException("error occured during migrating automation data");
        }

        return response;
    }

    public async Task<MigratedWhatsapp360DialogDefaultChannelResponse> MigrateWhatsapp360DialogDefaultChannelAsync(
        string companyId)
    {
        var response = new MigratedWhatsapp360DialogDefaultChannelResponse();

        try
        {
            var migrateWhatsapp360DialogDict = await _appDbContext.ConfigWhatsApp360DialogConfigs.AsSplitQuery()
                .Where(x => x.CompanyId == companyId && _appDbContext.ConfigWhatsappCloudApiConfigs.Any(y =>
                    y.CompanyId == companyId && y.WhatsappPhoneNumber == x.WhatsAppPhoneNumber))
                .ToDictionaryAsync(x => x.Id.ToString(), x => x.WhatsAppPhoneNumber);

            if (migrateWhatsapp360DialogDict?.Any() != true)
            {
                return response;
            }

            var companyTeamDefaultChannelSettings = await _appDbContext.CompanyStaffTeams
                .Where(x => x.CompanyId == companyId && x.DefaultChannels != null).ToListAsync();

            companyTeamDefaultChannelSettings = companyTeamDefaultChannelSettings.Where(x => x.DefaultChannels.Any(y =>
                y.channel == ChannelTypes.Whatsapp360Dialog &&
                migrateWhatsapp360DialogDict.Keys.Any(z =>
                    y.ids.Any(id => id == z)))).ToList();

            if (companyTeamDefaultChannelSettings?.Any() != true)
            {
                return response;
            }

            var migratedCompanyTeamSettingIds = new HashSet<long>();
            var migratedWhatsAppPhoneNumber = new HashSet<string>();

            foreach (var companyTeamDefaultChannelSetting in companyTeamDefaultChannelSettings)
            {
                var targetChannelIds = companyTeamDefaultChannelSetting.DefaultChannels
                    .Where(x => x.channel == ChannelTypes.Whatsapp360Dialog).ToList();
                var updatedCloudApiTargetIds = new HashSet<string>();

                targetChannelIds.ForEach(x =>
                {
                    foreach (var id in migrateWhatsapp360DialogDict.Keys)
                    {
                        if (x.ids.Contains(id))
                        {
                            updatedCloudApiTargetIds.Add(migrateWhatsapp360DialogDict[id]);
                            x.ids.Remove(id);
                        }
                    }
                });

                if (updatedCloudApiTargetIds.Any())
                {
                    if (companyTeamDefaultChannelSetting.DefaultChannels.Any(x =>
                            x.channel == ChannelTypes.WhatsappCloudApi && x.ids != null))
                    {
                        companyTeamDefaultChannelSetting.DefaultChannels.FirstOrDefault(x =>
                                x.channel == ChannelTypes.WhatsappCloudApi)!.ids
                            .AddRange(updatedCloudApiTargetIds.ToList());
                    }
                    else
                    {
                        companyTeamDefaultChannelSetting.DefaultChannels.RemoveAll(x =>
                            x.channel == ChannelTypes.WhatsappCloudApi);

                        companyTeamDefaultChannelSetting.DefaultChannels.Add(
                            new TargetedChannelModel()
                            {
                                channel = ChannelTypes.WhatsappCloudApi, ids = updatedCloudApiTargetIds.ToList()
                            });
                    }

                    companyTeamDefaultChannelSetting.DefaultChannels.RemoveAll(x =>
                        x.channel == ChannelTypes.Whatsapp360Dialog);

                    if (targetChannelIds.Any(x => x.channel == ChannelTypes.Whatsapp360Dialog && x.ids.Any()))
                    {
                        companyTeamDefaultChannelSetting.DefaultChannels.AddRange(targetChannelIds);
                    }

                    _appDbContext.Entry(companyTeamDefaultChannelSetting).Property(x => x.DefaultChannels).IsModified =
                        true;
                    await _appDbContext.SaveChangesAsync();
                    migratedCompanyTeamSettingIds.Add(companyTeamDefaultChannelSetting.Id);
                    migratedWhatsAppPhoneNumber.UnionWith(updatedCloudApiTargetIds);
                }
            }

            response.MigratedWhatsAppNumber = migratedWhatsAppPhoneNumber.ToList();
            var migratedTeamList = migratedCompanyTeamSettingIds.ToList();

            response.MigratedDefaultChannelTeams = await _appDbContext.CompanyStaffTeams.AsNoTracking()
                .Where(x => migratedTeamList.Contains(x.Id)).Select(x => x.TeamName).ToListAsync();

            response.MigratedWhatsAppNumber = migratedWhatsAppPhoneNumber.ToList();
            response.MigratedDefaultChannelsCount = migratedWhatsAppPhoneNumber.Count;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Error occured during migrating default channel setting for company id {CompanyId}",
                nameof(MigrateWhatsapp360DialogDefaultChannelAsync),
                companyId);

            throw new SleekflowUiException("error occured during migrating default channel setting");
        }

        return response;
    }

    public async Task<MigratedWhatsapp360DialogLastChannelResponse> MigrateWhatsapp360DialogLastChannelAsync(
        string companyId)
    {
        var response = new MigratedWhatsapp360DialogLastChannelResponse();

        try
        {
            _appDbContext.Database.SetCommandTimeout(120);

            var migrateWhatsapp360DialogDict = await _appDbContext.ConfigWhatsApp360DialogConfigs.AsSplitQuery()
                .Where(x => x.CompanyId == companyId && _appDbContext.ConfigWhatsappCloudApiConfigs.Any(y =>
                    y.CompanyId == companyId && y.WhatsappPhoneNumber == x.WhatsAppPhoneNumber))
                .ToDictionaryAsync(x => x.Id, x => x.WhatsAppPhoneNumber);

            if (migrateWhatsapp360DialogDict?.Any() != true)
            {
                return response;
            }

            var whatsApp360DialogIdList = migrateWhatsapp360DialogDict.Keys.ToList();

            var userProfileIdsLastChannelToMigrate = await _appDbContext.UserProfiles.Where(x =>
                    x.CompanyId == companyId &&
                    x.WhatsApp360DialogUserId.HasValue && x.WhatsApp360DialogUser.ChannelId.HasValue &&
                    whatsApp360DialogIdList.Contains(x.WhatsApp360DialogUser.ChannelId.Value))
                .Select(x => x.Id).ToListAsync();

            if (userProfileIdsLastChannelToMigrate?.Any() != true)
            {
                return response;
            }

            await _appDbContext.UserProfileCustomFields.AsSplitQuery().Where(x =>
                    userProfileIdsLastChannelToMigrate.Contains(x.UserProfileId) &&
                    _appDbContext.CompanyCustomUserProfileFields
                        .Where(y => y.CompanyId == companyId && y.FieldName == "LastChannel")
                        .Select(y => y.Id).ToList()
                        .Contains(x.CompanyDefinedFieldId))
                .ExecuteUpdateAsync(calls => calls.SetProperty(
                    p => p.Value,
                    ChannelTypes.WhatsappCloudApi));

            await _appDbContext.SaveChangesAsync();

            response.MigratedWhatsAppNumber = migrateWhatsapp360DialogDict.Values.ToList();

            response.MigratedLastChannelContactsNames = await _appDbContext.UserProfiles.AsNoTracking()
                .Where(x => userProfileIdsLastChannelToMigrate.Contains(x.Id))
                .Select(x => x.FirstName + " " + x.LastName).ToListAsync();
            response.MigratedLastChannelContactsCount = userProfileIdsLastChannelToMigrate.Count;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Error occured during migrating last channel setting for company id {CompanyId}",
                nameof(MigrateWhatsapp360DialogLastChannelAsync),
                companyId);

            throw new SleekflowUiException("error occured during migrating default channel setting");
        }

        return response;
    }

    public async Task MigrateWhatsapp360DialogConversationMessagesAsync(
        string companyId,
        bool isConfigExist = true)
    {
        try
        {
            var migrateWhatsapp360DialogDict = await GetMigrateWhatsapp360DialogDictAsync(companyId, isConfigExist);

            if (!migrateWhatsapp360DialogDict.Any())
            {
                return;
            }

            foreach (var whatsapp360ConfigChannelPhoneNumberKvp in migrateWhatsapp360DialogDict)
            {
                try
                {
                    List<ConversationMessage> messages;
                    do
                    {
                        messages = await _appDbContext.ConversationMessages
                            .Include(x => x.Whatsapp360DialogSender)
                            .Include(x => x.Whatsapp360DialogReceiver)
                            .Include(x => x.Whatsapp360DialogExtendedMessagePayload)
                            .Where(x => x.CompanyId == companyId &&
                                        x.Channel == ChannelTypes.Whatsapp360Dialog &&
                                        ((x.Whatsapp360DialogSender != null &&
                                          x.Whatsapp360DialogSender.ChannelId ==
                                          whatsapp360ConfigChannelPhoneNumberKvp.Key) ||
                                         (x.Whatsapp360DialogReceiver != null &&
                                          x.Whatsapp360DialogReceiver.ChannelId ==
                                          whatsapp360ConfigChannelPhoneNumberKvp.Key)))
                            .Take(500)
                            .ToListAsync();

                        var conversationIds = messages.Select(x => x.ConversationId).Distinct().ToList();

                        var conversationDict = await _appDbContext.Conversations
                            .Where(x => conversationIds.Contains(x.Id))
                            .ToDictionaryAsync(x => x.Id, x => x);

                        foreach (var message in messages)
                        {
                            if (!conversationDict.TryGetValue(message.ConversationId, out var conversation))
                            {
                                continue;
                            }

                            message.Channel = ChannelTypes.WhatsappCloudApi;
                            message.ChannelIdentityId = whatsapp360ConfigChannelPhoneNumberKvp.Value;

                            string contactWhatsappId = String.Empty;
                            WhatsApp360DialogSender whatsApp360DialogSender = null;

                            if (message.Whatsapp360DialogSender != null &&
                                message.Whatsapp360DialogSender.WhatsAppId !=
                                whatsapp360ConfigChannelPhoneNumberKvp.Value)
                            {
                                contactWhatsappId = message.Whatsapp360DialogSender.WhatsAppId;
                                whatsApp360DialogSender = message.Whatsapp360DialogSender;
                            }
                            else if (message.Whatsapp360DialogReceiver != null &&
                                     message.Whatsapp360DialogReceiver.WhatsAppId !=
                                     whatsapp360ConfigChannelPhoneNumberKvp.Value)
                            {
                                contactWhatsappId = message.Whatsapp360DialogReceiver.WhatsAppId;
                                whatsApp360DialogSender = message.Whatsapp360DialogReceiver;
                            }

                            if (!string.IsNullOrWhiteSpace(contactWhatsappId) && whatsApp360DialogSender != null)
                            {
                                var normalizeWhatsappPhoneNumber =
                                    PhoneNumberHelper.NormalizeWhatsappPhoneNumber(contactWhatsappId);

                                var existingWhatsappSender =
                                    await _appDbContext.WhatsappCloudApiSenders.FirstOrDefaultAsync(x =>
                                        x.CompanyId == companyId &&
                                        x.UserProfileId == conversation.UserProfileId &&
                                        x.ConversationId == conversation.Id &&
                                        (x.WhatsappId == contactWhatsappId ||
                                         x.WhatsappId == normalizeWhatsappPhoneNumber));


                                if (existingWhatsappSender != null)
                                {
                                    var dynamicChannelSender = existingWhatsappSender.DeepClone();

                                    dynamicChannelSender.WhatsappChannelPhoneNumber =
                                        whatsapp360ConfigChannelPhoneNumberKvp.Value;

                                    message.DynamicChannelSender = dynamicChannelSender;
                                }
                                else
                                {
                                    var whatsappCloudApiSender =
                                        await _appDbContext.WhatsappCloudApiSenders.FirstOrDefaultAsync(x =>
                                            x.CompanyId == companyId &&
                                            (x.WhatsappId == contactWhatsappId ||
                                             x.WhatsappId == normalizeWhatsappPhoneNumber ||
                                             x.ConversationId == message.ConversationId));

                                    if (whatsappCloudApiSender == null)
                                    {
                                        whatsappCloudApiSender = new WhatsappCloudApiSender()
                                        {
                                            CompanyId = companyId,
                                            ConversationId = conversation.Id,
                                            UserProfileId = conversation.UserProfileId,
                                            WhatsappChannelPhoneNumber =
                                                whatsapp360ConfigChannelPhoneNumberKvp.Value,
                                            WhatsappId = normalizeWhatsappPhoneNumber,
                                            WhatsappUserDisplayName = whatsApp360DialogSender.Name
                                        };

                                        await _appDbContext.WhatsappCloudApiSenders.AddAsync(
                                            whatsappCloudApiSender);
                                        await _appDbContext.SaveChangesAsync();
                                    }
                                    else
                                    {
                                        if (string.IsNullOrEmpty(whatsappCloudApiSender.ConversationId) ||
                                            string.IsNullOrEmpty(whatsappCloudApiSender.UserProfileId))
                                        {
                                            whatsappCloudApiSender.ConversationId = conversation.Id;
                                            whatsappCloudApiSender.UserProfileId = conversation.UserProfileId;
                                        }

                                        var dynamicChannelSender = whatsappCloudApiSender.DeepClone();

                                        dynamicChannelSender.WhatsappChannelPhoneNumber =
                                            whatsapp360ConfigChannelPhoneNumberKvp.Value;

                                        message.DynamicChannelSender = dynamicChannelSender;
                                    }
                                }
                            }

                            #region migrate payload, if required

                            if (message.Whatsapp360DialogExtendedMessagePayload != null)
                            {
                                switch (message.MessageType)
                                {
                                    case "template":
                                        if (message.Whatsapp360DialogExtendedMessagePayload
                                                .Whatsapp360DialogTemplateMessage != null)
                                        {
                                            var extendedMessagePayload = new ExtendedMessagePayload()
                                            {
                                                Channel = ChannelTypes.WhatsappCloudApi,
                                                ExtendedMessageType =
                                                    ExtendedMessageType.WhatsappCloudApiTemplateMessage,
                                                ExtendedMessagePayloadDetail =
                                                    new ExtendedMessagePayloadDetail()
                                                    {
                                                        WhatsappCloudApiTemplateMessageObject =
                                                            new WhatsappCloudApiTemplateMessageViewModel()
                                                            {
                                                                TemplateName =
                                                                    message
                                                                        .Whatsapp360DialogExtendedMessagePayload
                                                                        .Whatsapp360DialogTemplateMessage
                                                                        .TemplateName,
                                                                Language = message
                                                                    .Whatsapp360DialogExtendedMessagePayload
                                                                    .Whatsapp360DialogTemplateMessage.Language
                                                                    .GetString(),
                                                            }
                                                    }
                                            };

                                            if (message.Whatsapp360DialogExtendedMessagePayload
                                                    .Whatsapp360DialogTemplateMessage.Components != null)
                                            {
                                                foreach (var component in message
                                                             .Whatsapp360DialogExtendedMessagePayload
                                                             .Whatsapp360DialogTemplateMessage.Components)
                                                {
                                                    var templateComponentObject =
                                                        new WhatsappCloudApiTemplateMessageComponentObject()
                                                        {
                                                            Type = component.Type.GetString(),
                                                            Parameters =
                                                                new List<WhatsappCloudApiParameterObject>()
                                                        };

                                                    foreach (var parameterObject in component.Parameters)
                                                    {
                                                        switch (parameterObject.Type)
                                                        {
                                                            case WABA360Dialog.ApiClient.Payloads.Enums
                                                                .ParameterType
                                                                .text:
                                                                templateComponentObject.Parameters.Add(
                                                                    new WhatsappCloudApiParameterObject()
                                                                    {
                                                                        Type = "text", Text = parameterObject.Text
                                                                    });

                                                                break;

                                                            case WABA360Dialog.ApiClient.Payloads.Enums
                                                                .ParameterType
                                                                .image:
                                                                templateComponentObject.Parameters.Add(
                                                                    new WhatsappCloudApiParameterObject()
                                                                    {
                                                                        Type = "image",
                                                                        Image =
                                                                            new WhatsappCloudApiMediaObject()
                                                                            {
                                                                                Id = parameterObject.Image.Id,
                                                                                Link = parameterObject.Image
                                                                                    .Link,
                                                                                Caption = parameterObject.Image
                                                                                    .Caption
                                                                            }
                                                                    });

                                                                break;

                                                            case WABA360Dialog.ApiClient.Payloads.Enums
                                                                .ParameterType
                                                                .video:
                                                                templateComponentObject.Parameters.Add(
                                                                    new WhatsappCloudApiParameterObject()
                                                                    {
                                                                        Type = "video",
                                                                        Video =
                                                                            new WhatsappCloudApiMediaObject()
                                                                            {
                                                                                Id = parameterObject.Video.Id,
                                                                                Link = parameterObject.Video
                                                                                    .Link,
                                                                                Caption = parameterObject.Video
                                                                                    .Caption
                                                                            }
                                                                    });

                                                                break;

                                                            case WABA360Dialog.ApiClient.Payloads.Enums
                                                                .ParameterType
                                                                .document:
                                                                templateComponentObject.Parameters.Add(
                                                                    new WhatsappCloudApiParameterObject()
                                                                    {
                                                                        Type = "document",
                                                                        Document =
                                                                            new WhatsappCloudApiMediaObject()
                                                                            {
                                                                                Id =
                                                                                    parameterObject.Document.Id,
                                                                                Link = parameterObject.Document
                                                                                    .Link,
                                                                                Caption = parameterObject
                                                                                    .Document
                                                                                    .Caption,
                                                                                Filename = parameterObject
                                                                                    .Document
                                                                                    .Filename
                                                                            }
                                                                    });

                                                                break;

                                                            case WABA360Dialog.ApiClient.Payloads.Enums
                                                                .ParameterType
                                                                .date_time:
                                                                templateComponentObject.Parameters.Add(
                                                                    new WhatsappCloudApiParameterObject()
                                                                    {
                                                                        Type = "date_time",
                                                                        DateTime =
                                                                            new WhatsappCloudApiDateTimeObject()
                                                                            {
                                                                                FallbackValue =
                                                                                    $"{parameterObject.DateTime.Component.Month.ToString()} {parameterObject.DateTime.Component.DayOfMonth}, {parameterObject.DateTime.Component.Year}"
                                                                            }
                                                                    });

                                                                break;

                                                            case WABA360Dialog.ApiClient.Payloads.Enums
                                                                .ParameterType
                                                                .payload:
                                                                templateComponentObject.Parameters.Add(
                                                                    new WhatsappCloudApiParameterObject()
                                                                    {
                                                                        Type = "payload",
                                                                        Payload = parameterObject.Payload
                                                                    });

                                                                break;
                                                        }
                                                    }

                                                    if (extendedMessagePayload.ExtendedMessagePayloadDetail
                                                            .WhatsappCloudApiTemplateMessageObject.Components == null)
                                                    {
                                                        extendedMessagePayload.ExtendedMessagePayloadDetail
                                                                .WhatsappCloudApiTemplateMessageObject
                                                                .Components =
                                                            new List<
                                                                WhatsappCloudApiTemplateMessageComponentObject>();
                                                    }

                                                    extendedMessagePayload.ExtendedMessagePayloadDetail
                                                        .WhatsappCloudApiTemplateMessageObject.Components
                                                        .Add(templateComponentObject);
                                                }
                                            }


                                            message.ExtendedMessagePayload = extendedMessagePayload;

                                            _appDbContext.Whatsapp360DialogExtendedMessagePayload.Remove(
                                                message.Whatsapp360DialogExtendedMessagePayload);
                                        }

                                        break;

                                    case "interactive":
                                        if (message.Whatsapp360DialogExtendedMessagePayload
                                                .Whatsapp360DialogInteractiveObject != null)
                                        {
                                            var extendedMessagePayload = new ExtendedMessagePayload()
                                            {
                                                Channel = ChannelTypes.WhatsappCloudApi,
                                                ExtendedMessageType =
                                                    ExtendedMessageType.WhatsappCloudApiInteractiveMessage,
                                                ExtendedMessagePayloadDetail =
                                                    new ExtendedMessagePayloadDetail()
                                                    {
                                                        WhatsappCloudApiInteractiveObject =
                                                            new WhatsappCloudApiInteractiveObject()
                                                            {
                                                                Type = message
                                                                    .Whatsapp360DialogExtendedMessagePayload
                                                                    .Whatsapp360DialogInteractiveObject.Type
                                                                    .GetString()
                                                            }
                                                    }
                                            };

                                            if (message.Whatsapp360DialogExtendedMessagePayload
                                                    .Whatsapp360DialogInteractiveObject.Header != null)
                                            {
                                                switch (message.Whatsapp360DialogExtendedMessagePayload
                                                            .Whatsapp360DialogInteractiveObject.Header.Type)
                                                {
                                                    case HeaderType.text:
                                                        extendedMessagePayload.ExtendedMessagePayloadDetail
                                                            .WhatsappCloudApiInteractiveObject
                                                            .Header = new WhatsappCloudApiHeaderObject()
                                                        {
                                                            Type = "text",
                                                            Text = message
                                                                .Whatsapp360DialogExtendedMessagePayload
                                                                .Whatsapp360DialogInteractiveObject.Header
                                                                .Text
                                                        };

                                                        break;

                                                    case HeaderType.image:
                                                        extendedMessagePayload
                                                            .ExtendedMessagePayloadDetail
                                                            .WhatsappCloudApiInteractiveObject
                                                            .Header = new WhatsappCloudApiHeaderObject()
                                                        {
                                                            Type = "image",
                                                            Image = new WhatsappCloudApiMediaObject()
                                                            {
                                                                Id = message
                                                                    .Whatsapp360DialogExtendedMessagePayload
                                                                    .Whatsapp360DialogInteractiveObject.Header
                                                                    .Image
                                                                    .Id,
                                                                Link = message
                                                                    .Whatsapp360DialogExtendedMessagePayload
                                                                    .Whatsapp360DialogInteractiveObject.Header
                                                                    .Image.Link,
                                                                Caption = message
                                                                    .Whatsapp360DialogExtendedMessagePayload
                                                                    .Whatsapp360DialogInteractiveObject
                                                                    .Header
                                                                    .Image.Caption
                                                            }
                                                        };

                                                        break;

                                                    case HeaderType.video:
                                                        extendedMessagePayload
                                                            .ExtendedMessagePayloadDetail
                                                            .WhatsappCloudApiInteractiveObject
                                                            .Header = new WhatsappCloudApiHeaderObject()
                                                        {
                                                            Type = "video",
                                                            Video = new WhatsappCloudApiMediaObject()
                                                            {
                                                                Id = message
                                                                    .Whatsapp360DialogExtendedMessagePayload
                                                                    .Whatsapp360DialogInteractiveObject.Header
                                                                    .Video
                                                                    .Id,
                                                                Link = message
                                                                    .Whatsapp360DialogExtendedMessagePayload
                                                                    .Whatsapp360DialogInteractiveObject.Header
                                                                    .Video.Link,
                                                                Caption = message
                                                                    .Whatsapp360DialogExtendedMessagePayload
                                                                    .Whatsapp360DialogInteractiveObject
                                                                    .Header
                                                                    .Video.Caption
                                                            }
                                                        };

                                                        break;

                                                    case HeaderType.document:
                                                        extendedMessagePayload
                                                            .ExtendedMessagePayloadDetail
                                                            .WhatsappCloudApiInteractiveObject
                                                            .Header = new WhatsappCloudApiHeaderObject()
                                                        {
                                                            Type = "document",
                                                            Document = new WhatsappCloudApiMediaObject()
                                                            {
                                                                Id = message
                                                                    .Whatsapp360DialogExtendedMessagePayload
                                                                    .Whatsapp360DialogInteractiveObject.Header
                                                                    .Document.Id,
                                                                Link = message
                                                                    .Whatsapp360DialogExtendedMessagePayload
                                                                    .Whatsapp360DialogInteractiveObject.Header
                                                                    .Document.Link,
                                                                Filename = message
                                                                    .Whatsapp360DialogExtendedMessagePayload
                                                                    .Whatsapp360DialogInteractiveObject
                                                                    .Header
                                                                    .Document.Filename,
                                                                Caption = message
                                                                    .Whatsapp360DialogExtendedMessagePayload
                                                                    .Whatsapp360DialogInteractiveObject
                                                                    .Header
                                                                    .Document.Caption
                                                            }
                                                        };

                                                        break;
                                                }
                                            }

                                            if (message.Whatsapp360DialogExtendedMessagePayload
                                                    .Whatsapp360DialogInteractiveObject.Body != null)
                                            {
                                                extendedMessagePayload
                                                        .ExtendedMessagePayloadDetail
                                                        .WhatsappCloudApiInteractiveObject
                                                        .Body =
                                                    new WhatsappCloudApiTextBodyObject()
                                                    {
                                                        Text = message.Whatsapp360DialogExtendedMessagePayload
                                                            .Whatsapp360DialogInteractiveObject.Body.Text
                                                    };
                                            }

                                            if (message.Whatsapp360DialogExtendedMessagePayload
                                                    .Whatsapp360DialogInteractiveObject.Footer != null)
                                            {
                                                extendedMessagePayload
                                                        .ExtendedMessagePayloadDetail
                                                        .WhatsappCloudApiInteractiveObject
                                                        .Footer =
                                                    new WhatsappCloudApiTextFooterObject()
                                                    {
                                                        Text = message.Whatsapp360DialogExtendedMessagePayload
                                                            .Whatsapp360DialogInteractiveObject.Footer.Text
                                                    };
                                            }

                                            if (message.Whatsapp360DialogExtendedMessagePayload
                                                    .Whatsapp360DialogInteractiveObject.Action != null)
                                            {
                                                extendedMessagePayload
                                                        .ExtendedMessagePayloadDetail
                                                        .WhatsappCloudApiInteractiveObject
                                                        .Action =
                                                    new WhatsappCloudApiActionObject()
                                                    {
                                                        Button = message.Whatsapp360DialogExtendedMessagePayload
                                                            .Whatsapp360DialogInteractiveObject.Action
                                                            .Button,
                                                        Buttons = _mapper
                                                            .Map<List<WhatsappCloudApiButtonObject>>(
                                                                message.Whatsapp360DialogExtendedMessagePayload
                                                                    .Whatsapp360DialogInteractiveObject.Action
                                                                    .Buttons),
                                                        CatalogId = message
                                                            .Whatsapp360DialogExtendedMessagePayload
                                                            .Whatsapp360DialogInteractiveObject.Action
                                                            .CatalogId,
                                                        ProductRetailerId = message
                                                            .Whatsapp360DialogExtendedMessagePayload
                                                            .Whatsapp360DialogInteractiveObject
                                                            .Action
                                                            .ProductRetailerId,
                                                        Sections = _mapper
                                                            .Map<List<WhatsappCloudApiSectionObject>>(
                                                                message.Whatsapp360DialogExtendedMessagePayload
                                                                    .Whatsapp360DialogInteractiveObject.Action
                                                                    .Sections)
                                                    };
                                            }

                                            message.ExtendedMessagePayload = extendedMessagePayload;

                                            _appDbContext.Whatsapp360DialogExtendedMessagePayload.Remove(
                                                message.Whatsapp360DialogExtendedMessagePayload);
                                        }

                                        break;
                                    default:
                                        if (message is
                                            {
                                                Whatsapp360DialogExtendedMessagePayload.ReplyPayload: not null
                                            })
                                        {
                                            _appDbContext.Whatsapp360DialogExtendedMessagePayload.Remove(
                                                message.Whatsapp360DialogExtendedMessagePayload);
                                        }

                                        break;
                                }
                            }

                            #endregion

                            await _appDbContext.SaveChangesAsync();
                        }
                    }
                    while (messages != null && messages.Any());
                }
                catch (Exception ex)
                {
                    _logger.LogError($"Company {companyId} is migrating conversation messages from ");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                "Company {CompanyId} migrate messages error with {SerializeObject}",
                companyId,
                JsonConvert.SerializeObject(ex));
        }
    }

    public async Task<List<string>> PatchBusinessBalanceMarkupProfile(
        List<string> whiteListSleekflowCompanyIds,
        MarkupProfile markupProfile,
        DateTimeOffset effectiveDate,
        string sleekflowStaffId)
    {
        var getAllManagementWhatsappCloudApiWabasOutput = await GetAllWhatsappCloudApiWabas();
        var getAllManagementWhatsappCloudApiBusinessBalancesOutput = await GetAllWhatsappCloudApiBusinessBalances();

        var facebookBusinessId = getAllManagementWhatsappCloudApiWabasOutput
            .Wabas.Where(w => !w.SleekflowCompanyIds.Any(whiteListSleekflowCompanyIds.Contains))
            .Select(w => w.FacebookWabaBusinessId)
            .ToList();

        var businessBalances = getAllManagementWhatsappCloudApiBusinessBalancesOutput
            .BusinessBalances.Where(b => facebookBusinessId.Contains(b.FacebookBusinessId));
        var results = new List<string>();

        foreach (var businessBalance in businessBalances)
        {
            try
            {
                var updateWhatsappCloudApiBusinessBalanceMarkupProfileInput =
                    new UpdateWhatsappCloudApiBusinessBalanceMarkupProfileInput(
                        businessBalance.Id,
                        markupProfile,
                        true,
                        effectiveDate,
                        sleekflowStaffId);

                var result =
                    await _managementsApi.ManagementsUpdateWhatsappCloudApiBusinessBalanceMarkupProfilePostAsync(
                        updateWhatsappCloudApiBusinessBalanceMarkupProfileInput:
                        updateWhatsappCloudApiBusinessBalanceMarkupProfileInput);

                if (result.Success)
                {
                    results.Add(businessBalance.Id);
                }
                else
                {
                    throw new SleekflowUiException("error occured during patch business balance markup profile");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[WhatsApp Cloud Api] Unable to patch mark up profile {BusinessBalanceId}",
                    facebookBusinessId);
            }
        }

        return results;
    }

    public async Task<GetAllManagementWhatsappCloudApiConversationUsageAnalyticsOutput>
        GetAllWhatsappCloudApiConversationUsageAnalytics(DateTime start, DateTime end, string granularity)
    {
        GetAllManagementWhatsappCloudApiConversationUsageAnalyticsOutputOutput output;
        List<ManagementWhatsappCloudApiConversationUsageAnalytic> conversationUsageAnalytics =
            new List<ManagementWhatsappCloudApiConversationUsageAnalytic>();
        string continuationToken = null;
        do
        {
            output = await _managementsApi
                .ManagementsGetAllManagementWhatsappCloudApiConversationUsageAnalyticsPostAsync(
                    getAllManagementWhatsappCloudApiConversationUsageAnalyticsInput: new
                        GetAllManagementWhatsappCloudApiConversationUsageAnalyticsInput(
                            start,
                            end,
                            granularity,
                            50,
                            continuationToken));

            continuationToken = output.Data.ContinuationToken;
            conversationUsageAnalytics.AddRange(output.Data.WhatsappCloudApiConversationUsageAnalytics);
        }
        while (!string.IsNullOrEmpty(continuationToken));

        output.Data.WhatsappCloudApiConversationUsageAnalytics = conversationUsageAnalytics;
        return output.Data;
    }

    public async Task<(List<InternalByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsDto> ByBusiness,
            List<InternalByWabaWhatsappCloudApiDailyConversationUsageAnalyticsDto> ByWaba)>
        GetAllWhatsappCloudApiConversationUsageAnalyticsInternal(DateTime start, DateTime end, string granularity)
    {
        try
        {
            var apiResponse = await GetAllWhatsappCloudApiConversationUsageAnalytics(start, end, granularity);

            // Use the end of the requested period as the snapshot date for conversion
            var snapshotDateTime = new DateTime(end.Year, end.Month, end.Day, 23, 59, 59, DateTimeKind.Utc);

            var byBusinessList = new List<InternalByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsDto>();
            var byWabaList = new List<InternalByWabaWhatsappCloudApiDailyConversationUsageAnalyticsDto>();

            foreach (var analytic in apiResponse.WhatsappCloudApiConversationUsageAnalytics)
            {
                var converted = CmsCompanyDetailConverter
                    .ConvertToInternalWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData(
                        analytic,
                        snapshotDateTime);

                var byBusinessDto = _mapper
                    .Map<InternalByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsDto>(
                        converted.ByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshot);
                var byWabaDtos = _mapper
                    .Map<List<InternalByWabaWhatsappCloudApiDailyConversationUsageAnalyticsDto>>(
                        converted.ByWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshots);

                byBusinessList.Add(byBusinessDto);
                if (byWabaDtos?.Count > 0)
                {
                    byWabaList.AddRange(byWabaDtos);
                }
            }

            return (byBusinessList, byWabaList);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed to retrieve API data for period {Start} to {End} with granularity {Granularity}",
                start.ToString("yyyy-MM-dd"),
                end.ToString("yyyy-MM-dd"),
                granularity);
            return (new List<InternalByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsDto>(),
                new List<InternalByWabaWhatsappCloudApiDailyConversationUsageAnalyticsDto>());
        }
    }

    public async Task<DisassociateFacebookBusinessAccountFromCompanyOutputOutput>
        DisassociateFacebookBusinessAccountFromCompany(
            string sleekflowCompanyId,
            string facebookBusinessId,
            string sleekflowStaffId)
    {
        // Validation for disassociate
        var whatsappCloudApiConfigs = await _appDbContext.ConfigWhatsappCloudApiConfigs.AsNoTracking()
            .Where(x => x.CompanyId == sleekflowCompanyId && x.FacebookBusinessId == facebookBusinessId)
            .ToListAsync();

        if (whatsappCloudApiConfigs.Any())
        {
            throw new Exception(
                $"Cannot disassociate a facebook business account=[{facebookBusinessId}] if the company=[{sleekflowCompanyId}] has any channel connected to it");
        }

        var isNegativeBalance = (await _balancesApi.BalancesGetWhatsappCloudApiBusinessBalancesPostAsync(
                getWhatsappCloudApiBusinessBalancesInput: new GetWhatsappCloudApiBusinessBalancesInput(
                    sleekflowCompanyId))).Data.BusinessBalances
            .Any(x => x.FacebookBusinessId == facebookBusinessId && x.Balance.Amount < 0);

        if (isNegativeBalance)
        {
            throw new Exception(
                $"Cannot disassociate a facebook business account=[{facebookBusinessId}] if the balance of the facebook business account is negative");
        }

        return await _managementsApi.ManagementsDisassociateFacebookBusinessAccountFromCompanyPostAsync(
            disassociateFacebookBusinessAccountFromCompanyInput: new
                DisassociateFacebookBusinessAccountFromCompanyInput(
                    sleekflowCompanyId,
                    facebookBusinessId,
                    sleekflowStaffId));
    }

    public async Task<GetPhoneNumberWhatsappBusinessProfileOutputOutput> GetPhoneNumberWhatsappBusinessProfileAsync(
        string sleekflowCompanyId,
        string messagingHubWabaId,
        string messagingHubPhoneNumberId)
    {
        return await _channelsApi.ChannelsGetPhoneNumberWhatsappBusinessProfilePostAsync(
            getPhoneNumberWhatsappBusinessProfileInput: new GetPhoneNumberWhatsappBusinessProfileInput(
                sleekflowCompanyId,
                messagingHubWabaId,
                messagingHubPhoneNumberId));
    }

    public async Task<UpdatePhoneNumberWhatsappBusinessProfileOutputOutput>
        UpdatePhoneNumberWhatsappBusinessProfileAsync(
            string sleekflowCompanyId,
            string messagingHubWabaId,
            string messagingHubPhoneNumberId,
            string sleekflowStaffId,
            UpdatePhoneNumberBusinessProfileRequest updatePhoneNumberBusinessProfileRequest)
    {
        return await _channelsApi.ChannelsUpdatePhoneNumberWhatsappBusinessProfilePostAsync(
            updatePhoneNumberWhatsappBusinessProfileInput: new UpdatePhoneNumberWhatsappBusinessProfileInput(
                sleekflowCompanyId,
                messagingHubWabaId,
                messagingHubPhoneNumberId,
                sleekflowStaffId,
                updatePhoneNumberBusinessProfileRequest));
    }

    public async Task<UpdatePhoneNumberSettingsOutputOutput> UpdatePhoneNumberSettingsAsync(
        string sleekflowCompanyId,
        string messagingHubWabaId,
        string messagingHubPhoneNumberId,
        StorageConfiguration storageConfiguration,
        string sleekflowStaffId,
        CancellationToken cancellationToken = default)
    {
        return await _managementsApi.ManagementsUpdatePhoneNumberSettingsPostAsync(
            updatePhoneNumberSettingsInput: new UpdatePhoneNumberSettingsInput(
                sleekflowCompanyId,
                messagingHubWabaId,
                messagingHubPhoneNumberId,
                storageConfiguration,
                sleekflowStaffId),
            cancellationToken: cancellationToken);
    }

    public async Task<GetAuditLogsOutputOutput> GetAuditLogsAsync(
        List<string> auditingOperations,
        string sleekflowCompanyId,
        bool? hasCloudApiAuditsException,
        string continuationToken,
        int limit = 100,
        CancellationToken cancellationToken = default)
    {
        return await _managementsApi.ManagementsGetAuditLogsPostAsync(
            getAuditLogsInput: new GetAuditLogsInput(
                auditingOperations,
                sleekflowCompanyId,
                hasCloudApiAuditsException,
                continuationToken,
                limit),
            cancellationToken: cancellationToken);
    }

    private async Task<IDictionary<long?, string>> GetMigrateWhatsapp360DialogDictAsync(
        string companyId,
        bool isConfigExist)
    {
        return isConfigExist
            ? await _appDbContext.ConfigWhatsApp360DialogConfigs.AsSplitQuery()
                .Where(x => x.CompanyId == companyId && _appDbContext.ConfigWhatsappCloudApiConfigs.Any(y =>
                    y.CompanyId == companyId && y.WhatsappPhoneNumber == x.WhatsAppPhoneNumber))
                .ToDictionaryAsync(x => x?.Id, x => x.WhatsAppPhoneNumber)
            : await _appDbContext.SenderWhatsApp360DialogSenders.AsSplitQuery()
                .Where(x => x.CompanyId == companyId)
                .Where(x => _appDbContext.ConfigWhatsappCloudApiConfigs.Any(y =>
                    y.CompanyId == companyId && y.WhatsappPhoneNumber == x.ChannelWhatsAppPhoneNumber))
                .Select(x => new
                {
                    Key = x.ChannelId, Value = x.ChannelWhatsAppPhoneNumber
                })
                .Distinct()
                .ToDictionaryAsync(x => x.Key, x => x.Value);
    }

    public async Task<List<WhatsAppCloudApiMonthlySummarizedConversationUsageAnalytic>>
        GetMonthlyWhatsappCloudApiConversationUsageAnalytics(DateTime start, DateTime end)
    {
        // Get all business balances first to ensure we include all businesses
        var allBusinessBalances = await GetInternalWhatsappCloudApiBalanceDtosAsync();

        // Pre-calculate month ranges more efficiently
        var monthRanges = GenerateMonthRanges(start, end);

        // Use a thread-safe dictionary to collect monthly data by business ID and date
        var monthlyDataCollection =
            new ConcurrentDictionary<string, ConcurrentDictionary<string, MonthlySummarizedConversationUsageAnalytic>>(
                Environment.ProcessorCount, // Concurrency level
                allBusinessBalances.Count); // Initial capacity

        // Create zero money object once to avoid repeated allocations
        var zeroMoney = new Money("USD", 0);

        // Process months in parallel with optimized concurrency
        await Parallel.ForEachAsync(
            monthRanges,
            new ParallelOptions
            {
                MaxDegreeOfParallelism = Math.Min(Environment.ProcessorCount, 20) // Optimize based on CPU cores
            },
            async (monthRange, cancellationToken) =>
            {
                try
                {
                    var analytics = await GetManagementWhatsappCloudApiConversationUsageAnalytics(
                        monthRange.Start,
                        monthRange.End,
                        monthRange.IsNotFullMonth);

                    // Convert analytics to dictionary for a quick lookup with pre-allocated capacity
                    var analyticsDict = analytics.ToDictionary(
                        a => a.FacebookBusinessId,
                        a => a,
                        StringComparer.OrdinalIgnoreCase); // Case-insensitive comparison for better matching

                    var monthEndDate = monthRange.End.ToString("yyyy-MM-dd");

                    // Process all businesses, using analytics data if available, otherwise use zero values
                    foreach (var businessBalance in allBusinessBalances)
                    {
                        var facebookBusinessId = businessBalance.FacebookBusinessId;

                        // Get or create the thread-safe dictionary for this business
                        var businessMonthlyData = monthlyDataCollection.GetOrAdd(
                            facebookBusinessId,
                            _ => new ConcurrentDictionary<string, MonthlySummarizedConversationUsageAnalytic>());

                        MonthlySummarizedConversationUsageAnalytic monthlyAnalytic;

                        if (analyticsDict.TryGetValue(facebookBusinessId, out var analytic))
                        {
                            // Use actual analytics data
                            monthlyAnalytic = new MonthlySummarizedConversationUsageAnalytic
                            {
                                Date = monthEndDate,
                                TotalUsed = analytic.SummarizedConversationUsageAnalytic.TotalUsed,
                                TotalMarkup = analytic.SummarizedConversationUsageAnalytic.TotalMarkup,
                                TotalTransactionHandlingFee = analytic.SummarizedConversationUsageAnalytic
                                    .TotalTransactionHandlingFee
                            };
                        }
                        else
                        {
                            // Use zero values for businesses without analytics data
                            monthlyAnalytic = new MonthlySummarizedConversationUsageAnalytic
                            {
                                Date = monthEndDate,
                                TotalUsed = zeroMoney,
                                TotalMarkup = zeroMoney,
                                TotalTransactionHandlingFee = zeroMoney
                            };
                        }

                        // Add the monthly data (this will overwrite if the same date already exists, which is the desired behavior)
                        businessMonthlyData.AddOrUpdate(
                            monthEndDate,
                            monthlyAnalytic,
                            (key, existing) => monthlyAnalytic);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "Error processing month range {Start} to {End}",
                        monthRange.Start.ToString("yyyy-MM-dd"),
                        monthRange.End.ToString("yyyy-MM-dd"));
                }
            });

        // Now safely aggregate the collected data into the final result
        var results = new List<WhatsAppCloudApiMonthlySummarizedConversationUsageAnalytic>(monthlyDataCollection.Count);

        foreach (var businessData in monthlyDataCollection)
        {
            var facebookBusinessId = businessData.Key;
            var monthlyAnalytics = businessData.Value.Values.ToList();

            // Sort the monthly analytics by date to ensure proper ordering
            monthlyAnalytics.Sort((a, b) => string.CompareOrdinal(a.Date, b.Date));

            // Find the business name from the original data
            var businessBalance = allBusinessBalances.Find(b => b.FacebookBusinessId == facebookBusinessId);
            var facebookBusinessName = businessBalance?.FacebookBusinessName ?? string.Empty;

            var result = new WhatsAppCloudApiMonthlySummarizedConversationUsageAnalytic
            {
                FacebookBusinessId = facebookBusinessId,
                FacebookBusinessName = facebookBusinessName,
                MonthlySummarizedConversationUsageAnalytics = monthlyAnalytics
            };

            results.Add(result);
        }

        // Sort results by FacebookBusinessId for consistent output
        results.Sort((a, b) => string.CompareOrdinal(a.FacebookBusinessId, b.FacebookBusinessId));

        return results;
    }

    public async Task<(List<InternalByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsDto> ByBusiness,
            List<InternalByWabaWhatsappCloudApiDailyConversationUsageAnalyticsDto> ByWaba)>
        GetInternalWhatsappCloudApiConversationUsageAnalytics(DateTime start, DateTime end)
    {
        try
        {
            // Get all business balances first - this is the source of truth
            var allBusinessBalances = await GetInternalWhatsappCloudApiBalanceDtosAsync();

            // Early exit if no business balances
            if (allBusinessBalances == null || allBusinessBalances.Count == 0)
            {
                return (new List<InternalByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsDto>(),
                    new List<InternalByWabaWhatsappCloudApiDailyConversationUsageAnalyticsDto>());
            }

            // Pre-calculate WABA count for better capacity planning
            var totalWabaCount = allBusinessBalances.Sum(business => business.FacebookBusinessWabas?.Count ?? 0);

            // Create lookup sets for efficient filtering with pre-allocated capacity
            var validBusinessIds = new HashSet<string>(allBusinessBalances.Count, StringComparer.OrdinalIgnoreCase);
            var validWabaKeys = new HashSet<string>(totalWabaCount, StringComparer.OrdinalIgnoreCase);

            // Single pass to build all lookup collections with optimized string operations
            foreach (var businessBalance in allBusinessBalances)
            {
                validBusinessIds.Add(businessBalance.FacebookBusinessId);

                if (!(businessBalance.FacebookBusinessWabas?.Count > 0))
                {
                    continue;
                }

                var businessId = businessBalance.FacebookBusinessId;
                foreach (var waba in businessBalance.FacebookBusinessWabas)
                {
                    validWabaKeys.Add($"{businessId}_{waba.FacebookWabaId}");
                }
            }

            // Generate month ranges (start and end of each month)
            var monthRanges = GenerateSimpleMonthRanges(start, end);

            // Pre-allocate collections with capacity for better performance
            var byBusinessAggregated =
                new ConcurrentDictionary<string, InternalByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsDto>(
                    Environment.ProcessorCount,
                    allBusinessBalances.Count);

            var byWabaAggregated =
                new ConcurrentDictionary<string, InternalByWabaWhatsappCloudApiDailyConversationUsageAnalyticsDto>(
                    Environment.ProcessorCount,
                    totalWabaCount);

            // Pre-populate with default data for all valid businesses and WABAs
            foreach (var businessBalance in allBusinessBalances)
            {
                // Create business entry once and reuse
                var businessId = businessBalance.FacebookBusinessId;
                var businessName = businessBalance.FacebookBusinessName;

                // Create business entry with minimal cloning
                var defaultBusiness = new InternalByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsDto
                {
                    FacebookBusinessId = businessId,
                    FacebookBusinessName = businessName,
                    TotalConversations = 0,
                    TotalBusinessInitiatedConversations = 0,
                    TotalUserInitiatedConversations = 0,
                    TotalBusinessInitiatedPaidQuantity = 0,
                    TotalBusinessInitiatedFreeTierQuantity = 0,
                    TotalUserInitiatedPaidQuantity = 0,
                    TotalUserInitiatedFreeTierQuantity = 0,
                    TotalUserInitiatedFreeEntryPointQuantity = 0,
                    TotalUsedAmount = 0,
                    TotalUsedCurrencyIsoCode = "USD",
                    TotalMarkupAmount = 0,
                    TotalMarkupCurrencyIsoCode = "USD",
                    TotalTransactionHandlingFeeAmount = 0,
                    TotalTransactionHandlingFeeCurrencyIsoCode = "USD",
                    TotalAuthenticationTypeConversations = 0,
                    TotalMarketingTypeConversations = 0,
                    TotalMarketingLiteTypeConversations = 0,
                    TotalServiceTypeConversations = 0,
                    TotalUtilityTypeConversations = 0,
                    TotalAuthenticationPricingVolume = 0,
                    TotalAuthenticationInternationalPricingVolume = 0,
                    TotalMarketingPricingVolume = 0,
                    TotalMarketingLitePricingVolume = 0,
                    TotalReferralConversionPricingVolume = 0,
                    TotalServicePricingVolume = 0,
                    TotalUtilityPricingVolume = 0
                };

                byBusinessAggregated[businessId] = defaultBusiness;

                // Process WABAs if they exist
                if (!(businessBalance.FacebookBusinessWabas?.Count > 0))
                {
                    continue;
                }

                foreach (var waba in businessBalance.FacebookBusinessWabas)
                {
                    // Create WABA entry with minimal cloning
                    var defaultWaba = new InternalByWabaWhatsappCloudApiDailyConversationUsageAnalyticsDto
                    {
                        FacebookBusinessId = businessId,
                        FacebookBusinessName = businessName,
                        WabaId = waba.FacebookWabaId,
                        WabaName = waba.FacebookWabaName,
                        TotalConversations = 0,
                        TotalBusinessInitiatedConversations = 0,
                        TotalUserInitiatedConversations = 0,
                        TotalBusinessInitiatedPaidQuantity = 0,
                        TotalBusinessInitiatedFreeTierQuantity = 0,
                        TotalUserInitiatedPaidQuantity = 0,
                        TotalUserInitiatedFreeTierQuantity = 0,
                        TotalUserInitiatedFreeEntryPointQuantity = 0,
                        TotalUsedAmount = 0,
                        TotalUsedCurrencyIsoCode = "USD",
                        TotalMarkupAmount = 0,
                        TotalMarkupCurrencyIsoCode = "USD",
                        TotalTransactionHandlingFeeAmount = 0,
                        TotalTransactionHandlingFeeCurrencyIsoCode = "USD",
                        TotalAuthenticationTypeConversations = 0,
                        TotalMarketingTypeConversations = 0,
                        TotalMarketingLiteTypeConversations = 0,
                        TotalServiceTypeConversations = 0,
                        TotalUtilityTypeConversations = 0,
                        TotalAuthenticationPricingVolume = 0,
                        TotalAuthenticationInternationalPricingVolume = 0,
                        TotalMarketingPricingVolume = 0,
                        TotalMarketingLitePricingVolume = 0,
                        TotalReferralConversionPricingVolume = 0,
                        TotalServicePricingVolume = 0,
                        TotalUtilityPricingVolume = 0
                    };

                    byWabaAggregated[$"{businessId}_{waba.FacebookWabaId}"] = defaultWaba;
                }
            }

            // Process each month range in parallel with optimized concurrency
            await Parallel.ForEachAsync(
                monthRanges,
                new ParallelOptions
                {
                    MaxDegreeOfParallelism = Math.Min(Environment.ProcessorCount, 20)
                },
                async (monthRange, cancellationToken) =>
                {
                    try
                    {
                        var result = await GetInternalWhatsappCloudApiConversationUsageAnalyticsDto(
                            monthRange.Start,
                            monthRange.End);

                        // Filter and aggregate ByBusiness results - only include valid business IDs
                        if (result.ByBusinessDto?.Count > 0)
                        {
                            // Pre-filter valid business data to avoid repeated HashSet lookups
                            var validBusinessData =
                                new List<InternalByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsDto>(
                                    result.ByBusinessDto.Count);
                            foreach (var businessData in result.ByBusinessDto)
                            {
                                if (validBusinessIds.Contains(businessData.FacebookBusinessId))
                                {
                                    validBusinessData.Add(businessData);
                                }
                            }

                            // Process valid business data with optimized AddOrUpdate
                            foreach (var businessData in validBusinessData)
                            {
                                var businessId = businessData.FacebookBusinessId;
                                byBusinessAggregated.AddOrUpdate(
                                    businessId,
                                    businessData,
                                    (key, existing) => WhatsappCloudApiConversationUsageHelper
                                        .SumInternalByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsDto(
                                            existing,
                                            businessData));
                            }
                        }

                        // Filter and aggregate ByWaba results - only include valid WABA keys
                        if (result.ByWabaDto?.Count > 0)
                        {
                            // Pre-filter valid WABA data and pre-build keys to avoid repeated string concatenation
                            var validWabaData =
                                new List<(string Key, InternalByWabaWhatsappCloudApiDailyConversationUsageAnalyticsDto
                                    Data)>(result.ByWabaDto.Count);
                            foreach (var wabaData in result.ByWabaDto)
                            {
                                var key = $"{wabaData.FacebookBusinessId}_{wabaData.WabaId}";
                                if (validWabaKeys.Contains(key))
                                {
                                    validWabaData.Add((key, wabaData));
                                }
                            }

                            // Process valid WABA data with optimized AddOrUpdate
                            foreach (var (key, wabaData) in validWabaData)
                            {
                                byWabaAggregated.AddOrUpdate(
                                    key,
                                    wabaData,
                                    (existingKey, existing) => WhatsappCloudApiConversationUsageHelper
                                        .SumInternalByWabaWhatsappCloudApiDailyConversationUsageAnalyticsDto(
                                            existing,
                                            wabaData));
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "Error processing month range {Start} to {End}",
                            monthRange.Start.ToString("yyyy-MM-dd"),
                            monthRange.End.ToString("yyyy-MM-dd"));
                    }
                });

            // Convert aggregated results to lists with optimized sorting and minimal allocations
            var byBusinessResult =
                new List<InternalByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsDto>(
                    byBusinessAggregated.Count);
            var byWabaResult =
                new List<InternalByWabaWhatsappCloudApiDailyConversationUsageAnalyticsDto>(byWabaAggregated.Count);

            // Extract and sort business results more efficiently
            var businessEntries = byBusinessAggregated.ToArray();
            Array.Sort(businessEntries, (a, b) => string.CompareOrdinal(a.Key, b.Key));

            // Add sorted business results directly without LINQ
            foreach (var keyValuePair in businessEntries)
            {
                byBusinessResult.Add(keyValuePair.Value);
            }

            // Extract and sort WABA results more efficiently
            var wabaEntries = byWabaAggregated.ToArray();
            Array.Sort(wabaEntries, (a, b) => string.CompareOrdinal(a.Key, b.Key));

            // Add sorted WABA results directly without LINQ
            foreach (var keyValuePair in wabaEntries)
            {
                byWabaResult.Add(keyValuePair.Value);
            }

            return (byBusinessResult, byWabaResult);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error in GetInternalWhatsappCloudApiConversationUsageAnalytics for period {Start} to {End}",
                start.ToString("yyyy-MM-dd"),
                end.ToString("yyyy-MM-dd"));

            return (new List<InternalByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsDto>(),
                new List<InternalByWabaWhatsappCloudApiDailyConversationUsageAnalyticsDto>());
        }
    }

    private static List<(DateTime Start, DateTime End)> GenerateSimpleMonthRanges(DateTime start, DateTime end)
    {
        var ranges = new List<(DateTime Start, DateTime End)>();

        // Pre-calculate the number of months to avoid repeated calculations
        var totalMonths = ((end.Year - start.Year) * 12) + end.Month - start.Month + 1;
        ranges.Capacity = totalMonths; // Pre-allocate capacity for better performance

        for (var i = (start.Year * 12) + start.Month - 1;
             i < (end.Year * 12) + end.Month;
             i++)
        {
            var year = i / 12;
            var month = (i % 12) + 1;

            // Determine the start date for this month range
            DateTime monthStart;
            if (i == (start.Year * 12) + start.Month - 1)
            {
                // First month: use the actual start date
                monthStart = start;
            }
            else
            {
                // Other months: use the first day of the month
                monthStart = new DateTime(year, month, 1, 0, 0, 0, DateTimeKind.Utc);
            }

            // Determine the end date for this month range
            DateTime monthEnd;
            if (i == (end.Year * 12) + end.Month - 1)
            {
                // Last month: use the actual end date
                monthEnd = end;
            }
            else
            {
                // Other months: use the last day of the month
                monthEnd = new DateTime(year, month, DateTime.DaysInMonth(year, month), 23, 59, 59, DateTimeKind.Utc);
            }

            ranges.Add((monthStart, monthEnd));
        }

        return ranges;
    }

    private async Task<List<InternalWhatsappCloudApiBalanceDto>> GetInternalWhatsappCloudApiBalanceDtosAsync()
    {
        const string cacheKey = "Internal_GetAllWhatsAppCloudApiBalanceDto";

        try
        {
            // Try to get cached data first
            var cachedData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);

            if (!string.IsNullOrWhiteSpace(cachedData))
            {
                _logger.LogInformation("Returning cached InternalWhatsappCloudApiBalanceDtos");
                return JsonConvert.DeserializeObject<List<InternalWhatsappCloudApiBalanceDto>>(cachedData);
            }

            // If the cache is empty, get data from API
            var response = await GetAllWhatsappCloudApiBusinessBalances();

            if (response?.BusinessBalances != null)
            {
                // Pre-allocate list with capacity for better performance
                var balanceDtos = new List<InternalWhatsappCloudApiBalanceDto>(response.BusinessBalances.Count);

                foreach (var balance in response.BusinessBalances)
                {
                    var balanceDto = new InternalWhatsappCloudApiBalanceDto
                    {
                        FacebookBusinessId = balance.FacebookBusinessId,
                        FacebookBusinessName = balance.FacebookBusinessName,
                        FacebookBusinessWabas = balance.FacebookBusinessWabas is { Count: > 0 }
                            ? new List<InternalWhatsappCloudApiWabaDto>(balance.FacebookBusinessWabas.Count)
                            : []
                    };

                    if (balance.FacebookBusinessWabas?.Count > 0)
                    {
                        foreach (var waba in balance.FacebookBusinessWabas)
                        {
                            balanceDto.FacebookBusinessWabas.Add(new InternalWhatsappCloudApiWabaDto
                            {
                                FacebookWabaId = waba.FacebookWabaId,
                                FacebookWabaName = waba.FacebookWabaName
                            });
                        }
                    }

                    balanceDtos.Add(balanceDto);
                }

                // Cache the data for 1 hour
                var serializedData = JsonConvert.SerializeObject(balanceDtos);
                await _cacheManagerService.SaveCacheWithConstantKeyAsync(
                    cacheKey,
                    serializedData,
                    TimeSpan.FromHours(1));

                _logger.LogInformation(
                    "Successfully retrieved and cached {Count} InternalWhatsappCloudApiBalanceDtos",
                    balanceDtos.Count);
                return balanceDtos;
            }

            _logger.LogWarning("No business balances found in API response");
            return[];
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving InternalWhatsappCloudApiBalanceDtos");
            return[];
        }
    }

    private async Task<List<ByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData>>
        GetByBusinessDailyWhatsappCloudApiConversationUsageAnalyticMonthlySnapshot(DateTime startOfMonth)
    {
        var snapshotMonth = startOfMonth.ToString("yyyyMM");

        try
        {
            var snapshotFileStream = await _azureBlobStorageService.DownloadFromAzureBlob(
                $"whatsappcloudapi/by-business-month-daily-conversation-usage-snapshot-{snapshotMonth}.csv",
                "internalsnapshotdata");

            // Use more efficient memory stream handling
            using var ms = new MemoryStream(snapshotFileStream.ToArray());
            using var reader = new StreamReader(ms);

            var csvReaderConfiguration = new CsvConfiguration(cultureInfo: CultureInfo.InvariantCulture)
            {
                HeaderValidated = _ => { },
                MissingFieldFound = _ => { },
                BufferSize = 4096, // Optimize buffer size for better performance
                Delimiter = ",",
                HasHeaderRecord = true
            };

            using var csv = new CsvReader(reader, csvReaderConfiguration);

            // Use more efficient enumeration and pre-allocate list
            var records = csv.GetRecords<ByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData>();

            return records.ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error retrieving by-business WhatsApp Cloud API conversation usage analytics for month {Month}",
                snapshotMonth);
            return null;
        }
    }

    private async Task<List<ByWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData>>
        GetByWabaDailyWhatsappCloudApiConversationUsageAnalyticMonthlySnapshot(DateTime startOfMonth)
    {
        var snapshotMonth = startOfMonth.ToString("yyyyMM");

        try
        {
            var snapshotFileStream = await _azureBlobStorageService.DownloadFromAzureBlob(
                $"whatsappcloudapi/by-waba-month-daily-conversation-usage-snapshot-{snapshotMonth}.csv",
                "internalsnapshotdata");

            // Use more efficient memory stream handling
            using var ms = new MemoryStream(snapshotFileStream.ToArray());
            using var reader = new StreamReader(ms);

            var csvReaderConfiguration = new CsvConfiguration(cultureInfo: CultureInfo.InvariantCulture)
            {
                HeaderValidated = _ => { },
                MissingFieldFound = _ => { },
                BufferSize = 4096, // Optimize buffer size for better performance
                Delimiter = ",",
                HasHeaderRecord = true
            };

            using var csv = new CsvReader(reader, csvReaderConfiguration);

            // Use more efficient enumeration and pre-allocate list
            var records = csv.GetRecords<ByWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData>();

            return records.ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error retrieving by-waba WhatsApp Cloud API conversation usage analytics for month {Month}",
                snapshotMonth);
            return null;
        }
    }

    private static List<(DateTime Start, DateTime End, bool IsNotFullMonth)> GenerateMonthRanges(
        DateTime start,
        DateTime end)
    {
        var ranges = new List<(DateTime Start, DateTime End, bool IsNotFullMonth)>();

        // Pre-calculate the number of months to avoid repeated calculations
        var totalMonths = ((end.Year - start.Year) * 12) + end.Month - start.Month + 1;
        ranges.Capacity = totalMonths; // Pre-allocate capacity for better performance

        for (var i = (start.Year * 12) + start.Month - 1;
             i < (end.Year * 12) + end.Month;
             i++)
        {
            var year = i / 12;
            var month = (i % 12) + 1;
            var monthEndDate = new DateTime(year, month, DateTime.DaysInMonth(year, month), 0, 0, 0, DateTimeKind.Utc);
            var isNotFullMonth = monthEndDate > end;
            var endDate = isNotFullMonth ? end : monthEndDate;
            var monthStart = new DateTime(year, month, 1, 0, 0, 0, DateTimeKind.Utc);

            ranges.Add((monthStart, endDate, isNotFullMonth));
        }

        return ranges;
    }

    private async Task<List<ManagementWhatsappCloudApiConversationUsageAnalytic>>
        GetManagementWhatsappCloudApiConversationUsageAnalytics(
            DateTime start,
            DateTime end,
            bool isNotFullMonth = false)
    {
        // Try to get snapshot data first
        var snapshotData = await GetByBusinessDailyWhatsappCloudApiConversationUsageAnalyticMonthlySnapshot(start);

        if (snapshotData != null && snapshotData.Count != 0)
        {
            // Pre-allocate results list with capacity for better performance
            var results = new List<ManagementWhatsappCloudApiConversationUsageAnalytic>(snapshotData.Count);

            // Group data more efficiently with pre-allocated dictionary
            var groupedData = new Dictionary<string, List<ByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData>>();

            foreach (var item in snapshotData)
            {
                if (!groupedData.TryGetValue(item.FacebookBusinessId, out var group))
                {
                    group =[];
                    groupedData[item.FacebookBusinessId] = group;
                }

                group.Add(item);
            }

            // Process snapshot data in parallel with optimized concurrency
            var parallelResults = new ConcurrentBag<ManagementWhatsappCloudApiConversationUsageAnalytic>();

            await Parallel.ForEachAsync(
                groupedData,
                new ParallelOptions
                {
                    MaxDegreeOfParallelism = Math.Min(Environment.ProcessorCount, 20)
                },
                (kvp, cancellationToken) =>
                {
                    try
                    {
                        var (_, group) = kvp;

                        // Use more efficient aggregation with pre-allocated accumulator
                        var aggregated = group.Aggregate(
                            WhatsappCloudApiConversationUsageHelper
                                .SumByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData);

                        // Create Money objects once to avoid repeated allocations
                        var totalUsed = new Money(aggregated.TotalUsedCurrencyIsoCode, aggregated.TotalUsedAmount);
                        var totalMarkup = new Money(aggregated.TotalMarkupCurrencyIsoCode, aggregated.TotalMarkupAmount);
                        var totalTransactionHandlingFee = new Money(
                            aggregated.TotalTransactionHandlingFeeCurrencyIsoCode,
                            aggregated.TotalTransactionHandlingFeeAmount);

                        var result = new ManagementWhatsappCloudApiConversationUsageAnalytic(
                            new WhatsappCloudApiConversationUsageAnalyticDto(
                                totalUsed: totalUsed,
                                totalMarkup: totalMarkup,
                                totalTransactionHandlingFee: totalTransactionHandlingFee),
                            aggregated.FacebookBusinessId,
                            aggregated.FacebookBusinessName);

                        parallelResults.Add(result);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing snapshot data for business {BusinessId}", kvp.Key);
                    }

                    return ValueTask.CompletedTask;
                });

            // Convert to list more efficiently
            results.AddRange(parallelResults);
            return results;
        }

        // Fall back to API call with optimized error handling
        try
        {
            var granularity = isNotFullMonth
                ? WhatsappConversationAnalyticGranularityConst.DAILY
                : WhatsappConversationAnalyticGranularityConst.MONTHLY;

            var apiResponse = await GetAllWhatsappCloudApiConversationUsageAnalytics(start, end, granularity);
            return apiResponse.WhatsappCloudApiConversationUsageAnalytics;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed to retrieve API data for period {Start} to {End}",
                start.ToString("yyyy-MM-dd"),
                end.ToString("yyyy-MM-dd"));
            return[];
        }
    }

    private async Task<(List<InternalByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsDto>
            ByBusinessDto, List<InternalByWabaWhatsappCloudApiDailyConversationUsageAnalyticsDto> ByWabaDto)>
        GetInternalWhatsappCloudApiConversationUsageAnalyticsDto(DateTime start, DateTime end)
    {
        // Get snapshot data for the start of month with optimized parallel retrieval
        var startOfMonth = new DateTime(start.Year, start.Month, 1, 0, 0, 0, DateTimeKind.Utc);

        // Parallel retrieval of snapshot data
        var byBusinessSnapshotTask =
            GetByBusinessDailyWhatsappCloudApiConversationUsageAnalyticMonthlySnapshot(startOfMonth);
        var byWabaSnapshotTask = GetByWabaDailyWhatsappCloudApiConversationUsageAnalyticMonthlySnapshot(startOfMonth);

        await Task.WhenAll(byBusinessSnapshotTask, byWabaSnapshotTask);
        var byBusinessSnapshotData = await byBusinessSnapshotTask;
        var byWabaSnapshotData = await byWabaSnapshotTask;

        // Check if snapshot data is available with optimized boolean logic
        var hasSnapshotData = byBusinessSnapshotData?.Count > 0 && byWabaSnapshotData?.Count > 0;

        // If no snapshot data, enumerate days and perform DAILY requests per day in parallel, aggregating results
        if (!hasSnapshotData)
        {
            _logger.LogInformation(
                "No snapshot data available for month {Month}, fetching per-day DAILY analytics in parallel",
                startOfMonth.ToString("yyyyMM"));

            // Enumerate all calendar days in inclusive range
            var startDate = start.Date;
            var endDate = end.Date;
            var dates = new List<DateTime>();
            for (var d = startDate; d <= endDate; d = d.AddDays(1))
            {
                dates.Add(new DateTime(d.Year, d.Month, d.Day, 0, 0, 0, DateTimeKind.Utc));
            }

            var byBusinessAggregated = new ConcurrentDictionary<string,
                InternalByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsDto>(StringComparer.Ordinal);
            var byWabaAggregated = new ConcurrentDictionary<string,
                InternalByWabaWhatsappCloudApiDailyConversationUsageAnalyticsDto>(StringComparer.Ordinal);

            await Parallel.ForEachAsync(
                dates,
                new ParallelOptions
                {
                    MaxDegreeOfParallelism = 10
                },
                async (dayStartUtc, cancellationToken) =>
                {
                    // Define the end of the same day (inclusive)
                    var dayEndUtc = new DateTime(dayStartUtc.Year, dayStartUtc.Month, dayStartUtc.Day, 23, 59, 59,
                        DateTimeKind.Utc);

                    // Fetch DAILY analytics for this specific day
                    var dayResponse = await GetAllWhatsappCloudApiConversationUsageAnalytics(
                        dayStartUtc,
                        dayEndUtc,
                        WhatsappConversationAnalyticGranularityConst.DAILY);

                    foreach (var analytic in dayResponse.WhatsappCloudApiConversationUsageAnalytics)
                    {
                        var converted = CmsCompanyDetailConverter
                            .ConvertToInternalWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData(
                                analytic,
                                dayEndUtc);

                        var byBusinessDto = _mapper
                            .Map<InternalByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsDto>(
                                converted.ByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshot);
                        var byWabaDtos = _mapper
                            .Map<List<InternalByWabaWhatsappCloudApiDailyConversationUsageAnalyticsDto>>(
                                converted.ByWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshots);

                        // Aggregate by Business (thread-safe)
                        byBusinessAggregated.AddOrUpdate(
                            byBusinessDto.FacebookBusinessId,
                            byBusinessDto,
                            (key, existing) => WhatsappCloudApiConversationUsageHelper
                                .SumInternalByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsDto(existing,
                                    byBusinessDto));

                        // Aggregate by WABA (thread-safe)
                        foreach (var byWabaDto in byWabaDtos)
                        {
                            var wabaKey = $"{byWabaDto.FacebookBusinessId}_{byWabaDto.WabaId}";
                            byWabaAggregated.AddOrUpdate(
                                wabaKey,
                                byWabaDto,
                                (key, existing) => WhatsappCloudApiConversationUsageHelper
                                    .SumInternalByWabaWhatsappCloudApiDailyConversationUsageAnalyticsDto(existing,
                                        byWabaDto));
                        }
                    }
                });

            var byBusinessList = byBusinessAggregated.Count > 0
                ? new List<InternalByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsDto>(
                    byBusinessAggregated.Values)
                : new List<InternalByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsDto>();
            var byWabaList = byWabaAggregated.Count > 0
                ? new List<InternalByWabaWhatsappCloudApiDailyConversationUsageAnalyticsDto>(
                    byWabaAggregated.Values)
                : new List<InternalByWabaWhatsappCloudApiDailyConversationUsageAnalyticsDto>();

            return (byBusinessList, byWabaList);
        }

        // Optimized filtering for partial month with pre-calculated date strings
        var isNotFullMonth = start.Day != 1 || end.Day != DateTime.DaysInMonth(end.Year, end.Month);

        if (isNotFullMonth)
        {
            var startDate = start.ToString("yyyy-MM-dd");
            var endDate = end.ToString("yyyy-MM-dd");

            // Use more efficient filtering with pre-allocated capacity and early exit
            if (byBusinessSnapshotData.Count > 0)
            {
                byBusinessSnapshotData = byBusinessSnapshotData
                    .Where(x => string.CompareOrdinal(x.Date, startDate) >= 0 &&
                                string.CompareOrdinal(x.Date, endDate) <= 0)
                    .ToList();
            }

            if (byWabaSnapshotData.Count > 0)
            {
                byWabaSnapshotData = byWabaSnapshotData
                    .Where(x => string.CompareOrdinal(x.Date, startDate) >= 0 &&
                                string.CompareOrdinal(x.Date, endDate) <= 0)
                    .ToList();
            }
        }

        // Early exit if no data after filtering
        if (byBusinessSnapshotData.Count == 0 && byWabaSnapshotData.Count == 0)
        {
            return (new List<InternalByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsDto>(),
                new List<InternalByWabaWhatsappCloudApiDailyConversationUsageAnalyticsDto>());
        }

        // Optimized aggregation with pre-allocated dictionaries
        var byBusinessDict =
            new Dictionary<string, ByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData>(
                byBusinessSnapshotData.Count);

        if (byBusinessSnapshotData.Count > 0)
        {
            foreach (var item in byBusinessSnapshotData)
            {
                if (byBusinessDict.TryGetValue(item.FacebookBusinessId, out var existing))
                {
                    byBusinessDict[item.FacebookBusinessId] = WhatsappCloudApiConversationUsageHelper
                        .SumByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData(existing, item);
                }
                else
                {
                    byBusinessDict[item.FacebookBusinessId] = item;
                }
            }
        }

        // Optimized by-waba aggregation with pre-allocated dictionary
        var byWabaDict = new Dictionary<string, ByWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData>(
            byWabaSnapshotData.Count);

        if (byWabaSnapshotData.Count > 0)
        {
            foreach (var item in byWabaSnapshotData)
            {
                var key = $"{item.FacebookBusinessId}_{item.WabaId}";
                if (byWabaDict.TryGetValue(key, out var existing))
                {
                    byWabaDict[key] = WhatsappCloudApiConversationUsageHelper
                        .SumByWabaWhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData(existing, item);
                }
                else
                {
                    byWabaDict[key] = item;
                }
            }
        }

        // Optimized DTO conversion with pre-allocated lists
        var byBusinessDtos = byBusinessDict.Count > 0
            ? _mapper.Map<List<InternalByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsDto>>(
                byBusinessDict.Values)
            : new List<InternalByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsDto>();

        var byWabaDtos = byWabaDict.Count > 0
            ? _mapper.Map<List<InternalByWabaWhatsappCloudApiDailyConversationUsageAnalyticsDto>>(byWabaDict.Values)
            : new List<InternalByWabaWhatsappCloudApiDailyConversationUsageAnalyticsDto>();

        return (byBusinessDtos, byWabaDtos);
    }
}