using System;
using System.Collections.Generic;
using System.Diagnostics.Metrics;
using System.Linq;
using Microsoft.Extensions.Logging;
using Travis_backend.Constants;
using Travis_backend.OpenTelemetry.Meters.Constants;

namespace Travis_backend.OpenTelemetry.Meters;

public interface IConversationMeters : IBasicMeters
{
}

public class ConversationMeters : BaseMeters, IConversationMeters
{
    private const string ConversationMeterName = "conversation_meters.trigger_type";

    // Capturing the conversation meters with messaging types to analysis the consumption of the actions
    private readonly Dictionary<string, Counter<int>> _conversationCounters;
    private readonly ILogger<IConversationMeters> _logger;

    private readonly List<string> _optionsParams = new ()
    {
        ConversationMeterOptions.SendSuccess,
        ConversationMeterOptions.SendFailed,
        ConversationMeterOptions.ReceiveSuccess,
        ConversationMeterOptions.ReceiveFailed,
        ConversationMeterOptions.ProcessMessageSuccess,
        ConversationMeterOptions.ProcessMessageFailed,
    };

    public ConversationMeters(IMeterFactory meterFactory, ILogger<IConversationMeters> logger)
        : base(meterFactory, logger)
    {
        _logger = logger;
        if (!IsMeterEnabled)
        {
            return;
        }

        _conversationCounters = _optionsParams.SelectMany(
                o => _conversationTypes.Select(
                    c => (Key: GetComposeKey(c.Key, o), Value: GetComposeKey(c.Value, o))))
            .ToDictionary(
                k => k.Key,
                k => CreateCounter<int>($"{ConversationMeterName}.{k.Value}"));
    }

    protected override Counter<int> GetCounter<T>(T name, string? option = null)
    {
        if (name is string triggerType &&
            _conversationCounters.TryGetValue(GetComposeKey(triggerType, option), out var counter))
        {
            _logger.LogInformation(
                "[ConversationStateTypes] {ConversationType} Has Incremented",
                GetComposeKey(triggerType, option));
            return counter;
        }

        throw new ArgumentOutOfRangeException(nameof(name), name, null);
    }

    private readonly Dictionary<string, string> _conversationTypes = new ()
    {
        {
            ChannelTypes.WhatsappTwilio, "whatsapp_twilio"
        },
        {
            ChannelTypes.Whatsapp360Dialog, "whatsapp_360_dialog"
        },
        {
            ChannelTypes.WhatsappCloudApi, "whatsapp_cloud_api"
        },
        {
            ChannelTypes.Sms, "sms"
        },
        {
            ChannelTypes.Facebook, "facebook"
        },
        {
            ChannelTypes.Instagram, "instagram"
        },
        {
            ChannelTypes.LiveChat, "live_chat"
        },
        {
            ChannelTypes.Viber, "viber"
        },
        {
            ChannelTypes.Telegram, "telegram"
        },
        {
            ChannelTypes.Email, "email"
        },
        {
            ChannelTypes.Line, "line"
        },
        {
            ChannelTypes.Wechat, "wechat"
        },
        {
            ChannelTypes.Tiktok, "tiktok"
        },
        {
            ChannelTypes.Note, "note"
        },
        {
            ChannelTypes.LiveChatV2, "livechat_v2"
        }
    };
}