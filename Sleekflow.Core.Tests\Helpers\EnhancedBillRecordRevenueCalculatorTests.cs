using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.InternalDomain.Models;

namespace Sleekflow.Core.Tests.Helpers;

/// <summary>
/// Comprehensive tests for enhanced BillRecordRevenueCalculator with timezone-aware functionality,
/// variance tracking, and monitoring capabilities.
/// </summary>
[TestFixture]
public class EnhancedBillRecordRevenueCalculatorTests
{
    private Mock<ITimezoneAwareMrrCalculationService> _mockTimezoneService = null!;
    private BillRecordRevenueCalculatorService _billRecordRevenueCalculatorService = null!;
    private Mock<ILogger<BillRecordRevenueCalculatorService>> _mockBillRecordRevenueCalculatorServiceLogger = null!;

    [SetUp]
    public void Setup()
    {
        _mockTimezoneService = new Mock<ITimezoneAwareMrrCalculationService>();
        _mockBillRecordRevenueCalculatorServiceLogger = new Mock<ILogger<BillRecordRevenueCalculatorService>>();
        _billRecordRevenueCalculatorService = new BillRecordRevenueCalculatorService(
            _mockBillRecordRevenueCalculatorServiceLogger.Object,
            _mockTimezoneService.Object);
    }

    #region Backward Compatibility Tests

    [Test]
    public void SumMonthlyRecurringRevenue_WithNullCompanyId_ShouldUseOriginalCalculation()
    {
        // Arrange
        var billRecords = new List<BillRecord>
        {
            new ()
            {
                Id = 1,
                CompanyId = null, // No company ID
                PayAmount = 1000,
                currency = "usd",
                SubscriptionPlanId = "sleekflow_v10_startup",
                created = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                PeriodStart = new DateTime(2023, 1, 1),
                PeriodEnd = new DateTime(2023, 2, 1),
                CmsSalesPaymentRecords = new List<CmsSalesPaymentRecord>()
            }
        };

        // Act
        var testDateTime = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        var result =
            _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                billRecords,
                "UTC",
                testDateTime);

        // Assert
        result.Should().Be(1000); // Should use original month diff calculation (1 month)

        // Verify timezone service was not called
        _mockTimezoneService.Verify(
            x => x.GetPreciseMonthDiff(
                It.IsAny<DateTime>(),
                It.IsAny<DateTime>(),
                It.IsAny<string>(),
                It.IsAny<string?>()),
            Times.Never);
    }

    [Test]
    public void SumMonthlyRecurringRevenue_WithEmptyCompanyId_ShouldUseOriginalCalculation()
    {
        // Arrange
        var billRecords = new List<BillRecord>
        {
            new ()
            {
                Id = 1,
                CompanyId = string.Empty, // Empty company ID
                PayAmount = 1000,
                currency = "usd",
                SubscriptionPlanId = "sleekflow_v10_startup",
                created = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                PeriodStart = new DateTime(2023, 1, 1),
                PeriodEnd = new DateTime(2023, 2, 1),
                CmsSalesPaymentRecords = new List<CmsSalesPaymentRecord>()
            }
        };

        // Act
        var testDateTime = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        var result =
            _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                billRecords,
                "UTC",
                testDateTime);

        // Assert
        result.Should().Be(1000); // Should use original month diff calculation

        // Verify timezone service was not called
        _mockTimezoneService.Verify(
            x => x.GetPreciseMonthDiff(
                It.IsAny<DateTime>(),
                It.IsAny<DateTime>(),
                It.IsAny<string>(),
                It.IsAny<string?>()),
            Times.Never);
    }

    #endregion

    #region Enhanced Calculation Tests

    [Test]
    public void SumMonthlyRecurringRevenue_WithValidCompanyId_ShouldUseEnhancedCalculation()
    {
        // Arrange
        var companyId = "test-company-id";
        var billRecords = new List<BillRecord>
        {
            new ()
            {
                Id = 1,
                CompanyId = companyId,
                PayAmount = 1000,
                currency = "usd",
                SubscriptionPlanId = "sleekflow_v10_startup",
                created = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                PeriodStart = new DateTime(2023, 1, 1),
                PeriodEnd = new DateTime(2023, 2, 1),
                CmsSalesPaymentRecords = new List<CmsSalesPaymentRecord>()
            }
        };

        _mockTimezoneService.Setup(x => x.GetPreciseMonthDiff(
                It.IsAny<DateTime>(),
                It.IsAny<DateTime>(),
                companyId,
                It.IsAny<string?>()))
            .Returns(1.1m); // Slightly different from original calculation

        // Act
        var testDateTime = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        var result =
            _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                billRecords,
                "UTC",
                testDateTime);

        // Assert
        result.Should().BeApproximately(909.09m, 0.01m); // 1000 / 1.1

        // Verify timezone service was called
        _mockTimezoneService.Verify(
            x => x.GetPreciseMonthDiff(
                It.IsAny<DateTime>(),
                It.IsAny<DateTime>(),
                companyId,
                It.IsAny<string?>()),
            Times.Once);
    }

    #endregion

    #region Variance Tracking Tests

    [Test]
    public void SumMonthlyRecurringRevenue_WithLowVariance_ShouldLogInformation()
    {
        // Arrange
        var companyId = "test-company-id";
        var billRecords = new List<BillRecord>
        {
            new ()
            {
                Id = 1,
                CompanyId = companyId,
                PayAmount = 1000,
                currency = "usd",
                SubscriptionPlanId = "sleekflow_v10_startup",
                created = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                PeriodStart = new DateTime(2023, 1, 1),
                PeriodEnd = new DateTime(2023, 2, 1),
                CmsSalesPaymentRecords = new List<CmsSalesPaymentRecord>()
            }
        };

        _mockTimezoneService.Setup(x => x.GetPreciseMonthDiff(
                It.IsAny<DateTime>(),
                It.IsAny<DateTime>(),
                companyId,
                It.IsAny<string?>()))
            .Returns(1.005m); // 0.5% variance

        // Act
        var testDateTime = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        var result =
            _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                billRecords,
                "UTC",
                testDateTime);

        // Assert
        result.Should().BeApproximately(995.02m, 0.01m); // 1000 / 1.005

        // Verify information logging was called (variance < 1%)
        _mockBillRecordRevenueCalculatorServiceLogger.Verify(
            x => x.Log(
                LogLevel.Debug,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Timezone-aware MRR calculation")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.AtLeastOnce);

        // Verify no warning was logged (variance < 1%)
        _mockBillRecordRevenueCalculatorServiceLogger.Verify(
            x => x.Log(
                LogLevel.Warning,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("High MRR calculation variance")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Never);
    }

    [Test]
    public void SumMonthlyRecurringRevenue_WithHighVariance_ShouldLogWarning()
    {
        // Arrange
        var companyId = "test-company-id";
        var billRecords = new List<BillRecord>
        {
            new ()
            {
                Id = 1,
                CompanyId = companyId,
                PayAmount = 1000,
                currency = "usd",
                SubscriptionPlanId = "sleekflow_v10_startup",
                created = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                PeriodStart = new DateTime(2023, 1, 1),
                PeriodEnd = new DateTime(2023, 2, 1),
                CmsSalesPaymentRecords = new List<CmsSalesPaymentRecord>()
            }
        };

        _mockTimezoneService.Setup(x => x.GetPreciseMonthDiff(
                It.IsAny<DateTime>(),
                It.IsAny<DateTime>(),
                companyId,
                It.IsAny<string?>()))
            .Returns(1.05m); // 5% variance

        // Act
        var testDateTime = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        var result =
            _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                billRecords,
                "UTC",
                testDateTime);

        // Assert
        result.Should().BeApproximately(952.38m, 0.01m); // 1000 / 1.05

        // Verify warning logging was called (variance > 1%)
        _mockBillRecordRevenueCalculatorServiceLogger.Verify(
            x => x.Log(
                LogLevel.Debug,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("High MRR calculation variance detected")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.AtLeastOnce);
    }

    #endregion

    #region Error Handling and Fallback Tests

    [Test]
    public void SumMonthlyRecurringRevenue_WithTimezoneServiceException_ShouldFallbackToOriginal()
    {
        // Arrange
        var companyId = "test-company-id";
        var billRecords = new List<BillRecord>
        {
            new ()
            {
                Id = 1,
                CompanyId = companyId,
                PayAmount = 1000,
                currency = "usd",
                SubscriptionPlanId = "sleekflow_v10_startup",
                created = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                PeriodStart = new DateTime(2023, 1, 1),
                PeriodEnd = new DateTime(2023, 2, 1),
                CmsSalesPaymentRecords = new List<CmsSalesPaymentRecord>()
            }
        };

        _mockTimezoneService.Setup(x => x.GetPreciseMonthDiff(
                It.IsAny<DateTime>(),
                It.IsAny<DateTime>(),
                companyId,
                It.IsAny<string?>()))
            .Throws(new Exception("Timezone service failed"));

        // Act
        var testDateTime = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        var result =
            _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                billRecords,
                "UTC",
                testDateTime);

        // Assert
        result.Should().Be(1000); // Should fallback to original calculation

        // Verify error logging was called
        _mockBillRecordRevenueCalculatorServiceLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Enhanced MRR calculation failed")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.AtLeastOnce);
    }

    [Test]
    public void SumMonthlyRecurringRevenue_WithNoTimezoneService_ShouldUseOriginalCalculation()
    {
        // Arrange
        var companyId = "test-company-id";
        var billRecords = new List<BillRecord>
        {
            new ()
            {
                Id = 1,
                CompanyId = companyId,
                PayAmount = 1000,
                currency = "usd",
                SubscriptionPlanId = "sleekflow_v10_startup",
                created = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                PeriodStart = new DateTime(2023, 1, 1),
                PeriodEnd = new DateTime(2023, 2, 1),
                CmsSalesPaymentRecords = new List<CmsSalesPaymentRecord>()
            }
        };

        // Act
        var testDateTime = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        var result =
            _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                billRecords,
                "UTC",
                testDateTime);

        // Assert
        result.Should().Be(1000); // Should use original calculation

        // Verify error logging was called (timezone service failed, falling back to original calculation)
        _mockBillRecordRevenueCalculatorServiceLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Enhanced MRR calculation failed")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.AtLeastOnce);
    }

    #endregion

    #region Complex Scenarios Tests

    [Test]
    public void SumMonthlyRecurringRevenue_WithMultipleBillRecords_ShouldCalculateCorrectly()
    {
        // Arrange
        var companyId = "test-company-id";
        var billRecords = new List<BillRecord>
        {
            new ()
            {
                Id = 1,
                CompanyId = companyId,
                PayAmount = 1000,
                currency = "usd",
                SubscriptionPlanId = "sleekflow_v10_startup",
                created = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                PeriodStart = new DateTime(2023, 1, 1),
                PeriodEnd = new DateTime(2023, 2, 1),
                CmsSalesPaymentRecords = new List<CmsSalesPaymentRecord>()
            },
            new ()
            {
                Id = 2,
                CompanyId = companyId,
                PayAmount = 500,
                currency = "usd",
                SubscriptionPlanId = "sleekflow_v10_countrytier1_whatsapp_phone_number_monthly_aed",
                created = new DateTime(2023, 1, 15, 0, 0, 0, DateTimeKind.Utc),
                PeriodStart = new DateTime(2023, 1, 15),
                PeriodEnd = new DateTime(2023, 2, 15),
                CmsSalesPaymentRecords = new List<CmsSalesPaymentRecord>()
            }
        };

        _mockTimezoneService.SetupSequence(x => x.GetPreciseMonthDiff(
                It.IsAny<DateTime>(),
                It.IsAny<DateTime>(),
                companyId,
                It.IsAny<string?>()))
            .Returns(1.0m) // First record: exact month
            .Returns(1.0m); // Second record: exact month

        // Act
        var testDateTime = new DateTime(2023, 1, 15, 0, 0, 0, DateTimeKind.Utc);
        var result =
            _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                billRecords,
                "UTC",
                testDateTime);

        // Assert
        result.Should().Be(1500); // 1000 + 500

        // Verify timezone service was called twice
        _mockTimezoneService.Verify(
            x => x.GetPreciseMonthDiff(
                It.IsAny<DateTime>(),
                It.IsAny<DateTime>(),
                companyId,
                It.IsAny<string?>()),
            Times.Exactly(2));
    }

    [Test]
    public void SumMonthlyRecurringRevenue_WithMixedCompanyIds_ShouldHandleCorrectly()
    {
        // Arrange
        var companyId1 = "company-1";
        var companyId2 = "company-2";
        var billRecords = new List<BillRecord>
        {
            new ()
            {
                Id = 1,
                CompanyId = companyId1,
                PayAmount = 1000,
                currency = "usd",
                SubscriptionPlanId = "sleekflow_v10_countrytier1_whatsapp_phone_number_monthly_aed",
                created = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                PeriodStart = new DateTime(2023, 1, 1),
                PeriodEnd = new DateTime(2023, 2, 1),
                CmsSalesPaymentRecords = new List<CmsSalesPaymentRecord>()
            },
            new ()
            {
                Id = 2,
                CompanyId = null, // No company ID
                PayAmount = 500,
                currency = "usd",
                SubscriptionPlanId = "sleekflow_v10_countrytier1_whatsapp_phone_number_monthly_aed",
                created = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                PeriodStart = new DateTime(2023, 1, 1),
                PeriodEnd = new DateTime(2023, 2, 1),
                CmsSalesPaymentRecords = new List<CmsSalesPaymentRecord>()
            },
            new ()
            {
                Id = 3,
                CompanyId = companyId2,
                PayAmount = 750,
                currency = "usd",
                SubscriptionPlanId = "sleekflow_v10_countrytier1_whatsapp_phone_number_monthly_aed",
                created = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                PeriodStart = new DateTime(2023, 1, 1),
                PeriodEnd = new DateTime(2023, 2, 1),
                CmsSalesPaymentRecords = new List<CmsSalesPaymentRecord>()
            }
        };

        _mockTimezoneService.Setup(x => x.GetPreciseMonthDiff(
                It.IsAny<DateTime>(),
                It.IsAny<DateTime>(),
                companyId1,
                It.IsAny<string?>()))
            .Returns(1.1m);

        _mockTimezoneService.Setup(x => x.GetPreciseMonthDiff(
                It.IsAny<DateTime>(),
                It.IsAny<DateTime>(),
                companyId2,
                It.IsAny<string?>()))
            .Returns(0.9m);

        // Act
        var testDateTime = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        var result =
            _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                billRecords,
                "UTC",
                testDateTime);

        // Assert
        // Company 1: 1000 / 1.1 ≈ 909.09
        // No company: 500 / 1 = 500 (original calculation)
        // Company 2: 750 / 0.9 ≈ 833.33
        // Total: 909.09 + 500 + 833.33 ≈ 2242.42
        result.Should().BeApproximately(2242.42m, 0.01m);
    }

    [Test]
    public void SumMonthlyRecurringRevenue_WithZeroRevenue_ShouldReturnZero()
    {
        // Arrange
        var companyId = "test-company-id";
        var billRecords = new List<BillRecord>
        {
            new ()
            {
                Id = 1,
                CompanyId = companyId,
                PayAmount = 0, // Zero revenue
                currency = "usd",
                SubscriptionPlanId = "sleekflow_v10_startup",
                created = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                PeriodStart = new DateTime(2023, 1, 1),
                PeriodEnd = new DateTime(2023, 2, 1),
                CmsSalesPaymentRecords = new List<CmsSalesPaymentRecord>()
            }
        };

        // Act
        var testDateTime = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        var result =
            _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                billRecords,
                "UTC",
                testDateTime);

        // Assert
        result.Should().Be(0);

        // Verify timezone service was not called for zero revenue
        _mockTimezoneService.Verify(
            x => x.GetPreciseMonthDiff(
                It.IsAny<DateTime>(),
                It.IsAny<DateTime>(),
                It.IsAny<string>(),
                It.IsAny<string?>()),
            Times.Never);
    }

    #endregion

    #region Grace Period Integration Tests

    [Test]
    public void SumMonthlyRecurringRevenue_WithCmsSalesPaymentRecords_ShouldIncludeInCalculation()
    {
        // Arrange
        var companyId = "test-company-id";
        var billRecords = new List<BillRecord>
        {
            new ()
            {
                Id = 1,
                CompanyId = companyId,
                PayAmount = 1000, // Stripe payment
                currency = "usd",
                SubscriptionPlanId = "sleekflow_v10_startup",
                created = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                PeriodStart = new DateTime(2023, 1, 1),
                PeriodEnd = new DateTime(2023, 2, 1),
                CmsSalesPaymentRecords =
                [
                    new CmsSalesPaymentRecord
                    {
                        Id = 1,
                        SubscriptionFee = 500, // Additional CMS payment
                        Currency = "usd"
                    }
                ]
            }
        };

        _mockTimezoneService.Setup(x => x.GetPreciseMonthDiff(
                It.IsAny<DateTime>(),
                It.IsAny<DateTime>(),
                companyId,
                It.IsAny<string?>()))
            .Returns(1.0m);

        // Act
        var testDateTime = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        var result =
            _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                billRecords,
                "UTC",
                testDateTime);

        // Assert
        result.Should().Be(1500); // 1000 + 500
    }

    #endregion

    #region Performance Tests

    [Test]
    public void SumMonthlyRecurringRevenue_WithLargeBillRecordList_ShouldPerformEfficiently()
    {
        // Arrange
        var companyId = "test-company-id";
        var billRecords = new List<BillRecord>();

        // Create 1000 bill records
        for (int i = 0; i < 1000; i++)
        {
            billRecords.Add(
                new BillRecord
                {
                    Id = i,
                    CompanyId = companyId,
                    PayAmount = 100,
                    currency = "usd",
                    SubscriptionPlanId = "sleekflow_v10_countrytier1_whatsapp_phone_number_monthly_aed",
                    created = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                    PeriodStart = new DateTime(2023, 1, 1),
                    PeriodEnd = new DateTime(2023, 2, 1),
                    CmsSalesPaymentRecords = new List<CmsSalesPaymentRecord>()
                });
        }

        _mockTimezoneService.Setup(x => x.GetPreciseMonthDiff(
                It.IsAny<DateTime>(),
                It.IsAny<DateTime>(),
                companyId,
                It.IsAny<string?>()))
            .Returns(1.0m);

        // Act
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var testDateTime = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        var result =
            _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                billRecords,
                "UTC",
                testDateTime);
        stopwatch.Stop();

        // Assert
        result.Should().Be(100000); // 1000 * 100
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(5000); // Should complete within 5 seconds

        // Verify timezone service was called 1000 times
        _mockTimezoneService.Verify(
            x => x.GetPreciseMonthDiff(
                It.IsAny<DateTime>(),
                It.IsAny<DateTime>(),
                companyId,
                It.IsAny<string?>()),
            Times.Exactly(1000));
    }

    #endregion
}