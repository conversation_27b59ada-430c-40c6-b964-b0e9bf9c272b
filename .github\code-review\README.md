# GitHub API Code Review Module

A comprehensive JavaScript module for interacting with GitHub's REST API to facilitate automated code reviews. This module provides functions to fetch pull request information, post summary comments, and add inline comments to specific lines in PR files.

## Features

- 🔍 **Fetch PR Information**: Retrieve comprehensive pull request data including metadata, file changes, and diff information
- 💬 **Post Summary Comments**: Add overall review comments to pull requests
- 📝 **Post Inline Comments**: Add line-specific comments to files in pull requests
- 🔐 **Secure Authentication**: Uses GitHub tokens for secure API access
- 🚀 **Modern ES6**: Built with ES6 modules and async/await
- 📚 **Well Documented**: Comprehensive JSDoc documentation for all functions
- ⚡ **Error Handling**: Robust error handling with specific error messages

## Installation

1. Navigate to the `.github/code-review` directory:
```bash
cd .github/code-review
```

2. Install the required dependencies:
```bash
npm install
```

## Prerequisites

- Node.js 16.0.0 or higher
- A GitHub personal access token with appropriate permissions

### GitHub Token Setup

1. Go to GitHub Settings > Developer settings > Personal access tokens
2. Generate a new token with the following permissions:
   - `repo` (Full control of private repositories)
   - `pull_requests:write` (Write access to pull requests)
3. Store the token securely (environment variable recommended)

## Usage

### Basic Import

```javascript
import { fetchPRInfo, postSummaryComment, postInlineComment } from './github-api.js';
```

### 1. Fetch Pull Request Information

```javascript
const prUrl = 'https://github.com/owner/repo/pull/123';
const githubToken = process.env.GITHUB_TOKEN;

try {
  const prInfo = await fetchPRInfo(prUrl, githubToken);

  console.log('PR Title:', prInfo.prData.title);
  console.log('Author:', prInfo.prData.author);
  console.log('Files Changed:', prInfo.prData.changedFiles);

  // Access file changes
  prInfo.files.forEach(file => {
    console.log(`${file.filename}: +${file.additions} -${file.deletions}`);
  });
} catch (error) {
  console.error('Failed to fetch PR info:', error.message);
}
```

### 2. Post Summary Comment

```javascript
const comment = `
## 🤖 Automated Code Review

### Summary
This pull request has been automatically reviewed.

### Key Findings
- ✅ Code structure looks good
- ⚠️ Consider adding more unit tests
- 💡 Documentation could be improved

*Generated by SleekFlow Code Review Bot*
`;

try {
  const result = await postSummaryComment(prUrl, comment, githubToken);
  console.log('Comment posted:', result.htmlUrl);
} catch (error) {
  console.error('Failed to post comment:', error.message);
}
```

### 3. Post Inline Comment

```javascript
const filePath = 'src/components/UserProfile.js';
const lineNumber = 42;
const comment = `
💡 **Suggestion**: Consider adding error handling here:

\`\`\`javascript
try {
  const user = await fetchUser(userId);
  return user;
} catch (error) {
  console.error('Failed to fetch user:', error);
  throw new Error('User not found');
}
\`\`\`
`;

try {
  const result = await postInlineComment(prUrl, filePath, lineNumber, comment, githubToken);
  console.log('Inline comment posted:', result.htmlUrl);
} catch (error) {
  console.error('Failed to post inline comment:', error.message);
}
```

## API Reference

### `fetchPRInfo(prUrl, githubToken)`

Fetches comprehensive information about a pull request.

**Parameters:**
- `prUrl` (string): The GitHub pull request URL
- `githubToken` (string): GitHub personal access token

**Returns:**
```javascript
{
  prData: {
    id: number,
    number: number,
    title: string,
    body: string,
    state: string,
    author: string,
    authorAvatar: string,
    createdAt: string,
    updatedAt: string,
    mergedAt: string,
    baseRef: string,
    headRef: string,
    baseSha: string,
    headSha: string,
    mergeable: boolean,
    additions: number,
    deletions: number,
    changedFiles: number,
    reviewComments: number,
    comments: number,
    htmlUrl: string
  },
  files: Array<{
    filename: string,
    status: string,
    additions: number,
    deletions: number,
    changes: number,
    patch: string,
    blobUrl: string,
    rawUrl: string,
    contentsUrl: string,
    sha: string
  }>,
  commits: Array<{
    sha: string,
    message: string,
    author: object,
    committer: object,
    htmlUrl: string
  }>,
  repository: {
    owner: string,
    repo: string,
    fullName: string
  }
}
```

### `postSummaryComment(prUrl, comment, githubToken)`

Posts an overall summary comment to a pull request.

**Parameters:**
- `prUrl` (string): The GitHub pull request URL
- `comment` (string): The comment content (supports Markdown)
- `githubToken` (string): GitHub personal access token

**Returns:**
```javascript
{
  id: number,
  htmlUrl: string,
  body: string,
  createdAt: string,
  updatedAt: string,
  author: string
}
```

### `postInlineComment(prUrl, filePath, lineNumber, comment, githubToken)`

Posts an inline comment on a specific line of a pull request file.

**Parameters:**
- `prUrl` (string): The GitHub pull request URL
- `filePath` (string): The path of the file to comment on
- `lineNumber` (number): The line number in the new version of the file
- `comment` (string): The comment content (supports Markdown)
- `githubToken` (string): GitHub personal access token

**Returns:**
```javascript
{
  id: number,
  htmlUrl: string,
  path: string,
  position: number,
  originalPosition: number,
  commitId: string,
  body: string,
  createdAt: string,
  updatedAt: string,
  author: string,
  line: number,
  side: string
}
```

## Error Handling

The module provides specific error messages for common scenarios:

- **401 Unauthorized**: Invalid GitHub token or insufficient permissions
- **403 Forbidden**: API rate limit exceeded or access forbidden
- **404 Not Found**: Pull request, repository, or file not found
- **Invalid URL**: Malformed pull request URL
- **Missing Parameters**: Required parameters not provided

## Example Workflow

```javascript
import { fetchPRInfo, postSummaryComment, postInlineComment } from './github-api.js';

async function reviewPullRequest(prUrl, githubToken) {
  try {
    // 1. Fetch PR information
    const prInfo = await fetchPRInfo(prUrl, githubToken);

    // 2. Analyze the changes
    const largeFiles = prInfo.files.filter(f => f.changes > 100);
    const hasTests = prInfo.files.some(f => f.filename.includes('test'));

    // 3. Post summary comment
    let summary = `## 🔍 Code Review Summary\n\n`;
    summary += `**Files Changed:** ${prInfo.prData.changedFiles}\n`;
    summary += `**Lines Added:** ${prInfo.prData.additions}\n`;
    summary += `**Lines Deleted:** ${prInfo.prData.deletions}\n\n`;

    if (largeFiles.length > 0) {
      summary += `⚠️ **Large Files:** ${largeFiles.map(f => f.filename).join(', ')}\n`;
    }

    if (!hasTests) {
      summary += `❌ **Missing Tests:** Consider adding unit tests\n`;
    }

    await postSummaryComment(prUrl, summary, githubToken);

    // 4. Add inline comments for specific issues
    for (const file of largeFiles) {
      await postInlineComment(
        prUrl,
        file.filename,
        1,
        '💡 This file has many changes. Consider breaking it into smaller commits.',
        githubToken
      );
    }

    console.log('Review completed successfully!');
  } catch (error) {
    console.error('Review failed:', error.message);
  }
}
```

## Best Practices

1. **Environment Variables**: Store GitHub tokens in environment variables
2. **Rate Limiting**: Be mindful of GitHub API rate limits (5000 requests/hour for authenticated requests)
3. **Error Handling**: Always wrap API calls in try-catch blocks
4. **Markdown Support**: Use Markdown formatting in comments for better readability
5. **Line Numbers**: Ensure line numbers correspond to the new version of the file
6. **File Paths**: Use relative paths from the repository root

## Dependencies

- `@octokit/rest`: Official GitHub REST API client

## License

MIT License

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

---

# Automated Code Review Application

In addition to the GitHub API module, this directory contains a complete automated code review application that uses LangChain with Google Gemini LLM to analyze GitHub pull requests and provide comprehensive feedback.

## Application Features

- **Automated PR Analysis**: Fetches and analyzes pull request changes
- **Overall Summary**: Generates comprehensive PR summary comments using AI
- **Inline Comments**: Posts specific AI-generated feedback on individual lines of code
- **LLM-Powered**: Uses Google Gemini for intelligent code analysis
- **GitHub Integration**: Seamlessly integrates with GitHub's API

## Application Files

- `index.js` - Main application entry point
- `prompts/` - Directory containing LLM prompt templates
  - `overall-summary.txt` - Base prompt for generating overall PR summaries
  - `inline-comments.txt` - Base prompt for generating inline code comments
  - `overall-summary-template.txt` - Template for overall summary with variable substitution
  - `inline-comments-template.txt` - Template for inline comments with variable substitution

## Application Prerequisites

- Node.js 18.0.0 or higher
- GitHub personal access token with repository access
- Google Gemini API key

## Application Setup

1. **Install dependencies** (if not already done):
```bash
cd .github/code-review
npm install
```

2. **Set up environment variables**:
```bash
export GEMINI_API_KEY="your_gemini_api_key_here"
```

3. **Prepare GitHub token**:
   - Use the same GitHub personal access token setup as described above

## Application Usage

Run the automated code review on a pull request:

```bash
node index.js --github-token <your_github_token> --pr-url <github_pr_url>
```

### Example

```bash
node index.js \
  --github-token ghp_1234567890abcdef \
  --pr-url https://github.com/owner/repo/pull/123
```

### Command Line Options

- `-t, --github-token <token>` - GitHub personal access token (required)
- `-p, --pr-url <url>` - Full GitHub pull request URL (required)

## Application Workflow

The automated application executes the following workflow:

1. **Fetch PR Data**: Retrieves pull request metadata, file changes, and diff content
2. **Generate Summary**: Uses Gemini LLM to create an overall PR summary
3. **Post Summary**: Posts the summary as a comment on the PR
4. **Generate Inline Comments**: Analyzes individual files for specific feedback
5. **Post Inline Comments**: Posts targeted comments on specific lines of code

## Customization

### Prompt Templates

The application uses a two-tier template system for maximum flexibility:

#### Base Prompts
- **`overall-summary.txt`**: Base instruction prompt for overall PR summaries
- **`inline-comments.txt`**: Base instruction prompt for inline comments

#### Dynamic Templates
- **`overall-summary-template.txt`**: Template with variable substitution for overall summaries
- **`inline-comments-template.txt`**: Template with variable substitution for inline comments

#### Template Variables

**Overall Summary Template Variables:**
- `{basePrompt}` - The base prompt content
- `{title}` - PR title
- `{description}` - PR description
- `{author}` - PR author
- `{headRef}` - Source branch
- `{baseRef}` - Target branch
- `{additions}` - Lines added
- `{deletions}` - Lines deleted
- `{changedFiles}` - Number of files changed
- `{commitMessages}` - Formatted commit messages
- `{fileChanges}` - Formatted file change summaries

**Inline Comments Template Variables:**
- `{basePrompt}` - The base prompt content
- `{filePath}` - File path being analyzed
- `{status}` - File status (added, modified, deleted)
- `{additions}` - Lines added in file
- `{deletions}` - Lines deleted in file
- `{patch}` - Git diff patch content

#### Customization

To customize the AI behavior:
1. **Modify base prompts** (`overall-summary.txt`, `inline-comments.txt`) to change the general instructions
2. **Modify templates** (`*-template.txt`) to change how data is presented to the AI
3. **Variables are automatically substituted** using the `{variableName}` syntax

### AI Model Configuration

The application uses the following Gemini configuration:
- Model: `gemini-1.5-pro`
- Temperature: `0.1` (for consistent, focused responses)
- Max Output Tokens: `8192`

You can modify these settings in `index.js` if needed.

## Troubleshooting

### Common Application Issues

- **"GEMINI_API_KEY environment variable is required"**: Set the environment variable before running
- **"Invalid GitHub token"**: Ensure your token has the necessary repository permissions
- **"Pull request not found"**: Verify the PR URL is correct and accessible
- **"Failed to parse JSON"**: The LLM response may not be in the expected format; check prompt templates

---

## Support

For issues and questions, please create an issue in the repository or contact the SleekFlow development team.
