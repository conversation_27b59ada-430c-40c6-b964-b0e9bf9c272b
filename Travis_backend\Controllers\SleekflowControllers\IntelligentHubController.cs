﻿#nullable enable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.Apis.IntelligentHub.Api;
using Sleekflow.Apis.IntelligentHub.Model;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.ConversationServices;
using Travis_backend.Enums;
using Travis_backend.IntelligentHubDomain.Models;
using Travis_backend.IntelligentHubDomain.Services;
using Travis_backend.TenantHubDomain.Models;
using Travis_backend.TenantHubDomain.Services;
using IntelligentHubConfig = Travis_backend.IntelligentHubDomain.Models.IntelligentHubConfig;

namespace Travis_backend.Controllers.SleekflowControllers;

[Authorize]
[Route("IntelligentHub")]
public class IntelligentHubController : ControllerBase
{
    private readonly ICoreService _coreService;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ILogger<IntelligentHubController> _logger;
    private readonly ITextEnrichmentsApi _textEnrichmentsApi;
    private readonly IBlobsApi _blobsApi;
    private readonly IRecommendedRepliesApi _recommendedRepliesApi;
    private readonly IDocumentsApi _documentsApi;
    private readonly IKnowledgeBasesApi _knowledgeBasesApi;
    private readonly IIntelligentHubConfigsApi _intelligentHubConfigsApi;
    private readonly ITopicAnalyticsApi _topicAnalyticsApi;
    private readonly ICompanyUsageService _companyUsageService;
    private readonly IIntelligentHubService _intelligentHubService;
    private readonly IEnabledFeaturesService _enabledFeaturesService;

    public IntelligentHubController(
        ILogger<IntelligentHubController> logger,
        ITextEnrichmentsApi textEnrichmentsApi,
        IIntelligentHubConfigsApi intelligentHubConfigsApi,
        ITopicAnalyticsApi topicAnalyticsApi,
        IBlobsApi blobsApi,
        IDocumentsApi documentsApi,
        IKnowledgeBasesApi knowledgeBasesApi,
        ICoreService coreService,
        UserManager<ApplicationUser> userManager,
        IIntelligentHubService intelligentHubService,
        IEnabledFeaturesService enabledFeaturesService,
        IRecommendedRepliesApi recommendedRepliesApi)
    {
        _logger = logger;
        _textEnrichmentsApi = textEnrichmentsApi;
        _intelligentHubConfigsApi = intelligentHubConfigsApi;
        _topicAnalyticsApi = topicAnalyticsApi;
        _coreService = coreService;
        _userManager = userManager;
        _intelligentHubService = intelligentHubService;
        _enabledFeaturesService = enabledFeaturesService;
        _blobsApi = blobsApi;
        _documentsApi = documentsApi;
        _knowledgeBasesApi = knowledgeBasesApi;
        _recommendedRepliesApi = recommendedRepliesApi;
    }

    #region IntelligentHub Config

    [HttpPost("IntelligentHubConfigs/GetConfig")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<IntelligentHubConfig>> GetIntelligentHubConfig()
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var companyId = staff.CompanyId;

        try
        {
            var getIntelligentHubConfigOutputOutput =
                await _intelligentHubConfigsApi.IntelligentHubConfigsGetIntelligentHubConfigPostAsync(
                    getIntelligentHubConfigInput: new GetIntelligentHubConfigInput(companyId));

            var usageLimits = getIntelligentHubConfigOutputOutput.Data.IntelligentHubConfig != null
                ? getIntelligentHubConfigOutputOutput.Data.IntelligentHubConfig.UsageLimits
                : await _intelligentHubService.InitializeIntelligentHubConfig(companyId);

            usageLimits = await _intelligentHubService.CheckAndUpdateIntelligentHubConfig(companyId, usageLimits);

            return Ok(
                new IntelligentHubConfig(usageLimits));
        }
        catch (Exception e)
        {
            _logger.LogError(
                "Unable to get IntelligentHub Config. {CompanyId} {Exception}",
                companyId,
                e.ToString());

            // DEVSRecordBlobUploadHistory4398/DEVS-4296 temporarily all return 0, FE do not handle BadRequest yet
            return Ok(
                new IntelligentHubConfig(
                    IntelligentHubConfig.PriceableFeatureIds
                        .Select(
                            pf => new
                            {
                                Feature = pf, Limit = 0
                            })
                        .ToDictionary(
                            x => x.Feature,
                            x => x.Limit)));
        }
    }

    public class UpdateConfigRequest
    {
        [JsonProperty("usage_limits")]
        public Dictionary<string, int> UsageLimits { get; set; }
    }

    [HttpPost("IntelligentHubConfigs/UpdateConfig")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult> UpdateIntelligentHubConfig(
        [FromBody]
        UpdateConfigRequest updateConfigRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        if (staff.RoleType != StaffUserRole.Admin)
        {
            return Forbid("Only the Admin is authorized to perform this operation.");
        }

        if (updateConfigRequest.UsageLimits.Keys.Any(k => !IntelligentHubConfig.PriceableFeatureIds.Contains(k)))
        {
            return Forbid("Invalid priceable features");
        }

        var companyId = staff.CompanyId;

        try
        {
            var updateIntelligentHubConfigOutputOutput =
                await _intelligentHubConfigsApi.IntelligentHubConfigsUpdateIntelligentHubConfigPostAsync(
                    updateIntelligentHubConfigInput: new UpdateIntelligentHubConfigInput(
                        companyId,
                        updateConfigRequest.UsageLimits,
                        staff.Id.ToString()));

            return Ok(
                new IntelligentHubConfig(updateIntelligentHubConfigOutputOutput.Data.IntelligentHubConfig.UsageLimits));
        }
        catch (Exception e)
        {
            _logger.LogError(
                "Failed to update IntelligentHub Config. {CompanyId} {Message}",
                companyId,
                e.Message);

            return BadRequest("Failed to update IntelligentHub Config.");
        }
    }

    [HttpPost("IntelligentHubConfigs/GetFeatureUsageStatistics")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetFeatureUsageStatisticsOutput>> GetIntelligentHubFeatureUsageStatistics()
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var companyId = staff.CompanyId;

        try
        {
            var getIntelligentHubConfigOutputOutput =
                await _intelligentHubConfigsApi.IntelligentHubConfigsGetIntelligentHubConfigPostAsync(
                    getIntelligentHubConfigInput: new GetIntelligentHubConfigInput(companyId));

            var usageLimits = getIntelligentHubConfigOutputOutput.Data.IntelligentHubConfig != null
                ? getIntelligentHubConfigOutputOutput.Data.IntelligentHubConfig.UsageLimits
                : await _intelligentHubService.InitializeIntelligentHubConfig(companyId);

            usageLimits = await _intelligentHubService.CheckAndUpdateIntelligentHubConfig(companyId, usageLimits);

            var featureNames = new List<string>(usageLimits.Keys);

            var getFeatureUsagesOutputOutput =
                await _intelligentHubConfigsApi.IntelligentHubConfigsGetFeatureUsagesPostAsync(
                    getFeatureUsagesInput: new GetFeatureUsagesInput(
                        companyId,
                        featureNames,
                        await _intelligentHubService.GenerateIntelligentHubUsageFilter(companyId)));

            var featureUsageStatistics = new Dictionary<string, FeatureUsageStatistic>();

            featureNames.ForEach(
                fn =>
                {
                    var limit = usageLimits[fn];

                    if (getIntelligentHubConfigOutputOutput.Data.IntelligentHubConfig is
                            { UsageLimitOffsets: not null } &&
                        getIntelligentHubConfigOutputOutput.Data.IntelligentHubConfig.UsageLimitOffsets.TryGetValue(
                            fn,
                            out var offset))
                    {
                        limit += offset!.Value;
                    }

                    featureUsageStatistics.Add(
                        fn,
                        new FeatureUsageStatistic(
                            limit,
                            getFeatureUsagesOutputOutput.Data.FeatureUsages[fn]));
                });

            return Ok(new GetFeatureUsageStatisticsOutput(featureUsageStatistics));
        }
        catch (Exception e)
        {
            _logger.LogError("Unable to get IntelligentHub Usages. {CompanyId} {Exception}", companyId, e.ToString());

            // DEVS-4398/DEVS-4296 temporarily all return 0, FE do not handle BadRequest yet
            return Ok(
                new GetFeatureUsageStatisticsOutput(
                    IntelligentHubConfig.PriceableFeatureIds
                        .Select(
                            pf => new
                            {
                                Feature = pf, FeatureUsageStatistic = new FeatureUsageStatistic(0, 0)
                            })
                        .ToDictionary(
                            x => x.Feature,
                            x => x.FeatureUsageStatistic)));
        }
    }

    //GetAiFeatureSetting
    public class GetAiFeatureSettingRequest
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }
    }

    public class GetAiFeatureSettingResponse
    {
        [JsonProperty("enable_writing_assistant")]
        public bool EnableWritingAssistant { get; set; }

        [JsonProperty("enable_smart_reply")]
        public bool EnableSmartReply { get; set; }

        [JsonConstructor]
        public GetAiFeatureSettingResponse(bool enableWritingAssistant, bool enableSmartReply)
        {
            EnableWritingAssistant = enableWritingAssistant;
            EnableSmartReply = enableSmartReply;
        }
    }

    [HttpPost("IntelligentHubConfigs/GetAiFeatureSetting")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetAiFeatureSettingResponse>> GetAiFeatureSetting(
        [FromBody]
        GetAiFeatureSettingRequest getAiFeatureSettingRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getIntelligentHubConfigOutputOutput =
            await _intelligentHubConfigsApi.IntelligentHubConfigsGetIntelligentHubConfigPostAsync(
                getIntelligentHubConfigInput: new GetIntelligentHubConfigInput(
                    getAiFeatureSettingRequest.SleekflowCompanyId));

        if (getIntelligentHubConfigOutputOutput.Data.IntelligentHubConfig is null)
        {
            await _intelligentHubService.InitializeIntelligentHubConfig(getAiFeatureSettingRequest.SleekflowCompanyId);
        }
        else
        {
            await _intelligentHubService.CheckAndUpdateIntelligentHubConfig(
                getAiFeatureSettingRequest.SleekflowCompanyId,
                getIntelligentHubConfigOutputOutput.Data.IntelligentHubConfig.UsageLimits);
        }

        var getAiFeatureSettingOutputOutput =
            await _intelligentHubConfigsApi.IntelligentHubConfigsGetAiFeatureSettingPostAsync(
                getAiFeatureSettingInput: new GetAiFeatureSettingInput(
                    getAiFeatureSettingRequest.SleekflowCompanyId));

        if (getAiFeatureSettingOutputOutput.Success)
        {
            return Ok(
                new GetAiFeatureSettingResponse(
                    getAiFeatureSettingOutputOutput.Data.EnableWritingAssistant,
                    getAiFeatureSettingOutputOutput.Data.EnableSmartReply));
        }

        return StatusCode(
            getAiFeatureSettingOutputOutput.HttpStatusCode,
            getAiFeatureSettingOutputOutput.Message);
    }

    //UpdateAiFeatureSetting
    public class UpdateAiFeatureSettingRequest
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("enable_writing_assistant")]
        public bool EnableWritingAssistant { get; set; }

        [Required]
        [JsonProperty("enable_smart_reply")]
        public bool EnableSmartReply { get; set; }
    }

    public class UpdateAiFeatureSettingResponse
    {
        [JsonProperty("enable_writing_assistant")]
        public bool EnableWritingAssistant { get; set; }

        [JsonProperty("enable_smart_reply")]
        public bool EnableSmartReply { get; set; }

        [JsonConstructor]
        public UpdateAiFeatureSettingResponse(bool enableWritingAssistant, bool enableSmartReply)
        {
            EnableWritingAssistant = enableWritingAssistant;
            EnableSmartReply = enableSmartReply;
        }
    }

    [HttpPost("IntelligentHubConfigs/UpdateAiFeatureSetting")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<UpdateAiFeatureSettingResponse>> UpdateAiFeatureSetting(
        [FromBody]
        UpdateAiFeatureSettingRequest updateAiFeatureSettingRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var updateAiFeatureSettingOutputOutput =
            await _intelligentHubConfigsApi.IntelligentHubConfigsUpdateAiFeatureSettingPostAsync(
                updateAiFeatureSettingInput: new UpdateAiFeatureSettingInput(
                    updateAiFeatureSettingRequest.SleekflowCompanyId,
                    updateAiFeatureSettingRequest.EnableWritingAssistant,
                    updateAiFeatureSettingRequest.EnableSmartReply,
                    staff.Id.ToString()));

        if (updateAiFeatureSettingOutputOutput.Success)
        {
            return Ok(
                new UpdateAiFeatureSettingResponse(
                    updateAiFeatureSettingOutputOutput.Data.EnableWritingAssistant,
                    updateAiFeatureSettingOutputOutput.Data.EnableSmartReply));
        }

        return StatusCode(
            updateAiFeatureSettingOutputOutput.HttpStatusCode,
            updateAiFeatureSettingOutputOutput.Message);
    }

    #endregion

    #region Text Enrichments

    public class TextEnrichmentResponse
    {
        [JsonProperty("output_message")]
        public string OutputMessage { get; set; }

        [JsonProperty("date_time")]
        public DateTimeOffset DateTimeOffset { get; set; }

        [JsonProperty("request_id")]
        public string RequestId { get; set; }

        [JsonProperty("success")]
        public bool Success { get; set; }

        [JsonConstructor]
        public TextEnrichmentResponse(
            string outputMessage,
            DateTimeOffset dateTimeOffset,
            string requestId,
            bool success)
        {
            OutputMessage = outputMessage;
            DateTimeOffset = dateTimeOffset;
            RequestId = requestId;
            Success = success;
        }
    }

    public class TranslateRequest
    {
        [JsonProperty("message")]
        public string Message;

        [JsonProperty("target_language_code")]
        public string TargetLanguageCode;
    }

    [HttpPost("TextEnrichments/Translate")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<TextEnrichmentResponse>> TextEnrichmentTranslate(
        [FromBody]
        TranslateRequest translateRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var companyId = staff.CompanyId;
        var intelligentHubUsageFilter = await _intelligentHubService.GenerateIntelligentHubUsageFilter(companyId);

        var translateOutputOutput = await _textEnrichmentsApi.TextEnrichmentsTranslatePostAsync(
            translateInput: new TranslateInput(
                translateRequest.Message,
                translateRequest.TargetLanguageCode,
                intelligentHubUsageFilter,
                new SleekflowStaff(staff.Id.ToString()),
                companyId));

        if (translateOutputOutput.Success)
        {
            return Ok(
                new TextEnrichmentResponse(
                    translateOutputOutput.Data.OutputMessage,
                    translateOutputOutput.DateTime,
                    translateOutputOutput.RequestId,
                    translateOutputOutput.Success));
        }

        return StatusCode(
            translateOutputOutput.HttpStatusCode,
            translateOutputOutput.Message);
    }

    public class ChangeToneRequest
    {
        [JsonProperty("message")]
        public string Message;

        [JsonProperty("tone_type")]
        public string ToneType;
    }

    [HttpPost("TextEnrichments/ChangeTone")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<TextEnrichmentResponse>> TextEnrichmentChangeTone(
        [FromBody]
        ChangeToneRequest changeToneRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var companyId = staff.CompanyId;
        var intelligentHubUsageFilter = await _intelligentHubService.GenerateIntelligentHubUsageFilter(companyId);

        var changeToneOutputOutput = await _textEnrichmentsApi.TextEnrichmentsChangeTonePostAsync(
            changeToneInput: new ChangeToneInput(
                changeToneRequest.Message,
                changeToneRequest.ToneType,
                companyId,
                intelligentHubUsageFilter,
                new SleekflowStaff(staff.Id.ToString())));

        if (changeToneOutputOutput.Success)
        {
            return Ok(
                new TextEnrichmentResponse(
                    changeToneOutputOutput.Data.OutputMessage,
                    changeToneOutputOutput.DateTime,
                    changeToneOutputOutput.RequestId,
                    changeToneOutputOutput.Success));
        }

        return StatusCode(
            changeToneOutputOutput.HttpStatusCode,
            changeToneOutputOutput.Message);
    }

    public class RephraseRequest
    {
        [JsonProperty("message")]
        public string Message;

        [JsonProperty("rephrase_target_type")]
        public string RephraseTargetType;
    }

    [HttpPost("TextEnrichments/Rephrase")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<TextEnrichmentResponse>> TextEnrichmentRephrase(
        [FromBody]
        RephraseRequest rephraseRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var companyId = staff.CompanyId;
        var intelligentHubUsageFilter = await _intelligentHubService.GenerateIntelligentHubUsageFilter(companyId);

        var rephraseOutputOutput = await _textEnrichmentsApi.TextEnrichmentsRephrasePostAsync(
            rephraseInput: new RephraseInput(
                rephraseRequest.Message,
                rephraseRequest.RephraseTargetType,
                intelligentHubUsageFilter,
                new SleekflowStaff(staff.Id.ToString()),
                companyId));

        if (rephraseOutputOutput.Success)
        {
            return Ok(
                new TextEnrichmentResponse(
                    rephraseOutputOutput.Data.OutputMessage,
                    rephraseOutputOutput.DateTime,
                    rephraseOutputOutput.RequestId,
                    rephraseOutputOutput.Success));
        }

        return StatusCode(
            rephraseOutputOutput.HttpStatusCode,
            rephraseOutputOutput.Message);
    }

    public class CustomPromptRewriteRequest
    {
        [JsonProperty("message")]
        public string Message { get; set; }

        [JsonProperty("prompt")]
        public string Prompt { get; set; }
    }

    [HttpPost("TextEnrichments/CustomPromptRewrite")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<TextEnrichmentResponse>> TextEnrichmentCustomPromptRewrite(
        [FromBody]
        CustomPromptRewriteRequest customPromptRewriteRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var companyId = staff.CompanyId;
        var intelligentHubUsageFilter = await _intelligentHubService.GenerateIntelligentHubUsageFilter(companyId);

        var customPromptRewriteOutputOutput = await _textEnrichmentsApi.TextEnrichmentsCustomPromptRewritePostAsync(
            customPromptRewriteInput: new CustomPromptRewriteInput(
                customPromptRewriteRequest.Message,
                customPromptRewriteRequest.Prompt,
                companyId,
                intelligentHubUsageFilter,
                new SleekflowStaff(staff.Id.ToString())));

        if (customPromptRewriteOutputOutput.Success)
        {
            return Ok(
                new TextEnrichmentResponse(
                    customPromptRewriteOutputOutput.Data.OutputMessage,
                    customPromptRewriteOutputOutput.DateTime,
                    customPromptRewriteOutputOutput.RequestId,
                    customPromptRewriteOutputOutput.Success));
        }

        return StatusCode(
            customPromptRewriteOutputOutput.HttpStatusCode,
            customPromptRewriteOutputOutput.Message);
    }

    #endregion

    #region Blob

    public class CreateBlobDownloadSasUrlsRequest
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("blob_names")]
        public List<string> BlobNames { get; set; }

        [JsonProperty("blob_type")]
        public string BlobType { get; set; }
    }

    public class CreateBlobDownloadSasUrlsResponse
    {
        [JsonProperty("download_blobs")]
        public List<PublicBlob> DownloadBlobs { get; set; }

        [JsonConstructor]
        public CreateBlobDownloadSasUrlsResponse(
            List<PublicBlob> downloadBlobs)
        {
            DownloadBlobs = downloadBlobs;
        }
    }


    [HttpPost("Blobs/CreateBlobDownloadSasUrls")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<TextEnrichmentResponse>> CreateBlobDownloadSasUrls(
        [FromBody]
        CreateBlobDownloadSasUrlsRequest createBlobDownloadSasUrlsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var createBlobDownloadSasUrlsOutputOutput = await _blobsApi.BlobsCreateBlobDownloadSasUrlsPostAsync(
            createBlobDownloadSasUrlsInput: new CreateBlobDownloadSasUrlsInput(
                createBlobDownloadSasUrlsRequest.SleekflowCompanyId,
                createBlobDownloadSasUrlsRequest.BlobNames,
                createBlobDownloadSasUrlsRequest.BlobType));

        if (createBlobDownloadSasUrlsOutputOutput.Success)
        {
            return Ok(
                new CreateBlobDownloadSasUrlsResponse(createBlobDownloadSasUrlsOutputOutput.Data.DownloadBlobs));
        }

        return StatusCode(
            createBlobDownloadSasUrlsOutputOutput.HttpStatusCode,
            createBlobDownloadSasUrlsOutputOutput.Message);
    }


    public class CreateBlobUploadSasUrlsRequest
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Range(1, 16)]
        [JsonProperty("number_of_blobs")]
        public int NumberOfBlobs { get; set; }

        [JsonProperty("blob_type")]
        public string BlobType { get; set; }
    }

    public class CreateBlobUploadSasUrlsResponse
    {
        [JsonProperty("upload_blobs")]
        public List<PublicBlob> UploadBlobs { get; set; }

        [JsonConstructor]
        public CreateBlobUploadSasUrlsResponse(
            List<PublicBlob> uploadBlobs)
        {
            UploadBlobs = uploadBlobs;
        }
    }

    [HttpPost("Blobs/CreateBlobUploadSasUrls")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<TextEnrichmentResponse>> CreateBlobUploadSasUrls(
        [FromBody]
        CreateBlobUploadSasUrlsRequest createBlobUploadSasUrlsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var createBlobDownloadSasUrlsOutputOutput = await _blobsApi.BlobsCreateBlobUploadSasUrlsPostAsync(
            createBlobUploadSasUrlsInput: new CreateBlobUploadSasUrlsInput(
                createBlobUploadSasUrlsRequest.SleekflowCompanyId,
                createBlobUploadSasUrlsRequest.NumberOfBlobs,
                createBlobUploadSasUrlsRequest.BlobType));

        if (createBlobDownloadSasUrlsOutputOutput.Success)
        {
            return Ok(
                new CreateBlobDownloadSasUrlsResponse(createBlobDownloadSasUrlsOutputOutput.Data.UploadBlobs));
        }

        return StatusCode(
            createBlobDownloadSasUrlsOutputOutput.HttpStatusCode,
            createBlobDownloadSasUrlsOutputOutput.Message);
    }

    public class DeleteBlobsRequest
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("blob_names")]
        public List<string> BlobNames { get; set; }

        [JsonProperty("blob_type")]
        public string BlobType { get; set; }
    }

    public class DeleteBlobsResponse
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("blob_names")]
        public List<string> BlobNames { get; set; }

        [JsonProperty("blob_type")]
        public string BlobType { get; set; }

        [JsonConstructor]
        public DeleteBlobsResponse(string sleekflowCompanyId, List<string> blobNames, string blobType)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            BlobNames = blobNames;
            BlobType = blobType;
        }
    }

    [HttpPost("Blobs/DeleteBlobs")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<TextEnrichmentResponse>> DeleteBlobs(
        [FromBody]
        DeleteBlobsRequest deleteBlobsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var deleteBlobsOutputOutput = await _blobsApi.BlobsDeleteBlobsPostAsync(
            deleteBlobsInput: new DeleteBlobsInput(
                deleteBlobsRequest.SleekflowCompanyId,
                deleteBlobsRequest.BlobNames,
                deleteBlobsRequest.BlobType));

        if (deleteBlobsOutputOutput.Success)
        {
            return Ok(
                new DeleteBlobsResponse(
                    deleteBlobsOutputOutput.Data.SleekflowCompanyId,
                    deleteBlobsOutputOutput.Data.BlobNames,
                    deleteBlobsOutputOutput.Data.BlobType));
        }

        return StatusCode(
            deleteBlobsOutputOutput.HttpStatusCode,
            deleteBlobsOutputOutput.Message);
    }

    //GetBlobUploadHistory
    public class GetBlobUploadHistoryRequest
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("blob_id")]
        public string BlobId { get; set; }
    }

    public class GetBlobUploadHistoryResponse
    {
        [JsonProperty("blob_upload_history")]
        public BlobUploadHistory BlobUploadHistory { get; set; }

        [JsonConstructor]
        public GetBlobUploadHistoryResponse(BlobUploadHistory blobUploadHistory)
        {
            BlobUploadHistory = blobUploadHistory;
        }
    }

    [HttpPost("Blobs/GetBlobUploadHistory")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetBlobUploadHistoryResponse>> GetBlobUploadHistory(
        [FromBody]
        GetBlobUploadHistoryRequest getBlobUploadHistoryRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getBlobUploadHistoryOutputOutput = await _blobsApi.BlobsGetBlobUploadHistoryPostAsync(
            getBlobUploadHistoryInput: new GetBlobUploadHistoryInput(
                getBlobUploadHistoryRequest.SleekflowCompanyId,
                getBlobUploadHistoryRequest.BlobId));

        if (getBlobUploadHistoryOutputOutput.Success)
        {
            return Ok(
                new GetBlobUploadHistoryResponse(getBlobUploadHistoryOutputOutput.Data.BlobUploadHistory));
        }

        return StatusCode(
            getBlobUploadHistoryOutputOutput.HttpStatusCode,
            getBlobUploadHistoryOutputOutput.Message);
    }

    //RecordBlobUploadHistory
    public class RecordBlobUploadHistoryRequest
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("blob_id")]
        public string BlobId { get; set; }

        [JsonProperty("file_name")]
        public string FileName { get; set; }

        [JsonProperty("uploaded_by")]
        public string UploadedBy { get; set; }

        [Required]
        [JsonProperty("source_type")]
        public string SourceType { get; set; }

        [Required]
        [JsonProperty("blob_type")]
        public string BlobType { get; set; }
    }

    public class RecordBlobUploadHistoryResponse
    {
        [JsonProperty("blob_upload_history")]
        public BlobUploadHistory BlobUploadHistory { get; set; }

        [JsonConstructor]
        public RecordBlobUploadHistoryResponse(BlobUploadHistory blobUploadHistory)
        {
            BlobUploadHistory = blobUploadHistory;
        }
    }

    [HttpPost("Blobs/RecordBlobUploadHistory")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<RecordBlobUploadHistoryResponse>> RecordBlobUploadHistory(
        [FromBody]
        RecordBlobUploadHistoryRequest recordBlobUploadHistoryRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var recordBlobUploadHistoryOutputOutput = await _blobsApi.BlobsRecordBlobUploadHistoryPostAsync(
            recordBlobUploadHistoryInput: new RecordBlobUploadHistoryInput(
                recordBlobUploadHistoryRequest.SleekflowCompanyId,
                recordBlobUploadHistoryRequest.BlobId,
                recordBlobUploadHistoryRequest.FileName,
                recordBlobUploadHistoryRequest.UploadedBy,
                recordBlobUploadHistoryRequest.SourceType,
                recordBlobUploadHistoryRequest.BlobType));

        if (recordBlobUploadHistoryOutputOutput.Success)
        {
            return Ok(
                new RecordBlobUploadHistoryResponse(recordBlobUploadHistoryOutputOutput.Data.BlobUploadHistory));
        }

        return StatusCode(
            recordBlobUploadHistoryOutputOutput.HttpStatusCode,
            recordBlobUploadHistoryOutputOutput.Message);
    }

    #endregion

    #region Documents

    public class GetFileDocumentInformationRequest
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("blob_id")]
        public string BlobId { get; set; }
    }

    public class GetFileDocumentInformationResponse
    {
        [JsonProperty("document")]
        public KbDocument Document { get; set; }

        [JsonConstructor]
        public GetFileDocumentInformationResponse(KbDocument document)
        {
            Document = document;
        }
    }


    [HttpPost("Documents/GetFileDocumentInformation")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetFileDocumentInformationResponse>> GetFileDocumentInformation(
        [FromBody]
        GetFileDocumentInformationRequest getFileDocumentInformationRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getFileDocumentInformationOutputOutput = await _documentsApi.DocumentsGetFileDocumentInformationPostAsync(
            getFileDocumentInformationInput: new GetFileDocumentInformationInput(
                getFileDocumentInformationRequest.SleekflowCompanyId,
                getFileDocumentInformationRequest.BlobId
            ));

        if (getFileDocumentInformationOutputOutput.Success)
        {
            return Ok(
                new GetFileDocumentInformationResponse(
                    getFileDocumentInformationOutputOutput.Data.Document));
        }

        return StatusCode(
            getFileDocumentInformationOutputOutput.HttpStatusCode,
            getFileDocumentInformationOutputOutput.Message);
    }

    //GetFileDocumentChunks
    public class GetFileDocumentChunksRequest
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("document_id")]
        public string DocumentId { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [Range(1, 1000)]
        [JsonProperty("limit")]
        public int Limit { get; set; }
    }

    public class GetFileDocumentChunksResponse
    {
        [JsonProperty("document_chunks")]
        public List<Chunk> DocumentChunks { get; set; }

        [JsonProperty("next_continuation_token")]
        public string? NextContinuationToken { get; set; }

        [JsonConstructor]
        public GetFileDocumentChunksResponse(List<Chunk> documentChunks, string? nextContinuationToken)
        {
            DocumentChunks = documentChunks;
            NextContinuationToken = nextContinuationToken;
        }
    }

    [HttpPost("Documents/GetFileDocumentChunks")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetFileDocumentChunksResponse>> GetFileDocumentChunks(
        [FromBody]
        GetFileDocumentChunksRequest getFileDocumentChunksRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getFileDocumentChunksOutputOutput = await _documentsApi.DocumentsGetFileDocumentChunksPostAsync(
            getFileDocumentChunksInput: new GetFileDocumentChunksInput(
                getFileDocumentChunksRequest.SleekflowCompanyId,
                getFileDocumentChunksRequest.DocumentId,
                getFileDocumentChunksRequest.ContinuationToken,
                getFileDocumentChunksRequest.Limit));


        if (getFileDocumentChunksOutputOutput.Success)
        {
            return Ok(
                new GetFileDocumentChunksResponse(
                    getFileDocumentChunksOutputOutput.Data.DocumentChunks,
                    getFileDocumentChunksOutputOutput.Data.NextContinuationToken));
        }

        return StatusCode(
            getFileDocumentChunksOutputOutput.HttpStatusCode,
            getFileDocumentChunksOutputOutput.Message);
    }

    //EditFileDocumentChunksInput
    public class EditFileDocumentChunksRequest
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("document_id")]
        public string DocumentId { get; set; }

        [JsonProperty("editing_chunks")]
        public List<EditingChunkDto> EditingChunks { get; set; }
    }

    public class EditFileDocumentChunksResponse
    {
        [JsonProperty("edited_chunks")]
        public List<EditingChunkDto> EditedChunks { get; set; }

        [JsonConstructor]
        public EditFileDocumentChunksResponse(List<EditingChunkDto> editedChunks)
        {
            EditedChunks = editedChunks;
        }
    }

    [HttpPost("Documents/EditFileDocumentChunks")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<EditFileDocumentChunksResponse>> EditFileDocumentChunks(
        [FromBody]
        EditFileDocumentChunksRequest editFileDocumentChunksRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var editFileDocumentChunksOutputOutput = await _documentsApi.DocumentsEditFileDocumentChunksPostAsync(
            editFileDocumentChunksInput: new EditFileDocumentChunksInput(
                editFileDocumentChunksRequest.SleekflowCompanyId,
                editFileDocumentChunksRequest.DocumentId,
                editFileDocumentChunksRequest.EditingChunks
            ));


        if (editFileDocumentChunksOutputOutput.Success)
        {
            return Ok(
                new EditFileDocumentChunksResponse(
                    editFileDocumentChunksOutputOutput.Data.EditedChunks));
        }

        return StatusCode(
            editFileDocumentChunksOutputOutput.HttpStatusCode,
            editFileDocumentChunksOutputOutput.Message);
    }

    //CalculateFileDocumentStatistics
    public class CalculateFileDocumentStatisticsRequest
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("blob_id")]
        [Required]
        public string BlobId { get; set; }

        [JsonProperty("blob_type")]
        [Required]
        public string BlobType { get; set; }
    }

    public class CalculateFileDocumentStatisticsResponse
    {
        [JsonProperty("document_statistics")]
        public DocumentStatistics DocumentStatistics { get; set; }

        [JsonConstructor]
        public CalculateFileDocumentStatisticsResponse(DocumentStatistics documentStatistics)
        {
            DocumentStatistics = documentStatistics;
        }
    }

    [HttpPost("Documents/CalculateFileDocumentStatistics")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CalculateFileDocumentStatisticsResponse>> CalculateFileDocumentStatistics(
        [FromBody]
        CalculateFileDocumentStatisticsRequest calculateFileDocumentStatisticsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var calculateFileDocumentStatisticsOutputOutput =
            await _documentsApi.DocumentsCalculateFileDocumentStatisticsPostAsync(
                calculateFileDocumentStatisticsInput: new CalculateFileDocumentStatisticsInput(
                    calculateFileDocumentStatisticsRequest.SleekflowCompanyId,
                    calculateFileDocumentStatisticsRequest.BlobId,
                    calculateFileDocumentStatisticsRequest.BlobType));

        if (calculateFileDocumentStatisticsOutputOutput.Success)
        {
            return Ok(
                new CalculateFileDocumentStatisticsResponse(
                    calculateFileDocumentStatisticsOutputOutput.Data.DocumentStatistics));
        }

        return StatusCode(
            calculateFileDocumentStatisticsOutputOutput.HttpStatusCode,
            calculateFileDocumentStatisticsOutputOutput.Message);
    }

    //GetProcessFileDocumentStatus
    public class GetProcessFileDocumentStatusRequest
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("document_id")]
        public string DocumentId { get; set; }
    }

    public class GetProcessFileDocumentStatusResponse
    {
        [JsonProperty("document_id")]
        public string DocumentId { get; set; }

        [JsonProperty("file_document_process_status")]
        public string FileDocumentProcessStatus { get; set; }

        [JsonConstructor]
        public GetProcessFileDocumentStatusResponse(string documentId, string fileDocumentProcessStatus)
        {
            DocumentId = documentId;
            FileDocumentProcessStatus = fileDocumentProcessStatus;
        }
    }

    [HttpPost("Documents/GetProcessFileDocumentStatus")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetProcessFileDocumentStatusResponse>> GetProcessFileDocumentStatus(
        [FromBody]
        GetProcessFileDocumentStatusRequest getProcessFileDocumentStatusRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getProcessFileDocumentStatusOutputOutput =
            await _documentsApi.DocumentsGetProcessFileDocumentStatusPostAsync(
                getProcessFileDocumentStatusInput: new GetProcessFileDocumentStatusInput(
                    getProcessFileDocumentStatusRequest.SleekflowCompanyId,
                    getProcessFileDocumentStatusRequest.DocumentId));


        if (getProcessFileDocumentStatusOutputOutput.Success)
        {
            return Ok(
                new GetProcessFileDocumentStatusResponse(
                    getProcessFileDocumentStatusOutputOutput.Data.DocumentId,
                    getProcessFileDocumentStatusOutputOutput.Data.FileDocumentProcessStatus));
        }

        return StatusCode(
            getProcessFileDocumentStatusOutputOutput.HttpStatusCode,
            getProcessFileDocumentStatusOutputOutput.Message);
    }

    //GetFileDocumentList
    public class GetFileDocumentListRequest
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("filter_training_status")]
        public string? FilterTrainingStatus { get; set; }

        [JsonProperty("filter_exclude_training_status")]
        public List<string>? FilterExcludeTrainingStatus { get; set; }

        [JsonProperty("filter_agent")]
        public string? FilterAgent { get; set; }

        [JsonProperty("filter_exclude_agent")]
        public string? FilterExcludeAgent { get; set; }

        [JsonProperty("sort_by")]
        public string? SortBy { get; set; }

        [JsonProperty("sort_direction")]
        public string? SortDirection { get; set; }
    }

    public class GetFileDocumentListResponse
    {
        [JsonProperty("file_documents")]
        public List<FileDocument> FileDocuments { get; set; }

        [JsonConstructor]
        public GetFileDocumentListResponse(List<FileDocument> fileDocuments)
        {
            FileDocuments = fileDocuments;
        }
    }

    //CalculateRemainingPages
    public class CalculateRemainingPagesRequest
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("page_limit")]
        public int PageLimit { get; set; }
    }

    public class CalculateRemainingPagesResponse
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("page_limit")]
        [Required]
        public int PageLimit { get; set; }

        [JsonProperty("current_usage")]
        [Required]
        public int CurrentUsage { get; set; }

        [JsonProperty("remaining_page")]
        [Required]
        public int RemainingPage { get; set; }

        public CalculateRemainingPagesResponse(
            string sleekflowCompanyId,
            int pageLimit,
            int currentUsage,
            int remainingPage)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            PageLimit = pageLimit;
            CurrentUsage = currentUsage;
            RemainingPage = remainingPage;
        }
    }

    [HttpPost("Documents/CalculateRemainingPages")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CalculateRemainingPagesResponse>> CalculateRemainingPages(
        [FromBody]
        CalculateRemainingPagesRequest calculateRemainingPagesRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var calculateRemainingPagesOutputOutput =
            await _documentsApi.DocumentsCalculateRemainingPagesPostAsync(
                calculateRemainingPagesInput: new CalculateRemainingPagesInput(
                    calculateRemainingPagesRequest.SleekflowCompanyId,
                    calculateRemainingPagesRequest.PageLimit));

        if (calculateRemainingPagesOutputOutput.Success)
        {
            return Ok(
                new CalculateRemainingPagesResponse(
                    calculateRemainingPagesOutputOutput.Data.SleekflowCompanyId,
                    calculateRemainingPagesOutputOutput.Data.PageLimit,
                    calculateRemainingPagesOutputOutput.Data.CurrentUsage,
                    calculateRemainingPagesOutputOutput.Data.RemainingPage));
        }

        return StatusCode(
            calculateRemainingPagesOutputOutput.HttpStatusCode,
            calculateRemainingPagesOutputOutput.Message);
    }

    //SearchDocument
    public class SearchDocumentRequest
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("search_input")]
        public string SearchInput { get; set; }
    }

    public class SearchDocumentResponse
    {
        [JsonProperty("file_documents")]
        public List<FileDocument> FileDocuments { get; set; }

        public SearchDocumentResponse(List<FileDocument> fileDocuments)
        {
            FileDocuments = fileDocuments;
        }
    }

    //CreateFileDocument
    public class CreateFileDocumentRequest
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("blob_id")]
        public string BlobId { get; set; }

        [JsonProperty("blob_type")]
        public string BlobType { get; set; }

        [JsonProperty("assign_to_agent_ids")]
        [Required]
        public List<string> AssignToAgentIds { get; set; }
    }

    public class CreateFileDocumentResponse
    {
        [JsonProperty("file_document")]
        public FileDocument FileDocument { get; set; }

        [JsonConstructor]
        public CreateFileDocumentResponse(FileDocument fileDocument)
        {
            FileDocument = fileDocument;
        }
    }

    [HttpPost("Documents/CreateFileDocument")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CreateFileDocumentResponse>> CreateFileDocument(
        [FromBody]
        CreateFileDocumentRequest createFileDocumentRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var createFileDocumentOutputOutput = await _documentsApi.DocumentsCreateFileDocumentPostAsync(
            createFileDocumentInput:
            new CreateFileDocumentInput(
                createFileDocumentRequest.SleekflowCompanyId,
                createFileDocumentRequest.BlobId,
                createFileDocumentRequest.BlobType,
                createFileDocumentRequest.AssignToAgentIds));


        if (createFileDocumentOutputOutput.Success)
        {
            return Ok(
                new CreateFileDocumentResponse(
                    createFileDocumentOutputOutput.Data.FileDocument));
        }

        return StatusCode(
            createFileDocumentOutputOutput.HttpStatusCode,
            createFileDocumentOutputOutput.Message);
    }

    //DeleteFileDocument
    public class DeleteFileDocumentRequest
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("blob_id")]
        public string BlobId { get; set; }

        [JsonProperty("document_id")]
        public string DocumentId { get; set; }
    }

    public class DeleteFileDocumentResponse
    {
        [JsonConstructor]
        public DeleteFileDocumentResponse()
        {
        }
    }

    [HttpPost("Documents/DeleteFileDocument")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<DeleteFileDocumentResponse>> DeleteFileDocument(
        [FromBody]
        DeleteFileDocumentRequest deleteFileDocumentRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var deleteFileDocumentOutputOutput = await _documentsApi.DocumentsDeleteFileDocumentPostAsync(
            deleteFileDocumentInput:
            new DeleteFileDocumentInput(
                deleteFileDocumentRequest.SleekflowCompanyId,
                deleteFileDocumentRequest.DocumentId,
                deleteFileDocumentRequest.BlobId));


        if (deleteFileDocumentOutputOutput.Success)
        {
            return Ok(
                new DeleteFileDocumentResponse());
        }

        return StatusCode(
            deleteFileDocumentOutputOutput.HttpStatusCode,
            deleteFileDocumentOutputOutput.Message);
    }

    //GetFileDocumentChunkIds
    public class GetFileDocumentChunkIdsRequest
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("document_id")]
        public string DocumentId { get; set; }
    }

    public class GetFileDocumentChunkIdsResponse
    {
        [JsonProperty("chunk_ids")]
        public List<string> ChunkIds { get; set; }

        public GetFileDocumentChunkIdsResponse(List<string> chunkIds)
        {
            ChunkIds = chunkIds;
        }
    }

    [HttpPost("Documents/GetFileDocumentChunkIds")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetFileDocumentChunkIdsResponse>> GetFileDocumentChunkIds(
        [FromBody]
        GetFileDocumentChunkIdsRequest getFileDocumentChunkIdsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getFileDocumentChunkIdsOutputOutput =
            await _documentsApi.DocumentsGetFileDocumentChunkIdsPostAsync(
                getFileDocumentChunkIdsInput: new GetFileDocumentChunkIdsInput(
                    getFileDocumentChunkIdsRequest.SleekflowCompanyId,
                    getFileDocumentChunkIdsRequest.DocumentId));

        if (getFileDocumentChunkIdsOutputOutput.Success)
        {
            return Ok(
                new GetFileDocumentChunkIdsResponse(
                    getFileDocumentChunkIdsOutputOutput.Data.ChunkIds));
        }

        return StatusCode(
            getFileDocumentChunkIdsOutputOutput.HttpStatusCode,
            getFileDocumentChunkIdsOutputOutput.Message);
    }

    #endregion

    #region KnowledgeBases

    //RemoveKnowledgeBaseEntries
    public class RemoveKnowledgeBaseEntriesRequest
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty(PropertyName = "knowledge_base_entry_ids")]
        public List<string> KnowledgeBaseEntryIds { get; set; }
    }

    public class RemoveKnowledgeBaseEntriesResponse
    {
        [JsonProperty("deleted_entry_ids ")]
        public List<string> DeletedEntryIds { get; set; }

        [JsonConstructor]
        public RemoveKnowledgeBaseEntriesResponse(List<string> deletedEntryIds)
        {
            DeletedEntryIds = deletedEntryIds;
        }
    }

    //LoadFileDocumentChunksToKnowledgeBase
    public class LoadFileDocumentChunksToKnowledgeBaseRequest
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("document_id")]
        public string DocumentId { get; set; }

        [JsonProperty("chunk_ids")]
        public List<string> ChunkIds { get; set; }
    }

    public class LoadFileDocumentChunksToKnowledgeBaseResponse
    {
        [JsonConstructor]
        public LoadFileDocumentChunksToKnowledgeBaseResponse()
        {
        }
    }

//GetKnowledgeBaseEntries
    public class GetKnowledgeBaseEntriesRequest
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("filters")]
        public GetKnowledgeBaseEntriesFilters Filters { get; set; }

        [Range(1, 1000)]
        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }
    }

    public class GetKnowledgeBaseEntriesResponse
    {
        [JsonProperty("knowledge_base_entries")]
        public List<KnowledgeBaseEntry> KnowledgeBaseEntries { get; set; }

        [JsonProperty("next_continuation_token")]
        public string? NextContinuationToken { get; set; }

        [JsonConstructor]
        public GetKnowledgeBaseEntriesResponse(
            List<KnowledgeBaseEntry> knowledgeBaseEntries,
            string? nextContinuationToken)
        {
            KnowledgeBaseEntries = knowledgeBaseEntries;
            NextContinuationToken = nextContinuationToken;
        }
    }

    [HttpPost("KnowledgeBases/GetKnowledgeBaseEntries")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetKnowledgeBaseEntriesResponse>> RemoveKnowledgeBaseEntries(
        [FromBody]
        GetKnowledgeBaseEntriesRequest getKnowledgeBaseEntriesRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getKnowledgeBaseEntriesRequestOutputOutput =
            await _knowledgeBasesApi.KnowledgeBasesGetKnowledgeBaseEntriesPostAsync(
                getKnowledgeBaseEntriesInput: new GetKnowledgeBaseEntriesInput(
                    getKnowledgeBaseEntriesRequest.SleekflowCompanyId,
                    getKnowledgeBaseEntriesRequest.Filters,
                    getKnowledgeBaseEntriesRequest.Limit,
                    getKnowledgeBaseEntriesRequest.ContinuationToken));

        if (getKnowledgeBaseEntriesRequestOutputOutput.Success)
        {
            return Ok(
                new GetKnowledgeBaseEntriesResponse(
                    getKnowledgeBaseEntriesRequestOutputOutput.Data.KnowledgeBaseEntries,
                    getKnowledgeBaseEntriesRequestOutputOutput.Data.NextContinuationToken));
        }

        return StatusCode(
            getKnowledgeBaseEntriesRequestOutputOutput.HttpStatusCode,
            getKnowledgeBaseEntriesRequestOutputOutput.Message);
    }

//GetKnowledgebaseEntry
    public class GetKnowledgeBaseEntryRequest
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("knowledge_base_entry_id")]
        public string KnowledgeBaseEntryId { get; set; }
    }

    public class GetKnowledgeBaseEntryResponse
    {
        [JsonProperty("knowledge_base_entry")]
        public KnowledgeBaseEntry KnowledgeBaseEntry { get; set; }

        [JsonConstructor]
        public GetKnowledgeBaseEntryResponse(KnowledgeBaseEntry knowledgeBaseEntry)
        {
            KnowledgeBaseEntry = knowledgeBaseEntry;
        }
    }

    [HttpPost("KnowledgeBases/GetKnowledgeBaseEntry")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetKnowledgeBaseEntryResponse>> GetKnowledgeBaseEntry(
        [FromBody]
        GetKnowledgeBaseEntryRequest getKnowledgeBaseEntryRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getKnowledgeBaseEntryOutputOutput =
            await _knowledgeBasesApi.KnowledgeBasesGetKnowledgeBaseEntryPostAsync(
                getKnowledgeBaseEntryInput: new GetKnowledgeBaseEntryInput(
                    getKnowledgeBaseEntryRequest.SleekflowCompanyId,
                    getKnowledgeBaseEntryRequest.KnowledgeBaseEntryId));

        if (getKnowledgeBaseEntryOutputOutput.Success)
        {
            return Ok(
                new GetKnowledgeBaseEntryResponse(
                    getKnowledgeBaseEntryOutputOutput.Data.KnowledgeBaseEntry));
        }

        return StatusCode(
            getKnowledgeBaseEntryOutputOutput.HttpStatusCode,
            getKnowledgeBaseEntryOutputOutput.Message);
    }

    #endregion

    #region RecommendedReply

    public class RecommendedReplyRequest
    {
        [JsonProperty("conversation_context")]
        public List<SfChatEntry> ConversationContext { get; set; }

        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }
    }

    public class RecommendedReplyResponse
    {
        [JsonProperty("recommended_reply")]
        public string RecommendedReply { get; set; }

        [JsonConstructor]
        public RecommendedReplyResponse(string recommendedReply)
        {
            RecommendedReply = recommendedReply;
        }
    }

    [HttpPost("RecommendedReplies/RecommendReply")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<RecommendedReplyResponse>> RecommendedReply(
        [FromBody]
        RecommendedReplyRequest recommendedReplyRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var intelligentHubUsageFilter = await _intelligentHubService.GenerateIntelligentHubUsageFilter(staff.CompanyId);

        var recommendReplyOutputOutput = await _recommendedRepliesApi.RecommendedRepliesRecommendReplyPostAsync(
            recommendReplyInput: new RecommendReplyInput(
                recommendedReplyRequest.ConversationContext,
                recommendedReplyRequest.SleekflowCompanyId,
                intelligentHubUsageFilter,
                new SleekflowStaff(staff.Id.ToString())));

        if (recommendReplyOutputOutput.Success)
        {
            return Ok(
                new RecommendedReplyResponse(
                    recommendReplyOutputOutput.Data.RecommendedReply));
        }

        return StatusCode(
            recommendReplyOutputOutput.HttpStatusCode,
            recommendReplyOutputOutput.Message);
    }

    public class RecommendedReplyStreamingRequest
    {
        [JsonProperty("conversation_context")]
        public List<SfChatEntry> ConversationContext { get; set; }

        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("session_id")]
        public string SessionId { get; set; }

        [JsonProperty("client_request_id")]
        public string ClientRequestId { get; set; }
    }

    public class RecommendedReplyStreamingResponse
    {
        [JsonConstructor]
        public RecommendedReplyStreamingResponse()
        {
        }
    }

    [HttpPost("RecommendedReplies/RecommendReplyStreaming")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<RecommendedReplyStreamingResponse>> RecommendedReplyStreaming(
        [FromBody]
        RecommendedReplyStreamingRequest recommendedReplyStreamingRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var intelligentHubUsageFilter = await _intelligentHubService.GenerateIntelligentHubUsageFilter(staff.CompanyId);

        var recommendReplyStreamingOutput =
            await _recommendedRepliesApi.RecommendedRepliesRecommendReplyStreamingPostAsync(
                recommendReplyStreamingInput: new RecommendReplyStreamingInput(
                    recommendedReplyStreamingRequest.ConversationContext,
                    recommendedReplyStreamingRequest.SleekflowCompanyId,
                    intelligentHubUsageFilter,
                    new SleekflowStaff(staff.Id.ToString()),
                    recommendedReplyStreamingRequest.SessionId,
                    recommendedReplyStreamingRequest.ClientRequestId));

        if (recommendReplyStreamingOutput.Success)
        {
            return Ok(new RecommendedReplyStreamingResponse());
        }

        return StatusCode(
            recommendReplyStreamingOutput.HttpStatusCode,
            recommendReplyStreamingOutput.Message);
    }

    #endregion

    #region TopicAnalytics

    public class GetTopicsRequest
    {
    }

    /// <summary>
    /// Retrieves the list of topics for the given company.
    /// </summary>
    /// <param name="request">The request containing the company ID and any additional filters for retrieving topics.</param>
    /// <returns>An ActionResult containing the list of topics.</returns>
    [HttpPost("TopicAnalytics/GetTopics")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetTopicsOutputOutput>> GetTopics([FromBody] GetTopicsRequest request)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var isTopicAnalyticEnabled =
            await _enabledFeaturesService.GetIsFeatureEnabledForCompany(
                staff.CompanyId,
                FeatureFlags.TopicAnalytics);

        if (!isTopicAnalyticEnabled)
        {
            return NotFound();
        }

        var result = await _intelligentHubService.GetTopics(staff.CompanyId);

        return result;
    }


    public class CreateTopicRequest
    {
        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("terms")]
        public List<TopicAnalyticsTerm> Terms { get; set; }
    }

    /// <summary>
    /// Creates a new topic for the given company.
    /// </summary>
    /// <param name="request">The request containing the company ID, topic name, and terms for the new topic.</param>
    /// <returns>An ActionResult containing the result of the create operation.</returns>
    [HttpPost("TopicAnalytics/CreateTopic")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CreateTopicOutputOutput>> CreateTopic([FromBody] CreateTopicRequest request)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var isTopicAnalyticEnabled =
            await _enabledFeaturesService.GetIsFeatureEnabledForCompany(
                staff.CompanyId,
                FeatureFlags.TopicAnalytics);

        if (!isTopicAnalyticEnabled)
        {
            return NotFound();
        }

        var result = await CreateTopicOutputOutput(request, staff);

        return result;
    }

    private async Task<CreateTopicOutputOutput> CreateTopicOutputOutput(CreateTopicRequest request, Staff staff)
    {
        var result = await _intelligentHubService.CreateTopic(staff.CompanyId, staff.Id, request.Name, request.Terms);
        return result;
    }

    public class UpdateTopicRequest
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("updated_properties")]
        public Dictionary<string, object?> UpdatedProperties { get; set; }
    }

    /// <summary>
    /// Updates the specified topic for the given company.
    /// </summary>
    /// <param name="request">The request containing the company ID and the topic details to update.</param>
    /// <returns>An ActionResult containing the result of the update operation.</returns>
    [HttpPost("TopicAnalytics/UpdateTopic")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<UpdateTopicOutputOutput>> UpdateTopic([FromBody] UpdateTopicRequest request)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var isTopicAnalyticEnabled =
            await _enabledFeaturesService.GetIsFeatureEnabledForCompany(
                staff.CompanyId,
                FeatureFlags.TopicAnalytics);

        if (!isTopicAnalyticEnabled)
        {
            return NotFound();
        }

        var result = await UpdateTopicOutputOutput(request, staff);

        return result;
    }

    private async Task<UpdateTopicOutputOutput> UpdateTopicOutputOutput(UpdateTopicRequest request, Staff staff)
    {
        var result = await _intelligentHubService.UpdateTopic(
            staff.CompanyId,
            request.Id,
            staff.Id,
            request.UpdatedProperties);
        return result;
    }

    public class DeleteTopicsRequest
    {
        [JsonProperty("ids")]
        public List<string> Ids { get; set; }
    }

    /// <summary>
    /// Deletes the specified topics for the given company.
    /// </summary>
    /// <param name="request">The request containing the company ID and the list of topic IDs to delete.</param>
    /// <returns>An ActionResult containing the result of the delete operation.</returns>
    [HttpPost("TopicAnalytics/DeleteTopics")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<DeleteTopicsOutputOutput>> DeleteTopics([FromBody] DeleteTopicsRequest request)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var isTopicAnalyticEnabled =
            await _enabledFeaturesService.GetIsFeatureEnabledForCompany(
                staff.CompanyId,
                FeatureFlags.TopicAnalytics);

        if (!isTopicAnalyticEnabled)
        {
            return NotFound();
        }

        var result = await _intelligentHubService.DeleteTopics(staff.CompanyId, request.Ids, staff.Id);

        return result;
    }

    #endregion
}