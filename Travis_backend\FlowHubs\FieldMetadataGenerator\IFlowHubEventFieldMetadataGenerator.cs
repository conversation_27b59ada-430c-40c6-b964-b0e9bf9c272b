﻿#nullable enable
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Database.Services;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.FlowHubs.Constants;
using Travis_backend.FlowHubs.Models;
using Travis_backend.FlowHubs.Services;
using Travis_backend.Utils;

namespace Travis_backend.FlowHubs.FieldMetadataGenerator;

public interface IFlowHubEventFieldMetadataGenerator
{
    string EventName { get; }

    Dictionary<string, string> FieldPathOptionRequestPathDict { get; }


    Task<(HashSet<FieldMetadata> FieldMetadataSet, Type EventBodyType, string ContactObjectName)> GenerateAsync(
        string companyId,
        Dictionary<string, object?>? parameters);
}

public abstract class FlowHubEventFieldMetadataGeneratorBase
{
    private readonly IDbContextService _dbContextService;
    protected readonly IFieldMetadataVariableVisibilityService _fieldMetadataVariableVisibilityService;

    protected FlowHubEventFieldMetadataGeneratorBase(
        IDbContextService dbContextService,
        IFieldMetadataVariableVisibilityService fieldMetadataVariableVisibilityService)
    {
        _dbContextService = dbContextService;
        _fieldMetadataVariableVisibilityService = fieldMetadataVariableVisibilityService;
    }

    protected HashSet<FieldMetadata> GenerateFilteredFieldMetadata(JToken jsonData, string eventName)
    {
        // Get visible field paths and UI copy mapping
        var allAvailableFieldPaths = jsonData.GetFieldMetadata().Select(f => f.FieldPath);
        var visibleFieldPaths =
            _fieldMetadataVariableVisibilityService.GetVisibleFieldPaths(eventName, allAvailableFieldPaths);
        var uiCopyMapping = _fieldMetadataVariableVisibilityService.GetFieldUiCopyMapping(eventName);

        // Use iterative approach for better performance
        var fieldMetadataSet = JsonUtils.GetFilteredFieldMetadata(jsonData, visibleFieldPaths);

        // Apply UI copy mapping
        foreach (var metadata in fieldMetadataSet)
        {
            if (uiCopyMapping.TryGetValue(metadata.FieldPath, out var uiCopy))
            {
                metadata.UiCopy = uiCopy;
            }
        }

        return fieldMetadataSet;
    }

    protected async Task<(Dictionary<string, object> UserProfileDict, Dictionary<string, string> CustomFieldOptionsRequestPathDict)>
        GenerateUserProfileDictAsync(string companyId)
    {
        var userProfileDto = JsonUtils.GenerateSampleObjectFromType<UserProfileDto>();
        userProfileDto.Labels = new List<LabelData>
        {
            new ("normal label", HashTagColor.Blue.ToString(), HashTagType.Normal.ToString()),
            new ("shopify label", HashTagColor.Blue.ToString(), HashTagType.Shopify.ToString())
        };
        var userProfileDict = JsonConvert.DeserializeObject<Dictionary<string, JToken>>(JsonConvert.SerializeObject(userProfileDto))!;

        var dbContext = _dbContextService.GetDbContext();
        var companyCustomFields = await dbContext.CompanyCustomUserProfileFields
            .AsNoTracking()
            .Include(x => x.CustomUserProfileFieldOptions)
            .Where(x => x.CompanyId == companyId)
            .ToListAsync();

        foreach (var customField in companyCustomFields)
        {
            var sampleValue = GetCustomFieldSampleValue(customField);
            if (sampleValue is not null)
            {
                userProfileDict[customField.FieldName] = sampleValue;
            }
        }

        userProfileDict["conversation_id"] = JToken.FromObject(Guid.NewGuid().ToString());
        userProfileDict["PositionOfContactOwner"] = JToken.FromObject("sample value");
        userProfileDict["UniqueLink"] = JToken.FromObject("sample value");
        userProfileDict["MessageOfContactOwner"] = JToken.FromObject("sample value");

        return (userProfileDict
                    .ToDictionary(
                        e => e.Key,
                        object (e) => e.Value),
                companyCustomFields
                    .Where(x => x.Type is FieldDataType.Options)
                    .Select(x => GetRequestPathKeyValuePair(x))
                    .ToDictionary());
    }

    protected static void AddContactExpansionToEventBodyJson(JObject eventBodyJson)
    {
        var contactOwnerSample = JsonUtils.GenerateSampleObjectFromType<StaffDto>();
        eventBodyJson["contactOwner"] = JToken.FromObject(contactOwnerSample);

        var listSample = JsonUtils.GenerateSampleObjectFromType<ContactList>();
        eventBodyJson["lists"] = JToken.FromObject(new[] { listSample });
    }

    private static KeyValuePair<string, string> GetRequestPathKeyValuePair(
        CompanyCustomUserProfileField companyCustomUserProfileField)
        => companyCustomUserProfileField switch
        {
            { FieldName: "AssignedTeam" } => new KeyValuePair<string, string>(companyCustomUserProfileField.FieldName, $"FlowHub/SelectOptions/Teams"),
            _ => new KeyValuePair<string, string>(companyCustomUserProfileField.FieldName, $"FlowHub/SelectOptions/CustomFieldOptions/{companyCustomUserProfileField.Id}")
        };

    protected TInput? GetConcreteInput<TInput>(Dictionary<string, object?>? parameters)
        where TInput : class
        => parameters is null ? null : JsonConvert.DeserializeObject<TInput>(JsonConvert.SerializeObject(parameters))!;

    protected void PopulateOptionRequestPath(
        HashSet<FieldMetadata> fieldMetadataSet,
        Dictionary<string, string> customFieldOptionsRequestPathDict,
        Dictionary<string, string> additionalOptionsRequestPathDict)
    {
        foreach (var fieldMetadata in fieldMetadataSet)
        {
            if (fieldMetadata.FieldPath.StartsWith("contact.")
                || fieldMetadata.FieldPath.StartsWith("contact[")
                || fieldMetadata.FieldPath.StartsWith("post_updated_contact.")
                || fieldMetadata.FieldPath.StartsWith("pre_updated_contact."))
            {
                var customField = customFieldOptionsRequestPathDict.Keys.FirstOrDefault(x => fieldMetadata.FieldPath.Contains(x));

                if (customField is not null)
                {
                    fieldMetadata.OptionsRequestPath = customFieldOptionsRequestPathDict[customField];
                    fieldMetadata.FieldType = PredefineDataType.Options;
                }
            }

            if (additionalOptionsRequestPathDict.TryGetValue(fieldMetadata.FieldPath, out var additionalOptionsRequestPath))
            {
                fieldMetadata.OptionsRequestPath = additionalOptionsRequestPath;
                fieldMetadata.FieldType = PredefineDataType.Options;
            }
        }
    }

    private static JToken? GetCustomFieldSampleValue(CompanyCustomUserProfileField customField)
        => customField.Type switch
        {
            FieldDataType.Options when customField.FieldName == "AssignedTeam" => JToken.FromObject(123),
            FieldDataType.SingleLineText => JToken.FromObject("single line sample text"),
            FieldDataType.MultiLineText => JToken.FromObject("multi line\nsample text"),
            FieldDataType.Number => JToken.FromObject(123),
            FieldDataType.PhoneNumber => JToken.FromObject("85212345678"),
            FieldDataType.Email => JToken.FromObject("<EMAIL>"),
            FieldDataType.Options => customField.CustomUserProfileFieldOptions.FirstOrDefault()?.Value ?? "option value",
            FieldDataType.Boolean => JToken.FromObject(true),
            FieldDataType.DateTime => JToken.FromObject(DateTimeOffset.UtcNow),
            FieldDataType.TravisUser => JToken.FromObject(Guid.NewGuid().ToString()),
            FieldDataType.Channel => JToken.FromObject("whatsappcloudapi"),
            FieldDataType.Date => JToken.FromObject(DateTimeOffset.UtcNow),
            _ => null
        };

    protected static void ConvertFieldMetadataToSnakeCase(HashSet<FieldMetadata> fieldMetadataSet)
    {
        foreach (var fieldMetadata in fieldMetadataSet)
        {
            fieldMetadata.FieldPath = ConvertFieldPathToSnakeCase(fieldMetadata.FieldPath);
            fieldMetadata.DisplayPath = ConvertDisplayPathToSnakeCase(fieldMetadata.DisplayPath);
        }
    }

    private static string ConvertFieldPathToSnakeCase(string fieldPath)
    {
        if (string.IsNullOrEmpty(fieldPath))
        {
            return fieldPath;
        }

        // Split by dots and process each segment
        var segments = fieldPath.Split('.');
        var convertedSegments = new List<string>();

        foreach (var segment in segments)
        {
            convertedSegments.Add(ConvertSegmentToSnakeCase(segment));
        }

        return string.Join(".", convertedSegments);
    }

    private static string ConvertDisplayPathToSnakeCase(string displayPath)
    {
        if (string.IsNullOrEmpty(displayPath))
        {
            return displayPath;
        }

        // Split by forward slashes and process each segment
        var segments = displayPath.Split('/');
        var convertedSegments = new List<string>();

        foreach (var segment in segments)
        {
            convertedSegments.Add(ConvertSegmentToSnakeCase(segment));
        }

        return string.Join("/", convertedSegments);
    }

    private static string ConvertSegmentToSnakeCase(string segment)
    {
        if (string.IsNullOrEmpty(segment))
        {
            return segment;
        }

        // Handle array notation like "[*]" or "['key']" - return as-is
        if (segment.StartsWith('[') && segment.EndsWith(']'))
        {
            return segment;
        }

        // Handle segments with array notation like "LineItems[*]"
        var arrayMatch = Regex.Match(segment, @"^([^[\]]+)(\[.+\])$");
        if (arrayMatch.Success)
        {
            var propertyPart = arrayMatch.Groups[1].Value;
            var arrayPart = arrayMatch.Groups[2].Value;
            return ConvertPascalToSnakeCase(propertyPart) + arrayPart;
        }

        // Handle segments with quotes like "['Property Name']" - return as-is
        if (segment.StartsWith("['") && segment.EndsWith("']"))
        {
            return segment;
        }

        // Regular property name
        return ConvertPascalToSnakeCase(segment);
    }

    private static string ConvertPascalToSnakeCase(string input)
    {
        if (string.IsNullOrEmpty(input))
        {
            return input;
        }

        // Insert underscore before uppercase letters (but not at the start)
        // and convert to lowercase
        return Regex.Replace(input, @"(?<!^)([A-Z])", "_$1").ToLowerInvariant();
    }
}