using System;
using System.Linq;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;

namespace Travis_backend.TenantHubDomain.Services
{
    public interface IMobilePlatformDetectionService
    {
        bool IsMobileDevice(string userAgent);
    }

    public class MobilePlatformDetectionService : IMobilePlatformDetectionService
    {
        private static readonly string[] MobileIndicators = {
            // Traditional mobile browser indicators
            "Mobile", "Android", "iPhone", "iPad", "iPod",
            "BlackBerry", "Windows Phone", "Opera Mini",

            // Flutter/Dart app indicators
            "Dart/", "dart:io",

            // Common mobile app patterns
            "SleekFlow/", "Sleekflow/", "sleekflow/",

            // Mobile platform indicators in app User-Agents
            "iOS;", "Android;", "Scale/"
        };

        private readonly IMemoryCache _cache;
        private readonly ILogger<MobilePlatformDetectionService> _logger;

        public MobilePlatformDetectionService(IMemoryCache cache, ILogger<MobilePlatformDetectionService> logger)
        {
            _cache = cache ?? throw new ArgumentNullException(nameof(cache));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public bool IsMobileDevice(string userAgent)
        {
            if (string.IsNullOrEmpty(userAgent)) return false;

            // Use GetHashCode for a simple and efficient cache key.
            var cacheKey = $"mobile_detection:{userAgent.GetHashCode()}";

            // Performance: Check cache first to avoid repeated string operations.
            if (_cache.TryGetValue(cacheKey, out bool isMobile))
            {
                return isMobile;
            }

            // Performance: Use OrdinalIgnoreCase for case-insensitive comparison, which is faster than culture-aware comparisons.
            isMobile = MobileIndicators.Any(indicator =>
                userAgent.Contains(indicator, StringComparison.OrdinalIgnoreCase));

            // Log User-Agent detection for debugging purposes
            _logger.LogDebug("Mobile detection for User-Agent '{UserAgent}': {IsMobile}", userAgent, isMobile);

            // Cache the result for 1 hour to reduce processing for subsequent requests with the same user agent.
            var cacheEntryOptions = new MemoryCacheEntryOptions()
                .SetSlidingExpiration(TimeSpan.FromHours(1));

            _cache.Set(cacheKey, isMobile, cacheEntryOptions);

            return isMobile;
        }
    }
}
