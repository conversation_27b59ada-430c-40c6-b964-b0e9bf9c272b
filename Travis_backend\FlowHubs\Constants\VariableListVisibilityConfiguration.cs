﻿using System.Collections.Generic;
using System.Linq;
using Travis_backend.FlowHubs.Models;

namespace Travis_backend.FlowHubs.Constants;

public static class VariableListVisibilityConfiguration
{
    // Base default fields that should be visible across all events (when they exist)
    private static readonly List<VariableVisibilityConfiguration> _baseDefaultFields = new()
    {
        // Contact fields
        new() { FieldPath = "contact.id", IsVisibleInUi = true, UiCopy = "Contact ID" },
        new() { FieldPath = "contact.PhoneNumber", IsVisibleInUi = true, UiCopy = "Phone number" },
        new() { FieldPath = "contact.Email", IsVisibleInUi = true, UiCopy = "Email address" },
        new() { FieldPath = "contact.first_name", IsVisibleInUi = true, UiCopy = "First name" },
        new() { FieldPath = "contact.last_name", IsVisibleInUi = true, UiCopy = "Last name" },
        new() { FieldPath = "contact.company_id", IsVisibleInUi = true, UiCopy = "Company ID" },
        new() { FieldPath = "contact.country", IsVisibleInUi = true, UiCopy = "Country" }, // not mandatory field in contact
        new() { FieldPath = "contact.LastChannel", IsVisibleInUi = true, UiCopy = "Last channel type" },
        new() { FieldPath = "contact.ContactOwner", IsVisibleInUi = true, UiCopy = "Contact owner" },
        new() { FieldPath = "contact.job_title", IsVisibleInUi = true, UiCopy = "Job title" }, // not mandatory field in contact
        new() { FieldPath = "contact.created_at", IsVisibleInUi = true, UiCopy = "Created on" },
        new() { FieldPath = "contact.labels[*].label_value", IsVisibleInUi = true, UiCopy = "Label" },

        // Label and List fields
        new() { FieldPath = "labels[*].label_value", IsVisibleInUi = true, UiCopy = "abel" },
        new() { FieldPath = "lists[*].id", IsVisibleInUi = true, UiCopy = "List ID" },

        // ContactOwner fields
        new() { FieldPath = "contactOwner.first_name", IsVisibleInUi = true, UiCopy = "Contact owner first name" },
        new() { FieldPath = "contactOwner.last_name", IsVisibleInUi = true, UiCopy = "Contact owner last name" },
        new() { FieldPath = "contactOwner.id", IsVisibleInUi = true, UiCopy = "Contact owner id" },

        // Conversation fields
        new() { FieldPath = "conversation_id", IsVisibleInUi = true, UiCopy = "Conversation ID" },
        new() { FieldPath = "conversation.status", IsVisibleInUi = true, UiCopy = "Conversation status" },
    };

    private static readonly Dictionary<string, List<VariableVisibilityConfiguration>> _eventVariableConfigurations = new()
    {
        [TriggerIds.IncomingMessageReceived] = new()
        {
            new() { FieldPath = "message_id", IsVisibleInUi = true, UiCopy = "Message ID" },
            new() { FieldPath = "message.message_type", IsVisibleInUi = true, UiCopy = "Message format" },
            new() { FieldPath = "message.message_content", IsVisibleInUi = true, UiCopy = "Message content" },
            new() { FieldPath = "message.created_at", IsVisibleInUi = true, UiCopy = "Message created on" },
            new() { FieldPath = "message.message_body.order_message.catalog_id", IsVisibleInUi = true, UiCopy = "WhatsApp cart ID" },
            new() { FieldPath = "message.message_body.order_message.product_items_json", IsVisibleInUi = true, UiCopy = "WhatsApp cart details" },
            new() { FieldPath = "channel_id", IsVisibleInUi = true, UiCopy = "Channel" },
        },

        [TriggerIds.OutgoingMessageSent] = new()
        {
            new() { FieldPath = "message_id", IsVisibleInUi = true, UiCopy = "Message ID" },
            new() { FieldPath = "message.message_content", IsVisibleInUi = true, UiCopy = "Message content" },
            new() { FieldPath = "message.created_at", IsVisibleInUi = true, UiCopy = "Message created on" },
            new() { FieldPath = "message.message_type", IsVisibleInUi = true, UiCopy = "Message format" },
            new() { FieldPath = "message.message_status", IsVisibleInUi = true, UiCopy = "Message status" },
            new() { FieldPath = "message.message_delivery_type", IsVisibleInUi = true, UiCopy = "Message delivery type" },

            new() { FieldPath = "channel_id", IsVisibleInUi = true, UiCopy = "Channel" },
            new () { FieldPath = "sleekflow_staff_id", IsVisibleInUi = true, UiCopy = "Modified by" }
        },

        [TriggerIds.ClickToWhatsappAd] = new()
            {
                new() { FieldPath = "message_id", IsVisibleInUi = true, UiCopy = "Message ID" },
                new() { FieldPath = "message.message_content", IsVisibleInUi = true, UiCopy = "Message content" },
                new() { FieldPath = "message.created_at", IsVisibleInUi = true, UiCopy = "Message created on" },
                new() { FieldPath = "message.message_type", IsVisibleInUi = true, UiCopy = "Message format" },

                new() { FieldPath = "channel_id", IsVisibleInUi = true, UiCopy = "Channel" },

                new() { FieldPath = "message.headline", IsVisibleInUi = true, UiCopy = "Ad Headline" },
                new() { FieldPath = "message.body", IsVisibleInUi = true, UiCopy = "Ad Body Text" },
                new() { FieldPath = "message.source_url", IsVisibleInUi = true, UiCopy = "Ad Source URL" },
                new() { FieldPath = "message.source_id", IsVisibleInUi = true, UiCopy = "Ad Source ID" },
                new() { FieldPath = "message.ctwa_clid", IsVisibleInUi = true, UiCopy = "Ad Click ID" },
            },

        [TriggerIds.MessageStatusUpdated] = new()
        {
            new() { FieldPath = "message_id", IsVisibleInUi = true, UiCopy = "Message ID" },
            new() { FieldPath = "message.message_content", IsVisibleInUi = true, UiCopy = "Message content" },
            new() { FieldPath = "message.created_at", IsVisibleInUi = true, UiCopy = "Message created on" },
            new() { FieldPath = "message.message_type", IsVisibleInUi = true, UiCopy = "Message format" },
            new() { FieldPath = "message.message_status", IsVisibleInUi = true, UiCopy = "Message status" },
            new() { FieldPath = "message.message_delivery_type", IsVisibleInUi = true, UiCopy = "Message delivery type" },

            new() { FieldPath = "channel_id", IsVisibleInUi = true, UiCopy = "Channel" },
            new () { FieldPath = "sleekflow_staff_id", IsVisibleInUi = true, UiCopy = "Modified by" }
        },

        [TriggerIds.ConversationStatusUpdated] = new()
        {
            new() { FieldPath = "original_status", IsVisibleInUi = true, UiCopy = "Previous conversation status" },
            new() { FieldPath = "new_status", IsVisibleInUi = true, UiCopy = "New conversation status " },
            new () { FieldPath = "sleekflow_staff_id", IsVisibleInUi = true, UiCopy = "Modified by" }
        },

        [TriggerIds.LabelAdded] = new()
        {
            new() { FieldPath = "added_to_labels[*].label_value", IsVisibleInUi = true, UiCopy = "Added label" },
            new() { FieldPath = "labels[*].label_value", IsVisibleInUi = true, UiCopy = "Label" },
            new () { FieldPath = "sleekflow_staff_id", IsVisibleInUi = true, UiCopy = "Modified by" }
        },

        [TriggerIds.LabelRemoved] = new()
        {
            new() { FieldPath = "removed_from_labels[*].label_value", IsVisibleInUi = true, UiCopy = "Removed label" },
            new() { FieldPath = "labels[*].label_value", IsVisibleInUi = true, UiCopy = "Label" },
            new () { FieldPath = "sleekflow_staff_id", IsVisibleInUi = true, UiCopy = "Modified by" }
        },

        [TriggerIds.ListAdded] = new()
        {
            new() { FieldPath = "added_to_lists[*].list_id", IsVisibleInUi = true, UiCopy = "Added list" },
            new() { FieldPath = "lists[*].list_id", IsVisibleInUi = true, UiCopy = "All list" },
            new () { FieldPath = "sleekflow_staff_id", IsVisibleInUi = true, UiCopy = "Modified by" }
        },

        [TriggerIds.ListRemoved] = new()
        {
            new() { FieldPath = "removed_from_lists[*].list_id", IsVisibleInUi = true, UiCopy = "Removed list" },
            new() { FieldPath = "lists[*].list_id", IsVisibleInUi = true, UiCopy = "All list" },
            new () { FieldPath = "sleekflow_staff_id", IsVisibleInUi = true, UiCopy = "Modified by" }
        },

        [TriggerIds.TicketUpdated] = new()
        {
            new () { FieldPath = "sleekflow_staff_id", IsVisibleInUi = true, UiCopy = "Modified by" },
            new () { FieldPath = "updated_properties.type_id", IsVisibleInUi = true, UiCopy = "Ticket type updated" },
            new () { FieldPath = "updated_properties.status_id", IsVisibleInUi = true, UiCopy = "Updated ticket status" },
            new () { FieldPath = "updated_properties.priority_id", IsVisibleInUi = true, UiCopy = "Updated ticket priority" },
            new () { FieldPath = "updated_properties.due_date", IsVisibleInUi = true, UiCopy = "Updated due date" },
            new () { FieldPath = "updated_properties.assignee_id", IsVisibleInUi = true, UiCopy = "Updated ticket assignee" },
            new () { FieldPath = "updated_property_list[*]", IsVisibleInUi = true, UiCopy = "Updated ticket property" },
            new () { FieldPath = "ticket.current_assignee", IsVisibleInUi = true, UiCopy = "Ticket assignee" },
            new () { FieldPath = "ticket.resolution_agent", IsVisibleInUi = true, UiCopy = "Ticket resolved by" },
            new () { FieldPath = "ticket.created_at", IsVisibleInUi = true, UiCopy = "Ticket created on" },
            new () { FieldPath = "ticket.created_by", IsVisibleInUi = true, UiCopy = "Ticket created by" },
            new () { FieldPath = "ticket.id", IsVisibleInUi = true, UiCopy = "Ticket ID" },
            new () { FieldPath = "ticket.title", IsVisibleInUi = true, UiCopy = "Ticket title" },
            new () { FieldPath = "ticket.channel.channel_name", IsVisibleInUi = true, UiCopy = "Channel name" },
            new () { FieldPath = "ticket.status_name", IsVisibleInUi = true, UiCopy = "Ticket status" },
            new () { FieldPath = "ticket.priority_name", IsVisibleInUi = true, UiCopy = "Ticket priority" },
            new () { FieldPath = "ticket.due_date", IsVisibleInUi = true, UiCopy = "Ticket due date" },
            new () { FieldPath = "ticket.type_name", IsVisibleInUi = true, UiCopy = "Ticket type" },
            new () { FieldPath = "ticket.description", IsVisibleInUi = true, UiCopy = "Ticket description" },
            new () { FieldPath = "ticket.first_assignee", IsVisibleInUi = true, UiCopy = "First ticket assignee" },
        },

        [TriggerIds.NewContactAdded] = new()
        {
            new() { FieldPath = "contact.id", IsVisibleInUi = true, UiCopy = "Contact ID" },
            new() { FieldPath = "contact.created_at", IsVisibleInUi = false, UiCopy = "Contact created on" },
        },

        [TriggerIds.ContactPropertyUpdated] = new()
        {
            new() { FieldPath = "post_updated_contact.id", IsVisibleInUi = true, UiCopy = "Contact ID updated" },
            new() { FieldPath = "post_updated_contact.company_id", IsVisibleInUi = true, UiCopy = "Company ID updated" },
            new() { FieldPath = "post_updated_contact.first_name", IsVisibleInUi = true, UiCopy = "First name updated" },
            new() { FieldPath = "post_updated_contact.last_name", IsVisibleInUi = true, UiCopy = "Last name updated" },
            new() { FieldPath = "post_updated_contact.created_at", IsVisibleInUi = true, UiCopy = "Contact created on" },
            new() { FieldPath = "post_updated_contact.updated_at", IsVisibleInUi = true, UiCopy = "Contact updated on" },
            new() { FieldPath = "post_updated_contact.last_contact", IsVisibleInUi = true, UiCopy = "Last contacted on" },
            new() { FieldPath = "post_updated_contact.last_contact_from_customers", IsVisibleInUi = true, UiCopy = "Last message from contact" },
            new () { FieldPath = "sleekflow_staff_id", IsVisibleInUi = true, UiCopy = "Modified by" },
        },

        [TriggerIds.FbIgPostCommentReceived] = new()
        {
            new() { FieldPath = "contact.id", IsVisibleInUi = true, UiCopy = "Contact ID" },
            new() { FieldPath = "post_comment.post_id", IsVisibleInUi = true, UiCopy = "Post ID" },
            new() { FieldPath = "post_comment.from.facebook_name", IsVisibleInUi = true, UiCopy = "User name" },
            new() { FieldPath = "post_comment.text", IsVisibleInUi = true, UiCopy = "Comment" },
            new() { FieldPath = "page_id", IsVisibleInUi = true, UiCopy = "Page" },

            new () { FieldPath = "is_new_contact", IsVisibleInUi = true, UiCopy = "Contact is new" },
            new () { FieldPath = "conversation_id", IsVisibleInUi = true, UiCopy = "Conversation ID" },
        },

        [TriggerIds.WhatsappFlowSubmissionMessageReceived] = new()
        {
            new () { FieldPath = "message.message_content", IsVisibleInUi = true, UiCopy = "Message content" },
            new() { FieldPath = "channel_id", IsVisibleInUi = true, UiCopy = "Channel" },
            new () { FieldPath = "conversation_id", IsVisibleInUi = true, UiCopy = "Conversation ID" },
            new() { FieldPath = "message.id", IsVisibleInUi = true, UiCopy = "WhatsApp Flow message ID" },
            new() { FieldPath = "is_new_contact", IsVisibleInUi = true, UiCopy = "Contact is new" },
        },

        [TriggerIds.SchemafulObjectCreated] = new()
        {
            new () { FieldPath = "sleekflow_staff_id", IsVisibleInUi = true, UiCopy = "Modified by" },
            new () { FieldPath = "primary_property_value", IsVisibleInUi = true, UiCopy = "Primary property value" },
        },

        [TriggerIds.SchemafulObjectUpdated] = new()
        {
            new () { FieldPath = "sleekflow_staff_id", IsVisibleInUi = true, UiCopy = "Modified by" },
            new () { FieldPath = "primary_property_value", IsVisibleInUi = true, UiCopy = "Primary property value" },
        },

        [TriggerIds.LiveChtWebsiteUrlDetected] = new()
        {
            new() { FieldPath = "channel_id", IsVisibleInUi = true, UiCopy = "Channel" },
            new () { FieldPath = "tracking_url", IsVisibleInUi = true, UiCopy = "Page URL contains" },
            new() { FieldPath = "contact.id", IsVisibleInUi = true, UiCopy = "Contact ID" },
        },
    };

    private static List<VariableVisibilityConfiguration> GetMergedConfiguration(string eventName)
    {
        var eventSpecificConfig = _eventVariableConfigurations.TryGetValue(eventName, out var config)
            ? config
            : new List<VariableVisibilityConfiguration>();

        var mergedConfig = new List<VariableVisibilityConfiguration>();

        // Add base default fields first
        mergedConfig.AddRange(_baseDefaultFields);

        // Add event-specific fields (these will override base fields if there are duplicates)
        mergedConfig.AddRange(eventSpecificConfig);

        // Remove duplicates, keeping the event-specific version when there's a conflict
        var finalConfig = mergedConfig
            .GroupBy(x => x.FieldPath)
            .Select(g => g.Last()) // Last one wins (event-specific overrides base)
            .ToList();

        return finalConfig;
    }

    public static Dictionary<string, (bool IsVisible, string UiCopy)> GetVariableConfiguration(string eventName)
    {
        if (!_eventVariableConfigurations.TryGetValue(eventName, out var configurations))
        {
            return new Dictionary<string, (bool, string)>();
        }
        var mergedConfig = GetMergedConfiguration(eventName);

        var result = new Dictionary<string, (bool IsVisible, string UiCopy)>();
        foreach (var config in mergedConfig)
        {
            result[config.FieldPath] = (config.IsVisibleInUi, config.UiCopy);
        }

        return result;
    }

    public static HashSet<string> GetVisibleFieldPaths(string eventName)
    {
        var configurations = GetMergedConfiguration(eventName);

        return configurations
            .Where(config => config.IsVisibleInUi)
            .Select(config => config.FieldPath)
            .ToHashSet();
    }

    public static Dictionary<string, string> GetFieldUiCopyMapping(string eventName)
    {
        var configurations = GetMergedConfiguration(eventName);

        return configurations
            .Where(config => config.IsVisibleInUi)
            .ToDictionary(config => config.FieldPath, config => config.UiCopy);
    }
}